package detectors

import (
	"context"
	"regexp"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectWorkflowStepBypass 检测工作流步骤绕过
func (d *BusinessProcessDetector) detectWorkflowStepBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 简化的工作流步骤绕过检测
	if d.hasBusinessProcessFeatures(target) {
		maxConfidence = 0.6
		vulnerablePayload = "工作流步骤绕过测试"
		vulnerableRequest = target.URL
		vulnerableResponse = "检测到工作流步骤绕过功能"

		evidence = append(evidence, plugins.Evidence{
			Type:        "workflow-step-bypass",
			Description: "发现工作流步骤绕过功能",
			Content:     "检测到可能的工作流步骤绕过漏洞",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectStatusManipulation 检测状态篡改
func (d *BusinessProcessDetector) detectStatusManipulation(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 简化的状态篡改检测
	if d.hasBusinessProcessFeatures(target) {
		maxConfidence = 0.5
		vulnerablePayload = "状态篡改测试"
		vulnerableRequest = target.URL
		vulnerableResponse = "检测到状态篡改功能"

		evidence = append(evidence, plugins.Evidence{
			Type:        "status-manipulation",
			Description: "发现状态篡改功能",
			Content:     "检测到可能的状态篡改漏洞",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectApprovalBypass 检测审批绕过
func (d *BusinessProcessDetector) detectApprovalBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 简化的审批绕过检测
	if d.hasBusinessProcessFeatures(target) {
		maxConfidence = 0.5
		vulnerablePayload = "审批绕过测试"
		vulnerableRequest = target.URL
		vulnerableResponse = "检测到审批绕过功能"

		evidence = append(evidence, plugins.Evidence{
			Type:        "approval-bypass",
			Description: "发现审批绕过功能",
			Content:     "检测到可能的审批绕过漏洞",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectWorkflowInjection 检测工作流注入
func (d *BusinessProcessDetector) detectWorkflowInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 简化的工作流注入检测
	if d.hasBusinessProcessFeatures(target) {
		maxConfidence = 0.5
		vulnerablePayload = "工作流注入测试"
		vulnerableRequest = target.URL
		vulnerableResponse = "检测到工作流注入功能"

		evidence = append(evidence, plugins.Evidence{
			Type:        "workflow-injection",
			Description: "发现工作流注入功能",
			Content:     "检测到可能的工作流注入漏洞",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// 初始化方法

// initializeWorkflowPaths 初始化工作流路径列表
func (d *BusinessProcessDetector) initializeWorkflowPaths() {
	d.workflowPaths = []string{
		// 工作流相关路径
		"/workflow", "/workflows", "/process", "/processes", "/flow", "/flows",
		"/step", "/steps", "/stage", "/stages", "/phase", "/phases",
		"/task", "/tasks", "/job", "/jobs", "/activity", "/activities",
		"/action", "/actions", "/operation", "/operations", "/procedure", "/procedures",
		
		// 审批相关路径
		"/approval", "/approvals", "/approve", "/review", "/reviews",
		"/submit", "/submission", "/submissions", "/confirm", "/confirmation",
		"/verify", "/verification", "/validate", "/validation",
		"/accept", "/reject", "/pending", "/complete", "/finish",
		
		// 状态相关路径
		"/status", "/state", "/states", "/progress", "/progresss",
		"/current", "/next", "/previous", "/first", "/last", "/final",
		"/draft", "/submitted", "/approved", "/rejected", "/completed",
		
		// 中文路径
		"/流程", "/工作流", "/步骤", "/阶段", "/任务", "/活动", "/操作",
		"/审批", "/审核", "/提交", "/确认", "/验证", "/批准", "/拒绝",
		"/状态", "/进度", "/当前", "/下一步", "/上一步", "/完成",
	}
}

// initializeProcessSteps 初始化流程步骤列表
func (d *BusinessProcessDetector) initializeProcessSteps() {
	d.processSteps = []string{
		// 数字步骤
		"1", "2", "3", "4", "5", "6", "7", "8", "9", "10",
		"step1", "step2", "step3", "step4", "step5",
		"stage1", "stage2", "stage3", "stage4", "stage5",
		"phase1", "phase2", "phase3", "phase4", "phase5",
		
		// 英文步骤
		"start", "begin", "first", "initial", "draft",
		"submit", "review", "approve", "confirm", "verify",
		"process", "execute", "complete", "finish", "end",
		"final", "last", "done", "closed", "archived",
		
		// 中文步骤
		"开始", "起始", "初始", "草稿", "提交",
		"审核", "审批", "确认", "验证", "处理",
		"执行", "完成", "结束", "最终", "归档",
	}
}

// initializeStatusParameters 初始化状态参数列表
func (d *BusinessProcessDetector) initializeStatusParameters() {
	d.statusParameters = []string{
		// 英文状态参数
		"status", "state", "stage", "phase", "step", "level",
		"progress", "current", "next", "previous", "position",
		"workflow_status", "process_status", "task_status",
		"approval_status", "review_status", "submit_status",
		
		// 中文状态参数
		"状态", "阶段", "步骤", "进度", "当前", "下一步",
		"上一步", "位置", "流程状态", "任务状态", "审批状态",
	}
}

// initializeApprovalParameters 初始化审批参数列表
func (d *BusinessProcessDetector) initializeApprovalParameters() {
	d.approvalParameters = []string{
		// 英文审批参数
		"approval", "approve", "approved", "approver", "approval_id",
		"review", "reviewer", "review_id", "review_status",
		"submit", "submitter", "submit_id", "submit_status",
		"confirm", "confirmation", "confirm_id", "confirm_status",
		"verify", "verification", "verify_id", "verify_status",
		"accept", "reject", "pending", "complete", "finish",
		
		// 中文审批参数
		"审批", "审核", "提交", "确认", "验证",
		"批准", "拒绝", "待审", "完成", "结束",
		"审批人", "审核人", "提交人", "确认人", "验证人",
	}
}

// initializeWorkflowPayloads 初始化工作流载荷列表
func (d *BusinessProcessDetector) initializeWorkflowPayloads() {
	d.workflowPayloads = []WorkflowPayload{
		// 步骤绕过载荷
		{Name: "跳过步骤", Parameter: "step", Value: "999", Description: "尝试跳过到不存在的步骤", Category: "step_bypass"},
		{Name: "负步骤", Parameter: "step", Value: "-1", Description: "尝试使用负数步骤", Category: "step_bypass"},
		{Name: "零步骤", Parameter: "step", Value: "0", Description: "尝试使用零步骤", Category: "step_bypass"},
		{Name: "最终步骤", Parameter: "step", Value: "final", Description: "尝试直接跳到最终步骤", Category: "step_bypass"},
		{Name: "完成步骤", Parameter: "step", Value: "complete", Description: "尝试直接标记为完成", Category: "step_bypass"},
		
		// 状态篡改载荷
		{Name: "已批准状态", Parameter: "status", Value: "approved", Description: "尝试直接设置为已批准状态", Category: "status_manipulation"},
		{Name: "已完成状态", Parameter: "status", Value: "completed", Description: "尝试直接设置为已完成状态", Category: "status_manipulation"},
		{Name: "已确认状态", Parameter: "status", Value: "confirmed", Description: "尝试直接设置为已确认状态", Category: "status_manipulation"},
		{Name: "已验证状态", Parameter: "status", Value: "verified", Description: "尝试直接设置为已验证状态", Category: "status_manipulation"},
		{Name: "管理员状态", Parameter: "status", Value: "admin", Description: "尝试设置为管理员状态", Category: "status_manipulation"},
		
		// 审批绕过载荷
		{Name: "自动批准", Parameter: "approval", Value: "auto", Description: "尝试自动批准", Category: "approval_bypass"},
		{Name: "跳过审批", Parameter: "approval", Value: "skip", Description: "尝试跳过审批", Category: "approval_bypass"},
		{Name: "强制批准", Parameter: "approval", Value: "force", Description: "尝试强制批准", Category: "approval_bypass"},
		{Name: "管理员批准", Parameter: "approval", Value: "admin", Description: "尝试使用管理员批准", Category: "approval_bypass"},
		{Name: "系统批准", Parameter: "approval", Value: "system", Description: "尝试使用系统批准", Category: "approval_bypass"},
		
		// 工作流注入载荷
		{Name: "SQL注入", Parameter: "workflow_id", Value: "1' OR '1'='1", Description: "尝试SQL注入工作流ID", Category: "workflow_injection"},
		{Name: "脚本注入", Parameter: "workflow_name", Value: "<script>alert('xss')</script>", Description: "尝试脚本注入工作流名称", Category: "workflow_injection"},
		{Name: "命令注入", Parameter: "workflow_cmd", Value: "; ls -la", Description: "尝试命令注入工作流命令", Category: "workflow_injection"},
		{Name: "路径遍历", Parameter: "workflow_file", Value: "../../../etc/passwd", Description: "尝试路径遍历工作流文件", Category: "workflow_injection"},
		{Name: "XML注入", Parameter: "workflow_xml", Value: "<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>", Description: "尝试XML注入工作流配置", Category: "workflow_injection"},
	}
}

// initializePatterns 初始化检测模式
func (d *BusinessProcessDetector) initializePatterns() {
	// 流程检测模式
	processPatternStrings := []string{
		`(?i)workflow\s+(step|stage|phase|status|state)`,
		`(?i)(step|stage|phase|status|state)\s+workflow`,
		`(?i)process\s+(step|stage|phase|status|state)`,
		`(?i)(step|stage|phase|status|state)\s+process`,
		`(?i)business\s+(process|workflow|flow)`,
		`(?i)(process|workflow|flow)\s+business`,
		`(?i)流程\s*(步骤|阶段|状态)`,
		`(?i)(步骤|阶段|状态)\s*流程`,
		`(?i)工作流\s*(步骤|阶段|状态)`,
		`(?i)(步骤|阶段|状态)\s*工作流`,
	}

	d.processPatterns = make([]*regexp.Regexp, 0, len(processPatternStrings))
	for _, pattern := range processPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.processPatterns = append(d.processPatterns, compiled)
		}
	}

	// 绕过检测模式
	bypassPatternStrings := []string{
		`(?i)(bypass|skip|jump|override)\s+(step|stage|phase|approval|review)`,
		`(?i)(step|stage|phase|approval|review)\s+(bypass|skip|jump|override)`,
		`(?i)(force|auto|direct)\s+(approval|approve|confirm|complete)`,
		`(?i)(approval|approve|confirm|complete)\s+(force|auto|direct)`,
		`(?i)(绕过|跳过|强制)\s*(步骤|阶段|审批|审核)`,
		`(?i)(步骤|阶段|审批|审核)\s*(绕过|跳过|强制)`,
		`(?i)(自动|直接|强制)\s*(批准|确认|完成)`,
		`(?i)(批准|确认|完成)\s*(自动|直接|强制)`,
	}

	d.bypassPatterns = make([]*regexp.Regexp, 0, len(bypassPatternStrings))
	for _, pattern := range bypassPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.bypassPatterns = append(d.bypassPatterns, compiled)
		}
	}

	// 安全检测模式
	securityPatternStrings := []string{
		`(?i)(validation|verification|check|control)\s+(step|stage|phase|workflow|process)`,
		`(?i)(step|stage|phase|workflow|process)\s+(validation|verification|check|control)`,
		`(?i)(security|protection|guard|filter)\s+(workflow|process|approval)`,
		`(?i)(workflow|process|approval)\s+(security|protection|guard|filter)`,
		`(?i)(验证|校验|检查|控制)\s*(步骤|阶段|流程|工作流)`,
		`(?i)(步骤|阶段|流程|工作流)\s*(验证|校验|检查|控制)`,
		`(?i)(安全|保护|防护|过滤)\s*(工作流|流程|审批)`,
		`(?i)(工作流|流程|审批)\s*(安全|保护|防护|过滤)`,
	}

	d.securityPatterns = make([]*regexp.Regexp, 0, len(securityPatternStrings))
	for _, pattern := range securityPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.securityPatterns = append(d.securityPatterns, compiled)
		}
	}
}
