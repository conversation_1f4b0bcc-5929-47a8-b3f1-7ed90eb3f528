package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectStackOverflow 检测栈溢出
func (d *BufferOverflowDetector) detectStackOverflow(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试栈溢出载荷
	for _, payload := range d.stackOverflowPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送栈溢出请求
		resp, err := d.sendBufferRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查栈溢出响应
		confidence := d.checkStackResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("栈溢出: %s", payload[:min(50, len(payload))])
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "stack-overflow",
				Description: fmt.Sprintf("发现栈溢出: %s (置信度: %.2f)", payload[:min(20, len(payload))], confidence),
				Content:     d.extractBufferEvidence(resp, "stack-overflow"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectHeapOverflow 检测堆溢出
func (d *BufferOverflowDetector) detectHeapOverflow(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试堆溢出载荷
	for _, payload := range d.heapOverflowPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送堆溢出请求
		resp, err := d.sendBufferRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查堆溢出响应
		confidence := d.checkHeapResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("堆溢出: %s", payload[:min(50, len(payload))])
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "heap-overflow",
				Description: fmt.Sprintf("发现堆溢出: %s (置信度: %.2f)", payload[:min(20, len(payload))], confidence),
				Content:     d.extractBufferEvidence(resp, "heap-overflow"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectFormatStringOverflow 检测格式化字符串溢出
func (d *BufferOverflowDetector) detectFormatStringOverflow(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试格式化字符串载荷
	for _, payload := range d.formatStringPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送格式化字符串请求
		resp, err := d.sendBufferRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查格式化字符串响应
		confidence := d.checkFormatResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("格式化字符串: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "format-string-overflow",
				Description: fmt.Sprintf("发现格式化字符串溢出: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractBufferEvidence(resp, "format-string-overflow"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectIntegerOverflow 检测整数溢出
func (d *BufferOverflowDetector) detectIntegerOverflow(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试整数溢出载荷
	for _, payload := range d.integerOverflowPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送整数溢出请求
		resp, err := d.sendBufferRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查整数溢出响应
		confidence := d.checkIntegerResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("整数溢出: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "integer-overflow",
				Description: fmt.Sprintf("发现整数溢出: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractBufferEvidence(resp, "integer-overflow"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendBufferRequest 发送缓冲区溢出请求
func (d *BufferOverflowDetector) sendBufferRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendBufferGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST表单注入
	postResp, err := d.sendBufferPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 尝试PUT请求注入
	putResp, err := d.sendBufferPUTRequest(ctx, targetURL, payload)
	if err == nil && putResp != "" {
		return putResp, nil
	}

	// 返回POST响应（即使有错误）
	if postResp != "" {
		return postResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendBufferGETRequest 发送缓冲区溢出GET请求
func (d *BufferOverflowDetector) sendBufferGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendBufferPOSTRequest 发送缓冲区溢出POST请求
func (d *BufferOverflowDetector) sendBufferPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendBufferPUTRequest 发送缓冲区溢出PUT请求
func (d *BufferOverflowDetector) sendBufferPUTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "PUT", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkStackResponse 检查栈溢出响应
func (d *BufferOverflowDetector) checkStackResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示栈溢出
	} else if strings.Contains(response, "status: 502") || strings.Contains(response, "status: 503") {
		confidence += 0.3 // 网关错误可能表示服务崩溃
	}

	// 检查栈溢出模式匹配
	for _, pattern := range d.stackPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查栈溢出特定指示器
	stackIndicators := []string{
		"stack overflow", "stack smash", "stack corruption", "buffer overflow",
		"segmentation fault", "access violation", "memory corruption",
		"stack protector", "canary died", "__stack_chk_fail",
		"core dump", "signal 11", "sigsegv", "sigbus", "sigabrt",
		"栈溢出", "缓冲区溢出", "内存错误", "段错误",
	}

	for _, indicator := range stackIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // 栈溢出特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映（可能表示缓冲区被覆盖）
	if len(payload) > 100 {
		payloadSample := payload[:100]
		if strings.Contains(response, payloadSample) {
			confidence += 0.2
		}
	}

	return confidence
}

// checkHeapResponse 检查堆溢出响应
func (d *BufferOverflowDetector) checkHeapResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示堆溢出
	} else if strings.Contains(response, "status: 502") || strings.Contains(response, "status: 503") {
		confidence += 0.3 // 网关错误可能表示服务崩溃
	}

	// 检查堆溢出模式匹配
	for _, pattern := range d.heapPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查堆溢出特定指示器
	heapIndicators := []string{
		"heap overflow", "heap corruption", "heap buffer overflow",
		"double free", "use after free", "invalid heap pointer", "heap block corrupted",
		"malloc error", "free error", "realloc error", "heap manager", "memory allocator",
		"heap debug", "heap check", "heap validate", "heap walk",
		"堆溢出", "堆损坏", "内存分配错误", "释放错误",
	}

	for _, indicator := range heapIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // 堆溢出特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映（可能表示堆被覆盖）
	if len(payload) > 100 {
		payloadSample := payload[:100]
		if strings.Contains(response, payloadSample) {
			confidence += 0.2
		}
	}

	return confidence
}

// checkFormatResponse 检查格式化字符串响应
func (d *BufferOverflowDetector) checkFormatResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示格式化字符串错误
	} else if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能包含格式化字符串输出
	}

	// 检查格式化字符串模式匹配
	for _, pattern := range d.formatPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查格式化字符串特定指示器
	formatIndicators := []string{
		"format string", "printf error", "sprintf error", "fprintf error", "snprintf error",
		"invalid format", "format specifier", "(nil)", "0x", "格式字符串", "格式错误", "打印错误",
	}

	for _, indicator := range formatIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // 格式化字符串特定指示器的强指示器
			break
		}
	}

	// 检查十六进制地址泄露（格式化字符串攻击的典型特征）
	if strings.Contains(response, "0x") && (strings.Contains(payload, "%x") || strings.Contains(payload, "%p")) {
		confidence += 0.4
	}

	return confidence
}

// checkIntegerResponse 检查整数溢出响应
func (d *BufferOverflowDetector) checkIntegerResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示整数溢出
	} else if strings.Contains(response, "status: 400") {
		confidence += 0.3 // 客户端错误可能表示输入验证失败
	}

	// 检查整数溢出模式匹配
	for _, pattern := range d.integerPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查整数溢出特定指示器
	integerIndicators := []string{
		"integer overflow", "integer underflow", "arithmetic overflow", "numeric overflow",
		"value too large", "value too small", "out of range", "conversion error",
		"cast error", "type error", "size error", "length error",
		"bounds check", "array bounds", "index out of bounds", "buffer bounds",
		"整数溢出", "整数下溢", "数值溢出", "范围错误",
	}

	for _, indicator := range integerIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // 整数溢出特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	return confidence
}

// extractBufferEvidence 提取缓冲区溢出证据
func (d *BufferOverflowDetector) extractBufferEvidence(response, overflowType string) string {
	// 限制证据长度
	maxLength := 500
	if len(response) > maxLength {
		response = response[:maxLength] + "..."
	}

	// 根据溢出类型提取相关证据
	switch overflowType {
	case "stack-overflow":
		return d.extractStackEvidence(response)
	case "heap-overflow":
		return d.extractHeapEvidence(response)
	case "format-string-overflow":
		return d.extractFormatEvidence(response)
	case "integer-overflow":
		return d.extractIntegerEvidence(response)
	default:
		return response
	}
}

// extractStackEvidence 提取栈溢出证据
func (d *BufferOverflowDetector) extractStackEvidence(response string) string {
	evidence := "栈溢出证据:\n"

	// 查找栈溢出特定内容
	stackIndicators := []string{
		"stack overflow", "stack smash", "stack corruption", "buffer overflow",
		"segmentation fault", "access violation", "memory corruption",
		"stack protector", "canary died", "__stack_chk_fail",
	}

	for _, indicator := range stackIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现栈溢出指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractHeapEvidence 提取堆溢出证据
func (d *BufferOverflowDetector) extractHeapEvidence(response string) string {
	evidence := "堆溢出证据:\n"

	// 查找堆溢出特定内容
	heapIndicators := []string{
		"heap overflow", "heap corruption", "heap buffer overflow",
		"double free", "use after free", "invalid heap pointer", "heap block corrupted",
		"malloc error", "free error", "realloc error", "heap manager",
	}

	for _, indicator := range heapIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现堆溢出指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractFormatEvidence 提取格式化字符串证据
func (d *BufferOverflowDetector) extractFormatEvidence(response string) string {
	evidence := "格式化字符串溢出证据:\n"

	// 查找格式化字符串特定内容
	formatIndicators := []string{
		"format string", "printf error", "sprintf error", "fprintf error",
		"invalid format", "format specifier", "(nil)", "0x",
	}

	for _, indicator := range formatIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现格式化字符串指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractIntegerEvidence 提取整数溢出证据
func (d *BufferOverflowDetector) extractIntegerEvidence(response string) string {
	evidence := "整数溢出证据:\n"

	// 查找整数溢出特定内容
	integerIndicators := []string{
		"integer overflow", "integer underflow", "arithmetic overflow", "numeric overflow",
		"value too large", "value too small", "out of range", "conversion error",
		"bounds check", "array bounds", "index out of bounds",
	}

	for _, indicator := range integerIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现整数溢出指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
