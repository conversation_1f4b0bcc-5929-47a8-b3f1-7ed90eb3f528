package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectSecurityHeaders 检测安全头缺失
func (d *ConfigurationErrorDetector) detectSecurityHeaders(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 发送HEAD请求检查安全头
	resp, err := d.sendConfigRequest(ctx, target.URL)
	if err != nil {
		return evidence, 0.0, "", "", ""
	}

	vulnerableRequest = target.URL
	vulnerableResponse = resp

	// 检查缺失的安全头
	missingHeaders := []string{}
	responseLines := strings.Split(resp, "\n")

	for _, header := range d.securityHeaders {
		found := false
		for _, line := range responseLines {
			if strings.HasPrefix(strings.ToLower(line), strings.ToLower(header)+":") {
				found = true
				break
			}
		}

		if !found {
			// 检查是否是关键安全头
			if d.isCriticalSecurityHeader(header) {
				missingHeaders = append(missingHeaders, header)
			}
		}
	}

	// 计算置信度
	if len(missingHeaders) > 0 {
		confidence := float64(len(missingHeaders)) * 0.15
		if confidence > 1.0 {
			confidence = 1.0
		}

		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("缺失安全头: %s", strings.Join(missingHeaders, ", "))
		}

		evidence = append(evidence, plugins.Evidence{
			Type:        "security-headers-missing",
			Description: fmt.Sprintf("发现缺失的安全头: %s (置信度: %.2f)", strings.Join(missingHeaders, ", "), confidence),
			Content:     d.extractSecurityEvidence(resp, missingHeaders),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	// 检查信息泄露头
	infoLeakageHeaders := d.checkInformationLeakageHeaders(resp)
	if len(infoLeakageHeaders) > 0 {
		confidence := float64(len(infoLeakageHeaders)) * 0.2
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("信息泄露头: %s", strings.Join(infoLeakageHeaders, ", "))
		}

		evidence = append(evidence, plugins.Evidence{
			Type:        "information-leakage-headers",
			Description: fmt.Sprintf("发现信息泄露头: %s (置信度: %.2f)", strings.Join(infoLeakageHeaders, ", "), confidence),
			Content:     d.extractSecurityEvidence(resp, infoLeakageHeaders),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDebugMode 检测调试模式
func (d *ConfigurationErrorDetector) detectDebugMode(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	for _, debugPath := range d.debugPaths {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造调试URL
		debugURL := d.buildConfigURL(target.URL, debugPath)

		// 发送调试请求
		resp, err := d.sendConfigRequest(ctx, debugURL)
		if err != nil {
			continue
		}

		// 检查调试模式响应
		confidence := d.checkDebugModeResponse(resp, debugPath)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = debugPath
			vulnerableRequest = debugURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "debug-mode-enabled",
				Description: fmt.Sprintf("发现调试模式: %s (置信度: %.2f)", debugPath, confidence),
				Content:     d.extractConfigEvidence(resp, debugPath),
				Location:    debugURL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 500)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectConfigurationLeakage 检测配置文件泄露
func (d *ConfigurationErrorDetector) detectConfigurationLeakage(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	for _, configPath := range d.configPaths {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造配置文件URL
		configURL := d.buildConfigURL(target.URL, configPath)

		// 发送配置文件请求
		resp, err := d.sendConfigRequest(ctx, configURL)
		if err != nil {
			continue
		}

		// 检查配置文件响应
		confidence := d.checkConfigurationResponse(resp, configPath)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = configPath
			vulnerableRequest = configURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "configuration-leakage",
				Description: fmt.Sprintf("发现配置文件泄露: %s (置信度: %.2f)", configPath, confidence),
				Content:     d.extractConfigEvidence(resp, configPath),
				Location:    configURL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDefaultCredentials 检测默认凭据
func (d *ConfigurationErrorDetector) detectDefaultCredentials(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 查找登录页面
	loginPaths := []string{
		"/login",
		"/admin",
		"/admin/login",
		"/administrator",
		"/wp-admin",
		"/manager",
		"/console",
		"/dashboard",
	}

	for _, loginPath := range loginPaths {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造登录URL
		loginURL := d.buildConfigURL(target.URL, loginPath)

		// 检查登录页面是否存在
		resp, err := d.sendConfigRequest(ctx, loginURL)
		if err != nil {
			continue
		}

		// 如果找到登录页面，尝试默认凭据
		if d.isLoginPage(resp) {
			for _, cred := range d.defaultCredentials {
				if len(cred) != 2 {
					continue
				}

				username, password := cred[0], cred[1]

				// 尝试登录
				loginResp, err := d.attemptLogin(ctx, loginURL, username, password)
				if err != nil {
					continue
				}

				// 检查登录是否成功
				confidence := d.checkLoginSuccess(loginResp, username, password)
				if confidence > maxConfidence {
					maxConfidence = confidence
					vulnerablePayload = fmt.Sprintf("%s:%s", username, password)
					vulnerableRequest = loginURL
					vulnerableResponse = loginResp
				}

				if confidence > 0.7 {
					evidence = append(evidence, plugins.Evidence{
						Type:        "default-credentials",
						Description: fmt.Sprintf("发现默认凭据: %s:%s (置信度: %.2f)", username, password, confidence),
						Content:     d.extractConfigEvidence(loginResp, fmt.Sprintf("%s:%s", username, password)),
						Location:    loginURL,
						Timestamp:   time.Now(),
					})
				}

				// 添加延迟避免触发防护
				time.Sleep(time.Second * 2)
			}
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 800)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendConfigRequest 发送配置检测请求
func (d *ConfigurationErrorDetector) sendConfigRequest(ctx context.Context, targetURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// buildConfigURL 构造配置检测URL
func (d *ConfigurationErrorDetector) buildConfigURL(baseURL, path string) string {
	if strings.HasSuffix(baseURL, "/") && strings.HasPrefix(path, "/") {
		return baseURL + path[1:]
	} else if !strings.HasSuffix(baseURL, "/") && !strings.HasPrefix(path, "/") {
		return baseURL + "/" + path
	}
	return baseURL + path
}

// attemptLogin 尝试登录
func (d *ConfigurationErrorDetector) attemptLogin(ctx context.Context, loginURL, username, password string) (string, error) {
	// 构造POST数据
	data := url.Values{}
	data.Set("username", username)
	data.Set("password", password)
	data.Set("user", username)
	data.Set("pass", password)
	data.Set("login", "1")

	req, err := http.NewRequestWithContext(ctx, "POST", loginURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// isCriticalSecurityHeader 检查是否是关键安全头
func (d *ConfigurationErrorDetector) isCriticalSecurityHeader(header string) bool {
	criticalHeaders := []string{
		"X-Frame-Options",
		"X-XSS-Protection",
		"X-Content-Type-Options",
		"Strict-Transport-Security",
		"Content-Security-Policy",
	}

	headerLower := strings.ToLower(header)
	for _, critical := range criticalHeaders {
		if strings.ToLower(critical) == headerLower {
			return true
		}
	}
	return false
}

// checkInformationLeakageHeaders 检查信息泄露头
func (d *ConfigurationErrorDetector) checkInformationLeakageHeaders(response string) []string {
	var leakageHeaders []string
	responseLines := strings.Split(response, "\n")

	infoHeaders := []string{
		"Server",
		"X-Powered-By",
		"X-AspNet-Version",
		"X-AspNetMvc-Version",
		"X-Generator",
		"X-Drupal-Cache",
		"X-Varnish",
	}

	for _, line := range responseLines {
		for _, header := range infoHeaders {
			if strings.HasPrefix(strings.ToLower(line), strings.ToLower(header)+":") {
				leakageHeaders = append(leakageHeaders, header)
				break
			}
		}
	}

	return leakageHeaders
}

// checkDebugModeResponse 检查调试模式响应
func (d *ConfigurationErrorDetector) checkDebugModeResponse(response, path string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查调试模式特征
	debugIndicators := []string{
		"phpinfo", "debug mode", "development mode", "test mode",
		"stack trace", "error trace", "exception trace",
		"debug enabled", "debugging on", "dev mode",
		"调试模式", "开发模式", "测试模式", "错误信息",
	}

	for _, indicator := range debugIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.3
			break
		}
	}

	// 检查安全模式匹配
	for _, pattern := range d.securityPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查特定路径的调试特征
	if strings.Contains(path, "phpinfo") && strings.Contains(response, "php version") {
		confidence += 0.4
	}
	if strings.Contains(path, "debug") && strings.Contains(response, "debug") {
		confidence += 0.3
	}

	return confidence
}

// checkConfigurationResponse 检查配置文件响应
func (d *ConfigurationErrorDetector) checkConfigurationResponse(response, path string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.4
	}

	// 检查配置文件特征
	configIndicators := []string{
		"database", "password", "username", "api_key", "secret_key",
		"connection", "config", "configuration", "settings",
		"数据库", "密码", "用户名", "配置", "设置",
	}

	for _, indicator := range configIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.2
			break
		}
	}

	// 检查配置模式匹配
	for _, pattern := range d.configPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查特定文件类型的配置特征
	if strings.Contains(path, ".env") && strings.Contains(response, "=") {
		confidence += 0.4
	}
	if strings.Contains(path, "config") && (strings.Contains(response, "[") || strings.Contains(response, "{")) {
		confidence += 0.3
	}

	return confidence
}

// isLoginPage 检查是否是登录页面
func (d *ConfigurationErrorDetector) isLoginPage(response string) bool {
	response = strings.ToLower(response)

	loginIndicators := []string{
		"login", "password", "username", "signin", "sign in",
		"log in", "authentication", "auth", "登录", "密码", "用户名",
	}

	for _, indicator := range loginIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	// 检查表单元素
	if strings.Contains(response, "<form") &&
		(strings.Contains(response, "password") || strings.Contains(response, "login")) {
		return true
	}

	return false
}

// checkLoginSuccess 检查登录是否成功
func (d *ConfigurationErrorDetector) checkLoginSuccess(response, username, password string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查成功指示器
	successIndicators := []string{
		"welcome", "dashboard", "admin panel", "control panel",
		"logout", "profile", "settings", "管理面板", "欢迎",
		"仪表板", "控制面板", "注销", "个人资料", "设置",
	}

	for _, indicator := range successIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
			break
		}
	}

	// 检查重定向
	if strings.Contains(response, "status: 302") || strings.Contains(response, "status: 301") {
		confidence += 0.3
	}

	// 检查错误指示器（降低置信度）
	errorIndicators := []string{
		"invalid", "incorrect", "wrong", "failed", "error",
		"无效", "错误", "失败", "不正确",
	}

	for _, indicator := range errorIndicators {
		if strings.Contains(response, indicator) {
			confidence -= 0.3
			break
		}
	}

	// 检查是否仍在登录页面（降低置信度）
	if d.isLoginPage(response) {
		confidence -= 0.2
	}

	return confidence
}

// extractSecurityEvidence 提取安全证据
func (d *ConfigurationErrorDetector) extractSecurityEvidence(response string, headers []string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 查找包含安全头信息的行
	var securityLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)
		for _, header := range headers {
			if strings.Contains(lineLower, strings.ToLower(header)) {
				securityLines = append(securityLines, line)
				if len(securityLines) >= 5 { // 只取前5行
					break
				}
			}
		}
		if len(securityLines) >= 5 {
			break
		}
	}

	if len(securityLines) > 0 {
		return strings.Join(securityLines, "\n")
	}

	// 如果没有找到特定的安全信息，返回前200个字符
	if len(response) > 200 {
		return response[:200] + "..."
	}
	return response
}

// extractConfigEvidence 提取配置证据
func (d *ConfigurationErrorDetector) extractConfigEvidence(response, configInfo string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 查找包含配置相关信息的行
	var configLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)
		if strings.Contains(lineLower, "config") ||
			strings.Contains(lineLower, "debug") ||
			strings.Contains(lineLower, "password") ||
			strings.Contains(lineLower, "database") ||
			strings.Contains(lineLower, "api") ||
			strings.Contains(lineLower, "secret") ||
			strings.Contains(lineLower, "配置") ||
			strings.Contains(lineLower, "调试") ||
			strings.Contains(lineLower, "密码") {
			configLines = append(configLines, line)
			if len(configLines) >= 5 { // 只取前5行
				break
			}
		}
	}

	if len(configLines) > 0 {
		return strings.Join(configLines, "\n")
	}

	// 如果没有找到特定的配置信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
