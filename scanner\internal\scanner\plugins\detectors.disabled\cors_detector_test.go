package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestCORSDetectorBasicFunctionality 测试CORS检测器基础功能
func TestCORSDetectorBasicFunctionality(t *testing.T) {
	detector := NewCORSDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "cors-comprehensive", detector.GetID())
	assert.Equal(t, "CORS跨域资源共享检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "medium", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-346")
	assert.Contains(t, detector.GetCVE(), "CVE-2018-0269")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)
	assert.Contains(t, ports, 3000)
	assert.Contains(t, ports, 4200)
	assert.Contains(t, ports, 5000)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestCORSDetectorApplicability 测试CORS检测器适用性
func TestCORSDetectorApplicability(t *testing.T) {
	detector := NewCORSDetector()

	// 测试有CORS功能的目标
	corsTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Access-Control-Allow-Origin": "*",
			"Server":                      "nginx/1.18.0",
		},
	}
	assert.True(t, detector.IsApplicable(corsTarget))

	// 测试有CORS技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "JavaScript", Version: "ES6", Confidence: 0.9},
			{Name: "React", Version: "17.0.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有CORS链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/home",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api", Text: "API Service"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有CORS表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/form",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"origin": "text", "callback": "hidden"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试API相关URL
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/cors",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试普通Web目标（CORS是通用问题）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestCORSDetectorConfiguration 测试CORS检测器配置
func TestCORSDetectorConfiguration(t *testing.T) {
	detector := NewCORSDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 15*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 4, config.Concurrency)
	assert.True(t, config.FollowRedirects)
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestCORSDetectorTestOrigins 测试Origin列表
func TestCORSDetectorTestOrigins(t *testing.T) {
	detector := NewCORSDetector()

	// 检查测试Origin列表
	assert.NotEmpty(t, detector.testOrigins)
	assert.GreaterOrEqual(t, len(detector.testOrigins), 40)

	// 检查基础恶意Origin
	assert.Contains(t, detector.testOrigins, "https://evil.com")
	assert.Contains(t, detector.testOrigins, "http://evil.com")
	assert.Contains(t, detector.testOrigins, "https://attacker.com")
	assert.Contains(t, detector.testOrigins, "http://attacker.com")

	// 检查特殊Origin值
	assert.Contains(t, detector.testOrigins, "null")
	assert.Contains(t, detector.testOrigins, "undefined")
	assert.Contains(t, detector.testOrigins, "*")
	assert.Contains(t, detector.testOrigins, "")

	// 检查协议变化
	assert.Contains(t, detector.testOrigins, "ftp://evil.com")
	assert.Contains(t, detector.testOrigins, "file://evil.com")
	assert.Contains(t, detector.testOrigins, "javascript://evil.com")

	// 检查端口变化
	assert.Contains(t, detector.testOrigins, "https://evil.com:443")
	assert.Contains(t, detector.testOrigins, "http://evil.com:80")
	assert.Contains(t, detector.testOrigins, "https://evil.com:8080")

	// 检查子域名攻击
	assert.Contains(t, detector.testOrigins, "https://sub.evil.com")
	assert.Contains(t, detector.testOrigins, "http://www.evil.com")
	assert.Contains(t, detector.testOrigins, "https://api.evil.com")

	// 检查编码绕过
	assert.Contains(t, detector.testOrigins, "https://evil%2ecom")
	assert.Contains(t, detector.testOrigins, "http://evil%2ecom")

	// 检查Unicode绕过
	assert.Contains(t, detector.testOrigins, "https://evil\u002ecom")
	assert.Contains(t, detector.testOrigins, "http://evil\u002ecom")

	// 检查中文域名
	assert.Contains(t, detector.testOrigins, "https://恶意.com")
	assert.Contains(t, detector.testOrigins, "http://恶意.com")
}

// TestCORSDetectorBypassOrigins 测试绕过Origin列表
func TestCORSDetectorBypassOrigins(t *testing.T) {
	detector := NewCORSDetector()

	// 检查绕过Origin列表
	assert.NotEmpty(t, detector.bypassOrigins)
	assert.Greater(t, len(detector.bypassOrigins), 30)

	// 检查子域名绕过
	assert.Contains(t, detector.bypassOrigins, "https://evil.target.com")
	assert.Contains(t, detector.bypassOrigins, "http://evil.target.com")
	assert.Contains(t, detector.bypassOrigins, "https://target.com.evil.com")

	// 检查域名混淆
	assert.Contains(t, detector.bypassOrigins, "https://target-evil.com")
	assert.Contains(t, detector.bypassOrigins, "http://target-evil.com")

	// 检查协议绕过
	assert.Contains(t, detector.bypassOrigins, "//evil.com")
	assert.Contains(t, detector.bypassOrigins, "///evil.com")
	assert.Contains(t, detector.bypassOrigins, "////evil.com")

	// 检查大小写绕过
	assert.Contains(t, detector.bypassOrigins, "HTTPS://EVIL.COM")
	assert.Contains(t, detector.bypassOrigins, "HTTP://EVIL.COM")

	// 检查特殊字符绕过
	assert.Contains(t, detector.bypassOrigins, "https://evil.com.")
	assert.Contains(t, detector.bypassOrigins, "http://evil.com.")
	assert.Contains(t, detector.bypassOrigins, "https://evil.com#")

	// 检查IP地址绕过
	assert.Contains(t, detector.bypassOrigins, "https://*************")
	assert.Contains(t, detector.bypassOrigins, "http://127.0.0.1")

	// 检查十六进制IP
	assert.Contains(t, detector.bypassOrigins, "https://0x7f000001")
	assert.Contains(t, detector.bypassOrigins, "http://0x7f000001")

	// 检查IPv6绕过
	assert.Contains(t, detector.bypassOrigins, "https://[::1]")
	assert.Contains(t, detector.bypassOrigins, "http://[::1]")

	// 检查中文绕过
	assert.Contains(t, detector.bypassOrigins, "https://恶意目标.com")
	assert.Contains(t, detector.bypassOrigins, "http://恶意目标.com")
}

// TestCORSDetectorCORSHeaders 测试CORS头部列表
func TestCORSDetectorCORSHeaders(t *testing.T) {
	detector := NewCORSDetector()

	// 检查CORS头部列表
	assert.NotEmpty(t, detector.corsHeaders)
	assert.GreaterOrEqual(t, len(detector.corsHeaders), 25)

	// 检查标准CORS头部
	assert.Contains(t, detector.corsHeaders, "Access-Control-Allow-Origin")
	assert.Contains(t, detector.corsHeaders, "Access-Control-Allow-Methods")
	assert.Contains(t, detector.corsHeaders, "Access-Control-Allow-Headers")
	assert.Contains(t, detector.corsHeaders, "Access-Control-Allow-Credentials")
	assert.Contains(t, detector.corsHeaders, "Access-Control-Expose-Headers")
	assert.Contains(t, detector.corsHeaders, "Access-Control-Max-Age")

	// 检查非标准CORS头部
	assert.Contains(t, detector.corsHeaders, "X-Requested-With")
	assert.Contains(t, detector.corsHeaders, "Origin")
	assert.Contains(t, detector.corsHeaders, "Referer")

	// 检查安全相关头部
	assert.Contains(t, detector.corsHeaders, "Content-Security-Policy")
	assert.Contains(t, detector.corsHeaders, "X-Frame-Options")
	assert.Contains(t, detector.corsHeaders, "X-Content-Type-Options")

	// 检查自定义头部
	assert.Contains(t, detector.corsHeaders, "X-Custom-Header")
	assert.Contains(t, detector.corsHeaders, "X-API-Key")
	assert.Contains(t, detector.corsHeaders, "X-Auth-Token")

	// 检查中文头部
	assert.Contains(t, detector.corsHeaders, "X-来源")
	assert.Contains(t, detector.corsHeaders, "X-跨域")
}

// TestCORSDetectorDangerousHeaders 测试危险头部列表
func TestCORSDetectorDangerousHeaders(t *testing.T) {
	detector := NewCORSDetector()

	// 检查危险头部列表
	assert.NotEmpty(t, detector.dangerousHeaders)
	assert.Greater(t, len(detector.dangerousHeaders), 40)

	// 检查认证相关头部
	assert.Contains(t, detector.dangerousHeaders, "Authorization")
	assert.Contains(t, detector.dangerousHeaders, "Cookie")
	assert.Contains(t, detector.dangerousHeaders, "Set-Cookie")
	assert.Contains(t, detector.dangerousHeaders, "X-Auth-Token")
	assert.Contains(t, detector.dangerousHeaders, "X-API-Key")

	// 检查敏感信息头部
	assert.Contains(t, detector.dangerousHeaders, "X-Real-IP")
	assert.Contains(t, detector.dangerousHeaders, "X-Forwarded-For")
	assert.Contains(t, detector.dangerousHeaders, "X-User-ID")
	assert.Contains(t, detector.dangerousHeaders, "X-Username")

	// 检查系统信息头部
	assert.Contains(t, detector.dangerousHeaders, "Server")
	assert.Contains(t, detector.dangerousHeaders, "X-Powered-By")
	assert.Contains(t, detector.dangerousHeaders, "X-AspNet-Version")

	// 检查调试信息头部
	assert.Contains(t, detector.dangerousHeaders, "X-Debug")
	assert.Contains(t, detector.dangerousHeaders, "X-Debug-Token")
	assert.Contains(t, detector.dangerousHeaders, "X-Trace-ID")

	// 检查中文危险头部
	assert.Contains(t, detector.dangerousHeaders, "X-用户ID")
	assert.Contains(t, detector.dangerousHeaders, "X-用户名")
	assert.Contains(t, detector.dangerousHeaders, "X-认证")
}

// TestCORSDetectorPatterns 测试模式
func TestCORSDetectorPatterns(t *testing.T) {
	detector := NewCORSDetector()

	// 检查CORS模式
	assert.NotEmpty(t, detector.corsPatterns)
	assert.Greater(t, len(detector.corsPatterns), 15)

	// 检查漏洞模式
	assert.NotEmpty(t, detector.vulnerablePatterns)
	assert.Greater(t, len(detector.vulnerablePatterns), 15)
}

// TestCORSDetectorCORSFeatures 测试CORS功能检查
func TestCORSDetectorCORSFeatures(t *testing.T) {
	detector := NewCORSDetector()

	// 测试有CORS头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Access-Control-Allow-Origin": "*",
			"Server":                      "nginx/1.18.0",
		},
	}
	assert.True(t, detector.hasCORSFeatures(headerTarget))

	// 测试有CORS技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "JavaScript", Version: "ES6", Confidence: 0.9},
			{Name: "React", Version: "17.0.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasCORSFeatures(techTarget))

	// 测试有CORS链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api", Text: "API Service"},
		},
	}
	assert.True(t, detector.hasCORSFeatures(linkTarget))

	// 测试有CORS表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"origin": "text", "callback": "hidden"}},
		},
	}
	assert.True(t, detector.hasCORSFeatures(formTarget))

	// 测试无CORS功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasCORSFeatures(simpleTarget))
}

// TestCORSDetectorRiskScore 测试风险评分计算
func TestCORSDetectorRiskScore(t *testing.T) {
	detector := NewCORSDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 5.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 3.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 1.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestCORSDetectorLifecycle 测试检测器生命周期
func TestCORSDetectorLifecycle(t *testing.T) {
	detector := NewCORSDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
