{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/api/ai.ts", "../../src/api/assets.ts", "../../src/api/auth.ts", "../../src/api/client.ts", "../../src/api/compliance.ts", "../../src/api/dashboard.ts", "../../src/api/report.ts", "../../src/api/scan.ts", "../../src/api/settings.ts", "../../src/api/simple-client.ts", "../../src/api/ticket.ts", "../../src/api/users.ts", "../../src/api/vulnerability.ts", "../../src/components/helloworld.vue", "../../src/components/networkstatus.vue", "../../src/components/scancreatedialog.vue", "../../src/components/thewelcome.vue", "../../src/components/welcomeitem.vue", "../../src/components/icons/iconcommunity.vue", "../../src/components/icons/icondocumentation.vue", "../../src/components/icons/iconecosystem.vue", "../../src/components/icons/iconsupport.vue", "../../src/components/icons/icontooling.vue", "../../src/layouts/applayout.vue", "../../src/layouts/authlayout.vue", "../../src/layouts/mainlayout.vue", "../../src/layouts/simplelayout.vue", "../../src/pages/ai/aianalysispage.vue", "../../src/pages/assets/assetdetailpage.vue", "../../src/pages/assets/assetdiscoverypage.vue", "../../src/pages/assets/assetformpage.vue", "../../src/pages/assets/assetlistpage.vue", "../../src/pages/assets/components/assetcreatedialog.vue", "../../src/pages/assets/components/assetdetaildialog.vue", "../../src/pages/assets/components/assetdiscoverydialog.vue", "../../src/pages/auth/loginpagenew.vue", "../../src/pages/compliance/compliancelistpage.vue", "../../src/pages/compliance/components/createassessmentdialog.vue", "../../src/pages/dashboard/dashboardpage.vue", "../../src/pages/dashboard/dashboardpagenew.vue", "../../src/pages/debug/debugpage.vue", "../../src/pages/debug/diagnosticpage.vue", "../../src/pages/debug/elementdebugpage.vue", "../../src/pages/debug/routedebugpage.vue", "../../src/pages/error/notfoundpage.vue", "../../src/pages/reports/reportdetailpage.vue", "../../src/pages/reports/reportformpage.vue", "../../src/pages/reports/reportlistpage.vue", "../../src/pages/scans/scandetailpage.vue", "../../src/pages/scans/scanformpage.vue", "../../src/pages/scans/scanlistpage.vue", "../../src/pages/scans/components/scancreatedialog.vue", "../../src/pages/scans/components/scandetaildialog.vue", "../../src/pages/scans/components/scantemplatedialog.vue", "../../src/pages/scans/components/scheduledscandialog.vue", "../../src/pages/settings/systemconfigpage.vue", "../../src/pages/settings/systemlogspage.vue", "../../src/pages/settings/usermanagementpage.vue", "../../src/pages/settings/components/aisettings.vue", "../../src/pages/settings/components/accountmanagement.vue", "../../src/pages/settings/components/backupsettings.vue", "../../src/pages/settings/components/generalsettings.vue", "../../src/pages/settings/components/notificationsettings.vue", "../../src/pages/settings/components/scansettings.vue", "../../src/pages/settings/components/securitysettings.vue", "../../src/pages/settings/components/systeminfo.vue", "../../src/pages/simple/simplepage.vue", "../../src/pages/test/dashboardapitest.vue", "../../src/pages/test/layouttestpage.vue", "../../src/pages/test/routetestpage.vue", "../../src/pages/test/simpletestpage.vue", "../../src/pages/test/testpage.vue", "../../src/pages/tickets/ticketdetailpage.vue", "../../src/pages/tickets/ticketformpage.vue", "../../src/pages/tickets/ticketlistpage.vue", "../../src/pages/tickets/components/ticketcreatedialog.vue", "../../src/pages/tickets/components/ticketdetaildialog.vue", "../../src/pages/tickets/components/ticketeditdialog.vue", "../../src/pages/vulns/vulndetailpage.vue", "../../src/pages/vulns/vulnlistpage.vue", "../../src/router/index.ts", "../../src/stores/asset.ts", "../../src/stores/auth.ts", "../../src/stores/scan.ts", "../../src/stores/settings.ts", "../../src/types/api.ts", "../../src/types/common.ts", "../../src/utils/network.ts", "../../src/views/vulnerabilityevidencedetail.vue"], "errors": true, "version": "5.8.3"}