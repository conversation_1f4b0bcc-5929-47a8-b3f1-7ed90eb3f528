package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectVerticalPrivilegeEscalation 检测垂直权限提升
func (d *AuthorizationDetector) detectVerticalPrivilegeEscalation(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试管理员路径访问
	for _, adminPath := range d.adminPaths {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造管理员URL
		adminURL := d.buildAuthorizationURL(target.URL, adminPath)

		// 发送未授权访问请求
		resp, err := d.sendAuthorizationRequest(ctx, adminURL)
		if err != nil {
			continue
		}

		// 检查垂直权限提升响应
		confidence := d.checkVerticalPrivilegeEscalationResponse(resp, adminPath)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("垂直权限提升: %s", adminPath)
			vulnerableRequest = adminURL
			vulnerableResponse = resp
		}

		if confidence > 0.7 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "vertical-privilege-escalation",
				Description: fmt.Sprintf("发现垂直权限提升: %s (置信度: %.2f)", adminPath, confidence),
				Content:     d.extractAuthorizationEvidence(resp, "vertical-privilege-escalation"),
				Location:    adminURL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 800)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectHorizontalPrivilegeEscalation 检测水平权限提升
func (d *AuthorizationDetector) detectHorizontalPrivilegeEscalation(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试用户ID枚举和访问
	userIDs := []string{"1", "2", "3", "100", "1000", "admin", "root", "0", "-1"}

	for _, userPath := range d.userPaths {
		for _, userID := range userIDs {
			select {
			case <-ctx.Done():
				return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
			default:
			}

			// 替换用户ID占位符
			actualPath := strings.Replace(userPath, "{id}", userID, -1)
			userURL := d.buildAuthorizationURL(target.URL, actualPath)

			// 发送用户访问请求
			resp, err := d.sendAuthorizationRequest(ctx, userURL)
			if err != nil {
				continue
			}

			// 检查水平权限提升响应
			confidence := d.checkHorizontalPrivilegeEscalationResponse(resp, userID, actualPath)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("水平权限提升: %s", actualPath)
				vulnerableRequest = userURL
				vulnerableResponse = resp
			}

			if confidence > 0.7 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "horizontal-privilege-escalation",
					Description: fmt.Sprintf("发现水平权限提升: %s (置信度: %.2f)", actualPath, confidence),
					Content:     d.extractAuthorizationEvidence(resp, "horizontal-privilege-escalation"),
					Location:    userURL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 600)
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectAPIAccessControl 检测API访问控制
func (d *AuthorizationDetector) detectAPIAccessControl(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	for _, apiEndpoint := range d.apiEndpoints {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造API URL
		apiURL := d.buildAuthorizationURL(target.URL, apiEndpoint)

		// 测试不同HTTP方法的API访问
		methods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"}

		for _, method := range methods {
			// 发送API访问请求
			resp, err := d.sendAPIRequest(ctx, apiURL, method)
			if err != nil {
				continue
			}

			// 检查API访问控制响应
			confidence := d.checkAPIAccessControlResponse(resp, method, apiEndpoint)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("API访问控制: %s %s", method, apiEndpoint)
				vulnerableRequest = apiURL
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "api-access-control",
					Description: fmt.Sprintf("发现API访问控制问题: %s %s (置信度: %.2f)", method, apiEndpoint, confidence),
					Content:     d.extractAuthorizationEvidence(resp, "api-access-control"),
					Location:    apiURL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 400)
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectRBACFlaws 检测RBAC缺陷
func (d *AuthorizationDetector) detectRBACFlaws(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试角色和权限绕过
	for _, payload := range d.bypassPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造RBAC测试URL
		rbacURL := d.buildRBACTestURL(target.URL, payload)

		// 发送RBAC测试请求
		resp, err := d.sendRBACRequest(ctx, rbacURL, payload)
		if err != nil {
			continue
		}

		// 检查RBAC缺陷响应
		confidence := d.checkRBACFlawsResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("RBAC缺陷: %s", payload)
			vulnerableRequest = rbacURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "rbac-flaws",
				Description: fmt.Sprintf("发现RBAC缺陷: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractAuthorizationEvidence(resp, "rbac-flaws"),
				Location:    rbacURL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 700)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendAuthorizationRequest 发送授权请求
func (d *AuthorizationDetector) sendAuthorizationRequest(ctx context.Context, targetURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendAPIRequest 发送API请求
func (d *AuthorizationDetector) sendAPIRequest(ctx context.Context, targetURL, method string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, method, targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendRBACRequest 发送RBAC请求
func (d *AuthorizationDetector) sendRBACRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造请求数据
	data := url.Values{}

	// 根据载荷类型设置参数
	if strings.Contains(payload, "=") {
		parts := strings.Split(payload, "=")
		if len(parts) == 2 {
			data.Set(parts[0], parts[1])
		}
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	// 添加特殊头部载荷
	if strings.HasPrefix(payload, "X-") {
		parts := strings.Split(payload, ": ")
		if len(parts) == 2 {
			req.Header.Set(parts[0], parts[1])
		}
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// buildAuthorizationURL 构造授权URL
func (d *AuthorizationDetector) buildAuthorizationURL(baseURL, path string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + path
	}

	// 替换路径
	parsedURL.Path = path

	return parsedURL.String()
}

// buildRBACTestURL 构造RBAC测试URL
func (d *AuthorizationDetector) buildRBACTestURL(baseURL, payload string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL
	}

	// 如果载荷包含查询参数
	if strings.Contains(payload, "=") && !strings.HasPrefix(payload, "X-") {
		if parsedURL.RawQuery == "" {
			parsedURL.RawQuery = payload
		} else {
			parsedURL.RawQuery += "&" + payload
		}
	}

	return parsedURL.String()
}

// checkAuthorizationResponse 检查授权响应（通用方法）
func (d *AuthorizationDetector) checkAuthorizationResponse(response, path string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查认证模式匹配
	for _, pattern := range d.authPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查访问模式匹配
	for _, pattern := range d.accessPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查权限模式匹配
	for _, pattern := range d.privilegePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	return confidence
}

// checkVerticalPrivilegeEscalationResponse 检查垂直权限提升响应
func (d *AuthorizationDetector) checkVerticalPrivilegeEscalationResponse(response, adminPath string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.4
	} else if strings.Contains(response, "status: 302") || strings.Contains(response, "status: 301") {
		confidence += 0.2 // 重定向可能表示访问成功
	}

	// 检查管理员访问成功指示器
	adminIndicators := []string{
		"admin panel", "administration", "dashboard", "control panel",
		"management", "welcome admin", "admin area", "admin console",
		"system admin", "administrator", "admin dashboard",
		"管理面板", "管理控制台", "仪表板", "管理员", "后台管理",
	}

	for _, indicator := range adminIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
			break
		}
	}

	// 检查认证模式匹配
	for _, pattern := range d.authPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查权限模式匹配
	for _, pattern := range d.privilegePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查是否包含管理功能
	managementFeatures := []string{
		"user management", "system settings", "configuration",
		"logs", "audit", "monitoring", "statistics", "reports",
		"用户管理", "系统设置", "配置", "日志", "审计", "监控",
	}

	for _, feature := range managementFeatures {
		if strings.Contains(response, feature) {
			confidence += 0.2
			break
		}
	}

	return confidence
}

// checkHorizontalPrivilegeEscalationResponse 检查水平权限提升响应
func (d *AuthorizationDetector) checkHorizontalPrivilegeEscalationResponse(response, userID, path string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查用户信息泄露
	userInfoIndicators := []string{
		"profile", "account", "personal", "private", "settings",
		"email", "phone", "address", "password", "credit card",
		"个人资料", "账户", "私人", "设置", "邮箱", "电话", "地址", "密码",
	}

	for _, indicator := range userInfoIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.3
			break
		}
	}

	// 检查用户ID在响应中
	if strings.Contains(response, userID) {
		confidence += 0.2
	}

	// 检查是否包含其他用户的数据
	otherUserIndicators := []string{
		"user id", "user_id", "userid", "member id", "customer id",
		"account id", "profile id", "用户ID", "用户编号", "账户ID",
	}

	for _, indicator := range otherUserIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.2
			break
		}
	}

	// 检查权限模式匹配
	for _, pattern := range d.privilegePatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	return confidence
}

// checkAPIAccessControlResponse 检查API访问控制响应
func (d *AuthorizationDetector) checkAPIAccessControlResponse(response, method, endpoint string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	} else if strings.Contains(response, "status: 201") {
		confidence += 0.4 // 创建成功更可疑
	}

	// 检查API响应格式
	apiFormats := []string{
		"application/json", "application/xml", "text/json",
		"{", "}", "[", "]", "<?xml", "<response>",
	}

	for _, format := range apiFormats {
		if strings.Contains(response, format) {
			confidence += 0.2
			break
		}
	}

	// 检查API数据泄露
	apiDataIndicators := []string{
		"users", "accounts", "profiles", "customers", "members",
		"data", "records", "results", "items", "list",
		"id", "name", "email", "phone", "address",
		"用户", "账户", "数据", "记录", "列表",
	}

	for _, indicator := range apiDataIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.2
			break
		}
	}

	// 检查访问模式匹配
	for _, pattern := range d.accessPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 根据HTTP方法调整置信度
	if method == "DELETE" || method == "PUT" || method == "PATCH" {
		if confidence > 0.3 {
			confidence += 0.2 // 危险方法更可疑
		}
	}

	return confidence
}

// checkRBACFlawsResponse 检查RBAC缺陷响应
func (d *AuthorizationDetector) checkRBACFlawsResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查角色绕过成功指示器
	roleBypassIndicators := []string{
		"role changed", "role updated", "permission granted",
		"access granted", "privilege escalated", "admin access",
		"role bypass", "permission bypass", "auth bypass",
		"角色更改", "权限授予", "访问授权", "权限提升", "管理访问",
	}

	for _, indicator := range roleBypassIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	// 检查权限模式匹配
	for _, pattern := range d.privilegePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查认证模式匹配
	for _, pattern := range d.authPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 根据载荷类型调整置信度
	if strings.Contains(payload, "admin") || strings.Contains(payload, "root") {
		if confidence > 0.3 {
			confidence += 0.2 // 管理员相关载荷更危险
		}
	}

	return confidence
}

// extractAuthorizationEvidence 提取授权证据
func (d *AuthorizationDetector) extractAuthorizationEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var authLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "vertical-privilege-escalation":
			if strings.Contains(lineLower, "admin") ||
				strings.Contains(lineLower, "administrator") ||
				strings.Contains(lineLower, "management") ||
				strings.Contains(lineLower, "dashboard") ||
				strings.Contains(lineLower, "control") ||
				strings.Contains(lineLower, "panel") ||
				strings.Contains(lineLower, "console") ||
				strings.Contains(lineLower, "privilege") ||
				strings.Contains(lineLower, "管理") ||
				strings.Contains(lineLower, "仪表板") ||
				strings.Contains(lineLower, "控制台") {
				authLines = append(authLines, line)
			}
		case "horizontal-privilege-escalation":
			if strings.Contains(lineLower, "user") ||
				strings.Contains(lineLower, "profile") ||
				strings.Contains(lineLower, "account") ||
				strings.Contains(lineLower, "member") ||
				strings.Contains(lineLower, "customer") ||
				strings.Contains(lineLower, "personal") ||
				strings.Contains(lineLower, "private") ||
				strings.Contains(lineLower, "id") ||
				strings.Contains(lineLower, "用户") ||
				strings.Contains(lineLower, "账户") ||
				strings.Contains(lineLower, "个人") {
				authLines = append(authLines, line)
			}
		case "api-access-control":
			if strings.Contains(lineLower, "api") ||
				strings.Contains(lineLower, "json") ||
				strings.Contains(lineLower, "xml") ||
				strings.Contains(lineLower, "data") ||
				strings.Contains(lineLower, "records") ||
				strings.Contains(lineLower, "results") ||
				strings.Contains(lineLower, "items") ||
				strings.Contains(lineLower, "list") ||
				strings.Contains(lineLower, "接口") ||
				strings.Contains(lineLower, "数据") {
				authLines = append(authLines, line)
			}
		case "rbac-flaws":
			if strings.Contains(lineLower, "role") ||
				strings.Contains(lineLower, "permission") ||
				strings.Contains(lineLower, "privilege") ||
				strings.Contains(lineLower, "access") ||
				strings.Contains(lineLower, "auth") ||
				strings.Contains(lineLower, "bypass") ||
				strings.Contains(lineLower, "grant") ||
				strings.Contains(lineLower, "角色") ||
				strings.Contains(lineLower, "权限") ||
				strings.Contains(lineLower, "访问") ||
				strings.Contains(lineLower, "授权") {
				authLines = append(authLines, line)
			}
		default:
			if strings.Contains(lineLower, "admin") ||
				strings.Contains(lineLower, "auth") ||
				strings.Contains(lineLower, "login") ||
				strings.Contains(lineLower, "user") ||
				strings.Contains(lineLower, "role") ||
				strings.Contains(lineLower, "permission") ||
				strings.Contains(lineLower, "privilege") ||
				strings.Contains(lineLower, "access") ||
				strings.Contains(lineLower, "管理") ||
				strings.Contains(lineLower, "认证") ||
				strings.Contains(lineLower, "登录") ||
				strings.Contains(lineLower, "用户") ||
				strings.Contains(lineLower, "角色") ||
				strings.Contains(lineLower, "权限") ||
				strings.Contains(lineLower, "访问") {
				authLines = append(authLines, line)
			}
		}

		if len(authLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(authLines) > 0 {
		return strings.Join(authLines, "\n")
	}

	// 如果没有找到特定的授权信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
