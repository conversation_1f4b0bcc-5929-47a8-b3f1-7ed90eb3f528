<!-- 扫描详情页面 -->
<template>
  <div class="scan-detail-page">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" style="margin-right: 16px">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div>
          <h1 class="page-title">{{ scan.name }}</h1>
          <p class="page-description">扫描任务详细信息和结果</p>
        </div>
      </div>
      <div class="header-actions">
        <el-button v-if="scan.status === 'running'" type="danger" @click="handleStop">
          <el-icon><VideoPause /></el-icon>
          停止扫描
        </el-button>
        <el-button v-else type="primary" @click="testRerun" :loading="rerunLoading">
          🔄 重新扫描
        </el-button>
      </div>
    </div>

    <el-row :gutter="16">
      <el-col :span="24">
        <!-- 基本信息 -->
        <el-card class="info-card" v-loading="scanLoading">
          <template #header>
            <span>扫描信息</span>
          </template>

          <el-descriptions :column="3" border v-if="scan.id">
            <el-descriptions-item label="任务名称">
              {{ scan.name }}
            </el-descriptions-item>
            <el-descriptions-item label="扫描类型">
              <el-tag>{{ scan.type }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(scan.status)">
                {{ getStatusText(scan.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="扫描目标">
              {{ scan.target }}
            </el-descriptions-item>
            <el-descriptions-item label="进度">
              <el-progress :percentage="scan.progress" :status="getProgressStatus(scan.status)" />
            </el-descriptions-item>
            <el-descriptions-item label="发现漏洞">
              <span :style="{ color: scan.vulnCount > 0 ? '#f56c6c' : '#67c23a' }">
                {{ scan.vulnCount }} 个
              </span>
            </el-descriptions-item>
          </el-descriptions>
          <el-empty v-else-if="!scanLoading" description="扫描任务不存在" />
        </el-card>

        <!-- 扫描详情标签页 -->
        <el-card class="results-card">
          <el-tabs v-model="activeTab" type="border-card">
            <!-- 扫描结果 -->
            <el-tab-pane label="扫描结果" name="results">
              <div v-if="scan.status === 'running'" class="scanning-status">
                <el-icon class="scanning-icon"><Loading /></el-icon>
                <span>扫描进行中，请稍候...</span>
              </div>

              <div v-else-if="scan.status === 'completed'" class="scan-results">
                <!-- 扫描摘要 -->
                <el-card class="result-summary" shadow="never">
                  <div class="summary-content">
                    <el-icon class="summary-icon success"><CircleCheck /></el-icon>
                    <div class="summary-text">
                      <h3>扫描完成</h3>
                      <p>发现 {{ scan.vulnCount }} 个安全问题</p>
                    </div>
                    <div class="summary-actions">
                      <el-button type="primary" @click="$router.push('/vulnerabilities')">
                        查看所有漏洞
                      </el-button>
                      <el-button @click="handleGenerateReport">生成报告</el-button>
                    </div>
                  </div>
                </el-card>

                <!-- 扫描结果详情 -->
                <div class="scan-results-detail">
                  <!-- 目标信息统计 -->
                  <el-card class="targets-summary" shadow="never">
                    <template #header>
                      <div class="card-header">
                        <span>目标信息统计</span>
                      </div>
                    </template>
                    <div class="targets-stats">
                      <div class="stat-item">
                        <div class="stat-value">{{ targetStats.totalTargets }}</div>
                        <div class="stat-label">扫描目标</div>
                      </div>
                      <div class="stat-item">
                        <div class="stat-value">{{ targetStats.uniqueHosts }}</div>
                        <div class="stat-label">唯一主机</div>
                      </div>
                      <div class="stat-item">
                        <div class="stat-value">{{ targetStats.openPorts }}</div>
                        <div class="stat-label">开放端口</div>
                      </div>
                      <div class="stat-item">
                        <div class="stat-value">{{ targetStats.webServices }}</div>
                        <div class="stat-label">Web服务</div>
                      </div>
                    </div>
                  </el-card>

                  <!-- 发现的目标信息详情 -->
                  <el-card class="discovered-targets" shadow="never">
                    <template #header>
                      <div class="card-header">
                        <span>发现的目标信息详情</span>
                      </div>
                    </template>

                    <div v-loading="targetInfoLoading">



                      <!-- 目标信息详情列表 -->
                      <div v-if="targetInfo?.targets?.length > 0" class="targets-detail-list">
                        <div v-for="(target, index) in targetInfo.targets" :key="target.id || index" class="target-detail-card">
                          <el-card shadow="hover">
                            <template #header>
                              <div class="target-header">
                                <div class="target-info">
                                  <span class="target-name">{{ target.target || '未知目标' }}</span>
                                  <el-tag :type="getScanTypeColor(target.scan_type)" size="small">
                                    {{ getScanTypeLabel(target.scan_type) }}
                                  </el-tag>
                                  <el-tag :type="getTargetStatusColor(target.status)" size="small">
                                    {{ getStatusLabel(target.status) }}
                                  </el-tag>
                                </div>
                                <div class="target-actions">
                                  <el-button size="small" @click="toggleTargetDetail(index)">
                                    {{ expandedTargets.includes(index) ? '收起' : '展开' }}
                                  </el-button>
                                </div>
                              </div>
                            </template>

                            <!-- 目标详细信息 -->
                            <div v-show="expandedTargets.includes(index)" class="target-details">
                              <!-- 基本信息 -->
                              <div v-if="target.basic_info" class="info-section">
                                <h4><el-icon><InfoFilled /></el-icon> 基本信息</h4>
                                <el-descriptions :column="2" border size="small">
                                  <el-descriptions-item v-if="target.basic_info.url" label="URL">
                                    {{ target.basic_info.url }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.basic_info.domain" label="域名">
                                    {{ target.basic_info.domain }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.basic_info.ip" label="IP地址">
                                    {{ target.basic_info.ip }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.basic_info.port" label="端口">
                                    {{ target.basic_info.port }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.basic_info.protocol" label="协议">
                                    {{ target.basic_info.protocol }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.basic_info.status_code" label="状态码">
                                    <el-tag :type="getStatusCodeColor(target.basic_info.status_code)">
                                      {{ target.basic_info.status_code }}
                                    </el-tag>
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.basic_info.title" label="页面标题">
                                    {{ target.basic_info.title }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.basic_info.response_time" label="响应时间">
                                    {{ target.basic_info.response_time }}ms
                                  </el-descriptions-item>
                                </el-descriptions>
                              </div>

                              <!-- 技术栈信息 -->
                              <div v-if="target.tech_stack" class="info-section">
                                <h4><el-icon><Setting /></el-icon> 技术栈信息</h4>
                                <el-descriptions :column="2" border size="small">
                                  <el-descriptions-item v-if="target.tech_stack.web_server" label="Web服务器">
                                    {{ target.tech_stack.web_server }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.tech_stack.framework" label="开发框架">
                                    {{ target.tech_stack.framework }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.tech_stack.language" label="编程语言">
                                    {{ target.tech_stack.language }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.tech_stack.database" label="数据库">
                                    {{ target.tech_stack.database }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.tech_stack.cms" label="CMS系统">
                                    {{ target.tech_stack.cms }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.tech_stack.technologies?.length" label="技术组件">
                                    <el-tag
                                      v-for="tech in target.tech_stack.technologies"
                                      :key="tech"
                                      size="small"
                                      style="margin-right: 5px;"
                                    >
                                      {{ tech }}
                                    </el-tag>
                                  </el-descriptions-item>
                                </el-descriptions>
                              </div>

                              <!-- 服务信息 -->
                              <div v-if="target.services" class="info-section">
                                <h4><el-icon><Connection /></el-icon> 服务信息</h4>

                                <!-- 开放端口 -->
                                <div v-if="target.services.open_ports?.length" class="sub-section">
                                  <h5>开放端口</h5>
                                  <el-table :data="target.services.open_ports" size="small" border>
                                    <el-table-column prop="port" label="端口" width="80" />
                                    <el-table-column prop="protocol" label="协议" width="80" />
                                    <el-table-column prop="service" label="服务" width="120" />
                                    <el-table-column prop="version" label="版本" />
                                    <el-table-column prop="state" label="状态" width="80">
                                      <template #default="scope">
                                        <el-tag :type="scope.row.state === 'open' ? 'success' : 'danger'" size="small">
                                          {{ scope.row.state }}
                                        </el-tag>
                                      </template>
                                    </el-table-column>
                                  </el-table>
                                </div>

                                <!-- SSL信息 -->
                                <div v-if="target.services.ssl_info" class="sub-section">
                                  <h5>SSL证书信息</h5>
                                  <el-descriptions :column="2" border size="small">
                                    <el-descriptions-item label="启用状态">
                                      <el-tag :type="target.services.ssl_info.enabled ? 'success' : 'danger'">
                                        {{ target.services.ssl_info.enabled ? '已启用' : '未启用' }}
                                      </el-tag>
                                    </el-descriptions-item>
                                    <el-descriptions-item v-if="target.services.ssl_info.version" label="SSL版本">
                                      {{ target.services.ssl_info.version }}
                                    </el-descriptions-item>
                                    <el-descriptions-item v-if="target.services.ssl_info.cipher" label="加密套件">
                                      {{ target.services.ssl_info.cipher }}
                                    </el-descriptions-item>
                                    <el-descriptions-item v-if="target.services.ssl_info.valid_from && target.services.ssl_info.valid_to" label="有效期">
                                      {{ target.services.ssl_info.valid_from }} - {{ target.services.ssl_info.valid_to }}
                                    </el-descriptions-item>
                                  </el-descriptions>
                                </div>
                              </div>

                              <!-- 安全配置 -->
                              <div v-if="target.security_config" class="info-section">
                                <h4><el-icon><Lock /></el-icon> 安全配置</h4>

                                <!-- 安全响应头 -->
                                <div v-if="target.security_config.security_headers" class="sub-section">
                                  <h5>安全响应头</h5>
                                  <el-descriptions :column="1" border size="small">
                                    <el-descriptions-item
                                      v-for="(value, key) in target.security_config.security_headers"
                                      :key="key"
                                      :label="formatHeaderName(key)"
                                    >
                                      <el-tag type="success" size="small">{{ value }}</el-tag>
                                    </el-descriptions-item>
                                  </el-descriptions>
                                </div>

                                <!-- 缺失的安全头 -->
                                <div v-if="target.security_config.missing_headers?.length" class="sub-section">
                                  <h5>缺失的安全头</h5>
                                  <el-tag
                                    v-for="header in target.security_config.missing_headers"
                                    :key="header"
                                    type="warning"
                                    size="small"
                                    style="margin-right: 5px;"
                                  >
                                    {{ header }}
                                  </el-tag>
                                </div>
                              </div>

                              <!-- 目录结构 -->
                              <div v-if="target.directory_structure" class="info-section">
                                <h4><el-icon><FolderOpened /></el-icon> 目录结构探测</h4>

                                <div v-if="target.directory_structure.discovered_paths?.length" class="sub-section">
                                  <h5>发现的路径</h5>
                                  <el-tag
                                    v-for="path in target.directory_structure.discovered_paths"
                                    :key="path"
                                    size="small"
                                    style="margin-right: 5px; margin-bottom: 5px;"
                                  >
                                    {{ path }}
                                  </el-tag>
                                </div>

                                <div v-if="target.directory_structure.sensitive_files?.length" class="sub-section">
                                  <h5>敏感文件</h5>
                                  <el-tag
                                    v-for="file in target.directory_structure.sensitive_files"
                                    :key="file"
                                    type="warning"
                                    size="small"
                                    style="margin-right: 5px; margin-bottom: 5px;"
                                  >
                                    {{ file }}
                                  </el-tag>
                                </div>
                              </div>

                              <!-- 爬虫发现 -->
                              <div v-if="target.crawler_findings" class="info-section">
                                <h4><el-icon><Search /></el-icon> 网站爬虫发现</h4>
                                <el-descriptions :column="2" border size="small">
                                  <el-descriptions-item v-if="target.crawler_findings.total_urls" label="总URL数">
                                    {{ target.crawler_findings.total_urls }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.crawler_findings.forms_found" label="发现表单">
                                    {{ target.crawler_findings.forms_found }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.crawler_findings.parameters?.length" label="参数列表">
                                    <el-tag
                                      v-for="param in target.crawler_findings.parameters"
                                      :key="param"
                                      size="small"
                                      style="margin-right: 5px;"
                                    >
                                      {{ param }}
                                    </el-tag>
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.crawler_findings.technologies?.length" label="使用技术">
                                    <el-tag
                                      v-for="tech in target.crawler_findings.technologies"
                                      :key="tech"
                                      size="small"
                                      style="margin-right: 5px;"
                                    >
                                      {{ tech }}
                                    </el-tag>
                                  </el-descriptions-item>
                                </el-descriptions>
                              </div>

                              <!-- 统计信息 -->
                              <div v-if="target.statistics" class="info-section">
                                <h4><el-icon><DataAnalysis /></el-icon> 扫描统计</h4>
                                <el-descriptions :column="2" border size="small">
                                  <el-descriptions-item v-if="target.statistics.scan_duration" label="扫描耗时">
                                    {{ formatDuration(target.statistics.scan_duration) }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.statistics.requests_sent" label="发送请求">
                                    {{ target.statistics.requests_sent }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.statistics.responses_received" label="接收响应">
                                    {{ target.statistics.responses_received }}
                                  </el-descriptions-item>
                                  <el-descriptions-item v-if="target.statistics.errors_count" label="错误次数">
                                    {{ target.statistics.errors_count }}
                                  </el-descriptions-item>
                                </el-descriptions>
                              </div>

                              <!-- 无数据提示 -->
                              <div v-if="target.status === 'no_data'" class="no-data-section">
                                <el-empty description="该目标暂无详细信息收集数据" />
                              </div>
                            </div>
                          </el-card>
                        </div>
                      </div>

                      <!-- 空状态 -->
                      <div v-else-if="!targetInfoLoading" class="empty-state">
                        <div class="no-info-message">
                          <p>该任务暂无信息收集数据，可能是因为：</p>
                          <ul>
                            <li>1) 扫描尚未完成</li>
                            <li>2) 扫描过程中未启用信息收集</li>
                            <li>3) 信息收集过程出现错误</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </el-card>

                  <!-- 风险分布统计 -->
                  <el-card class="risk-distribution" shadow="never">
                    <template #header>
                      <div class="card-header">
                        <span>风险分布</span>
                      </div>
                    </template>
                    <div class="risk-stats">
                      <div class="risk-item critical">
                        <div class="risk-count">{{ riskStats.critical }}</div>
                        <div class="risk-label">严重</div>
                      </div>
                      <div class="risk-item high">
                        <div class="risk-count">{{ riskStats.high }}</div>
                        <div class="risk-label">高危</div>
                      </div>
                      <div class="risk-item medium">
                        <div class="risk-count">{{ riskStats.medium }}</div>
                        <div class="risk-label">中危</div>
                      </div>
                      <div class="risk-item low">
                        <div class="risk-count">{{ riskStats.low }}</div>
                        <div class="risk-label">低危</div>
                      </div>
                      <div class="risk-item info">
                        <div class="risk-count">{{ riskStats.info }}</div>
                        <div class="risk-label">信息</div>
                      </div>
                    </div>
                  </el-card>

                  <!-- 漏洞详细列表 -->
                  <el-card class="vulnerabilities-card" shadow="never">
                    <template #header>
                      <div class="card-header">
                        <span>发现的漏洞详情</span>
                        <div class="header-actions">
                          <el-select v-model="vulnSeverityFilter" placeholder="严重程度" clearable @change="loadVulnerabilities">
                            <el-option label="严重" value="critical" />
                            <el-option label="高危" value="high" />
                            <el-option label="中危" value="medium" />
                            <el-option label="低危" value="low" />
                            <el-option label="信息" value="info" />
                          </el-select>
                          <el-select v-model="vulnTypeFilter" placeholder="漏洞类型" clearable @change="loadVulnerabilities">
                            <el-option label="SSRF" value="SSRF" />
                            <el-option label="SQL注入" value="SQL_INJECTION" />
                            <el-option label="XSS" value="XSS" />
                            <el-option label="文件上传" value="FILE_UPLOAD" />
                            <el-option label="目录遍历" value="DIRECTORY_TRAVERSAL" />
                            <el-option label="信息泄露" value="INFO_DISCLOSURE" />
                          </el-select>
                          <el-button @click="loadVulnerabilities" :loading="vulnLoading">
                            <el-icon><Refresh /></el-icon>
                          </el-button>
                        </div>
                      </div>
                    </template>

                    <div v-loading="vulnLoading">
                      <!-- 漏洞卡片列表 -->
                      <div class="vulnerabilities-list">
                        <div v-for="vuln in vulnerabilities" :key="vuln.id" class="vulnerability-card">
                          <div class="vuln-header">
                            <div class="vuln-title">
                              <h4>{{ vuln.name }}</h4>
                              <div class="vuln-tags">
                                <el-tag :type="getSeverityType(vuln.severity)" size="small">
                                  {{ getSeverityText(vuln.severity) }}
                                </el-tag>
                                <el-tag type="info" size="small">{{ vuln.type }}</el-tag>
                                <el-tag v-if="vuln.cvss" type="warning" size="small">
                                  CVSS: {{ vuln.cvss }}
                                </el-tag>
                              </div>
                            </div>
                            <div class="vuln-actions">
                              <el-button type="primary" size="small" @click="viewVulnerability(vuln)">
                                详情
                              </el-button>
                            </div>
                          </div>

                          <div class="vuln-content">
                            <div class="vuln-description">
                              <p>{{ vuln.description || vuln.title }}</p>
                            </div>

                            <!-- 目标信息 -->
                            <div class="vuln-target-info">
                              <h5>目标信息</h5>
                              <div class="info-grid">
                                <div class="info-item" v-if="vuln.url">
                                  <span class="label">URL:</span>
                                  <span class="value">{{ vuln.url }}</span>
                                </div>
                                <div class="info-item" v-if="vuln.method">
                                  <span class="label">方法:</span>
                                  <span class="value">{{ vuln.method }}</span>
                                </div>
                                <div class="info-item" v-if="vuln.parameter">
                                  <span class="label">参数:</span>
                                  <span class="value">{{ vuln.parameter }}</span>
                                </div>
                                <div class="info-item" v-if="vuln.port">
                                  <span class="label">端口:</span>
                                  <span class="value">{{ vuln.port }}</span>
                                </div>
                              </div>
                            </div>

                            <!-- 技术细节 -->
                            <div class="vuln-technical-details" v-if="vuln.payload || vuln.evidence">
                              <h5>技术细节</h5>
                              <div class="details-content">
                                <div v-if="vuln.payload" class="detail-item">
                                  <span class="label">攻击载荷:</span>
                                  <code class="payload">{{ vuln.payload }}</code>
                                </div>
                                <div v-if="vuln.evidence" class="detail-item">
                                  <span class="label">漏洞证据:</span>
                                  <div class="evidence">{{ vuln.evidence.substring(0, 200) }}...</div>
                                </div>
                              </div>
                            </div>

                            <!-- 修复建议 -->
                            <div class="vuln-solution" v-if="vuln.solution">
                              <h5>修复建议</h5>
                              <p>{{ vuln.solution }}</p>
                            </div>

                            <!-- 参考链接 -->
                            <div class="vuln-references" v-if="vuln.references">
                              <h5>参考链接</h5>
                              <a :href="vuln.references" target="_blank" class="reference-link">
                                {{ vuln.references }}
                              </a>
                            </div>

                            <!-- 状态和时间 -->
                            <div class="vuln-meta">
                              <div class="meta-item">
                                <span class="label">状态:</span>
                                <el-tag :type="getVulnStatusType(vuln.status)" size="small">
                                  {{ getVulnStatusText(vuln.status) }}
                                </el-tag>
                              </div>
                              <div class="meta-item">
                                <span class="label">发现时间:</span>
                                <span class="value">{{ formatTime(vuln.created_at) }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 分页 -->
                      <div class="pagination-container" v-if="vulnTotal > 0">
                        <el-pagination
                          v-model:current-page="vulnPage"
                          v-model:page-size="vulnPageSize"
                          :page-sizes="[5, 10, 20, 50]"
                          :total="vulnTotal"
                          layout="total, sizes, prev, pager, next, jumper"
                          @size-change="loadVulnerabilities"
                          @current-change="loadVulnerabilities"
                        />
                      </div>

                      <!-- 空状态 -->
                      <el-empty v-if="!vulnLoading && vulnerabilities.length === 0" description="未发现安全问题" />
                    </div>
                  </el-card>
                </div>
              </div>

              <div v-else-if="scan.status === 'failed'" class="scan-failed">
                <el-result
                  icon="error"
                  title="扫描失败"
                  sub-title="扫描过程中发生错误，请检查配置后重试"
                >
                  <template #extra>
                    <el-button type="primary" @click="testRerun" :loading="rerunLoading">🔄 重新扫描</el-button>
                  </template>
                </el-result>
              </div>
            </el-tab-pane>



            <!-- 扫描日志 -->
            <el-tab-pane label="扫描日志" name="logs">
              <div class="log-toolbar">
                <div class="log-filters">
                  <el-select v-model="logLevel" placeholder="日志级别" style="width: 120px" @change="loadLogs">
                    <el-option label="全部" value="" />
                    <el-option label="DEBUG" value="DEBUG" />
                    <el-option label="INFO" value="INFO" />
                    <el-option label="WARN" value="WARN" />
                    <el-option label="ERROR" value="ERROR" />
                  </el-select>
                  <el-select v-model="logOrder" placeholder="排序方式" style="width: 120px; margin-left: 8px" @change="loadLogs">
                    <el-option label="最新在前" value="desc" />
                    <el-option label="最早在前" value="asc" />
                  </el-select>
                  <el-input
                    v-model="logKeyword"
                    placeholder="搜索日志内容"
                    style="width: 200px; margin-left: 8px"
                    @keyup.enter="searchLogs"
                  >
                    <template #append>
                      <el-button @click="searchLogs">
                        <el-icon><Search /></el-icon>
                      </el-button>
                    </template>
                  </el-input>
                </div>
                <div class="log-actions">
                  <el-button @click="loadLogs" :loading="logsLoading">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                  <el-switch
                    v-model="autoRefresh"
                    active-text="自动刷新"
                    @change="toggleAutoRefresh"
                  />
                </div>
              </div>

              <div class="log-container">
                <div v-if="logsLoading" class="log-loading">
                  <el-icon class="loading-icon"><Loading /></el-icon>
                  <span>加载日志中...</span>
                </div>
                <div v-else-if="logs.length === 0" class="log-empty">
                  <el-empty description="暂无日志数据" />
                </div>
                <div v-else class="log-list">
                  <div
                    v-for="log in logs"
                    :key="log.id"
                    :class="['log-item', `log-${log.level.toLowerCase()}`]"
                  >
                    <div class="log-header">
                      <span class="log-time">{{ formatTime(log.created_at) }}</span>
                      <el-tag :type="getLogLevelType(log.level)" size="small">{{ log.level }}</el-tag>
                      <span v-if="log.stage" class="log-stage">{{ log.stage }}</span>
                      <span v-if="log.target" class="log-target">{{ log.target }}</span>
                    </div>
                    <div class="log-message">{{ log.message }}</div>
                    <div v-if="log.details" class="log-details">{{ log.details }}</div>
                  </div>
                </div>

                <!-- 分页 -->
                <el-pagination
                  v-if="logTotal > 0"
                  v-model:current-page="logPage"
                  v-model:page-size="logSize"
                  :total="logTotal"
                  :page-sizes="[20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="loadLogs"
                  @current-change="loadLogs"
                  style="margin-top: 16px; text-align: center"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  VideoPause,
  Refresh,
  Loading,
  Search,
  CircleCheck,
  Folder,
  Document,
  Monitor,
  InfoFilled,
  Setting,
  Connection,
  Lock,
  FolderOpened,
  DataAnalysis
} from '@element-plus/icons-vue'
import { ApiClient } from '@/api/client'
import { createTask, formatScanTargets, formatScanConfig, getTaskVulnerabilities } from '@/api/scan'

const route = useRoute()
const router = useRouter()

// 响应式数据
const scan = ref({
  id: 0,
  name: '',
  type: '',
  target: '',
  targets: '',
  config: '',
  depth: 3,
  timeout: 30,
  threads: 10,
  asset_id: undefined,
  status: '',
  progress: 0,
  vulnCount: 0,
  createdAt: new Date()
})
const scanLoading = ref(false)
const rerunLoading = ref(false)

// 标签页
const activeTab = ref('results')

// 日志相关
const logs = ref([])
const logsLoading = ref(false)
const logLevel = ref('')
const logOrder = ref('desc') // 默认最新在前
const logKeyword = ref('')
const logPage = ref(1)
const logSize = ref(20)
const logTotal = ref(0)
const autoRefresh = ref(false)
let refreshTimer: number | null = null

// 漏洞相关
const vulnerabilities = ref([])
const vulnLoading = ref(false)
const vulnSeverityFilter = ref('')
const vulnTypeFilter = ref('')
const vulnPage = ref(1)
const vulnPageSize = ref(5) // 减少每页显示数量，因为现在是卡片展示
const vulnTotal = ref(0)

// 目标信息统计
const targetStats = ref({
  totalTargets: 0,
  uniqueHosts: 0,
  openPorts: 0,
  webServices: 0
})

// 标志：是否已经从后端设置了统计数据
const hasBackendStatistics = ref(false)

// 风险分布统计
const riskStats = ref({
  critical: 0,
  high: 0,
  medium: 0,
  low: 0,
  info: 0
})

// 发现的目标信息
const discoveredTargets = ref([])
const targetsLoading = ref(false)

// 目标信息详情
const targetInfo = ref<any>({})
const targetInfoLoading = ref(false)
const activeTargets = ref<any[]>([])

// 目标详情展开状态
const expandedTargets = ref<number[]>([])



// 方法
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || status
}

const getProgressStatus = (status: string) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return undefined
}

const handleStop = () => {
  ElMessage.success('扫描已停止')
  scan.value.status = 'failed'
}

// 测试重新扫描按钮事件绑定
const testRerun = () => {
  console.log('testRerun 被调用了！')
  ElMessage.info('重新扫描按钮点击事件正常工作！')
  // 调用实际的重新扫描方法
  handleRerun()
}



const handleRerun = async () => {
  console.log('handleRerun 方法被调用')
  console.log('当前scan数据:', scan.value)

  try {
    // 确认对话框
    console.log('显示确认对话框')
    await ElMessageBox.confirm(
      '确定要重新扫描吗？这将创建一个新的扫描任务。',
      '重新扫描确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    console.log('用户确认了重新扫描')

    rerunLoading.value = true

    // 解析原任务的目标和配置
    const targets = formatScanTargets(scan.value.targets)
    const config = formatScanConfig(scan.value.config)

    // 创建新的扫描任务
    const newTaskData = {
      name: `重新扫描-${scan.value.name}`,
      type: scan.value.type,
      description: `基于任务"${scan.value.name}"的重新扫描`,
      targets: targets,
      asset_id: scan.value.asset_id,
      config: config,
      depth: scan.value.depth || 3,
      timeout: scan.value.timeout || 30,
      threads: scan.value.threads || 10,
      schedule_type: 'immediate' // 立即执行
    }

    console.log('创建重新扫描任务:', newTaskData)

    const newTask = await createTask(newTaskData)
    console.log('创建任务成功，返回数据:', newTask)

    ElMessage.success(`重新扫描任务"${newTask.name}"已创建并启动`)

    // 跳转到新任务的详情页面
    router.push(`/scans/${newTask.id}`)

  } catch (error: any) {
    if (error.message !== 'cancel') {
      console.error('重新扫描失败:', error)
      ElMessage.error(error.response?.data?.message || '重新扫描失败')
    }
  } finally {
    rerunLoading.value = false
  }
}

const handleGenerateReport = () => {
  ElMessage.success('报告生成中，请稍后查看')
}

// 加载扫描任务详情
const loadScanDetail = async (scanId: string | number) => {
  scanLoading.value = true
  try {
    console.log('=== 开始加载扫描详情 ===')
    console.log('扫描ID:', scanId)

    const response = await ApiClient.get(`/scans/${scanId}`)
    console.log('API响应完整数据:', response)

    // 新的API响应格式包含task和statistics两部分
    const scanData = response.task || response
    const statistics = response.statistics
    console.log('提取的任务数据:', scanData)
    console.log('提取的统计信息:', statistics)

    // 设置扫描任务基本信息
    scan.value = {
      id: scanData.id,
      name: scanData.name,
      type: scanData.type,
      target: Array.isArray(scanData.targets) ? scanData.targets.join(', ') : scanData.targets,
      targets: scanData.targets,
      config: scanData.config || '{}',
      depth: scanData.depth || 3,
      timeout: scanData.timeout || 30,
      threads: scanData.threads || 10,
      asset_id: scanData.asset_id,
      status: scanData.status,
      progress: scanData.progress || 0,
      vulnCount: statistics ? (statistics.total_vulns || 0) : (scanData.vuln_count || 0),
      createdAt: new Date(scanData.created_at)
    }
    console.log('设置扫描任务信息:', scan.value)

    // 处理统计信息
    if (statistics) {
      console.log('=== 处理后端统计数据 ===')
      console.log('原始统计数据:', statistics)

      // 强制设置目标统计信息
      const newTargetStats = {
        totalTargets: statistics.total_targets || 0,
        uniqueHosts: statistics.unique_hosts || 0,
        openPorts: statistics.open_ports || 0,
        webServices: statistics.web_services || 0
      }

      // 强制设置风险分布统计
      const newRiskStats = {
        critical: statistics.critical_vulns || 0,
        high: statistics.high_vulns || 0,
        medium: statistics.medium_vulns || 0,
        low: statistics.low_vulns || 0,
        info: statistics.info_vulns || 0
      }

      console.log('准备设置目标统计:', newTargetStats)
      console.log('准备设置风险统计:', newRiskStats)

      // 使用Vue的nextTick确保响应式更新
      await nextTick()

      targetStats.value = newTargetStats
      riskStats.value = newRiskStats
      hasBackendStatistics.value = true

      console.log('设置完成 - 目标统计:', targetStats.value)
      console.log('设置完成 - 风险统计:', riskStats.value)
      console.log('设置完成 - 后端标志:', hasBackendStatistics.value)

      // 再次确保DOM更新
      await nextTick()
      console.log('DOM更新完成')

    } else {
      console.log('=== 没有后端统计数据，使用前端计算 ===')
      hasBackendStatistics.value = false
      await calculateStats(true) // 强制计算
    }

    console.log('=== 扫描详情加载完成 ===')
  } catch (error) {
    console.error('加载扫描详情失败:', error)
    ElMessage.error('加载扫描详情失败')
  } finally {
    scanLoading.value = false
  }
}

// 日志相关方法
const loadLogs = async () => {
  if (!scan.value.id) return

  logsLoading.value = true
  try {
    const params = {
      page: logPage.value,
      size: logSize.value,
      level: logLevel.value || undefined,
      order: logOrder.value,
    }

    const logsData = await ApiClient.get(`/scans/${scan.value.id}/logs`, params)
    logs.value = logsData.logs || []
    logTotal.value = logsData.total || 0
  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败')
  } finally {
    logsLoading.value = false
  }
}

const searchLogs = async () => {
  if (!logKeyword.value.trim()) {
    loadLogs()
    return
  }

  logsLoading.value = true
  try {
    const params = {
      page: logPage.value,
      size: logSize.value,
      keyword: logKeyword.value,
      order: logOrder.value,
    }

    const searchData = await ApiClient.get(`/scans/${scan.value.id}/logs/search`, params)
    logs.value = searchData.logs || []
    logTotal.value = searchData.total || 0
  } catch (error) {
    console.error('搜索日志失败:', error)
    ElMessage.error('搜索日志失败')
  } finally {
    logsLoading.value = false
  }
}

const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    refreshTimer = setInterval(() => {
      if (activeTab.value === 'logs') {
        loadLogs()
      }
    }, 3000) // 每3秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getLogLevelType = (level: string) => {
  const typeMap: Record<string, string> = {
    DEBUG: 'info',
    INFO: 'success',
    WARN: 'warning',
    ERROR: 'danger'
  }
  return typeMap[level] || 'info'
}

// 漏洞相关方法
const loadVulnerabilities = async () => {
  if (!scan.value.id) return

  vulnLoading.value = true
  try {
    const params = {
      page: vulnPage.value,
      size: vulnPageSize.value,
      severity: vulnSeverityFilter.value || undefined,
      type: vulnTypeFilter.value || undefined
    }

    const data = await getTaskVulnerabilities(scan.value.id, params)
    vulnerabilities.value = data.vulnerabilities || []
    vulnTotal.value = data.pagination?.total || 0

    // 只有在没有后端统计数据时才计算统计数据
    console.log('loadVulnerabilities - 当前targetStats:', targetStats.value)
    console.log('loadVulnerabilities - hasBackendStatistics标志:', hasBackendStatistics.value)

    if (!hasBackendStatistics.value) {
      // 检查是否已经有有效的统计数据
      const hasValidStats = targetStats.value.totalTargets > 0 ||
                            targetStats.value.uniqueHosts > 0 ||
                            targetStats.value.openPorts > 0 ||
                            targetStats.value.webServices > 0

      if (!hasValidStats) {
        console.log('loadVulnerabilities - 没有后端统计数据，调用calculateStats')
        await calculateStats()
      } else {
        console.log('loadVulnerabilities - 有有效统计数据，跳过calculateStats')
      }
    } else {
      console.log('loadVulnerabilities - 已有后端统计数据标志，跳过calculateStats')
    }
  } catch (error) {
    console.error('加载漏洞列表失败:', error)
    ElMessage.error('加载漏洞列表失败')
  } finally {
    vulnLoading.value = false
  }
}

// 计算统计数据
const calculateStats = async (forceCalculate = false) => {
  console.log('=== calculateStats 开始 ===')
  console.log('forceCalculate:', forceCalculate)
  console.log('scan.value.id:', scan.value.id)
  console.log('hasBackendStatistics.value:', hasBackendStatistics.value)

  if (!scan.value.id) {
    console.log('calculateStats - 没有扫描ID，退出')
    return
  }

  // 如果不是强制计算，且已经有后端统计数据，则跳过
  if (!forceCalculate) {
    if (hasBackendStatistics.value) {
      console.log('calculateStats - 已有后端统计数据标志，跳过前端计算')
      return
    }

    const hasValidStats = targetStats.value.totalTargets > 0 ||
                         targetStats.value.uniqueHosts > 0 ||
                         targetStats.value.openPorts > 0 ||
                         targetStats.value.webServices > 0
    if (hasValidStats) {
      console.log('calculateStats - 检测到有效统计数据，跳过前端计算')
      return
    }
  }

  console.log('calculateStats - 开始前端计算统计数据')

  try {
    // 获取所有漏洞数据用于统计
    const allVulnsData = await getTaskVulnerabilities(scan.value.id, { page: 1, size: 1000 })
    const allVulns = allVulnsData.vulnerabilities || []

    // 计算风险分布
    riskStats.value = {
      critical: allVulns.filter(v => v.severity?.toLowerCase() === 'critical').length,
      high: allVulns.filter(v => v.severity?.toLowerCase() === 'high').length,
      medium: allVulns.filter(v => v.severity?.toLowerCase() === 'medium').length,
      low: allVulns.filter(v => v.severity?.toLowerCase() === 'low').length,
      info: allVulns.filter(v => v.severity?.toLowerCase() === 'info').length
    }

    // 计算目标信息统计
    const uniqueUrls = new Set(allVulns.map(v => v.url).filter(Boolean))
    const uniqueHosts = new Set(allVulns.map(v => {
      try {
        return new URL(v.url).hostname
      } catch {
        return null
      }
    }).filter(Boolean))
    const uniquePorts = new Set(allVulns.map(v => v.port).filter(p => p > 0))
    const webServices = allVulns.filter(v => v.url && (v.url.startsWith('http://') || v.url.startsWith('https://'))).length

    const newTargetStats = {
      totalTargets: uniqueUrls.size,
      uniqueHosts: uniqueHosts.size,
      openPorts: uniquePorts.size,
      webServices: webServices
    }

    console.log('calculateStats - 计算得到的新统计数据:', newTargetStats)
    console.log('calculateStats - 设置前的targetStats:', targetStats.value)

    targetStats.value = newTargetStats

    console.log('calculateStats - 设置后的targetStats:', targetStats.value)
  } catch (error) {
    console.error('计算统计数据失败:', error)
  }
}

const getSeverityType = (severity: string) => {
  const typeMap: Record<string, string> = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return typeMap[severity] || 'info'
}

const getSeverityText = (severity: string) => {
  const textMap: Record<string, string> = {
    high: '高危',
    medium: '中危',
    low: '低危'
  }
  return textMap[severity] || severity
}

const getVulnStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    open: 'danger',
    confirmed: 'warning',
    fixed: 'success',
    ignored: 'info'
  }
  return typeMap[status] || 'info'
}

const getVulnStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    open: '待处理',
    confirmed: '已确认',
    fixed: '已修复',
    ignored: '已忽略'
  }
  return textMap[status] || status
}

const viewVulnerability = (vulnerability: any) => {
  // 跳转到漏洞详情页面
  router.push(`/vulnerabilities/${vulnerability.id}`)
}

// 生命周期
onMounted(async () => {
  console.log('onMounted - 开始加载扫描详情')
  // 加载扫描详情
  const scanId = route.params.id
  if (scanId) {
    await loadScanDetail(scanId)
    console.log('onMounted - 扫描详情加载完成，当前targetStats:', targetStats.value)
    // 加载完扫描详情后再加载日志和漏洞
    if (scan.value.id) {
      loadLogs()
      // 如果扫描已完成，加载漏洞列表和发现的目标信息
      if (scan.value.status === 'completed') {
        console.log('onMounted - 扫描已完成，准备加载漏洞列表和目标信息')
        loadVulnerabilities()
        loadTargetInfo()
      }
    }
  }
})



// 发现的目标信息相关方法
const loadTargetInfo = async () => {
  if (!scan.value.id) return

  targetInfoLoading.value = true
  try {
    // 调用目标信息详情API
    const result = await ApiClient.get(`/scans/${scan.value.id}/target-info`)

    if (result.code === 200) {
      targetInfo.value = result.data || {}
      console.log('目标信息数据:', targetInfo.value)
    } else {
      console.warn('获取目标信息失败:', result.message)
      // 如果API没有返回数据，尝试从扫描日志中提取信息
      targetInfo.value = await generateTargetInfoFromLogs()
    }
  } catch (error) {
    console.error('加载目标信息详情失败:', error)
    // 如果API调用失败，尝试从扫描日志中提取信息
    targetInfo.value = await generateTargetInfoFromLogs()
  } finally {
    targetInfoLoading.value = false
  }

  // 如果仍然没有数据，使用模拟数据作为后备
  if (!targetInfo.value?.targets?.length) {
    targetInfo.value = generateMockTargetInfo()
  }
}

// 兼容方法：保持原有的loadDiscoveredTargets方法名
const loadDiscoveredTargets = loadTargetInfo

// 将新的目标信息格式转换为表格数据格式
const convertTargetInfoToTableData = (targets: any[]) => {
  return targets.map((target, index) => {
    const ports = []
    const services = []

    // 从services.open_ports中提取端口信息
    if (target.services?.open_ports) {
      target.services.open_ports.forEach((port: any) => {
        ports.push({
          port: port.port,
          protocol: port.protocol,
          service: port.service,
          state: port.state,
          version: port.version
        })

        if (port.service && port.service !== 'unknown') {
          services.push({
            name: port.service,
            version: port.version || 'Unknown',
            banner: `${port.service} on port ${port.port}`
          })
        }
      })
    }

    // 操作系统信息
    const osInfo = target.os_detection ? {
      type: target.os_detection.os_family?.toLowerCase() || 'unknown',
      name: target.os_detection.os_name || '未识别'
    } : {
      type: 'unknown',
      name: '未识别'
    }

    return {
      id: target.id || index + 1,
      ip: target.basic_info?.ip || target.target,
      hostname: target.basic_info?.domain || target.basic_info?.hostname || '',
      scanType: target.scan_type || 'unknown',
      ports: ports,
      services: services,
      os: osInfo,
      vulnCount: 0, // 这里可以根据实际漏洞数据填充
      discoveredAt: target.discovered_at || new Date().toISOString()
    }
  })
}

// 生成基于扫描目标的发现目标数据
const generateMockDiscoveredTargets = () => {
  // 基于当前扫描任务的目标生成发现的目标信息
  const scanTargets = scan.value.targets || scan.value.target || ''
  const targetList = Array.isArray(scanTargets) ? scanTargets : scanTargets.split(',').map(t => t.trim())

  const mockTargets: any[] = []

  // 为每个扫描目标生成发现的信息
  targetList.forEach((target, index) => {
    if (!target) return

    // 解析目标（可能是IP、域名或URL）
    let ip = ''
    let hostname = ''
    let baseUrl = ''

    if (target.startsWith('http://') || target.startsWith('https://')) {
      // URL格式
      try {
        const url = new URL(target)
        hostname = url.hostname
        baseUrl = target
        // 模拟IP解析
        ip = `192.168.1.${100 + index}`
      } catch (e) {
        hostname = target
        ip = `192.168.1.${100 + index}`
      }
    } else if (target.match(/^\d+\.\d+\.\d+\.\d+$/)) {
      // IP地址格式
      ip = target
      hostname = `host-${target.replace(/\./g, '-')}.local`
    } else {
      // 域名格式
      hostname = target
      ip = `192.168.1.${100 + index}`
    }

    // 根据扫描类型生成相应的发现信息
    const discoveredTarget = {
      id: index + 1,
      ip: ip,
      hostname: hostname,
      baseUrl: baseUrl,
      ports: [] as any[],
      services: [] as any[],
      os: { type: 'unknown', name: '检测中...' },
      status: 'online',
      vulnCount: 0,
      scanType: scan.value.type,
      discoveredAt: new Date().toISOString()
    }

    // 根据扫描类型添加相应的端口和服务信息
    switch (scan.value.type) {
      case 'web_scan':
        discoveredTarget.ports = [
          { port: 80, protocol: 'tcp', service: 'http', state: 'open' },
          { port: 443, protocol: 'tcp', service: 'https', state: 'open' }
        ]
        discoveredTarget.services = [
          { name: 'HTTP Server', version: 'Unknown', banner: 'HTTP/1.1 Server' },
          { name: 'HTTPS Server', version: 'Unknown', banner: 'HTTPS/1.1 Server' }
        ]
        discoveredTarget.os = { type: 'linux', name: 'Linux (推测)' }
        discoveredTarget.vulnCount = Math.floor(Math.random() * 5) // 0-4个漏洞
        break

      case 'port_scan':
        // 网络端口扫描发现的信息
        const commonPorts = [
          { port: 22, protocol: 'tcp', service: 'ssh', state: 'open' },
          { port: 80, protocol: 'tcp', service: 'http', state: 'open' },
          { port: 443, protocol: 'tcp', service: 'https', state: 'open' },
          { port: 3306, protocol: 'tcp', service: 'mysql', state: 'open' },
          { port: 8080, protocol: 'tcp', service: 'http-alt', state: 'open' }
        ]
        discoveredTarget.ports = commonPorts.slice(0, Math.floor(Math.random() * 4) + 2)
        discoveredTarget.services = discoveredTarget.ports.map(p => ({
          name: p.service.toUpperCase(),
          version: 'Unknown',
          banner: `${p.service} service on port ${p.port}`
        }))
        discoveredTarget.os = { type: 'linux', name: 'Linux 4.x' }
        discoveredTarget.vulnCount = Math.floor(Math.random() * 3)
        break

      case 'host_scan':
        // 主机安全扫描发现的信息
        discoveredTarget.ports = [
          { port: 22, protocol: 'tcp', service: 'ssh', state: 'open' },
          { port: 80, protocol: 'tcp', service: 'http', state: 'open' }
        ]
        discoveredTarget.services = [
          { name: 'OpenSSH', version: '7.4', banner: 'SSH-2.0-OpenSSH_7.4' },
          { name: 'Apache', version: '2.4.6', banner: 'Apache/2.4.6 (CentOS)' }
        ]
        discoveredTarget.os = { type: 'linux', name: 'CentOS Linux 7' }
        discoveredTarget.vulnCount = Math.floor(Math.random() * 4)
        break

      case 'api_scan':
        // API接口扫描发现的信息
        discoveredTarget.ports = [
          { port: 80, protocol: 'tcp', service: 'http', state: 'open' },
          { port: 443, protocol: 'tcp', service: 'https', state: 'open' },
          { port: 8080, protocol: 'tcp', service: 'http-alt', state: 'open' }
        ]
        discoveredTarget.services = [
          { name: 'REST API', version: 'v1.0', banner: 'RESTful API Service' },
          { name: 'JSON API', version: 'Unknown', banner: 'JSON API Endpoint' }
        ]
        discoveredTarget.os = { type: 'linux', name: 'Ubuntu 18.04' }
        discoveredTarget.vulnCount = Math.floor(Math.random() * 6)
        break

      default:
        // 默认扫描类型
        discoveredTarget.ports = [
          { port: 80, protocol: 'tcp', service: 'http', state: 'open' }
        ]
        discoveredTarget.services = [
          { name: 'Web Server', version: 'Unknown', banner: 'HTTP Server' }
        ]
        discoveredTarget.vulnCount = Math.floor(Math.random() * 3)
    }

    mockTargets.push(discoveredTarget)
  })

  return mockTargets
}

// 从扫描日志中提取目标信息
const generateTargetInfoFromLogs = async () => {
  if (!scan.value.id) {
    return { targets: [], total_count: 0 }
  }

  try {
    // 获取所有扫描日志
    const logsData = await ApiClient.get(`/scans/${scan.value.id}/logs`, {
      page: 1,
      size: 1000, // 获取足够多的日志
      order: 'asc'
    })

    let allLogs = logsData.logs || []
    console.log(`获取到 ${allLogs.length} 条日志`)

    // 如果没有真实日志，创建一些测试日志用于演示
    if (allLogs.length === 0) {
      console.log('没有真实日志数据，创建测试日志')
      allLogs = [
        {
          id: 1,
          content: 'https://dvwa.bachang.org/login.php 指纹识别完成，检测到 2 个技术',
          category: '指纹识别',
          level: 'info'
        },
        {
          id: 2,
          content: 'https://dvwa.bachang.org/login.php 检测到框架: PHP',
          category: '指纹识别',
          level: 'info'
        },
        {
          id: 3,
          content: 'https://dvwa.bachang.org/login.php 检测到服务器: openresty',
          category: '指纹识别',
          level: 'info'
        },
        {
          id: 4,
          content: 'https://dvwa.bachang.org/login.php/backup 发现可访问目录: /backup [状态码: 200]',
          category: '目录扫描',
          level: 'warn'
        },
        {
          id: 5,
          content: 'https://dvwa.bachang.org/login.php 端口开放: 443/tcp open https',
          category: '端口扫描',
          level: 'info'
        },
        {
          id: 6,
          content: 'https://dvwa.bachang.org/login.php 端口开放: 80/tcp open http',
          category: '端口扫描',
          level: 'info'
        }
      ]
    }

    // 从日志中提取目标信息
    const targetMap = new Map()
    let scanTargets = scan.value.targets || scan.value.target || ''

    // 处理字符串化的数组
    if (typeof scanTargets === 'string') {
      try {
        // 尝试解析JSON格式的字符串
        if (scanTargets.startsWith('[') && scanTargets.endsWith(']')) {
          scanTargets = JSON.parse(scanTargets)
        }
      } catch (e) {
        // 如果不是JSON格式，按逗号分割
        scanTargets = scanTargets.split(',').map(t => t.trim())
      }
    }

    const targetList = Array.isArray(scanTargets) ? scanTargets : [scanTargets].filter(t => t)
    console.log('原始扫描目标:', scan.value.targets || scan.value.target)
    console.log('解析后的目标列表:', targetList)

    // 为每个目标初始化基础信息
    targetList.forEach((target, index) => {
      if (target) {
        // 确保target是字符串
        const targetUrl = Array.isArray(target) ? target[0] : target
        console.log(`初始化目标 ${index + 1}: ${targetUrl}`)

        targetMap.set(targetUrl, {
          id: index + 1,
          target: targetUrl,
          scan_type: scan.value.type,
          status: 'completed',
          discovered_at: new Date().toISOString(),
          basic_info: {
            url: targetUrl,
            domain: extractDomainFromTarget(targetUrl),
            ip: null,
            port: targetUrl.includes('https') ? 443 : 80,
            protocol: targetUrl.includes('https') ? 'https' : 'http',
            status_code: null,
            title: null,
            response_time: null
          },
          tech_stack: {
            web_server: null,
            framework: null,
            language: null,
            database: null,
            cms: null,
            technologies: []
          },
          services: {
            open_ports: [],
            ssl_info: null
          },
          security_config: {
            security_headers: {},
            missing_headers: []
          },
          directory_structure: {
            discovered_paths: [],
            sensitive_files: []
          },
          crawler_findings: {
            total_urls: 0,
            forms_found: 0,
            parameters: [],
            technologies: []
          }
        })
      }
    })

    // 解析日志内容，提取信息
    console.log(`开始解析 ${allLogs.length} 条日志`)
    allLogs.forEach(log => {
      const content = log.content || ''
      const category = log.category || ''

      // 查找对应的目标
      let currentTarget = null
      for (const [targetUrl, targetData] of targetMap) {
        if (content.includes(targetUrl)) {
          currentTarget = targetData
          break
        }
      }

      if (!currentTarget) {
        console.log(`未找到匹配目标的日志: ${content}`)
        return
      }

      console.log(`处理日志 [${category}]: ${content}`)

      // 根据日志类别和内容提取信息
      switch (category) {
        case '指纹识别':
          extractFingerprintInfo(content, currentTarget)
          break
        case '目录扫描':
          extractDirectoryInfo(content, currentTarget)
          break
        case '端口扫描':
          extractPortInfo(content, currentTarget)
          break
        case '服务识别':
          extractServiceInfo(content, currentTarget)
          break
        case 'SSL检测':
          extractSSLInfo(content, currentTarget)
          break
        case '网站爬虫':
          extractCrawlerInfo(content, currentTarget)
          break
        default:
          // 通用信息提取
          extractGeneralInfo(content, currentTarget)
          break
      }
    })

    const targets = Array.from(targetMap.values()).filter(target => target !== null)
    return {
      targets: targets,
      total_count: targets.length
    }

  } catch (error) {
    console.error('从日志提取目标信息失败:', error)
    return { targets: [], total_count: 0 }
  }
}

// 从指纹识别日志中提取信息
const extractFingerprintInfo = (content: string, target: any) => {
  // 检测到框架: PHP
  if (content.includes('检测到框架:')) {
    const match = content.match(/检测到框架:\s*([^\s]+)/)
    if (match) {
      target.tech_stack.framework = match[1]
      if (match[1].toLowerCase().includes('php')) {
        target.tech_stack.language = 'PHP'
      }
    }
  }

  // 检测到服务器: openresty
  if (content.includes('检测到服务器:')) {
    const match = content.match(/检测到服务器:\s*([^\s]+)/)
    if (match) {
      target.tech_stack.web_server = match[1]
    }
  }

  // 检测到技术
  if (content.includes('检测到') && content.includes('技术')) {
    const match = content.match(/检测到\s*(\d+)\s*个技术/)
    if (match) {
      // 可以记录技术数量，但具体技术需要从其他日志中提取
    }
  }
}

// 从目录扫描日志中提取信息
const extractDirectoryInfo = (content: string, target: any) => {
  // 发现可访问目录: /backup [状态码: 200]
  if (content.includes('发现可访问目录:')) {
    const match = content.match(/发现可访问目录:\s*([^\s\[]+)/)
    if (match) {
      const path = match[1]
      if (!target.directory_structure.discovered_paths.includes(path)) {
        target.directory_structure.discovered_paths.push(path)
      }
    }
  }

  // 发现敏感文件
  if (content.includes('发现敏感文件:')) {
    const match = content.match(/发现敏感文件:\s*([^\s\[]+)/)
    if (match) {
      const file = match[1]
      if (!target.directory_structure.sensitive_files.includes(file)) {
        target.directory_structure.sensitive_files.push(file)
      }
    }
  }
}

// 从端口扫描日志中提取信息
const extractPortInfo = (content: string, target: any) => {
  // 端口开放: 80/tcp open http
  if (content.includes('端口开放:') || content.includes('开放端口:')) {
    const match = content.match(/(\d+)\/(\w+)\s+(\w+)\s+([^\s]+)/)
    if (match) {
      const [, port, protocol, state, service] = match
      const portInfo = {
        port: parseInt(port),
        protocol: protocol,
        service: service,
        version: '',
        state: state
      }

      // 避免重复添加
      const exists = target.services.open_ports.some(p => p.port === portInfo.port && p.protocol === portInfo.protocol)
      if (!exists) {
        target.services.open_ports.push(portInfo)
      }
    }
  }
}

// 从服务识别日志中提取信息
const extractServiceInfo = (content: string, target: any) => {
  // 服务版本: Apache/2.4.41
  if (content.includes('服务版本:')) {
    const match = content.match(/服务版本:\s*([^\s]+)/)
    if (match) {
      const version = match[1]
      if (version.toLowerCase().includes('apache')) {
        target.tech_stack.web_server = version
      }
    }
  }
}

// 从SSL检测日志中提取信息
const extractSSLInfo = (content: string, target: any) => {
  if (content.includes('SSL') || content.includes('TLS')) {
    if (!target.services.ssl_info) {
      target.services.ssl_info = {
        enabled: true,
        version: null,
        cipher: null,
        valid_from: null,
        valid_to: null
      }
    }

    // SSL版本
    const versionMatch = content.match(/TLS[v]?[\d.]+|SSL[v]?[\d.]+/)
    if (versionMatch) {
      target.services.ssl_info.version = versionMatch[0]
    }
  }
}

// 从爬虫日志中提取信息
const extractCrawlerInfo = (content: string, target: any) => {
  // 发现URL: /admin/login
  if (content.includes('发现URL:')) {
    target.crawler_findings.total_urls += 1
  }

  // 发现表单
  if (content.includes('发现表单')) {
    target.crawler_findings.forms_found += 1
  }

  // 发现参数
  if (content.includes('发现参数:')) {
    const match = content.match(/发现参数:\s*([^\s]+)/)
    if (match) {
      const param = match[1]
      if (!target.crawler_findings.parameters.includes(param)) {
        target.crawler_findings.parameters.push(param)
      }
    }
  }
}

// 通用信息提取
const extractGeneralInfo = (content: string, target: any) => {
  // 状态码提取
  const statusMatch = content.match(/状态码:\s*(\d+)/)
  if (statusMatch) {
    target.basic_info.status_code = parseInt(statusMatch[1])
  }

  // 响应时间提取
  const timeMatch = content.match(/响应时间:\s*(\d+)ms/)
  if (timeMatch) {
    target.basic_info.response_time = parseInt(timeMatch[1])
  }

  // 页面标题提取
  const titleMatch = content.match(/页面标题:\s*([^\n\r]+)/)
  if (titleMatch) {
    target.basic_info.title = titleMatch[1].trim()
  }

  // IP地址提取
  const ipMatch = content.match(/IP[地址]*:\s*(\d+\.\d+\.\d+\.\d+)/)
  if (ipMatch) {
    target.basic_info.ip = ipMatch[1]
  }
}

// 生成模拟的目标信息详情数据
const generateMockTargetInfo = () => {
  const scanTargets = scan.value.targets || scan.value.target || ''
  const targetList = Array.isArray(scanTargets) ? scanTargets : scanTargets.split(',').map(t => t.trim())

  const targets = targetList.map((target, index) => {
    if (!target) return null

    const baseTarget = {
      id: index + 1,
      target: target,
      scan_type: scan.value.type,
      status: 'completed',
      discovered_at: new Date().toISOString()
    }

    // 根据扫描类型生成详细信息
    switch (scan.value.type) {
      case 'web_scan':
        return {
          ...baseTarget,
          basic_info: {
            url: target,
            domain: extractDomainFromTarget(target),
            ip: `192.168.1.${100 + index}`,
            port: target.includes('https') ? 443 : 80,
            protocol: target.includes('https') ? 'https' : 'http',
            status_code: 200,
            title: '网站标题示例',
            content_type: 'text/html',
            response_time: 150 + index * 10
          },
          tech_stack: {
            web_server: 'Apache/2.4.41',
            framework: 'Laravel',
            language: 'PHP',
            database: 'MySQL',
            cms: 'WordPress',
            technologies: ['jQuery', 'Bootstrap', 'Font Awesome']
          },
          services: {
            open_ports: [
              { port: 80, protocol: 'tcp', service: 'http', version: 'Apache 2.4.41', state: 'open' },
              { port: 443, protocol: 'tcp', service: 'https', version: 'Apache 2.4.41', state: 'open' }
            ],
            ssl_info: {
              enabled: true,
              version: 'TLSv1.3',
              cipher: 'TLS_AES_256_GCM_SHA384',
              valid_from: '2024-01-01',
              valid_to: '2025-01-01'
            }
          },
          security_config: {
            security_headers: {
              'x-frame-options': 'DENY',
              'x-content-type-options': 'nosniff',
              'x-xss-protection': '1; mode=block',
              'strict-transport-security': 'max-age=31536000'
            },
            missing_headers: ['Content-Security-Policy', 'Referrer-Policy']
          },
          directory_structure: {
            discovered_paths: ['/admin', '/api', '/uploads', '/assets'],
            sensitive_files: ['/robots.txt', '/sitemap.xml'],
            backup_files: []
          },
          crawler_findings: {
            total_urls: 125,
            forms_found: 8,
            parameters: ['id', 'name', 'email', 'search'],
            technologies: ['Google Analytics', 'jQuery', 'Bootstrap']
          },
          statistics: {
            scan_duration: 300 + index * 50,
            requests_sent: 1000 + index * 200,
            responses_received: 950 + index * 180,
            errors_count: 5 + index
          }
        }

      case 'port_scan':
        return {
          ...baseTarget,
          basic_info: {
            ip: target,
            hostname: `host-${target.replace(/\./g, '-')}.local`
          },
          services: {
            open_ports: [
              { port: 22, protocol: 'tcp', service: 'ssh', version: 'OpenSSH 7.4', state: 'open' },
              { port: 80, protocol: 'tcp', service: 'http', version: 'Apache 2.4.6', state: 'open' },
              { port: 443, protocol: 'tcp', service: 'https', version: 'Apache 2.4.6', state: 'open' },
              { port: 3306, protocol: 'tcp', service: 'mysql', version: 'MySQL 5.7', state: 'open' }
            ]
          },
          os_detection: {
            os_family: 'Linux',
            os_name: 'CentOS Linux 7',
            confidence: 95
          },
          statistics: {
            scan_duration: 180 + index * 30,
            requests_sent: 500 + index * 100,
            responses_received: 480 + index * 95,
            errors_count: 2 + index
          }
        }

      case 'api_scan':
        return {
          ...baseTarget,
          basic_info: {
            url: target,
            domain: extractDomainFromTarget(target),
            ip: `192.168.1.${100 + index}`
          },
          api_endpoints: [
            { path: '/api/v1/users', method: 'GET', auth_required: true },
            { path: '/api/v1/login', method: 'POST', auth_required: false },
            { path: '/api/v1/data', method: 'GET', auth_required: true }
          ],
          authentication: {
            auth_methods: ['JWT', 'API Key'],
            weak_auth: false
          },
          statistics: {
            scan_duration: 240 + index * 40,
            requests_sent: 800 + index * 150,
            responses_received: 760 + index * 140,
            errors_count: 3 + index
          }
        }

      default:
        return {
          ...baseTarget,
          basic_info: {
            ip: target,
            hostname: target
          },
          statistics: {
            scan_duration: 120 + index * 20,
            requests_sent: 200 + index * 50,
            responses_received: 190 + index * 45,
            errors_count: 1 + index
          }
        }
    }
  }).filter(Boolean)

  return {
    targets: targets,
    total_count: targets.length
  }
}

// 从目标字符串中提取域名
const extractDomainFromTarget = (target: string) => {
  if (target.startsWith('http://') || target.startsWith('https://')) {
    try {
      const url = new URL(target)
      return url.hostname
    } catch (e) {
      return target
    }
  }
  return target
}

// 获取扫描类型标签样式
const getScanTypeTagType = (scanType: string) => {
  const typeMap: Record<string, string> = {
    web_scan: 'success',
    port_scan: 'primary',
    host_scan: 'warning',
    api_scan: 'info'
  }
  return typeMap[scanType] || ''
}

// 获取扫描类型文本
const getScanTypeText = (scanType: string) => {
  const textMap: Record<string, string> = {
    web_scan: 'Web扫描',
    port_scan: '端口扫描',
    host_scan: '主机扫描',
    api_scan: 'API扫描'
  }
  return textMap[scanType] || scanType
}

// 获取端口状态类型样式
const getPortStateType = (state: string) => {
  const stateMap: Record<string, string> = {
    open: 'success',
    closed: 'danger',
    filtered: 'warning',
    unknown: 'info'
  }
  return stateMap[state] || 'info'
}

// 格式化发现时间
const formatDiscoveredTime = (timeStr: string) => {
  if (!timeStr) return '-'
  const date = new Date(timeStr)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))

  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`
  return date.toLocaleDateString()
}

// 获取目标状态类型
const getTargetStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    online: 'success',
    offline: 'danger',
    unknown: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取目标状态文本
const getTargetStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    online: '在线',
    offline: '离线',
    unknown: '未知'
  }
  return textMap[status] || status
}





onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

// 新增的辅助方法
const getScanTypeColor = (scanType: string) => {
  const colorMap: Record<string, string> = {
    'web_scan': 'primary',
    'port_scan': 'success',
    'host_scan': 'warning',
    'api_scan': 'info',
    'cve_scan': 'danger'
  }
  return colorMap[scanType] || 'default'
}

const getScanTypeLabel = (scanType: string) => {
  const labelMap: Record<string, string> = {
    'web_scan': 'Web扫描',
    'port_scan': '端口扫描',
    'host_scan': '主机扫描',
    'api_scan': 'API扫描',
    'cve_scan': 'CVE扫描'
  }
  return labelMap[scanType] || scanType
}



const getStatusCodeColor = (statusCode: number) => {
  if (statusCode >= 200 && statusCode < 300) return 'success'
  if (statusCode >= 300 && statusCode < 400) return 'warning'
  if (statusCode >= 400 && statusCode < 500) return 'danger'
  if (statusCode >= 500) return 'danger'
  return 'info'
}

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 90) return '#67c23a'
  if (confidence >= 70) return '#e6a23c'
  if (confidence >= 50) return '#f56c6c'
  return '#909399'
}



const getMethodColor = (method: string) => {
  const colorMap: Record<string, string> = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  }
  return colorMap[method] || 'default'
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${hours}小时${minutes}分${secs}秒`
}

// 目标详情展开/收起
const toggleTargetDetail = (index: number) => {
  const expandedIndex = expandedTargets.value.indexOf(index)
  if (expandedIndex > -1) {
    expandedTargets.value.splice(expandedIndex, 1)
  } else {
    expandedTargets.value.push(index)
  }
}

// 获取目标状态颜色
const getTargetStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'completed': 'success',
    'no_data': 'warning',
    'failed': 'danger',
    'running': 'primary'
  }
  return colorMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    'completed': '已完成',
    'no_data': '无数据',
    'failed': '失败',
    'running': '运行中',
    'pending': '等待中'
  }
  return labelMap[status] || status
}

// 格式化HTTP头名称
const formatHeaderName = (headerName: string) => {
  return headerName.replace(/_/g, '-').replace(/\b\w/g, l => l.toUpperCase())
}
</script>

<style lang="scss" scoped>
.scan-detail-page {
  padding: var(--spacing-md);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  
  .header-left {
    display: flex;
    align-items: flex-start;
  }
  
  .header-actions {
    display: flex;
    gap: var(--spacing-sm);
  }
}

.info-card,
.results-card {
  margin-bottom: var(--spacing-md);
}

.scanning-status {
  text-align: center;
  padding: var(--spacing-xl);
  
  .scanning-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    animation: spin 1s linear infinite;
  }
  
  span {
    font-size: 16px;
    color: var(--text-secondary);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.scan-completed,
.scan-failed {
  padding: var(--spacing-md);
}

// 扫描结果相关样式
.scan-results {
  .result-summary {
    margin-bottom: var(--spacing-md);

    .summary-content {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);

      .summary-icon {
        font-size: 48px;

        &.success {
          color: var(--color-success);
        }
      }

      .summary-text {
        flex: 1;

        h3 {
          margin: 0 0 var(--spacing-xs) 0;
          font-size: 20px;
          color: var(--text-primary);
        }

        p {
          margin: 0;
          color: var(--text-secondary);
        }
      }

      .summary-actions {
        display: flex;
        gap: var(--spacing-sm);
      }
    }
  }

  // 扫描结果详情
  .scan-results-detail {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);

    // 目标信息统计
    .targets-summary {
      .targets-stats {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: var(--spacing-md);

        .stat-item {
          flex: 1;
          min-width: 120px;
          text-align: center;
          padding: var(--spacing-md);
          background-color: var(--bg-light);
          border-radius: var(--border-radius-md);

          .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: var(--color-primary);
            margin-bottom: var(--spacing-xs);
          }

          .stat-label {
            color: var(--text-secondary);
            font-size: 14px;
          }
        }
      }
    }

    // 风险分布
    .risk-distribution {
      .risk-stats {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: var(--spacing-md);

        .risk-item {
          flex: 1;
          min-width: 100px;
          text-align: center;
          padding: var(--spacing-md);
          border-radius: var(--border-radius-md);

          .risk-count {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: var(--spacing-xs);
          }

          .risk-label {
            font-size: 14px;
          }

          &.critical {
            background-color: rgba(var(--color-danger-rgb), 0.1);
            .risk-count { color: #F56C6C; }
            .risk-label { color: #F56C6C; }
          }

          &.high {
            background-color: rgba(var(--color-danger-rgb), 0.05);
            .risk-count { color: #F56C6C; }
            .risk-label { color: #F56C6C; }
          }

          &.medium {
            background-color: rgba(var(--color-warning-rgb), 0.1);
            .risk-count { color: #E6A23C; }
            .risk-label { color: #E6A23C; }
          }

          &.low {
            background-color: rgba(var(--color-info-rgb), 0.1);
            .risk-count { color: #909399; }
            .risk-label { color: #909399; }
          }

          &.info {
            background-color: rgba(var(--color-info-rgb), 0.05);
            .risk-count { color: #909399; }
            .risk-label { color: #909399; }
          }
        }
      }
    }

    // 漏洞卡片
    .vulnerabilities-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-actions {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
        }
      }

      .vulnerabilities-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);

        .vulnerability-card {
          border: 1px solid var(--border-color);
          border-radius: var(--border-radius-md);
          overflow: hidden;

          .vuln-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            background-color: var(--bg-light);
            border-bottom: 1px solid var(--border-color);

            .vuln-title {
              h4 {
                margin: 0 0 var(--spacing-xs) 0;
                font-size: 16px;
                color: var(--text-primary);
              }

              .vuln-tags {
                display: flex;
                gap: var(--spacing-xs);
              }
            }
          }

          .vuln-content {
            padding: var(--spacing-md);

            .vuln-description {
              margin-bottom: var(--spacing-md);
              color: var(--text-secondary);
            }

            h5 {
              margin: var(--spacing-md) 0 var(--spacing-xs) 0;
              font-size: 14px;
              color: var(--text-primary);
              border-bottom: 1px solid var(--border-color);
              padding-bottom: var(--spacing-xs);
            }

            .info-grid {
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
              gap: var(--spacing-sm);

              .info-item {
                display: flex;

                .label {
                  font-weight: bold;
                  margin-right: var(--spacing-xs);
                  color: var(--text-secondary);
                }

                .value {
                  word-break: break-all;
                }
              }
            }

            .details-content {
              background-color: var(--bg-light);
              padding: var(--spacing-sm);
              border-radius: var(--border-radius-sm);

              .detail-item {
                margin-bottom: var(--spacing-sm);

                &:last-child {
                  margin-bottom: 0;
                }

                .label {
                  display: block;
                  font-weight: bold;
                  margin-bottom: var(--spacing-xs);
                  color: var(--text-secondary);
                }

                .payload {
                  display: block;
                  padding: var(--spacing-xs);
                  background-color: var(--bg-dark);
                  color: var(--text-light);
                  border-radius: var(--border-radius-sm);
                  font-family: monospace;
                  overflow-x: auto;
                }

                .evidence {
                  max-height: 100px;
                  overflow-y: auto;
                  white-space: pre-wrap;
                  font-family: monospace;
                  font-size: 12px;
                  background-color: var(--bg-dark);
                  color: var(--text-light);
                  padding: var(--spacing-xs);
                  border-radius: var(--border-radius-sm);
                }
              }
            }

            .vuln-solution {
              margin-bottom: var(--spacing-md);
            }

            .reference-link {
              color: var(--color-primary);
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }

            .vuln-meta {
              display: flex;
              justify-content: space-between;
              margin-top: var(--spacing-md);
              padding-top: var(--spacing-md);
              border-top: 1px solid var(--border-color);

              .meta-item {
                display: flex;
                align-items: center;

                .label {
                  margin-right: var(--spacing-xs);
                  color: var(--text-secondary);
                }
              }
            }
          }
        }
      }

      .pagination-container {
        margin-top: var(--spacing-md);
        display: flex;
        justify-content: center;
      }
    }
  }
}

// 日志相关样式
.log-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);

  .log-filters {
    display: flex;
    align-items: center;
  }

  .log-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }
}

.log-container {
  min-height: 400px;
}

.log-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;

  .loading-icon {
    font-size: 32px;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    animation: spin 1s linear infinite;
  }
}

.log-empty {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.log-list {
  .log-item {
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--border-color);
    background: var(--bg-secondary);

    &.log-debug {
      border-left-color: #909399;
    }

    &.log-info {
      border-left-color: #67c23a;
    }

    &.log-warn {
      border-left-color: #e6a23c;
    }

    &.log-error {
      border-left-color: #f56c6c;
    }

    .log-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-bottom: var(--spacing-xs);
      font-size: 12px;

      .log-time {
        color: var(--text-secondary);
        font-family: monospace;
      }

      .log-stage {
        color: var(--primary-color);
        font-weight: 500;
      }

      .log-target {
        color: var(--text-secondary);
        font-family: monospace;
      }
    }

    .log-message {
      font-size: 14px;
      color: var(--text-primary);
      line-height: 1.4;
    }

    .log-details {
      margin-top: var(--spacing-xs);
      padding: var(--spacing-xs);
      background: var(--bg-primary);
      border-radius: var(--border-radius);
      font-size: 12px;
      color: var(--text-secondary);
      font-family: monospace;
      white-space: pre-wrap;
    }
  }
}

// 目标详情弹窗样式
.target-detail-content {
  .detail-section {
      margin-bottom: var(--spacing-lg);

      h3 {
        margin: 0;
        color: var(--text-primary);
        font-size: 16px;
        font-weight: 600;
      }

      h4 {
        margin: var(--spacing-md) 0 var(--spacing-sm) 0;
        color: var(--text-secondary);
        font-size: 14px;
        font-weight: 500;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: var(--spacing-xs);
      }

      .sub-section {
        margin-bottom: var(--spacing-md);

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
}



// 目标详情列表样式
.targets-detail-list {
  .target-detail-card {
      margin-bottom: var(--spacing-md);

      .target-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .target-info {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);

          .target-name {
            font-weight: 600;
            font-size: 16px;
            color: var(--text-primary);
          }
        }
      }

      .target-details {
        .info-section {
          margin-bottom: var(--spacing-lg);

          h4 {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: var(--spacing-xs);
          }

          h5 {
            margin: var(--spacing-md) 0 var(--spacing-sm) 0;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
          }

          .sub-section {
            margin-bottom: var(--spacing-md);

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .no-data-section {
          text-align: center;
          padding: var(--spacing-xl);
          color: var(--text-secondary);
        }
      }
    }
}



// 空状态样式
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);

  .no-info-message {
    max-width: 500px;
    margin: 0 auto;

    p {
      font-size: 16px;
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
      font-weight: 500;
    }

    ul {
      text-align: left;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        padding: var(--spacing-xs) 0;
        color: var(--text-secondary);
        font-size: 14px;
        line-height: 1.5;

        &:before {
          content: "•";
          color: var(--color-primary);
          margin-right: var(--spacing-xs);
        }
      }
    }
  }
}
</style>
