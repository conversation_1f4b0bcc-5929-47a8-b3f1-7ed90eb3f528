package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// IsApplicable 检查是否适用于目标
func (d *OAuthVulnerabilityDetector) IsApplicable(target *plugins.ScanTarget) bool {
	if !d.IsEnabled() {
		return false
	}

	// 检查目标URL是否为HTTP/HTTPS
	if !strings.HasPrefix(target.URL, "http://") && !strings.HasPrefix(target.URL, "https://") {
		return false
	}

	// 检查是否可能是OAuth端点
	return d.isLikelyOAuthEndpoint(target.URL)
}

// isLikelyOAuthEndpoint 检查是否可能是OAuth端点
func (d *OAuthVulnerabilityDetector) isLikelyOAuthEndpoint(targetURL string) bool {
	urlLower := strings.ToLower(targetURL)

	// 检查URL路径
	oauthKeywords := []string{
		"oauth", "oauth2", "openid", "oidc", "sso", "auth", "login", "connect", "identity",
	}

	for _, keyword := range oauthKeywords {
		if strings.Contains(urlLower, keyword) {
			return true
		}
	}

	// 检查URL参数
	for _, param := range d.oauthParameters {
		if strings.Contains(urlLower, param+"=") {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *OAuthVulnerabilityDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableResponse string

	// 1. OAuth端点发现
	endpoints, discoveryEvidence, discoveryConfidence := d.discoverOAuthEndpoints(ctx, target)
	evidence = append(evidence, discoveryEvidence...)
	if discoveryConfidence > maxConfidence {
		maxConfidence = discoveryConfidence
	}

	// 2. 开放重定向测试
	for _, endpoint := range endpoints {
		redirectEvidence, redirectConfidence, redirectPayload, redirectResponse := d.testOpenRedirect(ctx, endpoint)
		evidence = append(evidence, redirectEvidence...)
		if redirectConfidence > maxConfidence {
			maxConfidence = redirectConfidence
			vulnerablePayload = redirectPayload
			vulnerableResponse = redirectResponse
		}
	}

	// 3. CSRF测试
	for _, endpoint := range endpoints {
		csrfEvidence, csrfConfidence, csrfPayload, csrfResponse := d.testCSRF(ctx, endpoint)
		evidence = append(evidence, csrfEvidence...)
		if csrfConfidence > maxConfidence {
			maxConfidence = csrfConfidence
			vulnerablePayload = csrfPayload
			vulnerableResponse = csrfResponse
		}
	}

	// 4. 授权码劫持测试
	for _, endpoint := range endpoints {
		codeEvidence, codeConfidence, codePayload, codeResponse := d.testAuthorizationCodeInterception(ctx, endpoint)
		evidence = append(evidence, codeEvidence...)
		if codeConfidence > maxConfidence {
			maxConfidence = codeConfidence
			vulnerablePayload = codePayload
			vulnerableResponse = codeResponse
		}
	}

	// 5. 客户端凭据泄露测试
	for _, endpoint := range endpoints {
		credEvidence, credConfidence, credPayload, credResponse := d.testClientCredentialsExposure(ctx, endpoint)
		evidence = append(evidence, credEvidence...)
		if credConfidence > maxConfidence {
			maxConfidence = credConfidence
			vulnerablePayload = credPayload
			vulnerableResponse = credResponse
		}
	}

	// 构建检测结果
	isVulnerable := maxConfidence >= 0.7
	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "OAuth安全漏洞",
		Description:       d.generateDescription(maxConfidence, vulnerablePayload),
		Remediation:       d.generateSolution(),
		References:        d.generateReferences(),
		Evidence:          evidence,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Payload:           vulnerablePayload,
		Response:          vulnerableResponse,
		Tags:              []string{"oauth", "oauth2", "openid", "authentication", "authorization"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
		Metadata: map[string]interface{}{
			"oauth_endpoints_found": len(endpoints),
			"detection_methods":     []string{"endpoint_discovery", "open_redirect", "csrf", "code_interception", "credential_exposure"},
		},
	}

	return result, nil
}

// discoverOAuthEndpoints 发现OAuth端点
func (d *OAuthVulnerabilityDetector) discoverOAuthEndpoints(ctx context.Context, target *plugins.ScanTarget) ([]string, []plugins.Evidence, float64) {
	var endpoints []string
	var evidence []plugins.Evidence
	confidence := 0.0

	// 解析目标URL
	parsedURL, err := url.Parse(target.URL)
	if err != nil {
		return endpoints, evidence, confidence
	}

	baseURL := fmt.Sprintf("%s://%s", parsedURL.Scheme, parsedURL.Host)

	// 1. 检查当前URL是否为OAuth端点
	if d.isOAuthEndpoint(target.URL) {
		endpoints = append(endpoints, target.URL)
		confidence = 0.8
		evidence = append(evidence, plugins.Evidence{
			Type:        "oauth_endpoint",
			Description: "发现OAuth端点",
			Content:     target.URL,
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	// 2. 尝试发现其他OAuth端点
	for _, endpoint := range d.oauthEndpoints {
		testURL := baseURL + endpoint
		if d.testEndpointExists(ctx, testURL) {
			endpoints = append(endpoints, testURL)
			confidence = 0.6
			evidence = append(evidence, plugins.Evidence{
				Type:        "oauth_endpoint_discovery",
				Description: fmt.Sprintf("发现OAuth端点: %s", endpoint),
				Content:     testURL,
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	// 3. 检查.well-known配置
	wellKnownURL := baseURL + "/.well-known/openid_configuration"
	if d.testEndpointExists(ctx, wellKnownURL) {
		endpoints = append(endpoints, wellKnownURL)
		confidence = 0.9
		evidence = append(evidence, plugins.Evidence{
			Type:        "openid_configuration",
			Description: "发现OpenID Connect配置端点",
			Content:     wellKnownURL,
			Location:    wellKnownURL,
			Timestamp:   time.Now(),
		})
	}

	return endpoints, evidence, confidence
}

// isOAuthEndpoint 检查是否为OAuth端点
func (d *OAuthVulnerabilityDetector) isOAuthEndpoint(targetURL string) bool {
	urlLower := strings.ToLower(targetURL)
	for _, endpoint := range d.oauthEndpoints {
		if strings.Contains(urlLower, strings.ToLower(endpoint)) {
			return true
		}
	}
	return false
}

// testEndpointExists 测试端点是否存在
func (d *OAuthVulnerabilityDetector) testEndpointExists(ctx context.Context, testURL string) bool {
	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return false
	}

	req.Header.Set("User-Agent", "Security Scanner OAuth Detector/1.0")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	// 检查状态码，200-299或400-499都可能表示端点存在
	return resp.StatusCode < 500
}

// testOpenRedirect 测试开放重定向
func (d *OAuthVulnerabilityDetector) testOpenRedirect(ctx context.Context, endpoint string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	// 恶意重定向URL列表
	maliciousURLs := []string{
		"http://evil.com",
		"https://attacker.com",
		"//evil.com",
		"http://evil.com/callback",
		"javascript:alert('XSS')",
		"data:text/html,<script>alert('XSS')</script>",
	}

	for _, maliciousURL := range maliciousURLs {
		// 构建测试URL
		testURL := d.buildOAuthURL(endpoint, map[string]string{
			"redirect_uri":  maliciousURL,
			"client_id":     "test_client",
			"response_type": "code",
			"scope":         "openid",
		})

		vulnerablePayload = testURL

		req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
		if err != nil {
			continue
		}

		req.Header.Set("User-Agent", "Security Scanner OAuth Detector/1.0")

		resp, err := d.httpClient.Do(req)
		if err != nil {
			continue
		}

		body, _ := io.ReadAll(resp.Body)
		resp.Body.Close()
		vulnerableResponse = string(body)

		// 检查是否重定向到恶意URL
		if resp.StatusCode >= 300 && resp.StatusCode < 400 {
			location := resp.Header.Get("Location")
			if strings.Contains(location, "evil.com") || strings.Contains(location, "attacker.com") {
				confidence = 0.9
				evidence = append(evidence, plugins.Evidence{
					Type:        "open_redirect",
					Description: fmt.Sprintf("发现开放重定向漏洞，重定向到: %s", location),
					Content:     fmt.Sprintf("请求URL: %s\n重定向位置: %s", testURL, location),
					Location:    endpoint,
					Timestamp:   time.Now(),
				})
				break
			}
		}

		// 检查响应中是否包含恶意URL
		if strings.Contains(vulnerableResponse, maliciousURL) {
			confidence = 0.7
			evidence = append(evidence, plugins.Evidence{
				Type:        "open_redirect_reflection",
				Description: "发现重定向URI反射，可能存在开放重定向风险",
				Content:     fmt.Sprintf("请求URL: %s\n恶意URL在响应中: %s", testURL, maliciousURL),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// testCSRF 测试CSRF
func (d *OAuthVulnerabilityDetector) testCSRF(ctx context.Context, endpoint string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	// 测试缺少state参数的请求
	testURL := d.buildOAuthURL(endpoint, map[string]string{
		"redirect_uri":  "http://localhost:8080/callback",
		"client_id":     "test_client",
		"response_type": "code",
		"scope":         "openid",
		// 故意不包含state参数
	})

	vulnerablePayload = testURL

	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	req.Header.Set("User-Agent", "Security Scanner OAuth Detector/1.0")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	body, _ := io.ReadAll(resp.Body)
	resp.Body.Close()
	vulnerableResponse = string(body)

	// 检查是否接受了没有state参数的请求
	if resp.StatusCode == 200 || (resp.StatusCode >= 300 && resp.StatusCode < 400) {
		// 检查响应中是否没有要求state参数
		if !strings.Contains(strings.ToLower(vulnerableResponse), "state") &&
			!strings.Contains(strings.ToLower(vulnerableResponse), "csrf") {
			confidence = 0.6
			evidence = append(evidence, plugins.Evidence{
				Type:        "csrf_missing_state",
				Description: "OAuth流程缺少state参数，可能存在CSRF攻击风险",
				Content:     fmt.Sprintf("请求URL: %s\n响应状态: %d", testURL, resp.StatusCode),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// buildOAuthURL 构建OAuth URL
func (d *OAuthVulnerabilityDetector) buildOAuthURL(baseURL string, params map[string]string) string {
	u, err := url.Parse(baseURL)
	if err != nil {
		return baseURL
	}

	q := u.Query()
	for key, value := range params {
		q.Set(key, value)
	}
	u.RawQuery = q.Encode()

	return u.String()
}

// testAuthorizationCodeInterception 测试授权码劫持
func (d *OAuthVulnerabilityDetector) testAuthorizationCodeInterception(ctx context.Context, endpoint string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	// 测试使用HTTP重定向URI（不安全）
	httpRedirectURI := "http://attacker.com/callback"
	testURL := d.buildOAuthURL(endpoint, map[string]string{
		"redirect_uri":  httpRedirectURI,
		"client_id":     "test_client",
		"response_type": "code",
		"scope":         "openid",
		"state":         "test_state",
	})

	vulnerablePayload = testURL

	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	req.Header.Set("User-Agent", "Security Scanner OAuth Detector/1.0")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	body, _ := io.ReadAll(resp.Body)
	resp.Body.Close()
	vulnerableResponse = string(body)

	// 检查是否接受HTTP重定向URI
	if resp.StatusCode == 200 || (resp.StatusCode >= 300 && resp.StatusCode < 400) {
		// 检查是否没有拒绝HTTP URI
		if !strings.Contains(strings.ToLower(vulnerableResponse), "https required") &&
			!strings.Contains(strings.ToLower(vulnerableResponse), "invalid redirect") {
			confidence = 0.7
			evidence = append(evidence, plugins.Evidence{
				Type:        "insecure_redirect_uri",
				Description: "OAuth服务器接受HTTP重定向URI，存在授权码劫持风险",
				Content:     fmt.Sprintf("请求URL: %s\n不安全的重定向URI: %s", testURL, httpRedirectURI),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}
	}

	// 测试通配符重定向URI
	wildcardRedirectURI := "https://attacker.com/*"
	testURL2 := d.buildOAuthURL(endpoint, map[string]string{
		"redirect_uri":  wildcardRedirectURI,
		"client_id":     "test_client",
		"response_type": "code",
		"scope":         "openid",
		"state":         "test_state",
	})

	req2, err := http.NewRequestWithContext(ctx, "GET", testURL2, nil)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	req2.Header.Set("User-Agent", "Security Scanner OAuth Detector/1.0")

	resp2, err := d.httpClient.Do(req2)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	body2, _ := io.ReadAll(resp2.Body)
	resp2.Body.Close()

	// 检查是否接受通配符重定向URI
	if resp2.StatusCode == 200 || (resp2.StatusCode >= 300 && resp2.StatusCode < 400) {
		if !strings.Contains(strings.ToLower(string(body2)), "invalid redirect") {
			confidence = 0.8
			evidence = append(evidence, plugins.Evidence{
				Type:        "wildcard_redirect_uri",
				Description: "OAuth服务器可能接受通配符重定向URI，存在安全风险",
				Content:     fmt.Sprintf("请求URL: %s\n通配符重定向URI: %s", testURL2, wildcardRedirectURI),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// testClientCredentialsExposure 测试客户端凭据泄露
func (d *OAuthVulnerabilityDetector) testClientCredentialsExposure(ctx context.Context, endpoint string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	// 测试客户端凭据是否在URL中暴露
	testURL := d.buildOAuthURL(endpoint, map[string]string{
		"client_id":     "test_client_id",
		"client_secret": "test_client_secret",
		"grant_type":    "client_credentials",
		"scope":         "read",
	})

	vulnerablePayload = testURL

	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	req.Header.Set("User-Agent", "Security Scanner OAuth Detector/1.0")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	body, _ := io.ReadAll(resp.Body)
	resp.Body.Close()
	vulnerableResponse = string(body)

	// 检查是否接受URL中的客户端凭据
	if resp.StatusCode == 200 {
		// 检查响应中是否包含访问令牌
		if strings.Contains(vulnerableResponse, "access_token") ||
			strings.Contains(vulnerableResponse, "token_type") {
			confidence = 0.8
			evidence = append(evidence, plugins.Evidence{
				Type:        "client_credentials_in_url",
				Description: "OAuth服务器接受URL中的客户端凭据，存在凭据泄露风险",
				Content:     fmt.Sprintf("请求URL: %s\n响应包含访问令牌", testURL),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}
	}

	// 测试错误响应中是否泄露敏感信息
	invalidTestURL := d.buildOAuthURL(endpoint, map[string]string{
		"client_id":     "invalid_client",
		"client_secret": "invalid_secret",
		"grant_type":    "client_credentials",
	})

	req2, err := http.NewRequestWithContext(ctx, "GET", invalidTestURL, nil)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	req2.Header.Set("User-Agent", "Security Scanner OAuth Detector/1.0")

	resp2, err := d.httpClient.Do(req2)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	body2, _ := io.ReadAll(resp2.Body)
	resp2.Body.Close()

	errorResponse := string(body2)

	// 检查错误响应中是否包含敏感信息
	sensitiveKeywords := []string{
		"database", "sql", "connection", "server", "internal", "stack trace",
		"exception", "debug", "config", "password", "secret", "key",
	}

	for _, keyword := range sensitiveKeywords {
		if strings.Contains(strings.ToLower(errorResponse), keyword) {
			confidence = 0.6
			evidence = append(evidence, plugins.Evidence{
				Type:        "information_disclosure",
				Description: "OAuth错误响应中可能包含敏感信息",
				Content:     fmt.Sprintf("错误响应包含关键词: %s", keyword),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
			break
		}
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// generateVulnID 生成漏洞ID
func (d *OAuthVulnerabilityDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("oauth-vulnerability-%s-%d", d.id, time.Now().Unix())
}

// generateDescription 生成漏洞描述
func (d *OAuthVulnerabilityDetector) generateDescription(confidence float64, payload string) string {
	if confidence >= 0.9 {
		return fmt.Sprintf("发现高风险OAuth安全漏洞。检测载荷: %s", d.maskURL(payload))
	} else if confidence >= 0.7 {
		return fmt.Sprintf("发现可能的OAuth安全漏洞。检测载荷: %s", d.maskURL(payload))
	}
	return "OAuth实现存在潜在安全风险"
}

// generateSolution 生成解决方案
func (d *OAuthVulnerabilityDetector) generateSolution() string {
	return `1. 实施严格的重定向URI验证，使用白名单机制
2. 强制使用HTTPS重定向URI，禁止HTTP
3. 实施state参数防止CSRF攻击
4. 使用PKCE (Proof Key for Code Exchange) 增强安全性
5. 客户端凭据应通过安全的方式传输，避免在URL中暴露
6. 实施适当的错误处理，避免信息泄露
7. 定期轮换客户端凭据和访问令牌
8. 实施访问令牌的适当生命周期管理
9. 使用最新的OAuth 2.1或OpenID Connect规范
10. 进行定期的安全审计和渗透测试`
}

// generateReferences 生成参考资料
func (d *OAuthVulnerabilityDetector) generateReferences() []string {
	return []string{
		"https://owasp.org/www-project-web-security-testing-guide/latest/4-Web_Application_Security_Testing/06-Session_Management_Testing/09-Testing_for_Session_Fixation",
		"https://tools.ietf.org/html/rfc6749",
		"https://tools.ietf.org/html/rfc7636",
		"https://openid.net/connect/",
		"https://portswigger.net/web-security/oauth",
		"https://github.com/swisskyrepo/PayloadsAllTheThings/tree/master/OAuth",
		"https://oauth.net/2/security-considerations/",
	}
}

// calculateRiskScore 计算风险评分
func (d *OAuthVulnerabilityDetector) calculateRiskScore(confidence float64) float64 {
	baseScore := 7.5 // OAuth漏洞基础风险评分
	return baseScore * confidence
}

// maskURL 掩码URL用于显示
func (d *OAuthVulnerabilityDetector) maskURL(urlStr string) string {
	if len(urlStr) <= 50 {
		return urlStr
	}
	return urlStr[:30] + "..." + urlStr[len(urlStr)-20:]
}

// extractResponseEvidence 提取响应证据
func (d *OAuthVulnerabilityDetector) extractResponseEvidence(response string) string {
	if len(response) > 500 {
		return response[:500] + "... [截断]"
	}
	return response
}
