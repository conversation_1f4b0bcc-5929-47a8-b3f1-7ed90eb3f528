package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectPasswordComplexity 检测密码复杂度
func (d *PasswordPolicyDetector) detectPasswordComplexity(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试弱密码载荷
	for _, payload := range d.passwordTestPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送密码复杂度测试请求
		response, err := d.sendPasswordPolicyRequest(ctx, target.URL, payload, "complexity")
		if err != nil {
			continue
		}

		// 检查是否存在密码复杂度问题
		confidence := d.checkPasswordComplexityResponse(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("密码复杂度测试: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.4 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "password-complexity",
				Description: fmt.Sprintf("发现密码复杂度问题: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractPasswordPolicyEvidence(response, payload, "complexity"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 120)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPasswordHistory 检测密码历史
func (d *PasswordPolicyDetector) detectPasswordHistory(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试密码历史载荷
	historyPayloads := []string{
		"oldpassword", "previouspassword", "lastpassword", "samepassword",
		"password", "password123", "admin", "admin123", "user", "user123",
		"旧密码", "之前密码", "上次密码", "相同密码", "历史密码", "重复密码",
	}

	for _, payload := range historyPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送密码历史测试请求
		response, err := d.sendPasswordPolicyRequest(ctx, target.URL, payload, "history")
		if err != nil {
			continue
		}

		// 检查是否存在密码历史问题
		confidence := d.checkPasswordHistoryResponse(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("密码历史测试: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.4 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "password-history",
				Description: fmt.Sprintf("发现密码历史问题: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractPasswordPolicyEvidence(response, payload, "history"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 130)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPasswordExpiry 检测密码过期
func (d *PasswordPolicyDetector) detectPasswordExpiry(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试密码过期载荷
	expiryPayloads := []string{
		"expiredpassword", "oldpassword", "outdatedpassword", "expireduser",
		"过期密码", "过期用户", "旧密码", "过时密码", "失效密码", "超期密码",
	}

	for _, payload := range expiryPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送密码过期测试请求
		response, err := d.sendPasswordPolicyRequest(ctx, target.URL, payload, "expiry")
		if err != nil {
			continue
		}

		// 检查是否存在密码过期问题
		confidence := d.checkPasswordExpiryResponse(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("密码过期测试: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.4 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "password-expiry",
				Description: fmt.Sprintf("发现密码过期问题: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractPasswordPolicyEvidence(response, payload, "expiry"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 140)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPasswordReset 检测密码重置
func (d *PasswordPolicyDetector) detectPasswordReset(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试密码重置路径
	for _, path := range d.passwordResetPaths {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送密码重置测试请求
		response, err := d.sendPasswordResetRequest(ctx, target.URL, path)
		if err != nil {
			continue
		}

		// 检查是否存在密码重置问题
		confidence := d.checkPasswordResetResponse(response, path)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("密码重置测试: %s", path)
			vulnerableRequest = target.URL + path
			vulnerableResponse = response
		}

		if confidence > 0.4 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "password-reset",
				Description: fmt.Sprintf("发现密码重置问题: %s (置信度: %.2f)", path, confidence),
				Content:     d.extractPasswordPolicyEvidence(response, path, "reset"),
				Location:    target.URL + path,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendPasswordPolicyRequest 发送密码策略测试请求
func (d *PasswordPolicyDetector) sendPasswordPolicyRequest(ctx context.Context, targetURL, payload, testType string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendPasswordPolicyGETRequest(ctx, targetURL, payload, testType)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST表单注入
	postResp, err := d.sendPasswordPolicyPOSTRequest(ctx, targetURL, payload, testType)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 如果有POST响应（即使有错误），也返回
	if postResp != "" {
		return postResp, nil
	}

	return "", fmt.Errorf("所有密码策略请求都失败")
}

// sendPasswordPolicyGETRequest 发送密码策略GET请求
func (d *PasswordPolicyDetector) sendPasswordPolicyGETRequest(ctx context.Context, targetURL, payload, testType string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	// 根据测试类型添加不同参数
	switch testType {
	case "complexity":
		query.Set("password", payload)
		query.Set("new_password", payload)
		query.Set("pwd", payload)
		query.Set("newpwd", payload)
	case "history":
		query.Set("old_password", payload)
		query.Set("previous_password", payload)
		query.Set("last_password", payload)
		query.Set("current_password", payload)
	case "expiry":
		query.Set("expired_password", payload)
		query.Set("old_password", payload)
		query.Set("outdated_password", payload)
	default:
		query.Set("password", payload)
	}

	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// sendPasswordPolicyPOSTRequest 发送密码策略POST请求
func (d *PasswordPolicyDetector) sendPasswordPolicyPOSTRequest(ctx context.Context, targetURL, payload, testType string) (string, error) {
	// 构建表单数据
	formData := url.Values{}

	// 根据测试类型添加不同参数
	switch testType {
	case "complexity":
		formData.Set("password", payload)
		formData.Set("new_password", payload)
		formData.Set("pwd", payload)
		formData.Set("newpwd", payload)
		formData.Set("confirm_password", payload)
		formData.Set("password_confirm", payload)
	case "history":
		formData.Set("old_password", payload)
		formData.Set("previous_password", payload)
		formData.Set("last_password", payload)
		formData.Set("current_password", payload)
		formData.Set("new_password", "newpassword123")
	case "expiry":
		formData.Set("expired_password", payload)
		formData.Set("old_password", payload)
		formData.Set("outdated_password", payload)
		formData.Set("new_password", "newpassword123")
	default:
		formData.Set("password", payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// sendPasswordResetRequest 发送密码重置请求
func (d *PasswordPolicyDetector) sendPasswordResetRequest(ctx context.Context, targetURL, path string) (string, error) {
	// 构造重置URL
	resetURL := strings.TrimSuffix(targetURL, "/") + path

	req, err := http.NewRequestWithContext(ctx, "GET", resetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// checkPasswordComplexityResponse 检查密码复杂度响应
func (d *PasswordPolicyDetector) checkPasswordComplexityResponse(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	_ = strings.ToLower(payload) // 暂时不使用，但保留以备将来使用

	// 检查密码策略相关的响应模式
	for _, pattern := range d.policyPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查弱密码被接受的情况
	if d.isWeakPassword(payload) {
		if strings.Contains(responseLower, "success") ||
			strings.Contains(responseLower, "accepted") ||
			strings.Contains(responseLower, "valid") ||
			strings.Contains(responseLower, "ok") ||
			strings.Contains(responseLower, "成功") ||
			strings.Contains(responseLower, "接受") ||
			strings.Contains(responseLower, "有效") ||
			strings.Contains(responseLower, "通过") {
			confidence += 0.6 // 弱密码被接受是严重问题
		}
	}

	// 检查密码复杂度错误信息
	if strings.Contains(responseLower, "password") && (strings.Contains(responseLower, "weak") ||
		strings.Contains(responseLower, "simple") ||
		strings.Contains(responseLower, "complex") ||
		strings.Contains(responseLower, "strength") ||
		strings.Contains(responseLower, "policy") ||
		strings.Contains(responseLower, "requirement") ||
		strings.Contains(responseLower, "弱") ||
		strings.Contains(responseLower, "简单") ||
		strings.Contains(responseLower, "复杂") ||
		strings.Contains(responseLower, "强度") ||
		strings.Contains(responseLower, "策略") ||
		strings.Contains(responseLower, "要求")) {
		confidence += 0.4
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkPasswordHistoryResponse 检查密码历史响应
func (d *PasswordPolicyDetector) checkPasswordHistoryResponse(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)

	// 检查密码策略相关的响应模式
	for _, pattern := range d.policyPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查密码历史相关的错误信息
	if strings.Contains(responseLower, "history") ||
		strings.Contains(responseLower, "previous") ||
		strings.Contains(responseLower, "recent") ||
		strings.Contains(responseLower, "used") ||
		strings.Contains(responseLower, "same") ||
		strings.Contains(responseLower, "历史") ||
		strings.Contains(responseLower, "之前") ||
		strings.Contains(responseLower, "最近") ||
		strings.Contains(responseLower, "使用过") ||
		strings.Contains(responseLower, "相同") {
		confidence += 0.4
	}

	// 检查重复密码被接受的情况
	if strings.Contains(responseLower, "success") ||
		strings.Contains(responseLower, "accepted") ||
		strings.Contains(responseLower, "成功") ||
		strings.Contains(responseLower, "接受") {
		confidence += 0.3
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkPasswordExpiryResponse 检查密码过期响应
func (d *PasswordPolicyDetector) checkPasswordExpiryResponse(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)

	// 检查密码策略相关的响应模式
	for _, pattern := range d.policyPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查密码过期相关的错误信息
	if strings.Contains(responseLower, "expired") ||
		strings.Contains(responseLower, "expiry") ||
		strings.Contains(responseLower, "expiration") ||
		strings.Contains(responseLower, "outdated") ||
		strings.Contains(responseLower, "old") ||
		strings.Contains(responseLower, "过期") ||
		strings.Contains(responseLower, "失效") ||
		strings.Contains(responseLower, "过时") ||
		strings.Contains(responseLower, "旧") {
		confidence += 0.4
	}

	// 检查过期密码被接受的情况
	if strings.Contains(responseLower, "success") ||
		strings.Contains(responseLower, "accepted") ||
		strings.Contains(responseLower, "成功") ||
		strings.Contains(responseLower, "接受") {
		confidence += 0.3
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkPasswordResetResponse 检查密码重置响应
func (d *PasswordPolicyDetector) checkPasswordResetResponse(response, path string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)

	// 检查密码策略相关的响应模式
	for _, pattern := range d.policyPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查密码重置相关的内容
	if strings.Contains(responseLower, "reset") ||
		strings.Contains(responseLower, "forgot") ||
		strings.Contains(responseLower, "recover") ||
		strings.Contains(responseLower, "change") ||
		strings.Contains(responseLower, "重置") ||
		strings.Contains(responseLower, "忘记") ||
		strings.Contains(responseLower, "恢复") ||
		strings.Contains(responseLower, "修改") {
		confidence += 0.4
	}

	// 检查重置表单或链接
	if strings.Contains(responseLower, "form") ||
		strings.Contains(responseLower, "input") ||
		strings.Contains(responseLower, "email") ||
		strings.Contains(responseLower, "token") ||
		strings.Contains(responseLower, "表单") ||
		strings.Contains(responseLower, "输入") ||
		strings.Contains(responseLower, "邮箱") ||
		strings.Contains(responseLower, "令牌") {
		confidence += 0.3
	}

	// 检查不安全的重置机制
	if strings.Contains(responseLower, "username") ||
		strings.Contains(responseLower, "question") ||
		strings.Contains(responseLower, "answer") ||
		strings.Contains(responseLower, "用户名") ||
		strings.Contains(responseLower, "问题") ||
		strings.Contains(responseLower, "答案") {
		confidence += 0.2
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// isWeakPassword 检查是否为弱密码
func (d *PasswordPolicyDetector) isWeakPassword(password string) bool {
	// 检查弱密码模式
	for _, pattern := range d.weakPasswordPatterns {
		if matched, _ := regexp.MatchString(pattern, password); matched {
			return true
		}
	}

	// 检查长度
	if len(password) < 8 {
		return true
	}

	// 检查是否只包含数字
	if matched, _ := regexp.MatchString("^\\d+$", password); matched {
		return true
	}

	// 检查是否只包含字母
	if matched, _ := regexp.MatchString("^[a-zA-Z]+$", password); matched {
		return true
	}

	return false
}

// extractPasswordPolicyEvidence 提取密码策略证据
func (d *PasswordPolicyDetector) extractPasswordPolicyEvidence(response, payload, vulnType string) string {
	// 限制证据长度
	maxLength := 1000
	evidence := fmt.Sprintf("密码策略证据 (%s):\n", vulnType)

	// 添加载荷信息
	evidence += fmt.Sprintf("载荷: %s\n", payload)
	evidence += fmt.Sprintf("漏洞类型: %s\n", vulnType)

	// 检查响应中的密码策略相关模式
	evidence += "\n密码策略模式匹配:\n"
	for _, pattern := range d.policyPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现密码策略模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的弱点模式
	evidence += "\n弱点模式匹配:\n"
	for _, pattern := range d.weaknessPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现弱点模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的安全模式
	evidence += "\n安全模式匹配:\n"
	for _, pattern := range d.securityPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现安全模式: %s\n", pattern.String())
		}
	}

	// 添加响应摘要
	if len(response) > 200 {
		evidence += fmt.Sprintf("\n响应摘要: %s...\n", response[:200])
	} else {
		evidence += fmt.Sprintf("\n响应内容: %s\n", response)
	}

	// 分析载荷类型
	evidence += d.analyzePasswordPolicyPayloadType(payload, vulnType)

	// 限制证据长度
	if len(evidence) > maxLength {
		evidence = evidence[:maxLength] + "..."
	}

	return evidence
}

// analyzePasswordPolicyPayloadType 分析密码策略载荷类型
func (d *PasswordPolicyDetector) analyzePasswordPolicyPayloadType(payload, vulnType string) string {
	analysis := "\n载荷分析:\n"
	payloadLower := strings.ToLower(payload)

	// 密码复杂度载荷分析
	if vulnType == "complexity" {
		analysis += "- 载荷类型: 密码复杂度测试\n"

		if d.isWeakPassword(payload) {
			analysis += "- 测试类型: 弱密码测试\n"
		}

		if len(payload) < 8 {
			analysis += "- 弱点: 密码长度过短\n"
		}

		if matched, _ := regexp.MatchString("^\\d+$", payload); matched {
			analysis += "- 弱点: 纯数字密码\n"
		}

		if matched, _ := regexp.MatchString("^[a-zA-Z]+$", payload); matched {
			analysis += "- 弱点: 纯字母密码\n"
		}

		if strings.Contains(payloadLower, "password") {
			analysis += "- 弱点: 包含'password'关键词\n"
		}
	}

	// 密码历史载荷分析
	if vulnType == "history" {
		analysis += "- 载荷类型: 密码历史测试\n"

		if strings.Contains(payloadLower, "old") {
			analysis += "- 测试类型: 旧密码重用测试\n"
		} else if strings.Contains(payloadLower, "previous") {
			analysis += "- 测试类型: 之前密码重用测试\n"
		} else if strings.Contains(payloadLower, "same") {
			analysis += "- 测试类型: 相同密码重用测试\n"
		}
	}

	// 密码过期载荷分析
	if vulnType == "expiry" {
		analysis += "- 载荷类型: 密码过期测试\n"

		if strings.Contains(payloadLower, "expired") {
			analysis += "- 测试类型: 过期密码测试\n"
		} else if strings.Contains(payloadLower, "old") {
			analysis += "- 测试类型: 旧密码测试\n"
		} else if strings.Contains(payloadLower, "outdated") {
			analysis += "- 测试类型: 过时密码测试\n"
		}
	}

	// 密码重置载荷分析
	if vulnType == "reset" {
		analysis += "- 载荷类型: 密码重置测试\n"

		if strings.Contains(payload, "reset") {
			analysis += "- 测试类型: 重置功能测试\n"
		} else if strings.Contains(payload, "forgot") {
			analysis += "- 测试类型: 忘记密码功能测试\n"
		} else if strings.Contains(payload, "recover") {
			analysis += "- 测试类型: 密码恢复功能测试\n"
		}
	}

	// 检查载荷特征
	if len(payload) == 0 {
		analysis += "- 载荷特征: 空密码\n"
	} else if len(payload) < 4 {
		analysis += "- 载荷特征: 极短密码\n"
	} else if len(payload) < 8 {
		analysis += "- 载荷特征: 短密码\n"
	} else if len(payload) > 20 {
		analysis += "- 载荷特征: 长密码\n"
	}

	if strings.Contains(payload, "123") {
		analysis += "- 载荷特征: 包含连续数字\n"
	}

	if strings.Contains(payload, "abc") {
		analysis += "- 载荷特征: 包含连续字母\n"
	}

	if strings.Contains(payload, "中") {
		analysis += "- 载荷特征: 包含中文字符\n"
	}

	return analysis
}
