# 插件化架构扩充实战示例

## 🎯 **实战案例：添加API速率限制检测器**

本文档通过一个完整的实战案例，展示如何为插件化架构添加新的检测器。

---

## 📋 **案例背景**

### **需求分析**
- **目标**：检测API端点是否缺乏速率限制保护
- **风险**：可能导致DoS攻击或资源滥用
- **检测方法**：发送连续请求，分析响应模式
- **适用范围**：API端点、REST服务、GraphQL等

### **技术要求**
- 实现`VulnerabilityDetector`接口
- 支持智能目标适用性检查
- 提供完整的测试覆盖
- 集成到现有扫描引擎

---

## 🛠️ **实现步骤详解**

### **步骤1：创建检测器主文件**

#### **文件结构**
```
scanner/internal/scanner/plugins/detectors/
├── api_rate_limiting_detector.go      # 主检测器实现
├── api_rate_limiting_detector_test.go # 测试文件
```

#### **核心结构定义**
```go
// APIRateLimitingDetector API速率限制检测器
type APIRateLimitingDetector struct {
    id          string
    name        string
    category    string
    severity    string
    description string
    enabled     bool
    config      *plugins.DetectorConfig

    // 检测配置
    requestCount    int           // 测试请求数量
    requestInterval time.Duration // 请求间隔
    httpClient      *http.Client
}
```

#### **关键特性**
- ✅ **完整接口实现**：实现所有`VulnerabilityDetector`接口方法
- ✅ **智能适用性检查**：基于URL模式和技术栈判断
- ✅ **可配置参数**：支持请求数量和间隔配置
- ✅ **错误处理**：优雅处理网络错误和超时

### **步骤2：实现核心检测逻辑**

#### **适用性检查**
```go
func (d *APIRateLimitingDetector) IsApplicable(target *plugins.ScanTarget) bool {
    // 1. 检查目标类型和协议
    if target.Type != "url" || (target.Protocol != "http" && target.Protocol != "https") {
        return false
    }

    // 2. 检查URL模式
    url := strings.ToLower(target.URL)
    apiIndicators := []string{
        "/api/", "/rest/", "/graphql", "/v1/", "/v2/", "/v3/",
        ".json", ".xml", "/service/", "/ws/", "/webservice/",
    }

    for _, indicator := range apiIndicators {
        if strings.Contains(url, indicator) {
            return true
        }
    }

    // 3. 检查技术栈
    for _, tech := range target.Technologies {
        if strings.Contains(strings.ToLower(tech.Name), "api") {
            return true
        }
    }

    return false
}
```

#### **速率限制测试**
```go
func (d *APIRateLimitingDetector) performRateLimitTest(ctx context.Context, target *plugins.ScanTarget) (*RateLimitTestResult, error) {
    result := &RateLimitTestResult{
        RateLimitHeaders: make(map[string]string),
    }

    // 发送连续请求测试速率限制
    for i := 0; i < d.requestCount; i++ {
        select {
        case <-ctx.Done():
            return result, ctx.Err()
        default:
        }

        // 发送HTTP请求
        req, err := http.NewRequestWithContext(ctx, "GET", target.URL, nil)
        if err != nil {
            continue
        }

        resp, err := d.httpClient.Do(req)
        if err != nil {
            continue
        }

        // 检查速率限制相关的HTTP头
        rateLimitHeaderNames := []string{
            "X-RateLimit-Limit", "X-RateLimit-Remaining", "X-RateLimit-Reset",
            "Retry-After", "X-Throttle-Limit", "X-Throttle-Remaining",
        }

        for _, headerName := range rateLimitHeaderNames {
            if value := resp.Header.Get(headerName); value != "" {
                result.RateLimitHeaders[headerName] = value
            }
        }

        // 统计响应状态
        if resp.StatusCode == 200 {
            result.SuccessfulReqs++
        } else if resp.StatusCode == 429 || resp.StatusCode == 503 {
            result.BlockedReqs++
        }

        resp.Body.Close()
        result.RequestsSent++

        // 请求间隔
        if i < d.requestCount-1 {
            time.Sleep(d.requestInterval)
        }
    }

    return result, nil
}
```

### **步骤3：创建完整测试套件**

#### **测试覆盖范围**
- ✅ **基本功能测试**：属性、配置、接口实现
- ✅ **适用性测试**：各种URL模式和技术栈
- ✅ **检测逻辑测试**：有/无速率限制的场景
- ✅ **边界条件测试**：超时、取消、错误处理
- ✅ **性能基准测试**：检测器执行性能

#### **测试服务器模拟**
```go
// 无速率限制的测试服务器
server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
    atomic.AddInt64(&requestCount, 1)
    w.WriteHeader(http.StatusOK)
    w.Write([]byte(`{"status": "ok"}`))
}))

// 有速率限制的测试服务器
server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
    count := atomic.AddInt64(&requestCount, 1)

    if count > 3 {
        w.Header().Set("X-RateLimit-Limit", "3")
        w.Header().Set("X-RateLimit-Remaining", "0")
        w.WriteHeader(http.StatusTooManyRequests)
        w.Write([]byte(`{"error": "Rate limit exceeded"}`))
    } else {
        w.WriteHeader(http.StatusOK)
        w.Write([]byte(`{"status": "ok"}`))
    }
}))
```

### **步骤4：注册到扫描引擎**

#### **在Web引擎中注册**
```go
// 在 web_engine.go 的 registerBuiltinDetectors 方法中添加
func (e *WebEngine) registerBuiltinDetectors() {
    // ... 现有检测器注册 ...

    // 注册API速率限制检测器 - 新增
    apiRateLimitingDetector := detectors.NewAPIRateLimitingDetector()
    if err := e.detectorManager.RegisterDetector(apiRateLimitingDetector); err != nil {
        logger.Errorf("注册API速率限制检测器失败: %v", err)
    }
}
```

#### **验证注册成功**
```go
// 检查检测器是否成功注册
stats := engine.detectorManager.GetStats()
fmt.Printf("已注册检测器数量: %d\n", stats.TotalDetectors)

// 获取特定检测器
detector, err := engine.detectorManager.GetDetector("api-rate-limiting-detector")
if err == nil {
    fmt.Printf("检测器注册成功: %s\n", detector.GetName())
}
```

<function_calls>
<invoke name="view">
<parameter name="path">scanner/internal/scanner/plugins/detectors/api_rate_limiting_detector.go
