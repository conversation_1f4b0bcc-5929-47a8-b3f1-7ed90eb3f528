package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectCRLFInjection 检测CRLF注入
func (d *HTTPHeaderInjectionDetector) detectCRLFInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试CRLF注入载荷
	for _, payload := range d.crlfPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送CRLF注入请求
		resp, err := d.sendHTTPRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查CRLF注入响应
		confidence := d.checkCRLFInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("CRLF注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "crlf-injection",
				Description: fmt.Sprintf("发现CRLF注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractHTTPEvidence(resp, "crlf-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectResponseSplitting 检测HTTP响应分割
func (d *HTTPHeaderInjectionDetector) detectResponseSplitting(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试响应分割载荷
	for _, payload := range d.responseSpittingPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送响应分割请求
		resp, err := d.sendHTTPRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查响应分割响应
		confidence := d.checkResponseSplittingResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("HTTP响应分割: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "response-splitting",
				Description: fmt.Sprintf("发现HTTP响应分割: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractHTTPEvidence(resp, "response-splitting"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectHeaderInjection 检测头部注入
func (d *HTTPHeaderInjectionDetector) detectHeaderInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试头部注入载荷
	for _, payload := range d.headerInjectionPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送头部注入请求
		resp, err := d.sendHTTPRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查头部注入响应
		confidence := d.checkHeaderInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("头部注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "header-injection",
				Description: fmt.Sprintf("发现头部注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractHTTPEvidence(resp, "header-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCachePoisoning 检测缓存投毒
func (d *HTTPHeaderInjectionDetector) detectCachePoisoning(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试缓存投毒载荷
	for _, payload := range d.cachePoisoningPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送缓存投毒请求
		resp, err := d.sendHTTPRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查缓存投毒响应
		confidence := d.checkCachePoisoningResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("缓存投毒: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "cache-poisoning",
				Description: fmt.Sprintf("发现缓存投毒: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractHTTPEvidence(resp, "cache-poisoning"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendHTTPRequest 发送HTTP头部注入请求
func (d *HTTPHeaderInjectionDetector) sendHTTPRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendHTTPGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST参数注入
	postResp, err := d.sendHTTPPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 尝试头部载荷注入
	headerResp, err := d.sendHTTPHeaderRequest(ctx, targetURL, payload)
	if err == nil && headerResp != "" {
		return headerResp, nil
	}

	// 返回GET响应（即使有错误）
	if getResp != "" {
		return getResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendHTTPGETRequest 发送HTTP头部注入GET请求
func (d *HTTPHeaderInjectionDetector) sendHTTPGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendHTTPPOSTRequest 发送HTTP头部注入POST请求
func (d *HTTPHeaderInjectionDetector) sendHTTPPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造POST数据
	postData := url.Values{}

	for _, param := range d.testParameters {
		postData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendHTTPHeaderRequest 发送HTTP头部载荷请求
func (d *HTTPHeaderInjectionDetector) sendHTTPHeaderRequest(ctx context.Context, targetURL, payload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置基础请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Connection", "keep-alive")

	// 尝试在各种头部中注入载荷
	testHeaders := []string{
		"X-Forwarded-For", "X-Real-IP", "X-Originating-IP", "X-Remote-IP",
		"X-Client-IP", "X-Forwarded-Host", "X-Original-Host", "Referer",
		"User-Agent", "X-Requested-With", "X-Custom-Header",
	}

	for _, header := range testHeaders {
		req.Header.Set(header, payload)
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkCRLFInjectionResponse 检查CRLF注入响应
func (d *HTTPHeaderInjectionDetector) checkCRLFInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 302") {
		confidence += 0.4 // 重定向响应可能表示注入成功
	}

	// 检查CRLF模式匹配
	for _, pattern := range d.crlfPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查CRLF特定指示器
	crlfIndicators := []string{
		"set-cookie:", "location:", "x-injected:", "content-type:",
		"\r\n", "%0d%0a", "%250d%250a", "crlf", "注入", "恶意",
	}

	for _, indicator := range crlfIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4 // CRLF特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkResponseSplittingResponse 检查HTTP响应分割响应
func (d *HTTPHeaderInjectionDetector) checkResponseSplittingResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 302") {
		confidence += 0.4 // 重定向响应可能表示注入成功
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应分割特定指示器
	splittingIndicators := []string{
		"http/1.1 200 ok", "http/1.1 302 found", "<script>alert",
		"<html><body>", "content-type: text/html", "content-length:",
		"响应分割", "注入成功", "恶意内容",
	}

	for _, indicator := range splittingIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.6 // 响应分割特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkHeaderInjectionResponse 检查头部注入响应
func (d *HTTPHeaderInjectionDetector) checkHeaderInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 403") {
		confidence += 0.3 // 禁止响应可能表示注入被检测
	}

	// 检查头部模式匹配
	for _, pattern := range d.headerPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查头部注入特定指示器
	headerIndicators := []string{
		"x-forwarded-for:", "x-real-ip:", "x-originating-ip:", "x-remote-ip:",
		"x-client-ip:", "authorization:", "x-api-key:", "x-user:",
		"cache-control:", "pragma:", "expires:", "location:",
		"头部注入", "认证绕过", "缓存控制", "重定向注入",
	}

	for _, indicator := range headerIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4 // 头部注入特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkCachePoisoningResponse 检查缓存投毒响应
func (d *HTTPHeaderInjectionDetector) checkCachePoisoningResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 302") {
		confidence += 0.3 // 重定向响应可能表示注入成功
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查缓存投毒特定指示器
	cacheIndicators := []string{
		"x-forwarded-host:", "host:", "x-original-host:", "x-host:",
		"x-forwarded-proto:", "x-forwarded-scheme:", "vary:",
		"x-cache:", "cache-control:", "expires:", "pragma:",
		"缓存投毒", "代理投毒", "CDN投毒", "缓存键",
	}

	for _, indicator := range cacheIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4 // 缓存投毒特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// extractHTTPEvidence 提取HTTP头部注入证据
func (d *HTTPHeaderInjectionDetector) extractHTTPEvidence(response, injectionType string) string {
	// 限制证据长度
	maxLength := 500
	if len(response) > maxLength {
		response = response[:maxLength] + "..."
	}

	// 根据注入类型提取相关证据
	switch injectionType {
	case "crlf-injection":
		return d.extractCRLFEvidence(response)
	case "response-splitting":
		return d.extractResponseSplittingEvidence(response)
	case "header-injection":
		return d.extractHeaderInjectionEvidence(response)
	case "cache-poisoning":
		return d.extractCachePoisoningEvidence(response)
	default:
		return response
	}
}

// extractCRLFEvidence 提取CRLF证据
func (d *HTTPHeaderInjectionDetector) extractCRLFEvidence(response string) string {
	evidence := "CRLF注入证据:\n"

	// 查找CRLF特定内容
	crlfIndicators := []string{
		"set-cookie:", "location:", "x-injected:", "content-type:",
		"\r\n", "%0d%0a", "%250d%250a", "crlf",
	}

	for _, indicator := range crlfIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现CRLF指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractResponseSplittingEvidence 提取响应分割证据
func (d *HTTPHeaderInjectionDetector) extractResponseSplittingEvidence(response string) string {
	evidence := "HTTP响应分割证据:\n"

	// 查找响应分割特定内容
	splittingIndicators := []string{
		"http/1.1 200 ok", "http/1.1 302 found", "<script>alert",
		"<html><body>", "content-type: text/html", "content-length:",
	}

	for _, indicator := range splittingIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现响应分割指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractHeaderInjectionEvidence 提取头部注入证据
func (d *HTTPHeaderInjectionDetector) extractHeaderInjectionEvidence(response string) string {
	evidence := "头部注入证据:\n"

	// 查找头部注入特定内容
	headerIndicators := []string{
		"x-forwarded-for:", "x-real-ip:", "x-originating-ip:", "x-remote-ip:",
		"x-client-ip:", "authorization:", "x-api-key:", "x-user:",
		"cache-control:", "pragma:", "expires:", "location:",
	}

	for _, indicator := range headerIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现头部注入指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractCachePoisoningEvidence 提取缓存投毒证据
func (d *HTTPHeaderInjectionDetector) extractCachePoisoningEvidence(response string) string {
	evidence := "缓存投毒证据:\n"

	// 查找缓存投毒特定内容
	cacheIndicators := []string{
		"x-forwarded-host:", "host:", "x-original-host:", "x-host:",
		"x-forwarded-proto:", "x-forwarded-scheme:", "vary:",
		"x-cache:", "cache-control:", "expires:", "pragma:",
	}

	for _, indicator := range cacheIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现缓存投毒指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}
