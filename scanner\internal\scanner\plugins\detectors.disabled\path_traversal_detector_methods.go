package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectBasicTraversal 检测基础路径遍历
func (d *PathTraversalDetector) detectBasicTraversal(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试基础路径遍历载荷
	for i, payload := range d.traversalPayloads {
		if i >= 10 { // 限制载荷数量避免过多请求
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送路径遍历请求
		resp, err := d.sendTraversalRequest(ctx, target.URL, "file", payload+"etc/passwd")
		if err != nil {
			continue
		}

		// 检查路径遍历响应
		confidence := d.checkTraversalResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload + "etc/passwd"
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "basic-traversal",
				Description: fmt.Sprintf("基础路径遍历载荷触发了文件访问响应 (置信度: %.2f)", confidence),
				Content:     d.extractTraversalEvidence(resp, payload),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectFileInclusion 检测文件包含
func (d *PathTraversalDetector) detectFileInclusion(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试文件包含载荷
	for i, payload := range d.fileInclusionPayloads {
		if i >= 8 { // 限制载荷数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送文件包含请求
		resp, err := d.sendTraversalRequest(ctx, target.URL, "include", payload)
		if err != nil {
			continue
		}

		// 检查文件包含响应
		confidence := d.checkFileInclusionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "file-inclusion",
				Description: fmt.Sprintf("文件包含载荷触发了文件内容响应 (置信度: %.2f)", confidence),
				Content:     d.extractFileInclusionEvidence(resp, payload),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPathBypass 检测路径绕过
func (d *PathTraversalDetector) detectPathBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试路径绕过载荷
	for i, payload := range d.bypassPayloads {
		if i >= 6 { // 限制载荷数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送路径绕过请求
		resp, err := d.sendTraversalRequest(ctx, target.URL, "path", payload)
		if err != nil {
			continue
		}

		// 检查路径绕过响应
		confidence := d.checkPathBypassResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "path-bypass",
				Description: fmt.Sprintf("路径绕过载荷触发了绕过响应 (置信度: %.2f)", confidence),
				Content:     d.extractPathBypassEvidence(resp, payload),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendTraversalRequest 发送路径遍历请求
func (d *PathTraversalDetector) sendTraversalRequest(ctx context.Context, targetURL, paramName, payload string) (string, error) {
	// 构造请求URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加参数
	query := parsedURL.Query()
	query.Set(paramName, payload)
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkTraversalResponse 检查路径遍历响应
func (d *PathTraversalDetector) checkTraversalResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查文件内容指示器
	for _, indicator := range d.fileIndicators {
		if strings.Contains(response, strings.ToLower(indicator)) {
			confidence += 0.4
			// 特定指示器的额外加分
			if strings.Contains(indicator, "root:x:") || strings.Contains(indicator, "daemon:x:") {
				confidence += 0.3
			}
			if strings.Contains(indicator, "[fonts]") || strings.Contains(indicator, "[boot loader]") {
				confidence += 0.3
			}
			break
		}
	}

	// 检查成功模式
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查错误模式
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查载荷特定的响应
	if strings.Contains(payload, "etc/passwd") && 
	   (strings.Contains(response, "root:") || strings.Contains(response, "daemon:")) {
		confidence += 0.4
	}

	if strings.Contains(payload, "win.ini") && 
	   (strings.Contains(response, "[fonts]") || strings.Contains(response, "[extensions]")) {
		confidence += 0.4
	}

	if strings.Contains(payload, "hosts") && 
	   (strings.Contains(response, "localhost") || strings.Contains(response, "127.0.0.1")) {
		confidence += 0.4
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkFileInclusionResponse 检查文件包含响应
func (d *PathTraversalDetector) checkFileInclusionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查文件内容指示器
	for _, indicator := range d.fileIndicators {
		if strings.Contains(response, strings.ToLower(indicator)) {
			confidence += 0.5
			break
		}
	}

	// 检查成功模式
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查特定文件的内容
	if strings.Contains(payload, "passwd") && strings.Contains(response, "root:") {
		confidence += 0.4
	}
	if strings.Contains(payload, "config") && strings.Contains(response, "database") {
		confidence += 0.3
	}
	if strings.Contains(payload, ".env") && strings.Contains(response, "secret") {
		confidence += 0.3
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkPathBypassResponse 检查路径绕过响应
func (d *PathTraversalDetector) checkPathBypassResponse(response, payload string) float64 {
	confidence := 0.0

	// 检查HTTP状态码（从响应中提取）
	if strings.Contains(response, "Status: 200") {
		confidence += 0.3
	}
	if strings.Contains(response, "Status: 403") || strings.Contains(response, "Status: 401") {
		confidence += 0.1 // 可能被阻止，但仍有一定价值
	}

	// 检查是否包含管理页面内容
	responseLower := strings.ToLower(response)
	adminIndicators := []string{
		"admin", "administrator", "management", "control panel",
		"dashboard", "login", "authentication", "管理", "登录",
	}

	for _, indicator := range adminIndicators {
		if strings.Contains(responseLower, indicator) {
			confidence += 0.2
			break
		}
	}

	// 检查错误模式
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(responseLower) {
			confidence += 0.1
			break
		}
	}

	return confidence
}

// extractTraversalEvidence 提取路径遍历证据
func (d *PathTraversalDetector) extractTraversalEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 5 {
			lineLower := strings.ToLower(line)
			
			// 查找包含文件内容的行
			for _, indicator := range d.fileIndicators {
				if strings.Contains(lineLower, strings.ToLower(indicator)) {
					// 返回相关行及其上下文
					start := i
					if start > 0 {
						start--
					}
					end := i + 2
					if end >= len(lines) {
						end = len(lines)
					}
					return strings.Join(lines[start:end], "\n")
				}
			}
		}
	}

	// 如果没有找到特定证据，返回响应的前500个字符
	if len(response) > 500 {
		return response[:500] + "..."
	}
	return response
}

// extractFileInclusionEvidence 提取文件包含证据
func (d *PathTraversalDetector) extractFileInclusionEvidence(response, payload string) string {
	return d.extractTraversalEvidence(response, payload)
}

// extractPathBypassEvidence 提取路径绕过证据
func (d *PathTraversalDetector) extractPathBypassEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")
	
	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}
	
	// 如果没有找到状态码，返回前200个字符
	if len(response) > 200 {
		return response[:200] + "..."
	}
	return response
}
