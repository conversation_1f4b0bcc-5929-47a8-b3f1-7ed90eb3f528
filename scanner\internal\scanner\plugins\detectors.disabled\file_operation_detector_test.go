package detectors

import (
	"testing"
	"time"

	"scanner/internal/scanner/plugins"
)

// TestFileOperationDetectorBasicFunctionality 测试文件操作检测器基本功能
func TestFileOperationDetectorBasicFunctionality(t *testing.T) {
	detector := NewFileOperationDetector()

	// 测试基本信息
	if detector.GetID() != "file-operation-comprehensive" {
		t.<PERSON><PERSON>("Expected ID 'file-operation-comprehensive', got '%s'", detector.GetID())
	}

	if detector.GetName() != "文件操作安全检测器" {
		t.<PERSON><PERSON><PERSON>("Expected name '文件操作安全检测器', got '%s'", detector.GetName())
	}

	if detector.GetCategory() != "web" {
		t.<PERSON><PERSON>("Expected category 'web', got '%s'", detector.GetCategory())
	}

	if detector.GetSeverity() != "high" {
		t.<PERSON><PERSON>rf("Expected severity 'high', got '%s'", detector.GetSeverity())
	}

	if !detector.IsEnabled() {
		t.<PERSON><PERSON>r("Expected detector to be enabled by default")
	}

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	expectedTypes := []string{"http", "https"}
	if len(targetTypes) != len(expectedTypes) {
		t.Errorf("Expected %d target types, got %d", len(expectedTypes), len(targetTypes))
	}

	// 测试端口
	ports := detector.GetRequiredPorts()
	if len(ports) == 0 {
		t.Error("Expected some required ports")
	}

	// 测试服务
	services := detector.GetRequiredServices()
	expectedServices := []string{"http", "https", "web", "api"}
	if len(services) != len(expectedServices) {
		t.Errorf("Expected %d services, got %d", len(expectedServices), len(services))
	}
}

// TestFileOperationDetectorApplicability 测试文件操作检测器适用性
func TestFileOperationDetectorApplicability(t *testing.T) {
	detector := NewFileOperationDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "HTTP URL目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有文件字段的表单目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Forms: []plugins.FormInfo{
					{
						Action: "/upload",
						Method: "POST",
						Fields: map[string]string{"file": "file", "upload": "file"},
					},
				},
			},
			expected: true,
		},
		{
			name: "有文件操作技术栈的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Technologies: []plugins.TechnologyInfo{
					{Name: "Apache", Version: "2.4", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有文件操作头部的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Headers: map[string]string{
					"Content-Type": "multipart/form-data",
				},
			},
			expected: true,
		},
		{
			name: "有文件操作链接的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/upload", Text: "File Upload"},
				},
			},
			expected: true,
		},
		{
			name: "非Web目标",
			target: &plugins.ScanTarget{
				Type:     "ip",
				Protocol: "tcp",
				Port:     22,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.IsApplicable(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestFileOperationDetectorConfiguration 测试文件操作检测器配置
func TestFileOperationDetectorConfiguration(t *testing.T) {
	detector := NewFileOperationDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	if config.Timeout != 30*time.Second {
		t.Errorf("Expected timeout 30s, got %v", config.Timeout)
	}

	if config.MaxRetries != 2 {
		t.Errorf("Expected max retries 2, got %d", config.MaxRetries)
	}

	if config.Concurrency != 5 {
		t.Errorf("Expected concurrency 5, got %d", config.Concurrency)
	}

	if config.RateLimit != 5 {
		t.Errorf("Expected rate limit 5, got %d", config.RateLimit)
	}

	if !config.FollowRedirects {
		t.Error("Expected FollowRedirects to be true")
	}

	if config.VerifySSL {
		t.Error("Expected VerifySSL to be false")
	}

	if config.MaxResponseSize != 5*1024*1024 {
		t.Errorf("Expected max response size 5MB, got %d", config.MaxResponseSize)
	}

	// 测试配置更新
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         45 * time.Second,
		MaxRetries:      3,
		Concurrency:     8,
		RateLimit:       8,
		FollowRedirects: false,
		VerifySSL:       true,
		MaxResponseSize: 10 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	if err != nil {
		t.Errorf("Failed to set configuration: %v", err)
	}

	updatedConfig := detector.GetConfiguration()
	if updatedConfig.Timeout != 45*time.Second {
		t.Errorf("Expected updated timeout 45s, got %v", updatedConfig.Timeout)
	}
}

// TestFileOperationDetectorFileUploadPaths 测试文件上传路径
func TestFileOperationDetectorFileUploadPaths(t *testing.T) {
	detector := NewFileOperationDetector()

	if len(detector.fileUploadPaths) == 0 {
		t.Error("Expected some file upload paths")
	}

	// 检查是否包含关键的文件上传路径
	expectedPaths := []string{
		"/upload",
		"/file/upload",
		"/fileupload",
		"/file-upload",
		"/upload/file",
		"/attachment/upload",
		"/image/upload",
		"/上传",
		"/文件上传",
	}

	for _, expected := range expectedPaths {
		found := false
		for _, path := range detector.fileUploadPaths {
			if path == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find file upload path '%s'", expected)
		}
	}
}

// TestFileOperationDetectorFileDownloadPaths 测试文件下载路径
func TestFileOperationDetectorFileDownloadPaths(t *testing.T) {
	detector := NewFileOperationDetector()

	if len(detector.fileDownloadPaths) == 0 {
		t.Error("Expected some file download paths")
	}

	// 检查是否包含关键的文件下载路径
	expectedPaths := []string{
		"/download",
		"/file/download",
		"/filedownload",
		"/file-download",
		"/download/file",
		"/attachment/download",
		"/image/download",
		"/下载",
		"/文件下载",
	}

	for _, expected := range expectedPaths {
		found := false
		for _, path := range detector.fileDownloadPaths {
			if path == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find file download path '%s'", expected)
		}
	}
}

// TestFileOperationDetectorFileUploadPayloads 测试文件上传载荷
func TestFileOperationDetectorFileUploadPayloads(t *testing.T) {
	detector := NewFileOperationDetector()

	if len(detector.fileUploadPayloads) == 0 {
		t.Error("Expected some file upload payloads")
	}

	// 检查是否包含关键的文件上传载荷
	expectedPayloads := []string{
		"test.txt",
		"test.php",
		"test.jsp",
		"shell.php",
		"webshell.jsp",
		"backdoor.asp",
		"测试.txt",
		"测试.php",
		"木马.exe",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.fileUploadPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find file upload payload '%s'", expected)
		}
	}
}

// TestFileOperationDetectorFileTraversalPayloads 测试文件遍历载荷
func TestFileOperationDetectorFileTraversalPayloads(t *testing.T) {
	detector := NewFileOperationDetector()

	if len(detector.fileTraversalPayloads) == 0 {
		t.Error("Expected some file traversal payloads")
	}

	// 检查是否包含关键的文件遍历载荷
	expectedPayloads := []string{
		"../",
		"..\\",
		"....//",
		"....\\\\",
		"%2e%2e%2f",
		"%2e%2e%5c",
		"../../../",
		"..\\..\\..\\",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.fileTraversalPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find file traversal payload '%s'", expected)
		}
	}
}

// TestFileOperationDetectorPatterns 测试检测模式
func TestFileOperationDetectorPatterns(t *testing.T) {
	detector := NewFileOperationDetector()

	// 测试文件操作模式
	if len(detector.fileOperationPatterns) == 0 {
		t.Error("Expected some file operation patterns")
	}

	// 测试漏洞模式
	if len(detector.vulnerabilityPatterns) == 0 {
		t.Error("Expected some vulnerability patterns")
	}

	// 测试安全模式
	if len(detector.securityPatterns) == 0 {
		t.Error("Expected some security patterns")
	}
}

// TestFileOperationDetectorFileOperationFeatures 测试文件操作功能检查
func TestFileOperationDetectorFileOperationFeatures(t *testing.T) {
	detector := NewFileOperationDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "有文件操作头部的目标",
			target: &plugins.ScanTarget{
				Headers: map[string]string{
					"Content-Type": "multipart/form-data",
				},
			},
			expected: true,
		},
		{
			name: "有文件操作技术栈的目标",
			target: &plugins.ScanTarget{
				Technologies: []plugins.TechnologyInfo{
					{Name: "Apache", Version: "2.4", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有文件操作链接的目标",
			target: &plugins.ScanTarget{
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/upload", Text: "File Upload"},
				},
			},
			expected: true,
		},
		{
			name: "普通目标",
			target: &plugins.ScanTarget{
				URL: "https://example.com",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.hasFileOperationFeatures(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestFileOperationDetectorRiskScore 测试风险评分
func TestFileOperationDetectorRiskScore(t *testing.T) {
	detector := NewFileOperationDetector()

	testCases := []struct {
		confidence float64
		expected   float64
	}{
		{0.0, 0.0},
		{0.5, 4.0},
		{0.8, 6.4},
		{1.0, 8.0},
	}

	for _, tc := range testCases {
		score := detector.calculateRiskScore(tc.confidence)
		// 使用浮点数比较的容差
		if score < tc.expected-0.01 || score > tc.expected+0.01 {
			t.Errorf("Expected risk score %.2f for confidence %.1f, got %.2f", tc.expected, tc.confidence, score)
		}
	}
}

// TestFileOperationDetectorLifecycle 测试检测器生命周期
func TestFileOperationDetectorLifecycle(t *testing.T) {
	detector := NewFileOperationDetector()

	// 测试初始化
	err := detector.Initialize()
	if err != nil {
		t.Errorf("Failed to initialize detector: %v", err)
	}

	// 测试验证
	err = detector.Validate()
	if err != nil {
		t.Errorf("Detector validation failed: %v", err)
	}

	// 测试启用/禁用
	detector.SetEnabled(false)
	if detector.IsEnabled() {
		t.Error("Expected detector to be disabled")
	}

	detector.SetEnabled(true)
	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled")
	}

	// 测试清理
	err = detector.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup detector: %v", err)
	}
}
