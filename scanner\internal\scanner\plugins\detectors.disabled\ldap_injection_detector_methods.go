package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectAuthBypass 检测认证绕过
func (d *LDAPInjectionDetector) detectAuthBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试认证绕过载荷
	for _, payload := range d.authBypassPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送认证绕过请求
		resp, err := d.sendLDAPRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查认证绕过响应
		confidence := d.checkAuthBypassResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("LDAP认证绕过: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ldap-auth-bypass",
				Description: fmt.Sprintf("发现LDAP认证绕过: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractLDAPEvidence(resp, "ldap-auth-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectInfoLeakage 检测信息泄露
func (d *LDAPInjectionDetector) detectInfoLeakage(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试信息泄露载荷
	for _, payload := range d.infoLeakagePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送信息泄露请求
		resp, err := d.sendLDAPRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查信息泄露响应
		confidence := d.checkInfoLeakageResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("LDAP信息泄露: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ldap-info-leakage",
				Description: fmt.Sprintf("发现LDAP信息泄露: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractLDAPEvidence(resp, "ldap-info-leakage"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 250)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectBlindInjection 检测盲注
func (d *LDAPInjectionDetector) detectBlindInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试盲注载荷
	for _, payload := range d.blindPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送盲注请求
		resp, err := d.sendLDAPRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查盲注响应
		confidence := d.checkBlindInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("LDAP盲注: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ldap-blind-injection",
				Description: fmt.Sprintf("发现LDAP盲注: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractLDAPEvidence(resp, "ldap-blind-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectErrorInjection 检测错误注入
func (d *LDAPInjectionDetector) detectErrorInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试错误注入载荷
	for _, payload := range d.errorPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送错误注入请求
		resp, err := d.sendLDAPRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查错误注入响应
		confidence := d.checkErrorInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("LDAP错误注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ldap-error-injection",
				Description: fmt.Sprintf("发现LDAP错误注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractLDAPEvidence(resp, "ldap-error-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendLDAPRequest 发送LDAP请求
func (d *LDAPInjectionDetector) sendLDAPRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendLDAPGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST参数注入
	postResp, err := d.sendLDAPPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 返回GET响应（即使有错误）
	if getResp != "" {
		return getResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendLDAPGETRequest 发送LDAP GET请求
func (d *LDAPInjectionDetector) sendLDAPGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendLDAPPOSTRequest 发送LDAP POST请求
func (d *LDAPInjectionDetector) sendLDAPPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造POST数据
	postData := url.Values{}

	for _, param := range d.testParameters {
		postData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkAuthBypassResponse 检查认证绕过响应
func (d *LDAPInjectionDetector) checkAuthBypassResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	} else if strings.Contains(response, "status: 302") {
		confidence += 0.4 // 重定向可能表示认证成功
	}

	// 检查认证绕过成功指示器
	authBypassIndicators := []string{
		"login success", "authentication success", "bind success",
		"access granted", "permission granted", "authorized",
		"welcome", "dashboard", "profile", "account page",
		"user information", "user data", "user list",
		"登录成功", "认证成功", "绑定成功", "访问授权",
		"权限验证", "欢迎", "仪表板", "用户信息",
	}

	for _, indicator := range authBypassIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5
			break
		}
	}

	// 检查LDAP特征模式匹配
	for _, pattern := range d.ldapPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	// 根据载荷类型调整置信度
	if strings.Contains(payload, "admin") || strings.Contains(payload, "administrator") {
		if confidence > 0.4 {
			confidence += 0.3 // 管理员相关载荷更危险
		}
	} else if payload == "*" || payload == "*)(&" {
		if confidence > 0.3 {
			confidence += 0.2 // 通配符载荷更危险
		}
	}

	return confidence
}

// checkInfoLeakageResponse 检查信息泄露响应
func (d *LDAPInjectionDetector) checkInfoLeakageResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查LDAP信息泄露指示器
	infoLeakageIndicators := []string{
		"uid=", "cn=", "mail=", "sn=", "givenname=",
		"distinguishedname=", "commonname=", "surname=",
		"telephonenumber=", "mobile=", "homephone=",
		"ou=", "o=", "dc=", "department=", "title=",
		"samaccountname=", "userprincipalname=", "displayname=",
		"objectclass=", "objectcategory=", "memberof=",
		"用户名=", "姓名=", "邮箱=", "电话=", "部门=",
	}

	for _, indicator := range infoLeakageIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
			break
		}
	}

	// 检查LDAP特征模式匹配
	for _, pattern := range d.ldapPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查搜索结果指示器
	searchResultIndicators := []string{
		"search result", "entries found", "records found",
		"total entries", "result count", "match found",
		"entries returned", "results", "directory listing",
		"搜索结果", "查询结果", "条目", "记录", "匹配",
	}

	for _, indicator := range searchResultIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.3
			break
		}
	}

	return confidence
}

// checkBlindInjectionResponse 检查盲注响应
func (d *LDAPInjectionDetector) checkBlindInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2
	}

	// 检查LDAP特征模式匹配
	for _, pattern := range d.ldapPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查盲注特定指示器
	blindIndicators := []string{
		"true", "false", "yes", "no", "1", "0",
		"found", "not found", "exists", "not exists",
		"match", "no match", "valid", "invalid",
		"真", "假", "是", "否", "存在", "不存在",
		"匹配", "不匹配", "有效", "无效",
	}

	for _, indicator := range blindIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.3
			break
		}
	}

	// 根据载荷类型调整置信度
	if strings.Contains(payload, "objectclass=*") || strings.Contains(payload, "objectcategory=*") {
		if confidence > 0.3 {
			confidence += 0.2 // 对象类查询更可能成功
		}
	} else if strings.Contains(payload, "nonexistent") {
		if confidence < 0.3 {
			confidence += 0.1 // 不存在的查询应该返回不同结果
		}
	}

	return confidence
}

// checkErrorInjectionResponse 检查错误注入响应
func (d *LDAPInjectionDetector) checkErrorInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码（错误状态码可能表示LDAP错误）
	if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示LDAP错误
	} else if strings.Contains(response, "status: 400") {
		confidence += 0.3 // 请求错误可能表示语法错误
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查LDAP特征模式匹配
	for _, pattern := range d.ldapPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查LDAP特定错误
	ldapErrors := []string{
		"ldap error", "ldap exception", "invalid ldap",
		"malformed ldap", "bad ldap", "ldap syntax",
		"ldap filter", "ldap query", "ldap bind",
		"ldap search", "ldap operation", "invalid dn",
		"invalid filter", "no such object", "no such attribute",
		"insufficient access", "access denied", "bind failed",
		"authentication failed", "credentials invalid",
		"ldap错误", "ldap异常", "无效ldap", "语法错误",
		"过滤器错误", "查询错误", "绑定失败", "认证失败",
	}

	for _, error := range ldapErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // LDAP特定错误的强指示器
			break
		}
	}

	return confidence
}

// extractLDAPEvidence 提取LDAP证据
func (d *LDAPInjectionDetector) extractLDAPEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var ldapLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "ldap-auth-bypass":
			if strings.Contains(lineLower, "login") ||
				strings.Contains(lineLower, "auth") ||
				strings.Contains(lineLower, "bind") ||
				strings.Contains(lineLower, "success") ||
				strings.Contains(lineLower, "granted") ||
				strings.Contains(lineLower, "welcome") ||
				strings.Contains(lineLower, "dashboard") ||
				strings.Contains(lineLower, "profile") ||
				strings.Contains(lineLower, "account") ||
				strings.Contains(lineLower, "user") ||
				strings.Contains(lineLower, "登录") ||
				strings.Contains(lineLower, "认证") ||
				strings.Contains(lineLower, "绑定") ||
				strings.Contains(lineLower, "成功") ||
				strings.Contains(lineLower, "授权") ||
				strings.Contains(lineLower, "欢迎") ||
				strings.Contains(lineLower, "用户") {
				ldapLines = append(ldapLines, line)
			}
		case "ldap-info-leakage":
			if strings.Contains(lineLower, "uid=") ||
				strings.Contains(lineLower, "cn=") ||
				strings.Contains(lineLower, "mail=") ||
				strings.Contains(lineLower, "sn=") ||
				strings.Contains(lineLower, "ou=") ||
				strings.Contains(lineLower, "dc=") ||
				strings.Contains(lineLower, "distinguishedname") ||
				strings.Contains(lineLower, "objectclass") ||
				strings.Contains(lineLower, "memberof") ||
				strings.Contains(lineLower, "search") ||
				strings.Contains(lineLower, "result") ||
				strings.Contains(lineLower, "entries") ||
				strings.Contains(lineLower, "用户名=") ||
				strings.Contains(lineLower, "姓名=") ||
				strings.Contains(lineLower, "邮箱=") ||
				strings.Contains(lineLower, "部门=") ||
				strings.Contains(lineLower, "搜索") ||
				strings.Contains(lineLower, "结果") ||
				strings.Contains(lineLower, "条目") {
				ldapLines = append(ldapLines, line)
			}
		case "ldap-blind-injection":
			if strings.Contains(lineLower, "true") ||
				strings.Contains(lineLower, "false") ||
				strings.Contains(lineLower, "yes") ||
				strings.Contains(lineLower, "no") ||
				strings.Contains(lineLower, "found") ||
				strings.Contains(lineLower, "exists") ||
				strings.Contains(lineLower, "match") ||
				strings.Contains(lineLower, "valid") ||
				strings.Contains(lineLower, "objectclass") ||
				strings.Contains(lineLower, "objectcategory") ||
				strings.Contains(lineLower, "真") ||
				strings.Contains(lineLower, "假") ||
				strings.Contains(lineLower, "是") ||
				strings.Contains(lineLower, "否") ||
				strings.Contains(lineLower, "存在") ||
				strings.Contains(lineLower, "匹配") ||
				strings.Contains(lineLower, "有效") {
				ldapLines = append(ldapLines, line)
			}
		case "ldap-error-injection":
			if strings.Contains(lineLower, "error") ||
				strings.Contains(lineLower, "exception") ||
				strings.Contains(lineLower, "invalid") ||
				strings.Contains(lineLower, "ldap") ||
				strings.Contains(lineLower, "syntax") ||
				strings.Contains(lineLower, "filter") ||
				strings.Contains(lineLower, "bind") ||
				strings.Contains(lineLower, "search") ||
				strings.Contains(lineLower, "operation") ||
				strings.Contains(lineLower, "access") ||
				strings.Contains(lineLower, "denied") ||
				strings.Contains(lineLower, "failed") ||
				strings.Contains(lineLower, "错误") ||
				strings.Contains(lineLower, "异常") ||
				strings.Contains(lineLower, "无效") ||
				strings.Contains(lineLower, "语法") ||
				strings.Contains(lineLower, "过滤器") ||
				strings.Contains(lineLower, "绑定") ||
				strings.Contains(lineLower, "搜索") ||
				strings.Contains(lineLower, "操作") ||
				strings.Contains(lineLower, "访问") ||
				strings.Contains(lineLower, "拒绝") ||
				strings.Contains(lineLower, "失败") {
				ldapLines = append(ldapLines, line)
			}
		default:
			if strings.Contains(lineLower, "ldap") ||
				strings.Contains(lineLower, "directory") ||
				strings.Contains(lineLower, "auth") ||
				strings.Contains(lineLower, "bind") ||
				strings.Contains(lineLower, "search") ||
				strings.Contains(lineLower, "filter") ||
				strings.Contains(lineLower, "uid") ||
				strings.Contains(lineLower, "cn") ||
				strings.Contains(lineLower, "mail") ||
				strings.Contains(lineLower, "ou") ||
				strings.Contains(lineLower, "dc") ||
				strings.Contains(lineLower, "目录") ||
				strings.Contains(lineLower, "认证") ||
				strings.Contains(lineLower, "绑定") ||
				strings.Contains(lineLower, "搜索") ||
				strings.Contains(lineLower, "过滤器") ||
				strings.Contains(lineLower, "用户") {
				ldapLines = append(ldapLines, line)
			}
		}

		if len(ldapLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(ldapLines) > 0 {
		return strings.Join(ldapLines, "\n")
	}

	// 如果没有找到特定的LDAP信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
