@echo off
chcp 65001 >nul
title 漏洞扫描器前端启动

echo =================================
echo     漏洞扫描器前端启动
echo =================================
echo.

:: 检查是否在正确目录
if not exist "web\package.json" (
    echo 错误: 请在scanner目录下运行此脚本
    pause
    exit /b 1
)

echo 正在启动前端服务...
cd web
start "前端服务" cmd /k "npm run dev"

:: 等待前端启动
timeout /t 5 /nobreak >nul

echo.
echo =================================
echo     前端启动完成
echo =================================
echo 前端地址: http://localhost:3000
echo.
echo 注意: 后端服务未启动，部分功能可能无法使用
echo.
echo 按任意键打开前端页面...
pause >nul

:: 打开浏览器
start http://localhost:3000

echo.
echo 前端已在后台运行，关闭此窗口不会停止服务
echo 如需停止服务，请关闭对应的命令行窗口
pause
