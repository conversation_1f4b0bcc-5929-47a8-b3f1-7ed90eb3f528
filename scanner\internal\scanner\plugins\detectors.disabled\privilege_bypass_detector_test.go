package detectors

import (
	"testing"
	"time"

	"scanner/internal/scanner/plugins"
)

// TestPrivilegeBypassDetectorBasicFunctionality 测试权限绕过检测器基本功能
func TestPrivilegeBypassDetectorBasicFunctionality(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	// 测试基本信息
	if detector.GetID() != "privilege-bypass-comprehensive" {
		t.<PERSON><PERSON>("Expected ID 'privilege-bypass-comprehensive', got '%s'", detector.GetID())
	}

	if detector.GetName() != "权限绕过漏洞综合检测器" {
		t.<PERSON><PERSON><PERSON>("Expected name '权限绕过漏洞综合检测器', got '%s'", detector.GetName())
	}

	if detector.GetCategory() != "web" {
		t.Errorf("Expected category 'web', got '%s'", detector.GetCategory())
	}

	if detector.GetSeverity() != "high" {
		t.<PERSON><PERSON><PERSON>("Expected severity 'high', got '%s'", detector.GetSeverity())
	}

	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled by default")
	}

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	expectedTypes := []string{"http", "https"}
	if len(targetTypes) != len(expectedTypes) {
		t.Errorf("Expected %d target types, got %d", len(expectedTypes), len(targetTypes))
	}

	// 测试端口
	ports := detector.GetRequiredPorts()
	if len(ports) == 0 {
		t.Error("Expected some required ports")
	}

	// 测试服务
	services := detector.GetRequiredServices()
	expectedServices := []string{"http", "https", "web", "api"}
	if len(services) != len(expectedServices) {
		t.Errorf("Expected %d services, got %d", len(expectedServices), len(services))
	}
}

// TestPrivilegeBypassDetectorApplicability 测试权限绕过检测器适用性
func TestPrivilegeBypassDetectorApplicability(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "HTTP URL目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有权限绕过关键词的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com/admin/privilege",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有管理员关键词的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com/administrator/panel",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有表单的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Forms: []plugins.FormInfo{
					{
						Action: "/login",
						Method: "POST",
						Fields: map[string]string{"username": "text", "password": "password"},
					},
				},
			},
			expected: true,
		},
		{
			name: "有权限控制技术栈的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Technologies: []plugins.TechnologyInfo{
					{Name: "Spring Security", Version: "5.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "非Web目标",
			target: &plugins.ScanTarget{
				Type:     "ip",
				Protocol: "tcp",
				Port:     22,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.IsApplicable(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestPrivilegeBypassDetectorConfiguration 测试权限绕过检测器配置
func TestPrivilegeBypassDetectorConfiguration(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	if config.Timeout != 15*time.Second {
		t.Errorf("Expected timeout 15s, got %v", config.Timeout)
	}

	if config.MaxRetries != 3 {
		t.Errorf("Expected max retries 3, got %d", config.MaxRetries)
	}

	if config.Concurrency != 8 {
		t.Errorf("Expected concurrency 8, got %d", config.Concurrency)
	}

	if config.RateLimit != 8 {
		t.Errorf("Expected rate limit 8, got %d", config.RateLimit)
	}

	if !config.FollowRedirects {
		t.Error("Expected FollowRedirects to be true")
	}

	if config.VerifySSL {
		t.Error("Expected VerifySSL to be false")
	}

	if config.MaxResponseSize != 1*1024*1024 {
		t.Errorf("Expected max response size 1MB, got %d", config.MaxResponseSize)
	}

	// 测试配置更新
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      5,
		Concurrency:     10,
		RateLimit:       10,
		FollowRedirects: false,
		VerifySSL:       true,
		MaxResponseSize: 5 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	if err != nil {
		t.Errorf("Failed to set configuration: %v", err)
	}

	updatedConfig := detector.GetConfiguration()
	if updatedConfig.Timeout != 30*time.Second {
		t.Errorf("Expected updated timeout 30s, got %v", updatedConfig.Timeout)
	}
}

// TestPrivilegeBypassDetectorAccessControlPayloads 测试访问控制载荷
func TestPrivilegeBypassDetectorAccessControlPayloads(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	if len(detector.accessControlPayloads) == 0 {
		t.Error("Expected some access control payloads")
	}

	// 检查是否包含关键的访问控制载荷
	expectedPayloads := []string{
		"../",
		"..\\",
		"..%2f",
		"..%5c",
		"../%00",
		"..\\%00",
		"..;/",
		"..;\\",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.accessControlPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find access control payload '%s'", expected)
		}
	}
}

// TestPrivilegeBypassDetectorAuthBypassPayloads 测试权限验证绕过载荷
func TestPrivilegeBypassDetectorAuthBypassPayloads(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	if len(detector.authBypassPayloads) == 0 {
		t.Error("Expected some auth bypass payloads")
	}

	// 检查是否包含关键的权限验证绕过载荷
	expectedPayloads := []string{
		"X-Forwarded-For: 127.0.0.1",
		"X-Real-IP: 127.0.0.1",
		"User-Agent: admin",
		"Authorization: Basic YWRtaW46YWRtaW4=",
		"Cookie: admin=true",
		"X-Admin: true",
		"Host: localhost",
		"X-Forwarded-Proto: https",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.authBypassPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find auth bypass payload '%s'", expected)
		}
	}
}

// TestPrivilegeBypassDetectorRoleBypassPayloads 测试角色权限绕过载荷
func TestPrivilegeBypassDetectorRoleBypassPayloads(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	if len(detector.roleBypassPayloads) == 0 {
		t.Error("Expected some role bypass payloads")
	}

	// 检查是否包含关键的角色权限绕过载荷
	expectedPayloads := []string{
		"role=admin",
		"role=administrator",
		"permission=admin",
		"access=admin",
		"level=admin",
		"group=admin",
		"type=admin",
		"admin=true",
		"is_admin=true",
		"roles[]=admin",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.roleBypassPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find role bypass payload '%s'", expected)
		}
	}
}

// TestPrivilegeBypassDetectorPathTraversalPayloads 测试路径遍历绕过载荷
func TestPrivilegeBypassDetectorPathTraversalPayloads(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	if len(detector.pathTraversalPayloads) == 0 {
		t.Error("Expected some path traversal payloads")
	}

	// 检查是否包含关键的路径遍历绕过载荷
	expectedPayloads := []string{
		"../admin",
		"..\\admin",
		"../../admin",
		"..%2fadmin",
		"..%5cadmin",
		"../admin%00",
		"..;/admin",
		"..%u2215admin",
		"../panel",
		"../dashboard",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.pathTraversalPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find path traversal payload '%s'", expected)
		}
	}
}

// TestPrivilegeBypassDetectorTestParameters 测试测试参数
func TestPrivilegeBypassDetectorTestParameters(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	if len(detector.testParameters) == 0 {
		t.Error("Expected some test parameters")
	}

	// 检查是否包含关键的测试参数
	expectedParams := []string{
		"privilege", "bypass", "access", "control", "permission", "authorization",
		"user", "role", "admin", "level", "group", "type", "session", "token",
		"权限", "绕过", "访问", "控制", "用户", "角色", "管理", "级别",
	}

	for _, expected := range expectedParams {
		found := false
		for _, param := range detector.testParameters {
			if param == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find test parameter '%s'", expected)
		}
	}
}

// TestPrivilegeBypassDetectorPatterns 测试检测模式
func TestPrivilegeBypassDetectorPatterns(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	// 测试权限绕过模式
	if len(detector.bypassPatterns) == 0 {
		t.Error("Expected some bypass patterns")
	}

	// 测试访问控制模式
	if len(detector.accessPatterns) == 0 {
		t.Error("Expected some access patterns")
	}

	// 测试权限验证模式
	if len(detector.authPatterns) == 0 {
		t.Error("Expected some auth patterns")
	}

	// 测试角色权限模式
	if len(detector.rolePatterns) == 0 {
		t.Error("Expected some role patterns")
	}
}

// TestPrivilegeBypassDetectorPrivilegeBypassFeatures 测试权限绕过功能检查
func TestPrivilegeBypassDetectorPrivilegeBypassFeatures(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "有认证头部的目标",
			target: &plugins.ScanTarget{
				Headers: map[string]string{
					"Authorization": "Bearer token",
				},
			},
			expected: true,
		},
		{
			name: "有权限控制技术栈的目标",
			target: &plugins.ScanTarget{
				Technologies: []plugins.TechnologyInfo{
					{Name: "Spring Security", Version: "5.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有权限绕过链接的目标",
			target: &plugins.ScanTarget{
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/admin", Text: "Admin Panel"},
				},
			},
			expected: true,
		},
		{
			name: "有权限绕过字段的表单目标",
			target: &plugins.ScanTarget{
				Forms: []plugins.FormInfo{
					{
						Fields: map[string]string{"role": "text", "permission": "text"},
					},
				},
			},
			expected: true,
		},
		{
			name: "普通目标",
			target: &plugins.ScanTarget{
				URL: "https://example.com",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.hasPrivilegeBypassFeatures(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestPrivilegeBypassDetectorRiskScore 测试风险评分
func TestPrivilegeBypassDetectorRiskScore(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	testCases := []struct {
		confidence float64
		expected   float64
	}{
		{0.0, 0.0},
		{0.5, 4.25},
		{0.8, 6.8},
		{1.0, 8.5},
	}

	for _, tc := range testCases {
		score := detector.calculateRiskScore(tc.confidence)
		// 使用浮点数比较的容差
		if score < tc.expected-0.01 || score > tc.expected+0.01 {
			t.Errorf("Expected risk score %.2f for confidence %.1f, got %.2f", tc.expected, tc.confidence, score)
		}
	}
}

// TestPrivilegeBypassDetectorLifecycle 测试检测器生命周期
func TestPrivilegeBypassDetectorLifecycle(t *testing.T) {
	detector := NewPrivilegeBypassDetector()

	// 测试初始化
	err := detector.Initialize()
	if err != nil {
		t.Errorf("Failed to initialize detector: %v", err)
	}

	// 测试验证
	err = detector.Validate()
	if err != nil {
		t.Errorf("Detector validation failed: %v", err)
	}

	// 测试启用/禁用
	detector.SetEnabled(false)
	if detector.IsEnabled() {
		t.Error("Expected detector to be disabled")
	}

	detector.SetEnabled(true)
	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled")
	}

	// 测试清理
	err = detector.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup detector: %v", err)
	}
}
