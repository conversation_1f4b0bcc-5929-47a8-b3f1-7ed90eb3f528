# 漏洞扫描器服务状态检查脚本
# 简单版本 - 检查后端和前端服务是否正常运行

Write-Host "==================================" -ForegroundColor Green
Write-Host "    漏洞扫描器服务状态检查" -ForegroundColor Green  
Write-Host "==================================" -ForegroundColor Green
Write-Host ""

# 检查后端服务 (端口 8080)
Write-Host "检查后端服务 (端口 8080)..." -ForegroundColor Yellow
$backend = netstat -an | findstr ":8080.*LISTENING"
if ($backend) {
    Write-Host "✓ 后端服务正在运行" -ForegroundColor Green
    
    # 检查健康状态
    try {
        $health = Invoke-RestMethod -Uri "http://localhost:8080/health" -TimeoutSec 5
        Write-Host "✓ 后端健康检查通过: $($health.message)" -ForegroundColor Green
        Write-Host "  版本: $($health.version)" -ForegroundColor Gray
        Write-Host "  状态: $($health.status)" -ForegroundColor Gray
    }
    catch {
        Write-Host "✗ 后端健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "✗ 后端服务未运行" -ForegroundColor Red
}

Write-Host ""

# 检查前端服务 (端口 3000)
Write-Host "检查前端服务 (端口 3000)..." -ForegroundColor Yellow
$frontend = netstat -an | findstr ":3000.*LISTENING"
if ($frontend) {
    Write-Host "✓ 前端服务正在运行" -ForegroundColor Green
    
    # 检查前端是否可访问
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✓ 前端页面可正常访问" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "⚠ 前端服务运行但页面访问异常: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ 前端服务未运行" -ForegroundColor Red
}

Write-Host ""

# 检查数据库文件
Write-Host "检查数据库状态..." -ForegroundColor Yellow
if (Test-Path "data/scanner.db") {
    $dbSize = (Get-Item "data/scanner.db").Length
    Write-Host "✓ 数据库文件存在 (大小: $([math]::Round($dbSize/1KB, 2)) KB)" -ForegroundColor Green
} else {
    Write-Host "✗ 数据库文件不存在" -ForegroundColor Red
}

Write-Host ""
Write-Host "==================================" -ForegroundColor Green
Write-Host "    服务访问地址" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host "前端地址: http://localhost:3000" -ForegroundColor Cyan
Write-Host "后端地址: http://localhost:8080" -ForegroundColor Cyan
Write-Host "健康检查: http://localhost:8080/health" -ForegroundColor Cyan
Write-Host "API文档: http://localhost:8080/swagger/index.html" -ForegroundColor Cyan
Write-Host ""

Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
