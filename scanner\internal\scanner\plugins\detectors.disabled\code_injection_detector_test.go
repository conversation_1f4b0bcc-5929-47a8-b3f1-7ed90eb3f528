package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestCodeInjectionDetectorBasicFunctionality 测试代码注入检测器基础功能
func TestCodeInjectionDetectorBasicFunctionality(t *testing.T) {
	detector := NewCodeInjectionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "code-injection-comprehensive", detector.GetID())
	assert.Equal(t, "代码注入漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "critical", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-94")
	assert.Contains(t, detector.GetCVE(), "CVE-2021-44228")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestCodeInjectionDetectorApplicability 测试代码注入检测器适用性
func TestCodeInjectionDetectorApplicability(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 测试有代码功能的目标
	codeTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/eval",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Content-Type": "application/x-php",
			"Server":       "Apache/2.4.41 (PHP/7.4.0)",
		},
	}
	assert.True(t, detector.IsApplicable(codeTarget))

	// 测试有代码技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "PHP", Version: "7.4.0", Confidence: 0.9},
			{Name: "Python", Version: "3.8.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有代码链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/home",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/eval", Text: "Code Eval"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有代码表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/execute",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"code": "textarea", "script": "hidden"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search?q=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试API相关URL
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/execute",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试普通Web目标（代码注入是通用问题）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestCodeInjectionDetectorConfiguration 测试代码注入检测器配置
func TestCodeInjectionDetectorConfiguration(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 18*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.True(t, config.FollowRedirects)
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 4, updatedConfig.Concurrency)
}

// TestCodeInjectionDetectorPHPPayloads 测试PHP载荷
func TestCodeInjectionDetectorPHPPayloads(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 检查PHP载荷列表
	assert.NotEmpty(t, detector.phpPayloads)
	assert.GreaterOrEqual(t, len(detector.phpPayloads), 25)

	// 检查基础PHP代码执行
	assert.Contains(t, detector.phpPayloads, `<?php system('id'); ?>`)
	assert.Contains(t, detector.phpPayloads, `<?php echo system('id'); ?>`)
	assert.Contains(t, detector.phpPayloads, `<?php passthru('id'); ?>`)
	assert.Contains(t, detector.phpPayloads, `<?php exec('id'); ?>`)

	// 检查PHP eval函数注入
	assert.Contains(t, detector.phpPayloads, `'; system('id'); //`)
	assert.Contains(t, detector.phpPayloads, `'; echo system('id'); //`)
	assert.Contains(t, detector.phpPayloads, `'; passthru('id'); //`)

	// 检查PHP文件操作
	assert.Contains(t, detector.phpPayloads, `'; file_get_contents('/etc/passwd'); //`)
	assert.Contains(t, detector.phpPayloads, `'; readfile('/etc/passwd'); //`)

	// 检查PHP信息泄露
	assert.Contains(t, detector.phpPayloads, `'; phpinfo(); //`)
	assert.Contains(t, detector.phpPayloads, `'; var_dump($_SERVER); //`)

	// 检查PHP函数调用
	assert.Contains(t, detector.phpPayloads, `'; call_user_func('system', 'id'); //`)
	assert.Contains(t, detector.phpPayloads, `'; call_user_func_array('system', array('id')); //`)

	// 检查PHP反射
	assert.Contains(t, detector.phpPayloads, `'; $r = new ReflectionFunction('system'); $r->invoke('id'); //`)

	// 检查PHP变量函数
	assert.Contains(t, detector.phpPayloads, `'; $f = 'system'; $f('id'); //`)

	// 检查PHP编码绕过
	assert.Contains(t, detector.phpPayloads, `'; eval(base64_decode('c3lzdGVtKCdpZCcpOw==')); //`)

	// 检查中文PHP载荷
	assert.Contains(t, detector.phpPayloads, `'; 系统('id'); //`)
	assert.Contains(t, detector.phpPayloads, `'; 执行('id'); //`)
}

// TestCodeInjectionDetectorPythonPayloads 测试Python载荷
func TestCodeInjectionDetectorPythonPayloads(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 检查Python载荷列表
	assert.NotEmpty(t, detector.pythonPayloads)
	assert.GreaterOrEqual(t, len(detector.pythonPayloads), 20)

	// 检查基础Python代码执行
	assert.Contains(t, detector.pythonPayloads, `__import__('os').system('id')`)
	assert.Contains(t, detector.pythonPayloads, `__import__('subprocess').call(['id'])`)
	assert.Contains(t, detector.pythonPayloads, `__import__('subprocess').check_output(['id'])`)

	// 检查Python exec/eval函数
	assert.Contains(t, detector.pythonPayloads, `exec('import os; os.system("id")')`)
	assert.Contains(t, detector.pythonPayloads, `eval('__import__("os").system("id")')`)

	// 检查Python文件操作
	assert.Contains(t, detector.pythonPayloads, `open('/etc/passwd').read()`)
	assert.Contains(t, detector.pythonPayloads, `__import__('builtins').open('/etc/passwd').read()`)

	// 检查Python模块导入
	assert.Contains(t, detector.pythonPayloads, `__import__('os').popen('id').read()`)
	assert.Contains(t, detector.pythonPayloads, `__import__('commands').getoutput('id')`)

	// 检查Python反射
	assert.Contains(t, detector.pythonPayloads, `getattr(__import__('os'), 'system')('id')`)
	assert.Contains(t, detector.pythonPayloads, `getattr(__builtins__, 'exec')('import os; os.system("id")')`)

	// 检查Python编码绕过
	assert.Contains(t, detector.pythonPayloads, `exec('aW1wb3J0IG9zOyBvcy5zeXN0ZW0oImxzIik='.decode('base64'))`)

	// 检查中文Python载荷
	assert.Contains(t, detector.pythonPayloads, `导入('os').系统('id')`)
	assert.Contains(t, detector.pythonPayloads, `执行('import os; os.system("id")')`)
}

// TestCodeInjectionDetectorJavaScriptPayloads 测试JavaScript载荷
func TestCodeInjectionDetectorJavaScriptPayloads(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 检查JavaScript载荷列表
	assert.NotEmpty(t, detector.javascriptPayloads)
	assert.GreaterOrEqual(t, len(detector.javascriptPayloads), 15)

	// 检查Node.js代码执行
	assert.Contains(t, detector.javascriptPayloads, `require('child_process').exec('id')`)
	assert.Contains(t, detector.javascriptPayloads, `require('child_process').spawn('id')`)
	assert.Contains(t, detector.javascriptPayloads, `require('child_process').execSync('id')`)

	// 检查Node.js文件操作
	assert.Contains(t, detector.javascriptPayloads, `require('fs').readFileSync('/etc/passwd')`)
	assert.Contains(t, detector.javascriptPayloads, `require('fs').readFile('/etc/passwd')`)

	// 检查JavaScript eval函数
	assert.Contains(t, detector.javascriptPayloads, `eval('require("child_process").exec("id")')`)
	assert.Contains(t, detector.javascriptPayloads, `Function('return require("child_process").exec("id")')()`)

	// 检查JavaScript全局对象
	assert.Contains(t, detector.javascriptPayloads, `global.process.mainModule.require('child_process').exec('id')`)

	// 检查JavaScript原型链
	assert.Contains(t, detector.javascriptPayloads, `''.__proto__.constructor.constructor('return require("child_process").exec("id")')()`)

	// 检查JavaScript编码绕过
	assert.Contains(t, detector.javascriptPayloads, `eval(Buffer.from('cmVxdWlyZSgiY2hpbGRfcHJvY2VzcyIpLmV4ZWMoImlkIik=', 'base64').toString())`)

	// 检查中文JavaScript载荷
	assert.Contains(t, detector.javascriptPayloads, `需要('child_process').执行('id')`)
	assert.Contains(t, detector.javascriptPayloads, `评估('require("child_process").exec("id")')`)
}

// TestCodeInjectionDetectorJavaPayloads 测试Java载荷
func TestCodeInjectionDetectorJavaPayloads(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 检查Java载荷列表
	assert.NotEmpty(t, detector.javaPayloads)
	assert.GreaterOrEqual(t, len(detector.javaPayloads), 15)

	// 检查Java Runtime执行
	assert.Contains(t, detector.javaPayloads, `Runtime.getRuntime().exec("id")`)
	assert.Contains(t, detector.javaPayloads, `Runtime.getRuntime().exec(new String[]{"id"})`)
	assert.Contains(t, detector.javaPayloads, `new ProcessBuilder("id").start()`)

	// 检查Java反射执行
	assert.Contains(t, detector.javaPayloads, `Class.forName("java.lang.Runtime").getMethod("getRuntime").invoke(null).getClass().getMethod("exec", String.class).invoke(Runtime.getRuntime(), "id")`)

	// 检查Java脚本引擎
	assert.Contains(t, detector.javaPayloads, `new javax.script.ScriptEngineManager().getEngineByName("JavaScript").eval("java.lang.Runtime.getRuntime().exec('id')")`)

	// 检查Java文件操作
	assert.Contains(t, detector.javaPayloads, `new java.io.FileInputStream("/etc/passwd")`)
	assert.Contains(t, detector.javaPayloads, `java.nio.file.Files.readAllLines(java.nio.file.Paths.get("/etc/passwd"))`)

	// 检查Java类加载
	assert.Contains(t, detector.javaPayloads, `Class.forName("java.lang.Runtime")`)
	assert.Contains(t, detector.javaPayloads, `ClassLoader.getSystemClassLoader().loadClass("java.lang.Runtime")`)

	// 检查Java表达式语言
	assert.Contains(t, detector.javaPayloads, `#{Runtime.getRuntime().exec("id")}`)
	assert.Contains(t, detector.javaPayloads, `${Runtime.getRuntime().exec("id")}`)

	// 检查中文Java载荷
	assert.Contains(t, detector.javaPayloads, `运行时.获取运行时().执行("id")`)
	assert.Contains(t, detector.javaPayloads, `新进程构建器("id").开始()`)
}

// TestCodeInjectionDetectorTestParameters 测试参数列表
func TestCodeInjectionDetectorTestParameters(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.GreaterOrEqual(t, len(detector.testParameters), 100)

	// 检查代码相关参数
	assert.Contains(t, detector.testParameters, "code")
	assert.Contains(t, detector.testParameters, "script")
	assert.Contains(t, detector.testParameters, "eval")
	assert.Contains(t, detector.testParameters, "exec")
	assert.Contains(t, detector.testParameters, "execute")
	assert.Contains(t, detector.testParameters, "run")

	// 检查文件相关参数
	assert.Contains(t, detector.testParameters, "file")
	assert.Contains(t, detector.testParameters, "path")
	assert.Contains(t, detector.testParameters, "content")
	assert.Contains(t, detector.testParameters, "data")

	// 检查通用参数
	assert.Contains(t, detector.testParameters, "param")
	assert.Contains(t, detector.testParameters, "input")
	assert.Contains(t, detector.testParameters, "output")
	assert.Contains(t, detector.testParameters, "query")

	// 检查用户相关参数
	assert.Contains(t, detector.testParameters, "user")
	assert.Contains(t, detector.testParameters, "username")
	assert.Contains(t, detector.testParameters, "password")
	assert.Contains(t, detector.testParameters, "login")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "代码")
	assert.Contains(t, detector.testParameters, "脚本")
	assert.Contains(t, detector.testParameters, "执行")
	assert.Contains(t, detector.testParameters, "用户")
}

// TestCodeInjectionDetectorPatterns 测试模式
func TestCodeInjectionDetectorPatterns(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 检查PHP模式
	assert.NotEmpty(t, detector.phpPatterns)
	assert.GreaterOrEqual(t, len(detector.phpPatterns), 15)

	// 检查Python模式
	assert.NotEmpty(t, detector.pythonPatterns)
	assert.GreaterOrEqual(t, len(detector.pythonPatterns), 15)

	// 检查JavaScript模式
	assert.NotEmpty(t, detector.javascriptPatterns)
	assert.GreaterOrEqual(t, len(detector.javascriptPatterns), 15)

	// 检查Java模式
	assert.NotEmpty(t, detector.javaPatterns)
	assert.GreaterOrEqual(t, len(detector.javaPatterns), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.GreaterOrEqual(t, len(detector.errorPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.GreaterOrEqual(t, len(detector.responsePatterns), 15)
}

// TestCodeInjectionDetectorCodeFeatures 测试代码功能检查
func TestCodeInjectionDetectorCodeFeatures(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 测试有代码头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Content-Type": "application/x-php",
			"Server":       "Apache/2.4.41 (PHP/7.4.0)",
		},
	}
	assert.True(t, detector.hasCodeFeatures(headerTarget))

	// 测试有代码技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "PHP", Version: "7.4.0", Confidence: 0.9},
			{Name: "Python", Version: "3.8.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasCodeFeatures(techTarget))

	// 测试有代码链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/eval", Text: "Code Eval"},
		},
	}
	assert.True(t, detector.hasCodeFeatures(linkTarget))

	// 测试有代码表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"code": "textarea", "script": "hidden"}},
		},
	}
	assert.True(t, detector.hasCodeFeatures(formTarget))

	// 测试无代码功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasCodeFeatures(simpleTarget))
}

// TestCodeInjectionDetectorRiskScore 测试风险评分计算
func TestCodeInjectionDetectorRiskScore(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 8.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 2.5)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestCodeInjectionDetectorLifecycle 测试检测器生命周期
func TestCodeInjectionDetectorLifecycle(t *testing.T) {
	detector := NewCodeInjectionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
