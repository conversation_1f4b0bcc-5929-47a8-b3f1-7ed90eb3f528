package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectSessionFixation 检测会话固定
func (d *SessionManagementDetector) detectSessionFixation(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 发送初始请求获取会话ID
	initialResp, err := d.sendSessionRequest(ctx, target.URL)
	if err != nil {
		return evidence, 0.0, "", "", ""
	}

	// 提取初始会话ID
	initialSessionID := d.extractSessionID(initialResp)
	if initialSessionID == "" {
		return evidence, 0.0, "", "", ""
	}

	// 模拟登录过程（如果有登录表单）
	loginURL := d.findLoginURL(target)
	if loginURL != "" {
		// 使用相同的会话ID进行登录
		loginResp, err := d.sendSessionRequestWithCookie(ctx, loginURL, initialSessionID)
		if err == nil {
			// 检查登录后的会话ID
			afterLoginSessionID := d.extractSessionID(loginResp)

			// 检查会话固定
			confidence := d.checkSessionFixation(initialSessionID, afterLoginSessionID, loginResp)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("会话固定: %s -> %s", initialSessionID, afterLoginSessionID)
				vulnerableRequest = loginURL
				vulnerableResponse = loginResp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "session-fixation",
					Description: fmt.Sprintf("发现会话固定漏洞 (置信度: %.2f)", confidence),
					Content:     d.extractSessionEvidence(loginResp, "session-fixation"),
					Location:    loginURL,
					Timestamp:   time.Now(),
				})
			}
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectInsecureSessionConfig 检测不安全的会话配置
func (d *SessionManagementDetector) detectInsecureSessionConfig(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 发送请求检查Cookie配置
	resp, err := d.sendSessionRequest(ctx, target.URL)
	if err != nil {
		return evidence, 0.0, "", "", ""
	}

	vulnerableRequest = target.URL
	vulnerableResponse = resp

	// 检查Cookie安全属性
	missingAttributes := d.checkCookieSecurityAttributes(resp)
	if len(missingAttributes) > 0 {
		confidence := float64(len(missingAttributes)) * 0.2
		if confidence > 1.0 {
			confidence = 1.0
		}

		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("缺失安全属性: %s", strings.Join(missingAttributes, ", "))
		}

		evidence = append(evidence, plugins.Evidence{
			Type:        "insecure-session-config",
			Description: fmt.Sprintf("发现不安全的会话配置: %s (置信度: %.2f)", strings.Join(missingAttributes, ", "), confidence),
			Content:     d.extractSessionEvidence(resp, "cookie-security"),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	// 检查会话超时配置
	timeoutIssues := d.checkSessionTimeout(resp)
	if len(timeoutIssues) > 0 {
		confidence := float64(len(timeoutIssues)) * 0.15
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("会话超时问题: %s", strings.Join(timeoutIssues, ", "))
		}

		evidence = append(evidence, plugins.Evidence{
			Type:        "session-timeout-issues",
			Description: fmt.Sprintf("发现会话超时问题: %s (置信度: %.2f)", strings.Join(timeoutIssues, ", "), confidence),
			Content:     d.extractSessionEvidence(resp, "session-timeout"),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectSessionHijacking 检测会话劫持
func (d *SessionManagementDetector) detectSessionHijacking(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 发送请求获取会话信息
	resp, err := d.sendSessionRequest(ctx, target.URL)
	if err != nil {
		return evidence, 0.0, "", "", ""
	}

	vulnerableRequest = target.URL
	vulnerableResponse = resp

	// 检查会话ID的可预测性
	sessionID := d.extractSessionID(resp)
	if sessionID != "" {
		predictability := d.checkSessionPredictability(sessionID)
		if predictability > 0.6 {
			confidence := predictability

			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("可预测的会话ID: %s", sessionID)
			}

			evidence = append(evidence, plugins.Evidence{
				Type:        "session-hijacking-predictable",
				Description: fmt.Sprintf("发现可预测的会话ID (置信度: %.2f)", confidence),
				Content:     d.extractSessionEvidence(resp, "session-predictability"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	// 检查会话传输安全性
	transmissionIssues := d.checkSessionTransmissionSecurity(resp)
	if len(transmissionIssues) > 0 {
		confidence := float64(len(transmissionIssues)) * 0.25
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("会话传输安全问题: %s", strings.Join(transmissionIssues, ", "))
		}

		evidence = append(evidence, plugins.Evidence{
			Type:        "session-transmission-insecure",
			Description: fmt.Sprintf("发现会话传输安全问题: %s (置信度: %.2f)", strings.Join(transmissionIssues, ", "), confidence),
			Content:     d.extractSessionEvidence(resp, "session-transmission"),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCSRFProtection 检测CSRF保护
func (d *SessionManagementDetector) detectCSRFProtection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 检查表单中的CSRF保护
	for _, form := range target.Forms {
		if form.Method == "POST" {
			// 检查是否有CSRF令牌
			hasCSRFToken := d.checkFormCSRFToken(form)
			if !hasCSRFToken {
				confidence := 0.7

				if confidence > maxConfidence {
					maxConfidence = confidence
					vulnerablePayload = fmt.Sprintf("表单缺少CSRF保护: %s", form.Action)
					vulnerableRequest = target.URL
				}

				evidence = append(evidence, plugins.Evidence{
					Type:        "csrf-protection-missing",
					Description: fmt.Sprintf("发现表单缺少CSRF保护: %s (置信度: %.2f)", form.Action, confidence),
					Content:     fmt.Sprintf("表单动作: %s, 方法: %s, 字段: %v", form.Action, form.Method, form.Fields),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}
		}
	}

	// 发送请求检查CSRF头部保护
	resp, err := d.sendSessionRequest(ctx, target.URL)
	if err == nil {
		csrfHeaderIssues := d.checkCSRFHeaders(resp)
		if len(csrfHeaderIssues) > 0 {
			confidence := float64(len(csrfHeaderIssues)) * 0.2
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("CSRF头部保护问题: %s", strings.Join(csrfHeaderIssues, ", "))
				vulnerableRequest = target.URL
				vulnerableResponse = resp
			}

			evidence = append(evidence, plugins.Evidence{
				Type:        "csrf-header-protection-issues",
				Description: fmt.Sprintf("发现CSRF头部保护问题: %s (置信度: %.2f)", strings.Join(csrfHeaderIssues, ", "), confidence),
				Content:     d.extractSessionEvidence(resp, "csrf-headers"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendSessionRequest 发送会话请求
func (d *SessionManagementDetector) sendSessionRequest(ctx context.Context, targetURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendSessionRequestWithCookie 发送带Cookie的会话请求
func (d *SessionManagementDetector) sendSessionRequestWithCookie(ctx context.Context, targetURL, sessionID string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Cookie", fmt.Sprintf("SESSIONID=%s", sessionID))

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// buildSessionURL 构造会话URL
func (d *SessionManagementDetector) buildSessionURL(baseURL, path string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + path
	}

	// 替换路径
	parsedURL.Path = path

	return parsedURL.String()
}

// checkSessionManagementResponse 检查会话管理响应（通用方法）
func (d *SessionManagementDetector) checkSessionManagementResponse(response, path string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2
	}

	// 检查会话模式匹配
	for _, pattern := range d.sessionPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查安全模式匹配
	for _, pattern := range d.securityPatterns {
		if pattern.MatchString(response) {
			confidence += 0.1
			break
		}
	}

	return confidence
}

// extractSessionID 提取会话ID
func (d *SessionManagementDetector) extractSessionID(response string) string {
	lines := strings.Split(response, "\n")

	for _, line := range lines {
		if strings.HasPrefix(strings.ToLower(line), "set-cookie:") {
			// 查找会话Cookie
			for _, sessionCookie := range d.sessionCookies {
				if strings.Contains(strings.ToLower(line), strings.ToLower(sessionCookie)) {
					// 提取Cookie值
					parts := strings.Split(line, "=")
					if len(parts) >= 2 {
						value := strings.Split(parts[1], ";")[0]
						return strings.TrimSpace(value)
					}
				}
			}
		}
	}

	return ""
}

// findLoginURL 查找登录URL
func (d *SessionManagementDetector) findLoginURL(target *plugins.ScanTarget) string {
	// 检查表单中是否有登录表单
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)
			if strings.Contains(fieldNameLower, "password") ||
				strings.Contains(fieldNameLower, "login") ||
				strings.Contains(fieldNameLower, "username") {
				return form.Action
			}
		}
	}

	// 检查链接中是否有登录链接
	for _, link := range target.Links {
		linkLower := strings.ToLower(link.URL)
		if strings.Contains(linkLower, "login") ||
			strings.Contains(linkLower, "signin") ||
			strings.Contains(linkLower, "auth") {
			return link.URL
		}
	}

	return ""
}

// checkSessionFixation 检查会话固定
func (d *SessionManagementDetector) checkSessionFixation(initialSessionID, afterLoginSessionID, response string) float64 {
	confidence := 0.0

	// 如果会话ID在登录后没有改变，可能存在会话固定
	if initialSessionID != "" && afterLoginSessionID != "" {
		if initialSessionID == afterLoginSessionID {
			confidence += 0.8 // 高置信度
		}
	}

	// 检查响应中是否有会话固定相关的模式
	response = strings.ToLower(response)
	for _, pattern := range d.vulnerabilityPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	return confidence
}

// checkCookieSecurityAttributes 检查Cookie安全属性
func (d *SessionManagementDetector) checkCookieSecurityAttributes(response string) []string {
	var missingAttributes []string
	lines := strings.Split(response, "\n")

	var hasCookie bool
	var hasSecure, hasHttpOnly, hasSameSite bool

	for _, line := range lines {
		if strings.HasPrefix(strings.ToLower(line), "set-cookie:") {
			hasCookie = true
			lineLower := strings.ToLower(line)

			// 检查会话Cookie
			isSessionCookie := false
			for _, sessionCookie := range d.sessionCookies {
				if strings.Contains(lineLower, strings.ToLower(sessionCookie)) {
					isSessionCookie = true
					break
				}
			}

			if isSessionCookie {
				// 检查安全属性
				if strings.Contains(lineLower, "secure") {
					hasSecure = true
				}
				if strings.Contains(lineLower, "httponly") {
					hasHttpOnly = true
				}
				if strings.Contains(lineLower, "samesite") {
					hasSameSite = true
				}
			}
		}
	}

	if hasCookie {
		if !hasSecure {
			missingAttributes = append(missingAttributes, "Secure")
		}
		if !hasHttpOnly {
			missingAttributes = append(missingAttributes, "HttpOnly")
		}
		if !hasSameSite {
			missingAttributes = append(missingAttributes, "SameSite")
		}
	}

	return missingAttributes
}

// checkSessionTimeout 检查会话超时配置
func (d *SessionManagementDetector) checkSessionTimeout(response string) []string {
	var timeoutIssues []string
	lines := strings.Split(response, "\n")

	for _, line := range lines {
		if strings.HasPrefix(strings.ToLower(line), "set-cookie:") {
			lineLower := strings.ToLower(line)

			// 检查会话Cookie
			isSessionCookie := false
			for _, sessionCookie := range d.sessionCookies {
				if strings.Contains(lineLower, strings.ToLower(sessionCookie)) {
					isSessionCookie = true
					break
				}
			}

			if isSessionCookie {
				// 检查是否有过期时间设置
				if !strings.Contains(lineLower, "expires") && !strings.Contains(lineLower, "max-age") {
					timeoutIssues = append(timeoutIssues, "无过期时间设置")
				}

				// 检查是否设置了过长的过期时间
				if strings.Contains(lineLower, "expires") {
					// 简单检查是否包含未来很远的日期
					if strings.Contains(lineLower, "2030") || strings.Contains(lineLower, "2040") {
						timeoutIssues = append(timeoutIssues, "过期时间过长")
					}
				}
			}
		}
	}

	return timeoutIssues
}

// checkSessionPredictability 检查会话ID的可预测性
func (d *SessionManagementDetector) checkSessionPredictability(sessionID string) float64 {
	confidence := 0.0

	// 检查会话ID长度（太短可能不安全）
	if len(sessionID) < 16 {
		confidence += 0.3
	}

	// 检查是否包含可预测的模式
	predictablePatterns := []string{
		"123", "abc", "test", "admin", "user", "session",
		"000", "111", "aaa", "zzz",
	}

	sessionIDLower := strings.ToLower(sessionID)
	for _, pattern := range predictablePatterns {
		if strings.Contains(sessionIDLower, pattern) {
			confidence += 0.4
			break
		}
	}

	// 检查是否全是数字（可能是递增的）
	isAllDigits := true
	for _, char := range sessionID {
		if char < '0' || char > '9' {
			isAllDigits = false
			break
		}
	}
	if isAllDigits {
		confidence += 0.5
	}

	return confidence
}

// checkSessionTransmissionSecurity 检查会话传输安全性
func (d *SessionManagementDetector) checkSessionTransmissionSecurity(response string) []string {
	var transmissionIssues []string

	// 检查是否通过HTTP传输（不安全）
	if strings.Contains(strings.ToLower(response), "http://") {
		transmissionIssues = append(transmissionIssues, "HTTP传输不安全")
	}

	// 检查是否缺少安全传输头
	response = strings.ToLower(response)
	if !strings.Contains(response, "strict-transport-security") {
		transmissionIssues = append(transmissionIssues, "缺少HSTS头")
	}

	return transmissionIssues
}

// checkFormCSRFToken 检查表单CSRF令牌
func (d *SessionManagementDetector) checkFormCSRFToken(form plugins.FormInfo) bool {
	for fieldName := range form.Fields {
		fieldNameLower := strings.ToLower(fieldName)

		// 检查CSRF令牌字段
		for _, tokenName := range d.csrfTokenNames {
			if strings.Contains(fieldNameLower, strings.ToLower(tokenName)) {
				return true
			}
		}
	}

	return false
}

// checkCSRFHeaders 检查CSRF头部保护
func (d *SessionManagementDetector) checkCSRFHeaders(response string) []string {
	var csrfIssues []string
	lines := strings.Split(response, "\n")

	var hasCSRFHeader bool

	for _, line := range lines {
		lineLower := strings.ToLower(line)

		// 检查CSRF相关头部
		if strings.Contains(lineLower, "x-csrf-token") ||
			strings.Contains(lineLower, "x-xsrf-token") ||
			strings.Contains(lineLower, "csrf-token") {
			hasCSRFHeader = true
			break
		}
	}

	if !hasCSRFHeader {
		csrfIssues = append(csrfIssues, "缺少CSRF头部保护")
	}

	return csrfIssues
}

// extractSessionEvidence 提取会话证据
func (d *SessionManagementDetector) extractSessionEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var sessionLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "session-fixation":
			if strings.Contains(lineLower, "session") ||
				strings.Contains(lineLower, "cookie") ||
				strings.Contains(lineLower, "login") ||
				strings.Contains(lineLower, "auth") {
				sessionLines = append(sessionLines, line)
			}
		case "cookie-security":
			if strings.Contains(lineLower, "set-cookie") ||
				strings.Contains(lineLower, "cookie") ||
				strings.Contains(lineLower, "secure") ||
				strings.Contains(lineLower, "httponly") ||
				strings.Contains(lineLower, "samesite") {
				sessionLines = append(sessionLines, line)
			}
		case "session-timeout":
			if strings.Contains(lineLower, "expires") ||
				strings.Contains(lineLower, "max-age") ||
				strings.Contains(lineLower, "timeout") ||
				strings.Contains(lineLower, "session") {
				sessionLines = append(sessionLines, line)
			}
		case "session-predictability":
			if strings.Contains(lineLower, "session") ||
				strings.Contains(lineLower, "id") ||
				strings.Contains(lineLower, "token") {
				sessionLines = append(sessionLines, line)
			}
		case "session-transmission":
			if strings.Contains(lineLower, "http") ||
				strings.Contains(lineLower, "https") ||
				strings.Contains(lineLower, "transport") ||
				strings.Contains(lineLower, "security") {
				sessionLines = append(sessionLines, line)
			}
		case "csrf-headers":
			if strings.Contains(lineLower, "csrf") ||
				strings.Contains(lineLower, "xsrf") ||
				strings.Contains(lineLower, "token") ||
				strings.Contains(lineLower, "protection") {
				sessionLines = append(sessionLines, line)
			}
		default:
			if strings.Contains(lineLower, "session") ||
				strings.Contains(lineLower, "cookie") ||
				strings.Contains(lineLower, "auth") ||
				strings.Contains(lineLower, "login") ||
				strings.Contains(lineLower, "csrf") ||
				strings.Contains(lineLower, "token") ||
				strings.Contains(lineLower, "会话") ||
				strings.Contains(lineLower, "认证") ||
				strings.Contains(lineLower, "登录") ||
				strings.Contains(lineLower, "令牌") {
				sessionLines = append(sessionLines, line)
			}
		}

		if len(sessionLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(sessionLines) > 0 {
		return strings.Join(sessionLines, "\n")
	}

	// 如果没有找到特定的会话信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
