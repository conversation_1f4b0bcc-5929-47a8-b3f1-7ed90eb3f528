package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestJSONInjectionDetectorBasicFunctionality 测试JSON注入检测器基础功能
func TestJSONInjectionDetectorBasicFunctionality(t *testing.T) {
	detector := NewJSONInjectionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "json-injection-comprehensive", detector.GetID())
	assert.Equal(t, "JSON注入漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-94")
	assert.Contains(t, detector.GetCVE(), "CVE-2021-44228")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestJSONInjectionDetectorApplicability 测试JSON注入检测器适用性
func TestJSONInjectionDetectorApplicability(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 测试有JSON功能的目标
	jsonTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/data",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Content-Type": "application/json",
			"Accept":       "application/json",
		},
	}
	assert.True(t, detector.IsApplicable(jsonTarget))

	// 测试有JSON技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "Node.js", Version: "14.0.0", Confidence: 0.9},
			{Name: "Express", Version: "4.17.1", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有JSON链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/home",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api/data", Text: "API Data"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有JSON表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/form",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"data": "text", "config": "hidden"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search?q=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试API相关URL
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/users",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试普通Web目标（JSON注入是通用问题）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestJSONInjectionDetectorConfiguration 测试JSON注入检测器配置
func TestJSONInjectionDetectorConfiguration(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 12*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.True(t, config.FollowRedirects) // JSON注入跟随重定向
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 15*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 4, updatedConfig.Concurrency)
}

// TestJSONInjectionDetectorStructurePayloads 测试JSON结构载荷
func TestJSONInjectionDetectorStructurePayloads(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 检查JSON结构载荷列表
	assert.NotEmpty(t, detector.structurePayloads)
	assert.GreaterOrEqual(t, len(detector.structurePayloads), 25)

	// 检查基础JSON结构破坏
	assert.Contains(t, detector.structurePayloads, `{"test": "value", "admin": true}`)
	assert.Contains(t, detector.structurePayloads, `{"test": "value", "role": "admin"}`)
	assert.Contains(t, detector.structurePayloads, `{"test": "value", "isAdmin": true}`)
	assert.Contains(t, detector.structurePayloads, `{"test": "value", "permissions": ["admin"]}`)

	// 检查JSON键注入
	assert.Contains(t, detector.structurePayloads, `{"test": "value", "admin": "true"}`)
	assert.Contains(t, detector.structurePayloads, `{"test": "value", "user_role": "administrator"}`)
	assert.Contains(t, detector.structurePayloads, `{"test": "value", "access_level": "admin"}`)
	assert.Contains(t, detector.structurePayloads, `{"test": "value", "privilege": "elevated"}`)

	// 检查JSON值注入
	assert.Contains(t, detector.structurePayloads, `{"test": "value\", \"admin\": true, \"test2\": \"value2"}`)
	assert.Contains(t, detector.structurePayloads, `{"test": "value\", \"role\": \"admin\", \"test2\": \"value2"}`)
	assert.Contains(t, detector.structurePayloads, `{"test": "value\", \"isAdmin\": true, \"test2\": \"value2"}`)

	// 检查JSON数组注入
	assert.Contains(t, detector.structurePayloads, `["test", "admin", true]`)
	assert.Contains(t, detector.structurePayloads, `["test", {"admin": true}]`)
	assert.Contains(t, detector.structurePayloads, `["test", {"role": "admin"}]`)

	// 检查JSON嵌套注入
	assert.Contains(t, detector.structurePayloads, `{"test": {"admin": true}}`)
	assert.Contains(t, detector.structurePayloads, `{"test": {"role": "admin"}}`)
	assert.Contains(t, detector.structurePayloads, `{"test": {"user": {"admin": true}}}`)

	// 检查中文JSON结构载荷
	assert.Contains(t, detector.structurePayloads, `{"测试": "值", "管理员": true}`)
	assert.Contains(t, detector.structurePayloads, `{"测试": "值", "角色": "管理员"}`)
}

// TestJSONInjectionDetectorTypeConfusionPayloads 测试类型混淆载荷
func TestJSONInjectionDetectorTypeConfusionPayloads(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 检查类型混淆载荷列表
	assert.NotEmpty(t, detector.typeConfusionPayloads)
	assert.GreaterOrEqual(t, len(detector.typeConfusionPayloads), 30)

	// 检查字符串到数字混淆
	assert.Contains(t, detector.typeConfusionPayloads, `{"id": "1", "admin": "true"}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"id": "0", "admin": "false"}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"id": "-1", "admin": "1"}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"id": "999999", "admin": "0"}`)

	// 检查数字到字符串混淆
	assert.Contains(t, detector.typeConfusionPayloads, `{"id": 1, "admin": true}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"id": 0, "admin": false}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"id": -1, "admin": 1}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"id": 999999, "admin": 0}`)

	// 检查布尔值混淆
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": "true"}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": "false"}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": "1"}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": "0"}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": 1}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": 0}`)

	// 检查空值混淆
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": null}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": "null"}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": ""}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": " "}`)

	// 检查数组混淆
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": []}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": [true]}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": ["true"]}`)

	// 检查对象混淆
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": {}}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": {"value": true}}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"admin": {"value": "true"}}`)

	// 检查中文类型混淆载荷
	assert.Contains(t, detector.typeConfusionPayloads, `{"管理员": "真"}`)
	assert.Contains(t, detector.typeConfusionPayloads, `{"管理员": "假"}`)
}

// TestJSONInjectionDetectorLogicBypassPayloads 测试逻辑绕过载荷
func TestJSONInjectionDetectorLogicBypassPayloads(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 检查逻辑绕过载荷列表
	assert.NotEmpty(t, detector.logicBypassPayloads)
	assert.GreaterOrEqual(t, len(detector.logicBypassPayloads), 25)

	// 检查认证绕过
	assert.Contains(t, detector.logicBypassPayloads, `{"username": "admin", "password": ""}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"username": "admin", "password": null}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"username": "admin", "password": []}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"username": "admin", "password": {}}`)

	// 检查权限绕过
	assert.Contains(t, detector.logicBypassPayloads, `{"user": "test", "admin": true}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"user": "test", "role": "admin"}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"user": "test", "permissions": ["admin"]}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"user": "test", "access_level": 9999}`)

	// 检查价格绕过
	assert.Contains(t, detector.logicBypassPayloads, `{"product": "test", "price": 0}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"product": "test", "price": -1}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"product": "test", "price": null}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"product": "test", "discount": 100}`)

	// 检查数量绕过
	assert.Contains(t, detector.logicBypassPayloads, `{"product": "test", "quantity": -1}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"product": "test", "quantity": 999999}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"product": "test", "quantity": null}`)

	// 检查状态绕过
	assert.Contains(t, detector.logicBypassPayloads, `{"status": "active", "deleted": false}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"status": "inactive", "enabled": true}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"status": null, "visible": true}`)

	// 检查中文逻辑绕过载荷
	assert.Contains(t, detector.logicBypassPayloads, `{"用户名": "管理员", "密码": ""}`)
	assert.Contains(t, detector.logicBypassPayloads, `{"用户": "测试", "管理员": true}`)
}

// TestJSONInjectionDetectorCodeExecutionPayloads 测试代码执行载荷
func TestJSONInjectionDetectorCodeExecutionPayloads(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 检查代码执行载荷列表
	assert.NotEmpty(t, detector.codeExecutionPayloads)
	assert.GreaterOrEqual(t, len(detector.codeExecutionPayloads), 25)

	// 检查JavaScript代码执行
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "value", "eval": "alert('XSS')"}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "value", "script": "<script>alert('XSS')</script>"}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "value", "code": "console.log('test')"}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "value", "function": "function(){alert('XSS')}"}`)

	// 检查Node.js代码执行
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "value", "require": "require('child_process').exec('id')"}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "value", "process": "process.exit(0)"}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "value", "global": "global.process.exit(0)"}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "value", "module": "module.exports = {}"}`)

	// 检查模板注入
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "{{7*7}}", "template": "{{constructor.constructor('alert(1)')()}}"}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "${7*7}", "template": "${constructor.constructor('alert(1)')()}"}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"test": "#{7*7}", "template": "#{constructor.constructor('alert(1)')()}"}`)

	// 检查原型污染
	assert.Contains(t, detector.codeExecutionPayloads, `{"__proto__": {"admin": true}}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"constructor": {"prototype": {"admin": true}}}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"__proto__.admin": true}`)

	// 检查中文代码执行载荷
	assert.Contains(t, detector.codeExecutionPayloads, `{"测试": "值", "评估": "alert('XSS')"}`)
	assert.Contains(t, detector.codeExecutionPayloads, `{"测试": "值", "脚本": "<script>alert('XSS')</script>"}`)
}

// TestJSONInjectionDetectorTestParameters 测试参数列表
func TestJSONInjectionDetectorTestParameters(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.GreaterOrEqual(t, len(detector.testParameters), 100)

	// 检查JSON相关参数
	assert.Contains(t, detector.testParameters, "json")
	assert.Contains(t, detector.testParameters, "data")
	assert.Contains(t, detector.testParameters, "config")
	assert.Contains(t, detector.testParameters, "settings")
	assert.Contains(t, detector.testParameters, "params")
	assert.Contains(t, detector.testParameters, "query")

	// 检查API相关参数
	assert.Contains(t, detector.testParameters, "api")
	assert.Contains(t, detector.testParameters, "endpoint")
	assert.Contains(t, detector.testParameters, "service")
	assert.Contains(t, detector.testParameters, "method")
	assert.Contains(t, detector.testParameters, "action")

	// 检查用户相关参数
	assert.Contains(t, detector.testParameters, "user")
	assert.Contains(t, detector.testParameters, "username")
	assert.Contains(t, detector.testParameters, "userid")
	assert.Contains(t, detector.testParameters, "login")
	assert.Contains(t, detector.testParameters, "auth")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "数据")
	assert.Contains(t, detector.testParameters, "配置")
	assert.Contains(t, detector.testParameters, "设置")
	assert.Contains(t, detector.testParameters, "参数")
}

// TestJSONInjectionDetectorPatterns 测试模式
func TestJSONInjectionDetectorPatterns(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 检查JSON结构模式
	assert.NotEmpty(t, detector.structurePatterns)
	assert.GreaterOrEqual(t, len(detector.structurePatterns), 15)

	// 检查类型模式
	assert.NotEmpty(t, detector.typePatterns)
	assert.GreaterOrEqual(t, len(detector.typePatterns), 15)

	// 检查逻辑模式
	assert.NotEmpty(t, detector.logicPatterns)
	assert.GreaterOrEqual(t, len(detector.logicPatterns), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.GreaterOrEqual(t, len(detector.errorPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.GreaterOrEqual(t, len(detector.responsePatterns), 15)
}

// TestJSONInjectionDetectorJSONFeatures 测试JSON功能检查
func TestJSONInjectionDetectorJSONFeatures(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 测试有JSON头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Content-Type": "application/json",
			"Accept":       "application/json",
		},
	}
	assert.True(t, detector.hasJSONFeatures(headerTarget))

	// 测试有JSON技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "Node.js", Version: "14.0.0", Confidence: 0.9},
			{Name: "Express", Version: "4.17.1", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasJSONFeatures(techTarget))

	// 测试有JSON链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api/data", Text: "API Data"},
		},
	}
	assert.True(t, detector.hasJSONFeatures(linkTarget))

	// 测试有JSON表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"data": "text", "config": "hidden"}},
		},
	}
	assert.True(t, detector.hasJSONFeatures(formTarget))

	// 测试无JSON功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasJSONFeatures(simpleTarget))
}

// TestJSONInjectionDetectorRiskScore 测试风险评分计算
func TestJSONInjectionDetectorRiskScore(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.5)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 2.5)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestJSONInjectionDetectorLifecycle 测试检测器生命周期
func TestJSONInjectionDetectorLifecycle(t *testing.T) {
	detector := NewJSONInjectionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
