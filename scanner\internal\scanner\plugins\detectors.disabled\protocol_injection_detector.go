package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// ProtocolInjectionDetector 协议注入检测器
// 支持协议注入检测，包括HTTP协议注入、SMTP协议注入、FTP协议注入等多种协议注入检测技术
type ProtocolInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	httpProtocolPayloads []string         // HTTP协议注入载荷
	smtpProtocolPayloads []string         // SMTP协议注入载荷
	ftpProtocolPayloads  []string         // FTP协议注入载荷
	ldapProtocolPayloads []string         // LDAP协议注入载荷
	dnsProtocolPayloads  []string         // DNS协议注入载荷
	sshProtocolPayloads  []string         // SSH协议注入载荷
	testParameters       []string         // 测试参数
	httpPatterns         []*regexp.Regexp // HTTP协议模式
	smtpPatterns         []*regexp.Regexp // SMTP协议模式
	ftpPatterns          []*regexp.Regexp // FTP协议模式
	ldapPatterns         []*regexp.Regexp // LDAP协议模式
	dnsPatterns          []*regexp.Regexp // DNS协议模式
	sshPatterns          []*regexp.Regexp // SSH协议模式
	errorPatterns        []*regexp.Regexp // 错误模式
	responsePatterns     []*regexp.Regexp // 响应模式
	httpClient           *http.Client
}

// NewProtocolInjectionDetector 创建协议注入检测器
func NewProtocolInjectionDetector() *ProtocolInjectionDetector {
	detector := &ProtocolInjectionDetector{
		id:          "protocol-injection-comprehensive",
		name:        "协议注入漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-0708"},
		cwe:         []string{"CWE-74", "CVE-75", "CWE-77", "CWE-78", "CWE-79", "CWE-94"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测协议注入漏洞，包括HTTP协议注入、SMTP协议注入、FTP协议注入等多种协议注入检测技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // 协议注入检测需要适中时间
		MaxRetries:      2,
		Concurrency:     2,
		RateLimit:       2,
		FollowRedirects: true, // 跟随重定向检查协议处理
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024, // 3MB，协议响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeHTTPProtocolPayloads()
	detector.initializeSMTPProtocolPayloads()
	detector.initializeFTPProtocolPayloads()
	detector.initializeLDAPProtocolPayloads()
	detector.initializeDNSProtocolPayloads()
	detector.initializeSSHProtocolPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *ProtocolInjectionDetector) GetID() string            { return d.id }
func (d *ProtocolInjectionDetector) GetName() string          { return d.name }
func (d *ProtocolInjectionDetector) GetCategory() string      { return d.category }
func (d *ProtocolInjectionDetector) GetSeverity() string      { return d.severity }
func (d *ProtocolInjectionDetector) GetCVE() []string         { return d.cve }
func (d *ProtocolInjectionDetector) GetCWE() []string         { return d.cwe }
func (d *ProtocolInjectionDetector) GetVersion() string       { return d.version }
func (d *ProtocolInjectionDetector) GetAuthor() string        { return d.author }
func (d *ProtocolInjectionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *ProtocolInjectionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *ProtocolInjectionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *ProtocolInjectionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *ProtocolInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *ProtocolInjectionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *ProtocolInjectionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *ProtocolInjectionDetector) GetDependencies() []string         { return []string{} }
func (d *ProtocolInjectionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *ProtocolInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *ProtocolInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *ProtocolInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *ProtocolInjectionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 2
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *ProtocolInjectionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *ProtocolInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.httpProtocolPayloads) == 0 {
		return fmt.Errorf("HTTP协议载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *ProtocolInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 协议注入检测适用于有协议处理功能的Web应用
	// 检查是否有协议相关的特征
	if d.hasProtocolFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于协议相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	protocolKeywords := []string{
		"protocol", "http", "smtp", "ftp", "ldap", "dns", "ssh",
		"mail", "email", "file", "directory", "proxy", "redirect",
		"forward", "tunnel", "bridge", "gateway", "endpoint",
		"协议", "邮件", "文件", "目录", "代理", "重定向", "转发",
	}

	for _, keyword := range protocolKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 协议注入是通用Web漏洞，默认适用于所有Web目标
}

// hasProtocolFeatures 检查是否有协议功能
func (d *ProtocolInjectionDetector) hasProtocolFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有协议相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "server" &&
			(strings.Contains(valueLower, "apache") ||
				strings.Contains(valueLower, "nginx") ||
				strings.Contains(valueLower, "iis") ||
				strings.Contains(valueLower, "tomcat")) {
			return true
		}

		if strings.Contains(keyLower, "proxy") ||
			strings.Contains(keyLower, "forward") ||
			strings.Contains(keyLower, "redirect") ||
			strings.Contains(valueLower, "proxy") ||
			strings.Contains(valueLower, "forward") ||
			strings.Contains(valueLower, "redirect") {
			return true
		}
	}

	// 检查技术栈中是否有协议相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		protocolTechnologies := []string{
			"apache", "nginx", "iis", "tomcat", "jetty", "express",
			"spring", "django", "flask", "laravel", "symfony",
			"proxy", "gateway", "load balancer", "reverse proxy",
			"协议", "代理", "网关", "负载均衡", "反向代理",
		}

		for _, protocolTech := range protocolTechnologies {
			if strings.Contains(techNameLower, protocolTech) {
				return true
			}
		}
	}

	// 检查链接中是否有协议相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "protocol") ||
			strings.Contains(linkURLLower, "proxy") ||
			strings.Contains(linkURLLower, "redirect") ||
			strings.Contains(linkTextLower, "protocol") ||
			strings.Contains(linkTextLower, "proxy") ||
			strings.Contains(linkTextLower, "协议") ||
			strings.Contains(linkTextLower, "代理") {
			return true
		}
	}

	// 检查表单中是否有协议相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			protocolFields := []string{
				"protocol", "http", "smtp", "ftp", "ldap", "dns", "ssh",
				"mail", "email", "url", "uri", "host", "server", "proxy",
				"redirect", "forward", "endpoint", "gateway", "bridge",
				"协议", "邮件", "地址", "主机", "服务器", "代理", "重定向",
			}

			for _, protocolField := range protocolFields {
				if strings.Contains(fieldNameLower, protocolField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *ProtocolInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种协议注入检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. HTTP协议注入检测
	httpEvidence, httpConfidence, httpPayload, httpRequest, httpResponse := d.detectHTTPProtocolInjection(ctx, target)
	if httpConfidence > maxConfidence {
		maxConfidence = httpConfidence
		vulnerablePayload = httpPayload
		vulnerableRequest = httpRequest
		vulnerableResponse = httpResponse
	}
	evidence = append(evidence, httpEvidence...)

	// 2. SMTP协议注入检测
	smtpEvidence, smtpConfidence, smtpPayload, smtpRequest, smtpResponse := d.detectSMTPProtocolInjection(ctx, target)
	if smtpConfidence > maxConfidence {
		maxConfidence = smtpConfidence
		vulnerablePayload = smtpPayload
		vulnerableRequest = smtpRequest
		vulnerableResponse = smtpResponse
	}
	evidence = append(evidence, smtpEvidence...)

	// 3. FTP协议注入检测
	ftpEvidence, ftpConfidence, ftpPayload, ftpRequest, ftpResponse := d.detectFTPProtocolInjection(ctx, target)
	if ftpConfidence > maxConfidence {
		maxConfidence = ftpConfidence
		vulnerablePayload = ftpPayload
		vulnerableRequest = ftpRequest
		vulnerableResponse = ftpResponse
	}
	evidence = append(evidence, ftpEvidence...)

	// 4. LDAP协议注入检测
	ldapEvidence, ldapConfidence, ldapPayload, ldapRequest, ldapResponse := d.detectLDAPProtocolInjection(ctx, target)
	if ldapConfidence > maxConfidence {
		maxConfidence = ldapConfidence
		vulnerablePayload = ldapPayload
		vulnerableRequest = ldapRequest
		vulnerableResponse = ldapResponse
	}
	evidence = append(evidence, ldapEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "协议注入漏洞",
		Description:       "检测到协议注入漏洞，攻击者可能通过注入恶意协议数据来绕过安全控制、执行未授权操作或获取敏感信息",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "验证和过滤协议输入，使用安全的协议处理函数，实施协议级别的访问控制",
		References:        []string{"https://owasp.org/www-community/attacks/Protocol_Injection", "https://cwe.mitre.org/data/definitions/74.html", "https://cwe.mitre.org/data/definitions/77.html"},
		Tags:              []string{"protocol", "injection", "http", "smtp", "ftp", "ldap", "dns", "ssh", "web", "network"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *ProtocolInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"http-protocol",
		"smtp-protocol",
		"ftp-protocol",
		"ldap-protocol",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyProtocolMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了协议注入漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "protocol-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用协议验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *ProtocolInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("protocol_injection_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *ProtocolInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (协议注入通常是高风险漏洞)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyProtocolMethod 验证协议方法
func (d *ProtocolInjectionDetector) verifyProtocolMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "http-protocol":
		return d.verifyHTTPProtocol(ctx, target)
	case "smtp-protocol":
		return d.verifySMTPProtocol(ctx, target)
	case "ftp-protocol":
		return d.verifyFTPProtocol(ctx, target)
	case "ldap-protocol":
		return d.verifyLDAPProtocol(ctx, target)
	default:
		return 0.0
	}
}

// verifyHTTPProtocol 验证HTTP协议
func (d *ProtocolInjectionDetector) verifyHTTPProtocol(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的HTTP协议验证
	if d.hasProtocolFeatures(target) {
		return 0.8 // 有协议特征的目标可能有HTTP协议注入
	}
	return 0.4
}

// verifySMTPProtocol 验证SMTP协议
func (d *ProtocolInjectionDetector) verifySMTPProtocol(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的SMTP协议验证
	if d.hasProtocolFeatures(target) {
		return 0.7 // 有协议特征的目标可能有SMTP协议注入
	}
	return 0.3
}

// verifyFTPProtocol 验证FTP协议
func (d *ProtocolInjectionDetector) verifyFTPProtocol(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的FTP协议验证
	if d.hasProtocolFeatures(target) {
		return 0.6 // 有协议特征的目标可能有FTP协议注入
	}
	return 0.2
}

// verifyLDAPProtocol 验证LDAP协议
func (d *ProtocolInjectionDetector) verifyLDAPProtocol(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的LDAP协议验证
	if d.hasProtocolFeatures(target) {
		return 0.6 // 有协议特征的目标可能有LDAP协议注入
	}
	return 0.2
}

// initializeHTTPProtocolPayloads 初始化HTTP协议注入载荷列表
func (d *ProtocolInjectionDetector) initializeHTTPProtocolPayloads() {
	d.httpProtocolPayloads = []string{
		// HTTP头部注入载荷
		"\r\nSet-Cookie: injected=true",
		"\r\nLocation: http://evil.com",
		"\r\nContent-Type: text/html\r\n\r\n<script>alert('XSS')</script>",
		"\r\nX-Injected-Header: true",
		"\r\nCache-Control: no-cache\r\n\r\nHTTP/1.1 200 OK",

		// CRLF注入载荷
		"%0d%0aSet-Cookie: injected=true",
		"%0d%0aLocation: http://evil.com",
		"%0d%0aContent-Type: text/html%0d%0a%0d%0a<script>alert('XSS')</script>",
		"%0a%0dSet-Cookie: injected=true",
		"%0a%0dLocation: http://evil.com",

		// HTTP响应分割载荷
		"\r\n\r\nHTTP/1.1 200 OK\r\nContent-Type: text/html\r\n\r\n<html><body>Injected</body></html>",
		"%0d%0a%0d%0aHTTP/1.1 200 OK%0d%0aContent-Type: text/html%0d%0a%0d%0a<html><body>Injected</body></html>",
		"\n\nHTTP/1.1 200 OK\nContent-Type: text/html\n\n<html><body>Injected</body></html>",

		// HTTP请求走私载荷
		"GET /admin HTTP/1.1\r\nHost: localhost\r\n\r\n",
		"POST /admin HTTP/1.1\r\nHost: localhost\r\nContent-Length: 0\r\n\r\n",
		"Transfer-Encoding: chunked\r\n\r\n0\r\n\r\nGET /admin HTTP/1.1\r\nHost: localhost\r\n\r\n",

		// HTTP方法注入载荷
		"GET\r\nHost: evil.com\r\n\r\n",
		"POST\r\nHost: evil.com\r\n\r\n",
		"PUT\r\nHost: evil.com\r\n\r\n",
		"DELETE\r\nHost: evil.com\r\n\r\n",

		// 中文HTTP协议载荷
		"\r\n注入头部: 测试",
		"%0d%0a注入头部: 测试",
		"\r\n\r\nHTTP/1.1 200 OK\r\n内容类型: text/html\r\n\r\n<html><body>注入内容</body></html>",
		"GET /管理员 HTTP/1.1\r\nHost: localhost\r\n\r\n",
	}
}

// initializeSMTPProtocolPayloads 初始化SMTP协议注入载荷列表
func (d *ProtocolInjectionDetector) initializeSMTPProtocolPayloads() {
	d.smtpProtocolPayloads = []string{
		// SMTP命令注入载荷
		"\r\nMAIL FROM: <<EMAIL>>",
		"\r\nRCPT TO: <<EMAIL>>",
		"\r\nDATA\r\nSubject: Injected Email\r\n\r\nThis is an injected email.\r\n.",
		"\r\nQUIT",
		"\r\nHELO evil.com",

		// SMTP头部注入载荷
		"%0d%0aMAIL FROM: <<EMAIL>>",
		"%0d%0aRCPT TO: <<EMAIL>>",
		"%0d%0aDATA%0d%0aSubject: Injected Email%0d%0a%0d%0aThis is an injected email.%0d%0a.",
		"%0a%0dMAIL FROM: <<EMAIL>>",
		"%0a%0dRCPT TO: <<EMAIL>>",

		// SMTP邮件头注入载荷
		"\nBcc: <EMAIL>",
		"\nCc: <EMAIL>",
		"\nSubject: Injected Subject",
		"\nFrom: <EMAIL>",
		"\nTo: <EMAIL>",

		// SMTP内容注入载荷
		"\r\n\r\nThis is injected content in the email body.",
		"%0d%0a%0d%0aThis is injected content in the email body.",
		"\n\nThis is injected content in the email body.",

		// SMTP认证绕过载荷
		"\r\nAUTH PLAIN dGVzdAB0ZXN0AHRlc3Q=",
		"\r\nAUTH LOGIN",
		"\r\nSTARTTLS",

		// 中文SMTP协议载荷
		"\r\n发件人: <攻击者@evil.com>",
		"\r\n收件人: <受害者@target.com>",
		"\r\n主题: 注入邮件",
		"\r\n\r\n这是注入的邮件内容。",
	}
}

// initializeFTPProtocolPayloads 初始化FTP协议注入载荷列表
func (d *ProtocolInjectionDetector) initializeFTPProtocolPayloads() {
	d.ftpProtocolPayloads = []string{
		// FTP命令注入载荷
		"\r\nUSER anonymous",
		"\r\nPASS guest",
		"\r\nLIST",
		"\r\nPWD",
		"\r\nCWD /",

		// FTP路径遍历载荷
		"\r\nRETR ../../../etc/passwd",
		"\r\nSTOR ../../../tmp/injected.txt",
		"\r\nCWD ../../../",
		"\r\nLIST ../../../",
		"\r\nRETR ..\\..\\..\\windows\\system32\\config\\sam",

		// FTP端口注入载荷
		"\r\nPORT 127,0,0,1,0,22",
		"\r\nPASV",
		"\r\nEPRT |1|127.0.0.1|22|",
		"\r\nEPSV",

		// FTP数据注入载荷
		"%0d%0aUSER anonymous",
		"%0d%0aPASS guest",
		"%0d%0aLIST",
		"%0a%0dUSER anonymous",
		"%0a%0aPASS guest",

		// FTP认证绕过载荷
		"\r\nUSER root",
		"\r\nPASS admin",
		"\r\nUSER ftp",
		"\r\nPASS ftp",

		// 中文FTP协议载荷
		"\r\n用户 匿名",
		"\r\n密码 访客",
		"\r\n列表",
		"\r\n目录 /",
		"\r\n获取 ../../../etc/passwd",
	}
}

// initializeLDAPProtocolPayloads 初始化LDAP协议注入载荷列表
func (d *ProtocolInjectionDetector) initializeLDAPProtocolPayloads() {
	d.ldapProtocolPayloads = []string{
		// LDAP过滤器注入载荷
		"*)(uid=*))(|(uid=*",
		"*)(|(password=*))",
		"admin)(&(password=*))",
		"*))%00",
		"*()|%00",

		// LDAP搜索注入载荷
		"(&(objectClass=*)(uid=*))",
		"(|(uid=*)(cn=*))",
		"(&(!(uid=*))(password=*))",
		"(objectClass=*)",
		"(uid=*)",

		// LDAP DN注入载荷
		"cn=admin,dc=example,dc=com",
		"uid=*,ou=people,dc=example,dc=com",
		"ou=*,dc=example,dc=com",
		"dc=*",

		// LDAP属性注入载荷
		"userPassword",
		"sambaNTPassword",
		"sambaLMPassword",
		"unicodePwd",
		"userAccountControl",

		// LDAP操作注入载荷
		"add: objectClass",
		"replace: userPassword",
		"delete: uid",
		"modify: cn",

		// 中文LDAP协议载荷
		"用户=*",
		"密码=*",
		"组织=*",
		"部门=*",
		"姓名=*",
	}
}

// initializeDNSProtocolPayloads 初始化DNS协议注入载荷列表
func (d *ProtocolInjectionDetector) initializeDNSProtocolPayloads() {
	d.dnsProtocolPayloads = []string{
		// DNS查询注入载荷
		"evil.com",
		"*.evil.com",
		"subdomain.evil.com",
		"127.0.0.1.evil.com",
		"localhost.evil.com",

		// DNS记录类型注入载荷
		"A evil.com",
		"AAAA evil.com",
		"CNAME evil.com",
		"MX evil.com",
		"TXT evil.com",

		// DNS缓存投毒载荷
		"cache.evil.com",
		"poison.evil.com",
		"spoof.evil.com",
		"fake.evil.com",

		// DNS隧道载荷
		"tunnel.evil.com",
		"exfiltrate.evil.com",
		"data.evil.com",
		"command.evil.com",

		// 中文DNS协议载荷
		"恶意.com",
		"攻击.com",
		"隧道.com",
		"数据.com",
		"命令.com",
	}
}

// initializeSSHProtocolPayloads 初始化SSH协议注入载荷列表
func (d *ProtocolInjectionDetector) initializeSSHProtocolPayloads() {
	d.sshProtocolPayloads = []string{
		// SSH命令注入载荷
		"SSH-2.0-OpenSSH_7.4",
		"SSH-1.99-OpenSSH_3.9p1",
		"SSH-2.0-libssh_0.6.3",
		"SSH-2.0-PuTTY_Release_0.70",

		// SSH认证注入载荷
		"root",
		"admin",
		"user",
		"guest",
		"test",

		// SSH密钥注入载荷
		"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ",
		"ssh-dss AAAAB3NzaC1kc3MAAACBA",
		"ecdsa-sha2-nistp256 AAAAE2VjZHNh",
		"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5",

		// SSH隧道注入载荷
		"-L 8080:localhost:80",
		"-R 8080:localhost:80",
		"-D 1080",
		"-N -f",

		// 中文SSH协议载荷
		"用户名",
		"密码",
		"密钥",
		"隧道",
		"转发",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *ProtocolInjectionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 协议相关参数
		"protocol", "proto", "scheme", "method", "type", "format", "encoding",
		"http", "https", "smtp", "ftp", "ldap", "dns", "ssh", "ssl", "tls",
		"url", "uri", "link", "href", "src", "action", "target", "destination",

		// 网络相关参数
		"host", "hostname", "server", "domain", "subdomain", "ip", "address",
		"port", "endpoint", "gateway", "proxy", "tunnel", "bridge", "relay",
		"redirect", "forward", "location", "referer", "origin", "referrer",

		// 邮件相关参数
		"email", "mail", "from", "to", "cc", "bcc", "subject", "message",
		"sender", "recipient", "reply", "bounce", "envelope", "header",
		"smtp_host", "smtp_port", "smtp_user", "smtp_pass", "mailserver",

		// 文件传输相关参数
		"file", "filename", "path", "filepath", "directory", "folder", "upload",
		"download", "transfer", "ftp_host", "ftp_port", "ftp_user", "ftp_pass",
		"sftp", "ftps", "scp", "rsync", "sync", "backup", "archive",

		// 认证相关参数
		"user", "username", "login", "account", "uid", "userid", "identity",
		"password", "pass", "pwd", "secret", "token", "key", "auth", "credential",
		"session", "cookie", "ticket", "certificate", "signature", "hash",

		// 查询相关参数
		"query", "search", "filter", "criteria", "condition", "expression",
		"ldap_query", "ldap_filter", "ldap_base", "ldap_scope", "ldap_attr",
		"dns_query", "dns_type", "dns_class", "dns_name", "dns_record",

		// 数据相关参数
		"data", "content", "body", "payload", "input", "output", "response",
		"request", "packet", "frame", "stream", "buffer", "chunk", "block",
		"text", "html", "xml", "json", "csv", "binary", "base64", "hex",

		// 配置相关参数
		"config", "setting", "option", "parameter", "property", "attribute",
		"value", "field", "variable", "constant", "flag", "switch", "mode",
		"timeout", "retry", "delay", "interval", "limit", "size", "length",

		// 中文参数
		"协议", "方案", "方法", "类型", "格式", "编码", "地址", "主机", "服务器",
		"域名", "子域名", "端口", "网关", "代理", "隧道", "桥接", "中继",
		"重定向", "转发", "位置", "来源", "邮件", "发件人", "收件人", "主题",
		"消息", "文件", "路径", "目录", "上传", "下载", "传输", "用户", "用户名",
		"密码", "认证", "会话", "查询", "搜索", "过滤", "条件", "数据", "内容",
		"请求", "响应", "配置", "设置", "选项", "参数", "属性", "值", "字段",
	}
}

// initializePatterns 初始化检测模式
func (d *ProtocolInjectionDetector) initializePatterns() {
	// HTTP协议模式 - 检测HTTP协议注入相关的响应内容
	httpPatternStrings := []string{
		// HTTP头部注入特征
		`(?i)set-cookie:.*injected`,
		`(?i)location:.*evil\.com`,
		`(?i)x-injected-header:`,
		`(?i)cache-control:.*no-cache`,
		`(?i)content-type:.*text/html`,

		// CRLF注入特征
		`(?i)%0d%0a`,
		`(?i)%0a%0d`,
		`(?i)\r\n`,
		`(?i)\n\r`,
		`(?i)\\r\\n`,

		// HTTP响应分割特征
		`(?i)http/1\.[01]\s+200\s+ok`,
		`(?i)http/1\.[01]\s+302\s+found`,
		`(?i)http/1\.[01]\s+404\s+not\s+found`,
		`(?i)content-length:\s*\d+`,
		`(?i)transfer-encoding:\s*chunked`,

		// 中文HTTP协议模式
		`(?i)(注入|攻击).*头部`,
		`(?i)(恶意|非法).*请求`,
		`(?i)(协议|HTTP).*注入`,
		`(?i)(响应|内容).*分割`,
	}

	d.httpPatterns = make([]*regexp.Regexp, 0, len(httpPatternStrings))
	for _, pattern := range httpPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.httpPatterns = append(d.httpPatterns, compiled)
		}
	}

	// SMTP协议模式 - 检测SMTP协议注入相关的响应内容
	smtpPatternStrings := []string{
		// SMTP命令特征
		`(?i)mail\s+from:`,
		`(?i)rcpt\s+to:`,
		`(?i)data`,
		`(?i)quit`,
		`(?i)helo\s+`,

		// SMTP响应特征
		`(?i)220\s+.*smtp`,
		`(?i)250\s+ok`,
		`(?i)354\s+start\s+mail`,
		`(?i)221\s+bye`,
		`(?i)550\s+.*rejected`,

		// SMTP头部注入特征
		`(?i)subject:.*injected`,
		`(?i)from:.*evil\.com`,
		`(?i)to:.*victim`,
		`(?i)bcc:.*attacker`,
		`(?i)cc:.*attacker`,

		// 中文SMTP协议模式
		`(?i)(邮件|SMTP).*注入`,
		`(?i)(发件人|收件人).*攻击`,
		`(?i)(主题|内容).*注入`,
		`(?i)(邮件|消息).*伪造`,
	}

	d.smtpPatterns = make([]*regexp.Regexp, 0, len(smtpPatternStrings))
	for _, pattern := range smtpPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.smtpPatterns = append(d.smtpPatterns, compiled)
		}
	}

	// FTP协议模式 - 检测FTP协议注入相关的响应内容
	ftpPatternStrings := []string{
		// FTP命令特征
		`(?i)user\s+`,
		`(?i)pass\s+`,
		`(?i)list`,
		`(?i)pwd`,
		`(?i)cwd\s+`,

		// FTP响应特征
		`(?i)220\s+.*ftp`,
		`(?i)230\s+user\s+logged\s+in`,
		`(?i)331\s+password\s+required`,
		`(?i)226\s+transfer\s+complete`,
		`(?i)550\s+.*not\s+found`,

		// FTP路径遍历特征
		`(?i)\.\.\/`,
		`(?i)\.\.\\`,
		`(?i)\/etc\/passwd`,
		`(?i)\\windows\\system32`,
		`(?i)\/tmp\/`,

		// 中文FTP协议模式
		`(?i)(文件|FTP).*注入`,
		`(?i)(路径|目录).*遍历`,
		`(?i)(上传|下载).*攻击`,
		`(?i)(传输|文件).*注入`,
	}

	d.ftpPatterns = make([]*regexp.Regexp, 0, len(ftpPatternStrings))
	for _, pattern := range ftpPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.ftpPatterns = append(d.ftpPatterns, compiled)
		}
	}

	// LDAP协议模式 - 检测LDAP协议注入相关的响应内容
	ldapPatternStrings := []string{
		// LDAP过滤器特征
		`(?i)\*\)\(`,
		`(?i)\(\|\(`,
		`(?i)\(&\(`,
		`(?i)\(!\(`,
		`(?i)objectclass=`,

		// LDAP属性特征
		`(?i)userpassword`,
		`(?i)sambantpassword`,
		`(?i)unicodepwd`,
		`(?i)useraccountcontrol`,
		`(?i)distinguishedname`,

		// LDAP错误特征
		`(?i)ldap.*error`,
		`(?i)invalid.*filter`,
		`(?i)bad.*search`,
		`(?i)syntax.*error`,
		`(?i)authentication.*failed`,

		// 中文LDAP协议模式
		`(?i)(LDAP|目录).*注入`,
		`(?i)(过滤器|查询).*注入`,
		`(?i)(认证|授权).*绕过`,
		`(?i)(用户|密码).*泄露`,
	}

	d.ldapPatterns = make([]*regexp.Regexp, 0, len(ldapPatternStrings))
	for _, pattern := range ldapPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.ldapPatterns = append(d.ldapPatterns, compiled)
		}
	}

	// DNS协议模式 - 检测DNS协议注入相关的响应内容
	dnsPatternStrings := []string{
		// DNS查询特征
		`(?i)nslookup`,
		`(?i)dig\s+`,
		`(?i)host\s+`,
		`(?i)dns.*query`,
		`(?i)resolve.*`,

		// DNS记录特征
		`(?i)a\s+record`,
		`(?i)aaaa\s+record`,
		`(?i)cname\s+record`,
		`(?i)mx\s+record`,
		`(?i)txt\s+record`,

		// DNS错误特征
		`(?i)nxdomain`,
		`(?i)servfail`,
		`(?i)refused`,
		`(?i)timeout`,
		`(?i)no.*answer`,

		// 中文DNS协议模式
		`(?i)(DNS|域名).*注入`,
		`(?i)(查询|解析).*攻击`,
		`(?i)(缓存|投毒).*攻击`,
		`(?i)(隧道|泄露).*数据`,
	}

	d.dnsPatterns = make([]*regexp.Regexp, 0, len(dnsPatternStrings))
	for _, pattern := range dnsPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.dnsPatterns = append(d.dnsPatterns, compiled)
		}
	}

	// SSH协议模式 - 检测SSH协议注入相关的响应内容
	sshPatternStrings := []string{
		// SSH版本特征
		`(?i)ssh-[12]\.[0-9]`,
		`(?i)openssh`,
		`(?i)libssh`,
		`(?i)putty`,
		`(?i)dropbear`,

		// SSH认证特征
		`(?i)password.*authentication`,
		`(?i)public.*key.*authentication`,
		`(?i)keyboard.*interactive`,
		`(?i)gssapi`,
		`(?i)hostbased`,

		// SSH错误特征
		`(?i)permission.*denied`,
		`(?i)authentication.*failed`,
		`(?i)connection.*refused`,
		`(?i)host.*key.*verification`,
		`(?i)protocol.*mismatch`,

		// 中文SSH协议模式
		`(?i)(SSH|远程).*注入`,
		`(?i)(认证|登录).*绕过`,
		`(?i)(隧道|转发).*攻击`,
		`(?i)(密钥|证书).*泄露`,
	}

	d.sshPatterns = make([]*regexp.Regexp, 0, len(sshPatternStrings))
	for _, pattern := range sshPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.sshPatterns = append(d.sshPatterns, compiled)
		}
	}

	// 错误模式 - 检测协议注入相关的错误信息
	errorPatternStrings := []string{
		// 通用协议错误
		`(?i)(protocol|network).*error`,
		`(?i)(invalid|illegal).*protocol`,
		`(?i)(malformed|corrupt).*request`,
		`(?i)(injection|attack).*detected`,
		`(?i)security.*violation`,

		// 连接错误
		`(?i)connection.*refused`,
		`(?i)connection.*timeout`,
		`(?i)connection.*reset`,
		`(?i)connection.*closed`,
		`(?i)network.*unreachable`,

		// 认证错误
		`(?i)authentication.*failed`,
		`(?i)authorization.*denied`,
		`(?i)access.*forbidden`,
		`(?i)permission.*denied`,
		`(?i)credential.*invalid`,

		// 中文错误模式
		`(?i)(协议|网络).*错误`,
		`(?i)(无效|非法).*协议`,
		`(?i)(恶意|可疑).*请求`,
		`(?i)(注入|攻击).*检测`,
		`(?i)安全.*违规`,
		`(?i)连接.*拒绝`,
		`(?i)认证.*失败`,
		`(?i)授权.*拒绝`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测协议注入成功的响应特征
	responsePatternStrings := []string{
		// 成功注入指示器
		`(?i)(injection|exploit).*success`,
		`(?i)(protocol|header).*injected`,
		`(?i)(command|request).*executed`,
		`(?i)(bypass|circumvent).*security`,
		`(?i)(access|privilege).*gained`,

		// 协议特定成功指示器
		`(?i)(http|smtp|ftp|ldap).*injection`,
		`(?i)(crlf|response).*splitting`,
		`(?i)(header|mail).*injection`,
		`(?i)(directory|path).*traversal`,
		`(?i)(filter|query).*injection`,

		// 数据泄露指示器
		`(?i)(data|information).*leaked`,
		`(?i)(credential|password).*exposed`,
		`(?i)(file|directory).*accessed`,
		`(?i)(system|server).*information`,
		`(?i)(configuration|setting).*disclosed`,

		// 控制获取指示器
		`(?i)(control|command).*gained`,
		`(?i)(shell|terminal).*access`,
		`(?i)(admin|root).*privilege`,
		`(?i)(system|server).*compromised`,
		`(?i)(backdoor|tunnel).*established`,

		// 中文响应模式
		`(?i)(注入|利用).*成功`,
		`(?i)(协议|头部).*注入`,
		`(?i)(命令|请求).*执行`,
		`(?i)(绕过|规避).*安全`,
		`(?i)(访问|权限).*获取`,
		`(?i)(数据|信息).*泄露`,
		`(?i)(凭据|密码).*暴露`,
		`(?i)(文件|目录).*访问`,
		`(?i)(控制|命令).*获取`,
		`(?i)(系统|服务器).*妥协`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
