package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectAPIAuthBypass 检测API认证绕过
func (d *APISecurityDetector) detectAPIAuthBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试API端点的认证绕过
	for _, endpoint := range d.apiEndpoints {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造API URL
		apiURL := d.buildAPIURL(target.URL, endpoint)

		// 测试不同的认证绕过载荷
		for _, payload := range d.authBypassPayloads {
			// 发送认证绕过请求
			resp, err := d.sendAuthBypassRequest(ctx, apiURL, payload)
			if err != nil {
				continue
			}

			// 检查认证绕过响应
			confidence := d.checkAuthBypassResponse(resp, payload)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("API认证绕过: %s", payload)
				vulnerableRequest = apiURL
				vulnerableResponse = resp
			}

			if confidence > 0.7 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "api-auth-bypass",
					Description: fmt.Sprintf("发现API认证绕过: %s (置信度: %.2f)", payload, confidence),
					Content:     d.extractAPIEvidence(resp, "api-auth-bypass"),
					Location:    apiURL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 400)
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 600)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectAPIDataLeakage 检测API数据泄露
func (d *APISecurityDetector) detectAPIDataLeakage(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试API端点的数据泄露
	for _, endpoint := range d.apiEndpoints {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造API URL
		apiURL := d.buildAPIURL(target.URL, endpoint)

		// 测试不同的HTTP方法
		for _, method := range d.apiMethods {
			// 发送数据泄露测试请求
			resp, err := d.sendDataLeakageRequest(ctx, apiURL, method)
			if err != nil {
				continue
			}

			// 检查数据泄露响应
			confidence := d.checkDataLeakageResponse(resp, method, endpoint)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("API数据泄露: %s %s", method, endpoint)
				vulnerableRequest = apiURL
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "api-data-leakage",
					Description: fmt.Sprintf("发现API数据泄露: %s %s (置信度: %.2f)", method, endpoint, confidence),
					Content:     d.extractAPIEvidence(resp, "api-data-leakage"),
					Location:    apiURL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 300)
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 500)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectAPIRateLimitBypass 检测API速率限制绕过
func (d *APISecurityDetector) detectAPIRateLimitBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试API端点的速率限制绕过
	testEndpoints := []string{"/api", "/api/v1", "/api/users", "/api/auth"}

	for _, endpoint := range testEndpoints {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造API URL
		apiURL := d.buildAPIURL(target.URL, endpoint)

		// 测试速率限制绕过方法
		bypassMethods := []string{
			"X-Forwarded-For",
			"X-Real-IP",
			"X-Originating-IP",
			"X-Remote-IP",
			"User-Agent",
		}

		for _, bypassMethod := range bypassMethods {
			// 发送速率限制绕过请求
			resp, err := d.sendRateLimitBypassRequest(ctx, apiURL, bypassMethod)
			if err != nil {
				continue
			}

			// 检查速率限制绕过响应
			confidence := d.checkRateLimitBypassResponse(resp, bypassMethod)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("API速率限制绕过: %s", bypassMethod)
				vulnerableRequest = apiURL
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "api-rate-limit-bypass",
					Description: fmt.Sprintf("发现API速率限制绕过: %s (置信度: %.2f)", bypassMethod, confidence),
					Content:     d.extractAPIEvidence(resp, "api-rate-limit-bypass"),
					Location:    apiURL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 800)
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 1000)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectGraphQLInjection 检测GraphQL注入
func (d *APISecurityDetector) detectGraphQLInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试GraphQL端点
	graphqlEndpoints := []string{"/graphql", "/api/graphql", "/v1/graphql", "/query"}

	for _, endpoint := range graphqlEndpoints {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造GraphQL URL
		graphqlURL := d.buildAPIURL(target.URL, endpoint)

		// 测试GraphQL查询
		for _, query := range d.graphqlQueries {
			// 发送GraphQL注入请求
			resp, err := d.sendGraphQLRequest(ctx, graphqlURL, query)
			if err != nil {
				continue
			}

			// 检查GraphQL注入响应
			confidence := d.checkGraphQLInjectionResponse(resp, query)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("GraphQL注入: %s", query)
				vulnerableRequest = graphqlURL
				vulnerableResponse = resp
			}

			if confidence > 0.7 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "graphql-injection",
					Description: fmt.Sprintf("发现GraphQL注入: %s (置信度: %.2f)", query, confidence),
					Content:     d.extractAPIEvidence(resp, "graphql-injection"),
					Location:    graphqlURL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 500)
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 700)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendAuthBypassRequest 发送认证绕过请求
func (d *APISecurityDetector) sendAuthBypassRequest(ctx context.Context, targetURL, payload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	// 根据载荷类型设置认证头部
	if strings.HasPrefix(payload, "Bearer") {
		req.Header.Set("Authorization", payload)
	} else if strings.HasPrefix(payload, "Basic") {
		req.Header.Set("Authorization", payload)
	} else if strings.Contains(payload, "api_key") {
		req.Header.Set("X-API-Key", strings.TrimPrefix(payload, "api_key="))
	} else if strings.Contains(payload, "session_id") {
		req.Header.Set("Cookie", payload)
	} else if strings.HasPrefix(payload, "X-") {
		parts := strings.Split(payload, ": ")
		if len(parts) == 2 {
			req.Header.Set(parts[0], parts[1])
		}
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendDataLeakageRequest 发送数据泄露测试请求
func (d *APISecurityDetector) sendDataLeakageRequest(ctx context.Context, targetURL, method string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, method, targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendRateLimitBypassRequest 发送速率限制绕过请求
func (d *APISecurityDetector) sendRateLimitBypassRequest(ctx context.Context, targetURL, bypassMethod string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	// 根据绕过方法设置特殊头部
	switch bypassMethod {
	case "X-Forwarded-For":
		req.Header.Set("X-Forwarded-For", "127.0.0.1")
	case "X-Real-IP":
		req.Header.Set("X-Real-IP", "127.0.0.1")
	case "X-Originating-IP":
		req.Header.Set("X-Originating-IP", "127.0.0.1")
	case "X-Remote-IP":
		req.Header.Set("X-Remote-IP", "127.0.0.1")
	case "User-Agent":
		req.Header.Set("User-Agent", "RateLimitBypass/1.0")
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendGraphQLRequest 发送GraphQL请求
func (d *APISecurityDetector) sendGraphQLRequest(ctx context.Context, targetURL, query string) (string, error) {
	// 构造GraphQL请求数据
	graphqlData := fmt.Sprintf(`{"query": "%s"}`, strings.ReplaceAll(query, `"`, `\"`))

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(graphqlData))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// buildAPIURL 构造API URL
func (d *APISecurityDetector) buildAPIURL(baseURL, endpoint string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + endpoint
	}

	// 替换路径
	parsedURL.Path = endpoint

	return parsedURL.String()
}

// checkAuthBypassResponse 检查认证绕过响应
func (d *APISecurityDetector) checkAuthBypassResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.4
	} else if strings.Contains(response, "status: 201") {
		confidence += 0.5 // 创建成功更可疑
	}

	// 检查认证绕过成功指示器
	authBypassIndicators := []string{
		"access granted", "authentication successful", "login successful",
		"authorized", "authenticated", "welcome", "dashboard",
		"user data", "profile", "account", "admin panel",
		"访问授权", "认证成功", "登录成功", "已授权", "已认证", "欢迎", "仪表板",
	}

	for _, indicator := range authBypassIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	// 检查API模式匹配
	for _, pattern := range d.apiPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 根据载荷类型调整置信度
	if payload == "" || payload == " " || payload == "null" {
		if confidence > 0.3 {
			confidence += 0.3 // 空认证更危险
		}
	} else if strings.Contains(payload, "admin") {
		if confidence > 0.3 {
			confidence += 0.2 // 管理员相关载荷更危险
		}
	}

	return confidence
}

// checkDataLeakageResponse 检查数据泄露响应
func (d *APISecurityDetector) checkDataLeakageResponse(response, method, endpoint string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查敏感数据类型
	for _, dataType := range d.sensitiveDataTypes {
		if strings.Contains(response, strings.ToLower(dataType)) {
			confidence += 0.3
			break
		}
	}

	// 检查数据泄露模式匹配
	for _, pattern := range d.dataLeakagePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查API模式匹配
	for _, pattern := range d.apiPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查数据结构指示器
	dataStructureIndicators := []string{
		"[{", "}]", "\"id\":", "\"name\":", "\"email\":", "\"password\":",
		"\"users\":", "\"data\":", "\"records\":", "\"items\":",
		"用户", "数据", "记录", "项目",
	}

	for _, indicator := range dataStructureIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.2
			break
		}
	}

	// 根据HTTP方法调整置信度
	if method == "GET" && confidence > 0.3 {
		confidence += 0.1 // GET方法数据泄露更常见
	}

	return confidence
}

// checkRateLimitBypassResponse 检查速率限制绕过响应
func (d *APISecurityDetector) checkRateLimitBypassResponse(response, bypassMethod string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.4
	} else if strings.Contains(response, "status: 429") {
		// 仍然返回429表示绕过失败
		return 0.1
	}

	// 检查速率限制绕过成功指示器
	rateLimitBypassIndicators := []string{
		"rate limit bypassed", "quota bypassed", "limit exceeded",
		"too many requests", "rate limit", "quota exceeded",
		"request successful", "access granted",
		"速率限制绕过", "配额绕过", "限制超出", "请求过多", "速率限制", "配额超出",
	}

	for _, indicator := range rateLimitBypassIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.3
			break
		}
	}

	// 检查API模式匹配
	for _, pattern := range d.apiPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 根据绕过方法调整置信度
	if strings.Contains(bypassMethod, "IP") && confidence > 0.3 {
		confidence += 0.2 // IP相关绕过更有效
	}

	return confidence
}

// checkGraphQLInjectionResponse 检查GraphQL注入响应
func (d *APISecurityDetector) checkGraphQLInjectionResponse(response, query string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查GraphQL响应指示器
	graphqlIndicators := []string{
		"\"data\":", "\"errors\":", "\"extensions\":",
		"graphql", "query", "mutation", "subscription",
		"__schema", "__type", "__typename",
		"查询", "变更", "订阅", "模式", "类型",
	}

	for _, indicator := range graphqlIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.3
			break
		}
	}

	// 检查GraphQL模式匹配
	for _, pattern := range d.graphqlPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查注入成功指示器
	injectionIndicators := []string{
		"syntax error", "parse error", "validation error",
		"field not found", "type not found", "resolver error",
		"database error", "sql error", "exception",
		"语法错误", "解析错误", "验证错误", "字段未找到", "类型未找到",
	}

	for _, indicator := range injectionIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
			break
		}
	}

	// 根据查询类型调整置信度
	if strings.Contains(query, "__schema") || strings.Contains(query, "__type") {
		if confidence > 0.3 {
			confidence += 0.3 // 内省查询更危险
		}
	} else if strings.Contains(query, "DROP") || strings.Contains(query, "INSERT") {
		if confidence > 0.3 {
			confidence += 0.4 // SQL注入更危险
		}
	}

	return confidence
}

// extractAPIEvidence 提取API证据
func (d *APISecurityDetector) extractAPIEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var apiLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "api-auth-bypass":
			if strings.Contains(lineLower, "auth") ||
				strings.Contains(lineLower, "login") ||
				strings.Contains(lineLower, "token") ||
				strings.Contains(lineLower, "bearer") ||
				strings.Contains(lineLower, "api") ||
				strings.Contains(lineLower, "access") ||
				strings.Contains(lineLower, "granted") ||
				strings.Contains(lineLower, "successful") ||
				strings.Contains(lineLower, "认证") ||
				strings.Contains(lineLower, "登录") ||
				strings.Contains(lineLower, "令牌") ||
				strings.Contains(lineLower, "接口") ||
				strings.Contains(lineLower, "访问") ||
				strings.Contains(lineLower, "成功") {
				apiLines = append(apiLines, line)
			}
		case "api-data-leakage":
			if strings.Contains(lineLower, "data") ||
				strings.Contains(lineLower, "user") ||
				strings.Contains(lineLower, "email") ||
				strings.Contains(lineLower, "password") ||
				strings.Contains(lineLower, "id") ||
				strings.Contains(lineLower, "name") ||
				strings.Contains(lineLower, "phone") ||
				strings.Contains(lineLower, "address") ||
				strings.Contains(lineLower, "数据") ||
				strings.Contains(lineLower, "用户") ||
				strings.Contains(lineLower, "邮箱") ||
				strings.Contains(lineLower, "密码") ||
				strings.Contains(lineLower, "姓名") ||
				strings.Contains(lineLower, "电话") ||
				strings.Contains(lineLower, "地址") {
				apiLines = append(apiLines, line)
			}
		case "api-rate-limit-bypass":
			if strings.Contains(lineLower, "rate") ||
				strings.Contains(lineLower, "limit") ||
				strings.Contains(lineLower, "quota") ||
				strings.Contains(lineLower, "bypass") ||
				strings.Contains(lineLower, "request") ||
				strings.Contains(lineLower, "too many") ||
				strings.Contains(lineLower, "exceeded") ||
				strings.Contains(lineLower, "速率") ||
				strings.Contains(lineLower, "限制") ||
				strings.Contains(lineLower, "配额") ||
				strings.Contains(lineLower, "绕过") ||
				strings.Contains(lineLower, "请求") ||
				strings.Contains(lineLower, "过多") ||
				strings.Contains(lineLower, "超出") {
				apiLines = append(apiLines, line)
			}
		case "graphql-injection":
			if strings.Contains(lineLower, "graphql") ||
				strings.Contains(lineLower, "query") ||
				strings.Contains(lineLower, "mutation") ||
				strings.Contains(lineLower, "schema") ||
				strings.Contains(lineLower, "type") ||
				strings.Contains(lineLower, "field") ||
				strings.Contains(lineLower, "error") ||
				strings.Contains(lineLower, "syntax") ||
				strings.Contains(lineLower, "查询") ||
				strings.Contains(lineLower, "变更") ||
				strings.Contains(lineLower, "模式") ||
				strings.Contains(lineLower, "类型") ||
				strings.Contains(lineLower, "字段") ||
				strings.Contains(lineLower, "错误") ||
				strings.Contains(lineLower, "语法") {
				apiLines = append(apiLines, line)
			}
		default:
			if strings.Contains(lineLower, "api") ||
				strings.Contains(lineLower, "rest") ||
				strings.Contains(lineLower, "graphql") ||
				strings.Contains(lineLower, "json") ||
				strings.Contains(lineLower, "xml") ||
				strings.Contains(lineLower, "endpoint") ||
				strings.Contains(lineLower, "service") ||
				strings.Contains(lineLower, "auth") ||
				strings.Contains(lineLower, "token") ||
				strings.Contains(lineLower, "data") ||
				strings.Contains(lineLower, "接口") ||
				strings.Contains(lineLower, "服务") ||
				strings.Contains(lineLower, "端点") ||
				strings.Contains(lineLower, "认证") ||
				strings.Contains(lineLower, "令牌") ||
				strings.Contains(lineLower, "数据") {
				apiLines = append(apiLines, line)
			}
		}

		if len(apiLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(apiLines) > 0 {
		return strings.Join(apiLines, "\n")
	}

	// 如果没有找到特定的API信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
