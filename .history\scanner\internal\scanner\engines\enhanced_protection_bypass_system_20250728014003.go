package engines

import (
	"context"
	"crypto/md5"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/pkg/logger"
)

// EnhancedProtectionBypassSystem 增强防护绕过系统
// 专门用于检测和绕过各种Web应用防护技术
type EnhancedProtectionBypassSystem struct {
	// 核心组件
	responseAnalyzer  *ResponseConsistencyAnalyzer
	timingAnalyzer    *TimingAnalyzer
	semanticAnalyzer  *SemanticAnalyzer
	anomalyDetector   *ProtectionAnomalyDetector
	bypassGenerator   *BypassPayloadGenerator
	evidenceCollector *EvidenceCollector

	// 配置
	config *ProtectionBypassConfig

	// 状态
	detectionCache map[string]*ProtectionProfile
}

// ProtectionBypassConfig 防护绕过配置
type ProtectionBypassConfig struct {
	// 检测配置
	MaxTestPayloads             int     `json:"max_test_payloads"`
	ResponseSimilarityThreshold float64 `json:"response_similarity_threshold"`
	TimingAnalysisEnabled       bool    `json:"timing_analysis_enabled"`
	SemanticAnalysisEnabled     bool    `json:"semantic_analysis_enabled"`

	// 绕过配置
	EnableEncodingBypass  bool `json:"enable_encoding_bypass"`
	EnableProtocolBypass  bool `json:"enable_protocol_bypass"`
	EnablePayloadMutation bool `json:"enable_payload_mutation"`
	MaxBypassAttempts     int  `json:"max_bypass_attempts"`

	// 性能配置
	RequestTimeout        time.Duration `json:"request_timeout"`
	RequestInterval       time.Duration `json:"request_interval"`
	MaxConcurrentRequests int           `json:"max_concurrent_requests"`
}

// ProtectionProfile 防护配置文件
type ProtectionProfile struct {
	Target             string             `json:"target"`
	ProtectionDetected bool               `json:"protection_detected"`
	ProtectionType     string             `json:"protection_type"`
	WAFInfo            *WAFInfo           `json:"waf_info,omitempty"`
	FilteringBehavior  *FilteringBehavior `json:"filtering_behavior,omitempty"`
	BypassStrategies   []string           `json:"bypass_strategies"`
	Confidence         float64            `json:"confidence"`
	DetectionTime      time.Time          `json:"detection_time"`
	Evidence           []string           `json:"evidence"`
}

// WAFInfo WAF信息
type WAFInfo struct {
	Vendor           string  `json:"vendor"`
	Product          string  `json:"product"`
	Version          string  `json:"version,omitempty"`
	DetectionMethod  string  `json:"detection_method"`
	Confidence       float64 `json:"confidence"`
	BypassDifficulty string  `json:"bypass_difficulty"`
}

// FilteringBehavior 过滤行为
type FilteringBehavior struct {
	InputSanitization     bool     `json:"input_sanitization"`
	OutputEncoding        bool     `json:"output_encoding"`
	ErrorNormalization    bool     `json:"error_normalization"`
	ResponseNormalization bool     `json:"response_normalization"`
	RateLimiting          bool     `json:"rate_limiting"`
	FilteredPatterns      []string `json:"filtered_patterns"`
}

// ResponseConsistencyAnalyzer 响应一致性分析器
type ResponseConsistencyAnalyzer struct {
	normalBaseline *ResponseBaseline
	threshold      float64
	sampleSize     int
}

// ResponseBaseline 响应基线
type ResponseBaseline struct {
	AvgResponseTime  time.Duration     `json:"avg_response_time"`
	AvgContentLength int               `json:"avg_content_length"`
	StatusCodes      map[int]int       `json:"status_codes"`
	ContentHashes    map[string]int    `json:"content_hashes"`
	CommonHeaders    map[string]string `json:"common_headers"`
	ErrorPatterns    []string          `json:"error_patterns"`
}

// ResponseFingerprint 响应指纹
type ResponseFingerprint struct {
	StatusCode      int               `json:"status_code"`
	ContentLength   int               `json:"content_length"`
	ContentHash     string            `json:"content_hash"`
	Headers         map[string]string `json:"headers"`
	ResponseTime    time.Duration     `json:"response_time"`
	ErrorIndicators []string          `json:"error_indicators"`
}

// TimingAnalyzer 时间分析器
type TimingAnalyzer struct {
	baselineTime time.Duration
	variance     time.Duration
	sampleSize   int
	tolerance    float64
}

// SemanticAnalyzer 语义分析器
type SemanticAnalyzer struct {
	errorPatterns   map[string]*regexp.Regexp
	successPatterns map[string]*regexp.Regexp
	filterPatterns  map[string]*regexp.Regexp
	contextKeywords map[string][]string
}

// ProtectionAnomalyDetector 防护异常检测器
type ProtectionAnomalyDetector struct {
	baseline         *ResponseBaseline
	sensitivity      float64
	anomalyThreshold float64
}

// BypassPayloadGenerator 绕过载荷生成器
type BypassPayloadGenerator struct {
	encoders       map[string]PayloadEncoder
	mutators       map[string]PayloadMutator
	protocolBypass *ProtocolBypassGenerator
	wafBypassers   map[string]WAFBypasser
}

// EvidenceCollector 证据收集器
type EvidenceCollector struct {
	evidenceChain []Evidence
	verifiers     []EvidenceVerifier
}

// ProtectionEvidence 防护证据
type ProtectionEvidence struct {
	Type       string                 `json:"type"`
	Content    string                 `json:"content"`
	Timestamp  time.Time              `json:"timestamp"`
	Source     string                 `json:"source"`
	Confidence float64                `json:"confidence"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// NewEnhancedProtectionBypassSystem 创建增强防护绕过系统
func NewEnhancedProtectionBypassSystem(config *ProtectionBypassConfig) *EnhancedProtectionBypassSystem {
	if config == nil {
		config = &ProtectionBypassConfig{
			MaxTestPayloads:             20,
			ResponseSimilarityThreshold: 0.9,
			TimingAnalysisEnabled:       true,
			SemanticAnalysisEnabled:     true,
			EnableEncodingBypass:        true,
			EnableProtocolBypass:        true,
			EnablePayloadMutation:       true,
			MaxBypassAttempts:           50,
			RequestTimeout:              30 * time.Second,
			RequestInterval:             100 * time.Millisecond,
			MaxConcurrentRequests:       5,
		}
	}

	system := &EnhancedProtectionBypassSystem{
		config:         config,
		detectionCache: make(map[string]*ProtectionProfile),
	}

	// 初始化组件
	system.initializeComponents()

	return system
}

// initializeComponents 初始化组件
func (epbs *EnhancedProtectionBypassSystem) initializeComponents() {
	// 初始化响应一致性分析器
	epbs.responseAnalyzer = &ResponseConsistencyAnalyzer{
		threshold:  epbs.config.ResponseSimilarityThreshold,
		sampleSize: 5,
	}

	// 初始化时间分析器
	epbs.timingAnalyzer = &TimingAnalyzer{
		sampleSize: 10,
		tolerance:  0.2,
	}

	// 初始化语义分析器
	epbs.semanticAnalyzer = &SemanticAnalyzer{
		errorPatterns: map[string]*regexp.Regexp{
			"sql_error":    regexp.MustCompile(`(?i)(sql|mysql|oracle|postgresql).*error`),
			"php_error":    regexp.MustCompile(`(?i)fatal error.*php`),
			"asp_error":    regexp.MustCompile(`(?i)microsoft.*error`),
			"python_error": regexp.MustCompile(`(?i)traceback.*python`),
			"java_error":   regexp.MustCompile(`(?i)java\..*exception`),
		},
		successPatterns: map[string]*regexp.Regexp{
			"sql_success":   regexp.MustCompile(`(?i)(select|insert|update|delete).*successful`),
			"login_success": regexp.MustCompile(`(?i)(welcome|dashboard|profile)`),
			"admin_access":  regexp.MustCompile(`(?i)(admin|administrator|management)`),
		},
		filterPatterns: map[string]*regexp.Regexp{
			"input_filtered": regexp.MustCompile(`(?i)(filtered|blocked|denied|sanitized)`),
			"waf_blocked":    regexp.MustCompile(`(?i)(firewall|protection|security)`),
		},
	}

	// 初始化异常检测器
	epbs.anomalyDetector = &AnomalyDetector{
		sensitivity:      0.3,
		anomalyThreshold: 0.7,
	}

	// 初始化绕过载荷生成器
	epbs.bypassGenerator = NewBypassPayloadGenerator()

	// 初始化证据收集器
	epbs.evidenceCollector = &EvidenceCollector{
		evidenceChain: make([]Evidence, 0),
		verifiers:     make([]EvidenceVerifier, 0),
	}
}

// DetectProtection 检测防护措施
func (epbs *EnhancedProtectionBypassSystem) DetectProtection(ctx context.Context, target string) (*ProtectionProfile, error) {
	logger.Infof("开始检测目标防护措施: %s", target)

	// 检查缓存
	if cached, exists := epbs.detectionCache[target]; exists {
		if time.Since(cached.DetectionTime) < 1*time.Hour {
			logger.Debugf("使用缓存的防护检测结果: %s", target)
			return cached, nil
		}
	}

	profile := &ProtectionProfile{
		Target:        target,
		DetectionTime: time.Now(),
		Evidence:      make([]string, 0),
	}

	// 1. 建立基线
	baseline, err := epbs.establishBaseline(ctx, target)
	if err != nil {
		return nil, fmt.Errorf("建立基线失败: %v", err)
	}
	epbs.responseAnalyzer.normalBaseline = baseline
	epbs.anomalyDetector.baseline = baseline

	// 2. WAF检测
	wafInfo := epbs.detectWAF(ctx, target)
	if wafInfo != nil {
		profile.ProtectionDetected = true
		profile.ProtectionType = "WAF"
		profile.WAFInfo = wafInfo
		profile.Evidence = append(profile.Evidence, fmt.Sprintf("检测到WAF: %s %s", wafInfo.Vendor, wafInfo.Product))
	}

	// 3. 过滤行为检测
	filteringBehavior := epbs.detectFilteringBehavior(ctx, target)
	if filteringBehavior != nil {
		profile.ProtectionDetected = true
		if profile.ProtectionType == "" {
			profile.ProtectionType = "Application_Filter"
		}
		profile.FilteringBehavior = filteringBehavior
	}

	// 4. 响应一致性检测
	consistencyScore := epbs.analyzeResponseConsistency(ctx, target)
	if consistencyScore > epbs.config.ResponseSimilarityThreshold {
		profile.ProtectionDetected = true
		profile.Evidence = append(profile.Evidence, fmt.Sprintf("检测到响应一致性异常，相似度: %.2f", consistencyScore))
	}

	// 5. 计算总体置信度
	profile.Confidence = epbs.calculateOverallConfidence(profile)

	// 6. 生成绕过策略
	if profile.ProtectionDetected {
		profile.BypassStrategies = epbs.generateBypassStrategies(profile)
	}

	// 缓存结果
	epbs.detectionCache[target] = profile

	logger.Infof("防护检测完成: %s, 检测到防护: %v, 置信度: %.2f", target, profile.ProtectionDetected, profile.Confidence)
	return profile, nil
}

// establishBaseline 建立基线
func (epbs *EnhancedProtectionBypassSystem) establishBaseline(ctx context.Context, target string) (*ResponseBaseline, error) {
	logger.Debugf("建立响应基线: %s", target)

	baseline := &ResponseBaseline{
		StatusCodes:   make(map[int]int),
		ContentHashes: make(map[string]int),
		CommonHeaders: make(map[string]string),
		ErrorPatterns: make([]string, 0),
	}

	var totalResponseTime time.Duration
	var totalContentLength int
	sampleCount := epbs.responseAnalyzer.sampleSize

	// 发送正常请求建立基线
	normalQueries := []string{
		"test",
		"search",
		"query",
		"data",
		"info",
	}

	for i := 0; i < sampleCount; i++ {
		query := normalQueries[i%len(normalQueries)]

		start := time.Now()
		resp, err := epbs.sendRequest(ctx, target, map[string]string{"q": query})
		responseTime := time.Since(start)

		if err != nil {
			logger.Warnf("基线请求失败: %v", err)
			continue
		}

		// 收集响应指纹
		fingerprint := epbs.extractResponseFingerprint(resp, responseTime)

		// 更新基线统计
		baseline.StatusCodes[fingerprint.StatusCode]++
		baseline.ContentHashes[fingerprint.ContentHash]++
		totalResponseTime += responseTime
		totalContentLength += fingerprint.ContentLength

		// 收集公共头部
		for key, value := range fingerprint.Headers {
			if baseline.CommonHeaders[key] == "" {
				baseline.CommonHeaders[key] = value
			}
		}

		resp.Body.Close()

		// 请求间隔
		time.Sleep(epbs.config.RequestInterval)
	}

	baseline.AvgResponseTime = totalResponseTime / time.Duration(sampleCount)
	baseline.AvgContentLength = totalContentLength / sampleCount

	return baseline, nil
}

// detectWAF 检测WAF
func (epbs *EnhancedProtectionBypassSystem) detectWAF(ctx context.Context, target string) *WAFInfo {
	logger.Debugf("检测WAF: %s", target)

	// 发送正常请求
	resp, err := epbs.sendRequest(ctx, target, map[string]string{"q": "test"})
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	wafInfo := &WAFInfo{
		DetectionMethod: "header_analysis",
	}

	// 检查常见WAF头部
	wafSignatures := map[string]WAFInfo{
		"CF-RAY": {
			Vendor:           "Cloudflare",
			Product:          "Cloudflare WAF",
			Confidence:       0.9,
			BypassDifficulty: "hard",
		},
		"X-Sucuri-ID": {
			Vendor:           "Sucuri",
			Product:          "Sucuri WAF",
			Confidence:       0.9,
			BypassDifficulty: "medium",
		},
		"X-Akamai-Request-ID": {
			Vendor:           "Akamai",
			Product:          "Akamai Kona",
			Confidence:       0.9,
			BypassDifficulty: "hard",
		},
		"X-Azure-Ref": {
			Vendor:           "Microsoft",
			Product:          "Azure WAF",
			Confidence:       0.8,
			BypassDifficulty: "medium",
		},
	}

	// 检查响应头
	for header, signature := range wafSignatures {
		if resp.Header.Get(header) != "" {
			*wafInfo = signature
			wafInfo.DetectionMethod = fmt.Sprintf("header_%s", header)
			return wafInfo
		}
	}

	// 检查Server头
	server := strings.ToLower(resp.Header.Get("Server"))
	if strings.Contains(server, "cloudflare") {
		wafInfo.Vendor = "Cloudflare"
		wafInfo.Product = "Cloudflare"
		wafInfo.Confidence = 0.8
		wafInfo.BypassDifficulty = "hard"
		return wafInfo
	}

	// 发送恶意载荷检测WAF
	maliciousPayloads := []string{
		"<script>alert(1)</script>",
		"' OR 1=1--",
		"../../../etc/passwd",
		"${7*7}",
	}

	for _, payload := range maliciousPayloads {
		resp, err := epbs.sendRequest(ctx, target, map[string]string{"q": payload})
		if err != nil {
			continue
		}

		body, _ := ioutil.ReadAll(resp.Body)
		bodyStr := string(body)
		resp.Body.Close()

		// 检查WAF特征响应
		if epbs.containsWAFSignature(bodyStr, resp.StatusCode) {
			wafInfo.Vendor = "Unknown"
			wafInfo.Product = "Generic WAF"
			wafInfo.Confidence = 0.7
			wafInfo.BypassDifficulty = "medium"
			wafInfo.DetectionMethod = "behavior_analysis"
			return wafInfo
		}

		time.Sleep(epbs.config.RequestInterval)
	}

	return nil
}

// containsWAFSignature 检查是否包含WAF特征
func (epbs *EnhancedProtectionBypassSystem) containsWAFSignature(body string, statusCode int) bool {
	wafKeywords := []string{
		"blocked",
		"forbidden",
		"access denied",
		"security",
		"firewall",
		"protection",
		"suspicious",
		"malicious",
	}

	bodyLower := strings.ToLower(body)
	for _, keyword := range wafKeywords {
		if strings.Contains(bodyLower, keyword) {
			return true
		}
	}

	// 检查状态码
	if statusCode == 403 || statusCode == 406 || statusCode == 501 {
		return true
	}

	return false
}

// detectFilteringBehavior 检测过滤行为
func (epbs *EnhancedProtectionBypassSystem) detectFilteringBehavior(ctx context.Context, target string) *FilteringBehavior {
	logger.Debugf("检测过滤行为: %s", target)

	behavior := &FilteringBehavior{
		FilteredPatterns: make([]string, 0),
	}

	// 测试输入净化
	testCases := map[string]string{
		"script_tag":        "<script>alert(1)</script>",
		"sql_injection":     "' OR 1=1--",
		"xss_payload":       "<img src=x onerror=alert(1)>",
		"path_traversal":    "../../../etc/passwd",
		"command_injection": "; cat /etc/passwd",
	}

	detectedFiltering := false

	for testName, payload := range testCases {
		// 发送原始载荷
		resp, err := epbs.sendRequest(ctx, target, map[string]string{"q": payload})
		if err != nil {
			continue
		}

		body, _ := ioutil.ReadAll(resp.Body)
		bodyStr := string(body)
		resp.Body.Close()

		// 检查载荷是否被过滤
		if epbs.isPayloadFiltered(payload, bodyStr) {
			behavior.FilteredPatterns = append(behavior.FilteredPatterns, testName)
			detectedFiltering = true

			// 检查过滤类型
			if strings.Contains(bodyStr, "&lt;") || strings.Contains(bodyStr, "&gt;") {
				behavior.OutputEncoding = true
			}
			if !strings.Contains(bodyStr, payload) {
				behavior.InputSanitization = true
			}
		}

		time.Sleep(epbs.config.RequestInterval)
	}

	// 测试错误标准化
	errorPayloads := []string{
		"' AND 1=0--",
		"' AND 1=1--",
		"invalid_function()",
	}

	responses := make([]string, 0)
	for _, payload := range errorPayloads {
		resp, err := epbs.sendRequest(ctx, target, map[string]string{"q": payload})
		if err != nil {
			continue
		}

		body, _ := ioutil.ReadAll(resp.Body)
		responses = append(responses, string(body))
		resp.Body.Close()

		time.Sleep(epbs.config.RequestInterval)
	}

	// 检查响应是否被标准化
	if epbs.areResponsesNormalized(responses) {
		behavior.ErrorNormalization = true
		detectedFiltering = true
	}

	if !detectedFiltering {
		return nil
	}

	return behavior
}

// isPayloadFiltered 检查载荷是否被过滤
func (epbs *EnhancedProtectionBypassSystem) isPayloadFiltered(payload, response string) bool {
	// 检查载荷是否完全消失
	if !strings.Contains(response, payload) {
		return true
	}

	// 检查是否被编码
	encodedVersions := []string{
		strings.ReplaceAll(payload, "<", "&lt;"),
		strings.ReplaceAll(payload, ">", "&gt;"),
		strings.ReplaceAll(payload, "'", "&#39;"),
		strings.ReplaceAll(payload, "\"", "&quot;"),
	}

	for _, encoded := range encodedVersions {
		if strings.Contains(response, encoded) {
			return true
		}
	}

	return false
}

// areResponsesNormalized 检查响应是否被标准化
func (epbs *EnhancedProtectionBypassSystem) areResponsesNormalized(responses []string) bool {
	if len(responses) < 2 {
		return false
	}

	// 计算响应相似度
	firstResponse := responses[0]
	for i := 1; i < len(responses); i++ {
		similarity := epbs.calculateStringSimilarity(firstResponse, responses[i])
		if similarity < 0.9 {
			return false
		}
	}

	return true
}

// analyzeResponseConsistency 分析响应一致性
func (epbs *EnhancedProtectionBypassSystem) analyzeResponseConsistency(ctx context.Context, target string) float64 {
	logger.Debugf("分析响应一致性: %s", target)

	// 发送多种恶意载荷
	maliciousPayloads := []string{
		"<script>alert('xss')</script>",
		"' UNION SELECT NULL--",
		"../../../etc/passwd",
		"${7*7}",
		"; cat /etc/passwd",
		"<img src=x onerror=alert(1)>",
	}

	responses := make([]ResponseFingerprint, 0)

	for _, payload := range maliciousPayloads {
		start := time.Now()
		resp, err := epbs.sendRequest(ctx, target, map[string]string{"q": payload})
		responseTime := time.Since(start)

		if err != nil {
			continue
		}

		fingerprint := epbs.extractResponseFingerprint(resp, responseTime)
		responses = append(responses, fingerprint)
		resp.Body.Close()

		time.Sleep(epbs.config.RequestInterval)
	}

	if len(responses) < 2 {
		return 0.0
	}

	// 计算响应相似度
	totalSimilarity := 0.0
	comparisons := 0

	for i := 0; i < len(responses); i++ {
		for j := i + 1; j < len(responses); j++ {
			similarity := epbs.calculateResponseSimilarity(responses[i], responses[j])
			totalSimilarity += similarity
			comparisons++
		}
	}

	return totalSimilarity / float64(comparisons)
}

// sendRequest 发送HTTP请求
func (epbs *EnhancedProtectionBypassSystem) sendRequest(ctx context.Context, target string, params map[string]string) (*http.Response, error) {
	client := &http.Client{
		Timeout: epbs.config.RequestTimeout,
	}

	// 构建URL
	req, err := http.NewRequestWithContext(ctx, "GET", target, nil)
	if err != nil {
		return nil, err
	}

	// 添加参数
	q := req.URL.Query()
	for key, value := range params {
		q.Add(key, value)
	}
	req.URL.RawQuery = q.Encode()

	// 设置常见的User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")

	return client.Do(req)
}

// extractResponseFingerprint 提取响应指纹
func (epbs *EnhancedProtectionBypassSystem) extractResponseFingerprint(resp *http.Response, responseTime time.Duration) ResponseFingerprint {
	// 读取响应体
	body, _ := ioutil.ReadAll(resp.Body)

	// 计算内容哈希
	contentHash := fmt.Sprintf("%x", md5.Sum(body))

	// 提取重要头部
	headers := make(map[string]string)
	importantHeaders := []string{"Server", "Content-Type", "Set-Cookie", "X-Powered-By"}
	for _, header := range importantHeaders {
		if value := resp.Header.Get(header); value != "" {
			headers[header] = value
		}
	}

	return ResponseFingerprint{
		StatusCode:    resp.StatusCode,
		ContentLength: len(body),
		ContentHash:   contentHash,
		Headers:       headers,
		ResponseTime:  responseTime,
	}
}

// calculateStringSimilarity 计算字符串相似度
func (epbs *EnhancedProtectionBypassSystem) calculateStringSimilarity(s1, s2 string) float64 {
	if s1 == s2 {
		return 1.0
	}

	// 使用简单的编辑距离算法
	len1, len2 := len(s1), len(s2)
	if len1 == 0 {
		return float64(len2)
	}
	if len2 == 0 {
		return float64(len1)
	}

	// 创建距离矩阵
	matrix := make([][]int, len1+1)
	for i := range matrix {
		matrix[i] = make([]int, len2+1)
	}

	// 初始化第一行和第一列
	for i := 0; i <= len1; i++ {
		matrix[i][0] = i
	}
	for j := 0; j <= len2; j++ {
		matrix[0][j] = j
	}

	// 填充矩阵
	for i := 1; i <= len1; i++ {
		for j := 1; j <= len2; j++ {
			cost := 0
			if s1[i-1] != s2[j-1] {
				cost = 1
			}

			matrix[i][j] = min(
				matrix[i-1][j]+1,      // 删除
				matrix[i][j-1]+1,      // 插入
				matrix[i-1][j-1]+cost, // 替换
			)
		}
	}

	distance := matrix[len1][len2]
	maxLen := max(len1, len2)

	return 1.0 - float64(distance)/float64(maxLen)
}

// calculateResponseSimilarity 计算响应相似度
func (epbs *EnhancedProtectionBypassSystem) calculateResponseSimilarity(r1, r2 ResponseFingerprint) float64 {
	score := 0.0

	// 状态码相似度 (权重: 0.3)
	if r1.StatusCode == r2.StatusCode {
		score += 0.3
	}

	// 内容长度相似度 (权重: 0.2)
	lengthDiff := math.Abs(float64(r1.ContentLength - r2.ContentLength))
	maxLength := math.Max(float64(r1.ContentLength), float64(r2.ContentLength))
	if maxLength > 0 {
		lengthSimilarity := 1.0 - (lengthDiff / maxLength)
		score += 0.2 * lengthSimilarity
	}

	// 内容哈希相似度 (权重: 0.4)
	if r1.ContentHash == r2.ContentHash {
		score += 0.4
	}

	// 响应时间相似度 (权重: 0.1)
	timeDiff := math.Abs(float64(r1.ResponseTime - r2.ResponseTime))
	maxTime := math.Max(float64(r1.ResponseTime), float64(r2.ResponseTime))
	if maxTime > 0 {
		timeSimilarity := 1.0 - (timeDiff / maxTime)
		score += 0.1 * timeSimilarity
	}

	return score
}

// calculateOverallConfidence 计算总体置信度
func (epbs *EnhancedProtectionBypassSystem) calculateOverallConfidence(profile *ProtectionProfile) float64 {
	confidence := 0.0

	// WAF检测置信度
	if profile.WAFInfo != nil {
		confidence += profile.WAFInfo.Confidence * 0.4
	}

	// 过滤行为检测置信度
	if profile.FilteringBehavior != nil {
		filteringScore := 0.0
		if profile.FilteringBehavior.InputSanitization {
			filteringScore += 0.3
		}
		if profile.FilteringBehavior.OutputEncoding {
			filteringScore += 0.2
		}
		if profile.FilteringBehavior.ErrorNormalization {
			filteringScore += 0.3
		}
		if len(profile.FilteringBehavior.FilteredPatterns) > 0 {
			filteringScore += 0.2
		}
		confidence += filteringScore * 0.3
	}

	// 证据数量加成
	evidenceBonus := math.Min(float64(len(profile.Evidence))*0.1, 0.3)
	confidence += evidenceBonus

	return math.Min(confidence, 1.0)
}

// generateBypassStrategies 生成绕过策略
func (epbs *EnhancedProtectionBypassSystem) generateBypassStrategies(profile *ProtectionProfile) []string {
	strategies := make([]string, 0)

	// 基于WAF类型的绕过策略
	if profile.WAFInfo != nil {
		switch strings.ToLower(profile.WAFInfo.Vendor) {
		case "cloudflare":
			strategies = append(strategies, []string{
				"unicode_encoding",
				"case_variation",
				"comment_insertion",
				"parameter_pollution",
			}...)
		case "akamai":
			strategies = append(strategies, []string{
				"double_encoding",
				"method_override",
				"header_injection",
			}...)
		case "aws":
			strategies = append(strategies, []string{
				"path_confusion",
				"fragment_injection",
				"protocol_downgrade",
			}...)
		default:
			strategies = append(strategies, []string{
				"encoding_bypass",
				"case_variation",
				"whitespace_variation",
			}...)
		}
	}

	// 基于过滤行为的绕过策略
	if profile.FilteringBehavior != nil {
		if profile.FilteringBehavior.InputSanitization {
			strategies = append(strategies, "payload_fragmentation")
		}
		if profile.FilteringBehavior.OutputEncoding {
			strategies = append(strategies, "context_breaking")
		}
		if profile.FilteringBehavior.ErrorNormalization {
			strategies = append(strategies, "blind_techniques")
		}
	}

	return strategies
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// maxIntProtection 返回两个整数中的较大值
func maxIntProtection(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// ProtocolBypassGenerator 协议绕过生成器
type ProtocolBypassGenerator struct {
	methods []string
	headers map[string]string
}

// PayloadMutator 载荷变异器接口
type PayloadMutator interface {
	Mutate(payload string) []string
	Name() string
}

// EvidenceVerifier 证据验证器接口
type EvidenceVerifier interface {
	Verify(evidence Evidence) VerificationResult
	GetReliability() float64
}

// BypassVerificationResult 绕过验证结果
type BypassVerificationResult struct {
	Valid           bool    `json:"valid"`
	ConfidenceBoost float64 `json:"confidence_boost"`
	Reason          string  `json:"reason"`
}

// NewBypassPayloadGenerator 创建绕过载荷生成器
func NewBypassPayloadGenerator() *BypassPayloadGenerator {
	generator := &BypassPayloadGenerator{
		encoders:     make(map[string]PayloadEncoder),
		mutators:     make(map[string]PayloadMutator),
		wafBypassers: make(map[string]WAFBypasser),
	}

	// 初始化编码器 - 使用smart_payload_generator中的实现
	generator.encoders["url"] = &URLEncoder{}
	generator.encoders["double_url"] = &DoubleURLEncoder{}
	generator.encoders["hex"] = &HexEncoder{}
	generator.encoders["unicode"] = &UnicodeEncoder{}

	// 初始化变异器
	generator.mutators["case"] = &CaseMutator{}
	generator.mutators["whitespace"] = &WhitespaceMutator{}
	generator.mutators["comment"] = &CommentMutator{}

	// 初始化WAF绕过器
	generator.wafBypassers["generic"] = &GenericWAFBypasser{}
	generator.wafBypassers["cloudflare"] = &CloudflareWAFBypasser{}
	generator.wafBypassers["akamai"] = &AkamaiWAFBypasser{}

	// 初始化协议绕过生成器
	generator.protocolBypass = &ProtocolBypassGenerator{
		methods: []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"},
		headers: map[string]string{
			"X-HTTP-Method-Override": "POST",
			"X-Method-Override":      "POST",
			"X-Forwarded-Method":     "POST",
		},
	}

	return generator
}
