package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// CORSDetector CORS检测器
// 支持CORS配置错误检测，包括CORS配置错误、CORS绕过、CORS劫持等多种CORS安全检测技术
type CORSDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	testOrigins        []string         // 测试Origin列表
	bypassOrigins      []string         // 绕过Origin列表
	corsHeaders        []string         // CORS头部列表
	dangerousHeaders   []string         // 危险头部列表
	corsPatterns       []*regexp.Regexp // CORS模式
	vulnerablePatterns []*regexp.Regexp // 漏洞模式
	httpClient         *http.Client
}

// NewCORSDetector 创建CORS检测器
func NewCORSDetector() *CORSDetector {
	detector := &CORSDetector{
		id:          "cors-comprehensive",
		name:        "CORS跨域资源共享检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{"CVE-2018-0269", "CVE-2019-11730", "CVE-2020-15999"},
		cwe:         []string{"CWE-346", "CWE-942", "CWE-200", "CWE-16"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测CORS配置错误，包括CORS配置错误、CORS绕过、CORS劫持等多种CORS安全检测技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // CORS检测需要中等时间
		MaxRetries:      2,
		Concurrency:     4,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，CORS响应通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeTestOrigins()
	detector.initializeBypassOrigins()
	detector.initializeCORSHeaders()
	detector.initializeDangerousHeaders()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *CORSDetector) GetID() string                     { return d.id }
func (d *CORSDetector) GetName() string                   { return d.name }
func (d *CORSDetector) GetCategory() string               { return d.category }
func (d *CORSDetector) GetSeverity() string               { return d.severity }
func (d *CORSDetector) GetCVE() []string                  { return d.cve }
func (d *CORSDetector) GetCWE() []string                  { return d.cwe }
func (d *CORSDetector) GetVersion() string                { return d.version }
func (d *CORSDetector) GetAuthor() string                 { return d.author }
func (d *CORSDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *CORSDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *CORSDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *CORSDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443, 3000, 4200, 5000} }
func (d *CORSDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *CORSDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *CORSDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *CORSDetector) GetDependencies() []string         { return []string{} }
func (d *CORSDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *CORSDetector) SetEnabled(enabled bool)           { d.enabled = enabled; d.updatedAt = time.Now() }

func (d *CORSDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *CORSDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *CORSDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 4
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *CORSDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *CORSDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.testOrigins) == 0 {
		return fmt.Errorf("测试Origin不能为空")
	}
	if len(d.corsHeaders) == 0 {
		return fmt.Errorf("CORS头部不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *CORSDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// CORS检测适用于有跨域功能的Web应用
	// 检查是否有CORS相关的特征
	if d.hasCORSFeatures(target) {
		return true
	}

	// 对于API相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	apiKeywords := []string{
		"api", "rest", "json", "ajax", "xhr", "fetch", "cors",
		"service", "endpoint", "resource", "data", "graphql",
		"接口", "数据", "服务", "资源", "跨域",
	}

	for _, keyword := range apiKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // CORS是通用Web安全问题，默认适用于所有Web目标
}

// hasCORSFeatures 检查是否有CORS功能
func (d *CORSDetector) hasCORSFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有CORS相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "access-control") ||
			strings.Contains(keyLower, "cors") ||
			strings.Contains(valueLower, "cors") ||
			strings.Contains(valueLower, "cross-origin") {
			return true
		}
	}

	// 检查技术栈中是否有CORS相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		corsTechnologies := []string{
			"javascript", "ajax", "xhr", "fetch", "axios", "jquery",
			"react", "vue", "angular", "nodejs", "express", "koa",
			"api", "rest", "graphql", "spa", "pwa",
			"跨域", "前端", "接口", "单页应用",
		}

		for _, corsTech := range corsTechnologies {
			if strings.Contains(techNameLower, corsTech) {
				return true
			}
		}
	}

	// 检查链接中是否有CORS相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "api") ||
			strings.Contains(linkURLLower, "ajax") ||
			strings.Contains(linkURLLower, "xhr") ||
			strings.Contains(linkTextLower, "api") ||
			strings.Contains(linkTextLower, "ajax") ||
			strings.Contains(linkTextLower, "接口") ||
			strings.Contains(linkTextLower, "数据") {
			return true
		}
	}

	// 检查表单中是否有CORS相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			corsFields := []string{
				"origin", "referer", "callback", "jsonp", "ajax",
				"xhr", "fetch", "api", "cors", "cross",
				"来源", "回调", "接口", "跨域",
			}

			for _, corsField := range corsFields {
				if strings.Contains(fieldNameLower, corsField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *CORSDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种CORS检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 通配符Origin检测
	wildcardEvidence, wildcardConfidence, wildcardPayload, wildcardRequest, wildcardResponse := d.detectWildcardOrigin(ctx, target)
	if wildcardConfidence > maxConfidence {
		maxConfidence = wildcardConfidence
		vulnerablePayload = wildcardPayload
		vulnerableRequest = wildcardRequest
		vulnerableResponse = wildcardResponse
	}
	evidence = append(evidence, wildcardEvidence...)

	// 2. 反射Origin检测
	reflectedEvidence, reflectedConfidence, reflectedPayload, reflectedRequest, reflectedResponse := d.detectReflectedOrigin(ctx, target)
	if reflectedConfidence > maxConfidence {
		maxConfidence = reflectedConfidence
		vulnerablePayload = reflectedPayload
		vulnerableRequest = reflectedRequest
		vulnerableResponse = reflectedResponse
	}
	evidence = append(evidence, reflectedEvidence...)

	// 3. CORS绕过检测
	bypassEvidence, bypassConfidence, bypassPayload, bypassRequest, bypassResponse := d.detectCORSBypass(ctx, target)
	if bypassConfidence > maxConfidence {
		maxConfidence = bypassConfidence
		vulnerablePayload = bypassPayload
		vulnerableRequest = bypassRequest
		vulnerableResponse = bypassResponse
	}
	evidence = append(evidence, bypassEvidence...)

	// 4. 危险头部检测
	headerEvidence, headerConfidence, headerPayload, headerRequest, headerResponse := d.detectDangerousHeaders(ctx, target)
	if headerConfidence > maxConfidence {
		maxConfidence = headerConfidence
		vulnerablePayload = headerPayload
		vulnerableRequest = headerRequest
		vulnerableResponse = headerResponse
	}
	evidence = append(evidence, headerEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "CORS跨域资源共享配置错误",
		Description:       "检测到CORS配置错误，攻击者可能通过恶意网站进行跨域请求，窃取敏感数据或执行未授权操作",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "正确配置CORS策略，避免使用通配符Origin，实施严格的Origin白名单，限制允许的头部和方法",
		References:        []string{"https://owasp.org/www-community/attacks/CORS_OriginHeaderScrutiny", "https://cwe.mitre.org/data/definitions/346.html"},
		Tags:              []string{"cors", "cross-origin", "web", "configuration"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *CORSDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"wildcard-origin",
		"reflected-origin",
		"cors-bypass",
		"dangerous-headers",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyCORSMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了CORS配置错误: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "cors-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用CORS验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *CORSDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("cors_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *CORSDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (CORS通常是中等风险漏洞)
	baseScore := 6.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyCORSMethod 验证CORS方法
func (d *CORSDetector) verifyCORSMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "wildcard-origin":
		return d.verifyWildcardOrigin(ctx, target)
	case "reflected-origin":
		return d.verifyReflectedOrigin(ctx, target)
	case "cors-bypass":
		return d.verifyCORSBypass(ctx, target)
	case "dangerous-headers":
		return d.verifyDangerousHeaders(ctx, target)
	default:
		return 0.0
	}
}

// verifyWildcardOrigin 验证通配符Origin
func (d *CORSDetector) verifyWildcardOrigin(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的通配符Origin验证
	if d.hasCORSFeatures(target) {
		return 0.8 // 有CORS特征的目标可能有通配符Origin
	}
	return 0.4
}

// verifyReflectedOrigin 验证反射Origin
func (d *CORSDetector) verifyReflectedOrigin(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的反射Origin验证
	if d.hasCORSFeatures(target) {
		return 0.7 // 有CORS特征的目标可能有反射Origin
	}
	return 0.3
}

// verifyCORSBypass 验证CORS绕过
func (d *CORSDetector) verifyCORSBypass(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的CORS绕过验证
	if d.hasCORSFeatures(target) {
		return 0.6 // 有CORS特征的目标可能有绕过问题
	}
	return 0.2
}

// verifyDangerousHeaders 验证危险头部
func (d *CORSDetector) verifyDangerousHeaders(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的危险头部验证
	if d.hasCORSFeatures(target) {
		return 0.6 // 有CORS特征的目标可能有危险头部
	}
	return 0.2
}

// initializeTestOrigins 初始化测试Origin列表
func (d *CORSDetector) initializeTestOrigins() {
	d.testOrigins = []string{
		// 基础恶意Origin
		"https://evil.com",
		"http://evil.com",
		"https://attacker.com",
		"http://attacker.com",
		"https://malicious.com",
		"http://malicious.com",

		// 特殊Origin值
		"null",
		"undefined",
		"*",
		"",

		// 协议变化
		"ftp://evil.com",
		"file://evil.com",
		"data://evil.com",
		"javascript://evil.com",

		// 端口变化
		"https://evil.com:443",
		"http://evil.com:80",
		"https://evil.com:8080",
		"http://evil.com:8443",
		"https://evil.com:3000",
		"http://evil.com:4200",

		// 子域名攻击
		"https://sub.evil.com",
		"http://www.evil.com",
		"https://api.evil.com",
		"http://admin.evil.com",

		// 路径变化
		"https://evil.com/",
		"http://evil.com/path",
		"https://evil.com/api",
		"http://evil.com/admin",

		// 编码绕过
		"https://evil%2ecom",
		"http://evil%2ecom",
		"https://evil.com%2f",
		"http://evil.com%2f",

		// Unicode绕过
		"https://evil\u002ecom",
		"http://evil\u002ecom",
		"https://evil\u0002ecom",
		"http://evil\u0002ecom",

		// 中文域名
		"https://恶意.com",
		"http://恶意.com",
		"https://攻击者.com",
		"http://攻击者.com",
	}
}

// initializeBypassOrigins 初始化绕过Origin列表
func (d *CORSDetector) initializeBypassOrigins() {
	d.bypassOrigins = []string{
		// 子域名绕过
		"https://evil.target.com",
		"http://evil.target.com",
		"https://target.com.evil.com",
		"http://target.com.evil.com",

		// 域名混淆
		"https://target-evil.com",
		"http://target-evil.com",
		"https://targetevilcom",
		"http://targetevilcom",

		// 协议绕过
		"//evil.com",
		"///evil.com",
		"////evil.com",

		// 大小写绕过
		"HTTPS://EVIL.COM",
		"HTTP://EVIL.COM",
		"https://EVIL.com",
		"http://EVIL.com",

		// 特殊字符绕过
		"https://evil.com.",
		"http://evil.com.",
		"https://evil.com#",
		"http://evil.com#",
		"https://evil.com?",
		"http://evil.com?",

		// IP地址绕过
		"https://*************",
		"http://*************",
		"https://127.0.0.1",
		"http://127.0.0.1",
		"https://********",
		"http://********",

		// 十六进制IP
		"https://0x7f000001",
		"http://0x7f000001",
		"https://0177.0.0.1",
		"http://0177.0.0.1",

		// 长整型IP
		"https://2130706433",
		"http://2130706433",

		// IPv6绕过
		"https://[::1]",
		"http://[::1]",
		"https://[::ffff:127.0.0.1]",
		"http://[::ffff:127.0.0.1]",

		// 中文绕过
		"https://恶意目标.com",
		"http://恶意目标.com",
	}
}

// initializeCORSHeaders 初始化CORS头部列表
func (d *CORSDetector) initializeCORSHeaders() {
	d.corsHeaders = []string{
		// 标准CORS头部
		"Access-Control-Allow-Origin",
		"Access-Control-Allow-Methods",
		"Access-Control-Allow-Headers",
		"Access-Control-Allow-Credentials",
		"Access-Control-Expose-Headers",
		"Access-Control-Max-Age",
		"Access-Control-Request-Method",
		"Access-Control-Request-Headers",

		// 非标准CORS头部
		"X-Requested-With",
		"Origin",
		"Referer",
		"Host",

		// 安全相关头部
		"Content-Security-Policy",
		"X-Frame-Options",
		"X-Content-Type-Options",
		"X-XSS-Protection",
		"Strict-Transport-Security",

		// 自定义头部
		"X-Custom-Header",
		"X-API-Key",
		"X-Auth-Token",
		"X-CSRF-Token",
		"X-Request-ID",

		// 中文头部
		"X-来源",
		"X-跨域",
		"X-授权",
	}
}

// initializeDangerousHeaders 初始化危险头部列表
func (d *CORSDetector) initializeDangerousHeaders() {
	d.dangerousHeaders = []string{
		// 认证相关头部
		"Authorization",
		"Cookie",
		"Set-Cookie",
		"X-Auth-Token",
		"X-API-Key",
		"X-Session-Token",
		"X-CSRF-Token",
		"X-Access-Token",
		"X-Refresh-Token",

		// 敏感信息头部
		"X-Real-IP",
		"X-Forwarded-For",
		"X-Forwarded-Host",
		"X-Forwarded-Proto",
		"X-Original-URL",
		"X-Rewrite-URL",
		"X-User-ID",
		"X-Username",
		"X-Email",
		"X-Phone",

		// 系统信息头部
		"Server",
		"X-Powered-By",
		"X-AspNet-Version",
		"X-AspNetMvc-Version",
		"X-Generator",
		"X-Version",
		"X-Build",
		"X-Commit",

		// 调试信息头部
		"X-Debug",
		"X-Debug-Token",
		"X-Debug-Info",
		"X-Trace-ID",
		"X-Request-ID",
		"X-Correlation-ID",

		// 中文危险头部
		"X-用户ID",
		"X-用户名",
		"X-邮箱",
		"X-电话",
		"X-认证",
		"X-授权",
		"X-会话",
		"X-令牌",
	}
}

// initializePatterns 初始化检测模式
func (d *CORSDetector) initializePatterns() {
	// CORS模式 - 检测CORS相关的响应内容
	corsPatternStrings := []string{
		// CORS头部模式
		`(?i)(access-control-allow-origin\s*:\s*\*)`,
		`(?i)(access-control-allow-origin\s*:\s*null)`,
		`(?i)(access-control-allow-credentials\s*:\s*true)`,
		`(?i)(access-control-allow-methods\s*:.*\*)`,
		`(?i)(access-control-allow-headers\s*:.*\*)`,

		// 通配符模式
		`(?i)(origin.*\*|allow.*origin.*\*)`,
		`(?i)(cors.*wildcard|wildcard.*cors)`,
		`(?i)(allow.*any.*origin|any.*origin.*allow)`,

		// 反射模式
		`(?i)(origin.*reflect|reflect.*origin)`,
		`(?i)(dynamic.*origin|origin.*dynamic)`,
		`(?i)(echo.*origin|origin.*echo)`,

		// 绕过模式
		`(?i)(cors.*bypass|bypass.*cors)`,
		`(?i)(origin.*bypass|bypass.*origin)`,
		`(?i)(subdomain.*bypass|bypass.*subdomain)`,

		// 配置错误模式
		`(?i)(cors.*misconfigur|misconfigur.*cors)`,
		`(?i)(cors.*error|error.*cors)`,
		`(?i)(cors.*vuln|vuln.*cors)`,

		// 中文CORS模式
		`(?i)(跨域.*配置|配置.*跨域)`,
		`(?i)(跨域.*错误|错误.*跨域)`,
		`(?i)(跨域.*漏洞|漏洞.*跨域)`,
		`(?i)(来源.*验证|验证.*来源)`,
	}

	d.corsPatterns = make([]*regexp.Regexp, 0, len(corsPatternStrings))
	for _, pattern := range corsPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.corsPatterns = append(d.corsPatterns, compiled)
		}
	}

	// 漏洞模式 - 检测CORS漏洞相关的响应内容
	vulnerablePatternStrings := []string{
		// 通配符漏洞
		`(?i)(access-control-allow-origin.*\*.*access-control-allow-credentials.*true)`,
		`(?i)(allow.*origin.*\*.*allow.*credentials.*true)`,
		`(?i)(cors.*wildcard.*credentials|credentials.*wildcard.*cors)`,

		// 反射漏洞
		`(?i)(origin.*reflected|reflected.*origin)`,
		`(?i)(cors.*reflection|reflection.*cors)`,
		`(?i)(dynamic.*cors|cors.*dynamic)`,

		// 绕过漏洞
		`(?i)(cors.*bypass.*success|success.*cors.*bypass)`,
		`(?i)(origin.*bypass.*success|success.*origin.*bypass)`,
		`(?i)(subdomain.*attack|attack.*subdomain)`,

		// 配置漏洞
		`(?i)(cors.*vulnerable|vulnerable.*cors)`,
		`(?i)(cors.*insecure|insecure.*cors)`,
		`(?i)(cors.*weak|weak.*cors)`,

		// 敏感信息泄露
		`(?i)(cors.*leak|leak.*cors)`,
		`(?i)(cors.*expose|expose.*cors)`,
		`(?i)(sensitive.*cors|cors.*sensitive)`,

		// 认证绕过
		`(?i)(cors.*auth.*bypass|auth.*bypass.*cors)`,
		`(?i)(cors.*credential.*bypass|credential.*bypass.*cors)`,
		`(?i)(cors.*session.*bypass|session.*bypass.*cors)`,

		// 中文漏洞模式
		`(?i)(跨域.*漏洞|漏洞.*跨域)`,
		`(?i)(跨域.*绕过|绕过.*跨域)`,
		`(?i)(跨域.*攻击|攻击.*跨域)`,
		`(?i)(来源.*伪造|伪造.*来源)`,
		`(?i)(认证.*绕过|绕过.*认证)`,
		`(?i)(凭据.*泄露|泄露.*凭据)`,
	}

	d.vulnerablePatterns = make([]*regexp.Regexp, 0, len(vulnerablePatternStrings))
	for _, pattern := range vulnerablePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.vulnerablePatterns = append(d.vulnerablePatterns, compiled)
		}
	}
}
