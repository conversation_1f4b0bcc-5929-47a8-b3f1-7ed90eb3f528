package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectCSRFToken 检测CSRF令牌
func (d *CSRFDetector) detectCSRFToken(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0

	// 发送GET请求获取页面内容
	resp, err := d.makeRequest(ctx, target.URL, "GET", nil)
	if err != nil {
		return evidence, confidence, target.URL, ""
	}

	// 检查页面是否包含表单
	if !d.containsForms(resp) {
		// 没有表单，不需要CSRF保护
		return evidence, confidence, target.URL, resp
	}

	// 检查是否存在CSRF令牌
	hasCSRFToken := d.checkCSRFTokenInResponse(resp)
	
	if !hasCSRFToken {
		confidence = 0.8 // 表单存在但缺少CSRF令牌，高置信度
		evidence = append(evidence, plugins.Evidence{
			Type:        "missing-csrf-token",
			Description: "页面包含表单但缺少CSRF令牌保护",
			Content:     d.extractFormEvidence(resp),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	} else {
		confidence = 0.1 // 存在CSRF令牌，低风险
		evidence = append(evidence, plugins.Evidence{
			Type:        "csrf-token-present",
			Description: "页面包含CSRF令牌保护",
			Content:     d.extractTokenEvidence(resp),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, confidence, target.URL, resp
}

// detectSameSiteCookie 检测SameSite Cookie属性
func (d *CSRFDetector) detectSameSiteCookie(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0

	// 发送GET请求获取响应头
	req, err := http.NewRequestWithContext(ctx, "GET", target.URL, nil)
	if err != nil {
		return evidence, confidence, target.URL, ""
	}

	// 设置常见的浏览器头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return evidence, confidence, target.URL, ""
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return evidence, confidence, target.URL, ""
	}
	responseContent := string(body)

	// 检查Set-Cookie头
	cookieHeaders := resp.Header.Values("Set-Cookie")
	if len(cookieHeaders) == 0 {
		// 没有设置Cookie，不需要SameSite检查
		return evidence, confidence, target.URL, responseContent
	}

	hasSameSite := false
	hasSecure := false
	hasHttpOnly := false
	cookieDetails := make([]string, 0)

	for _, cookie := range cookieHeaders {
		cookieLower := strings.ToLower(cookie)
		cookieDetails = append(cookieDetails, cookie)

		if strings.Contains(cookieLower, "samesite") {
			hasSameSite = true
		}
		if strings.Contains(cookieLower, "secure") {
			hasSecure = true
		}
		if strings.Contains(cookieLower, "httponly") {
			hasHttpOnly = true
		}
	}

	// 计算置信度
	if !hasSameSite {
		confidence += 0.5 // 缺少SameSite属性
	}
	if !hasSecure {
		confidence += 0.2 // 缺少Secure属性
	}
	if !hasHttpOnly {
		confidence += 0.1 // 缺少HttpOnly属性
	}

	// 生成证据
	if confidence > 0 {
		evidenceContent := fmt.Sprintf("Cookie安全属性检查:\n")
		evidenceContent += fmt.Sprintf("- SameSite: %v\n", hasSameSite)
		evidenceContent += fmt.Sprintf("- Secure: %v\n", hasSecure)
		evidenceContent += fmt.Sprintf("- HttpOnly: %v\n", hasHttpOnly)
		evidenceContent += fmt.Sprintf("\nCookie详情:\n%s", strings.Join(cookieDetails, "\n"))

		evidence = append(evidence, plugins.Evidence{
			Type:        "cookie-security",
			Description: fmt.Sprintf("Cookie缺少安全属性 (SameSite: %v, Secure: %v, HttpOnly: %v)", hasSameSite, hasSecure, hasHttpOnly),
			Content:     evidenceContent,
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, confidence, target.URL, responseContent
}

// detectRefererValidation 检测Referer验证
func (d *CSRFDetector) detectRefererValidation(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0

	// 如果没有表单，跳过Referer验证检测
	resp, err := d.makeRequest(ctx, target.URL, "GET", nil)
	if err != nil {
		return evidence, confidence, target.URL, ""
	}

	if !d.containsForms(resp) {
		return evidence, confidence, target.URL, resp
	}

	// 尝试发送带有不同Referer头的请求
	testReferers := []string{
		"",                           // 空Referer
		"http://evil.com",           // 恶意域名
		"https://attacker.example",  // 攻击者域名
		"http://localhost",          // 本地域名
	}

	for _, referer := range testReferers {
		headers := make(map[string]string)
		if referer != "" {
			headers["Referer"] = referer
		}

		testResp, err := d.makeRequest(ctx, target.URL, "GET", headers)
		if err != nil {
			continue
		}

		// 检查响应是否相同（表示没有Referer验证）
		if d.compareResponses(resp, testResp) {
			confidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "referer-validation",
				Description: fmt.Sprintf("使用Referer '%s'的请求未被拒绝", referer),
				Content:     fmt.Sprintf("原始响应长度: %d, 测试响应长度: %d", len(resp), len(testResp)),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, confidence, target.URL, resp
}

// makeRequest 发送HTTP请求
func (d *CSRFDetector) makeRequest(ctx context.Context, url, method string, headers map[string]string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, method, url, nil)
	if err != nil {
		return "", err
	}

	// 设置默认头部
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	// 设置自定义头部
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// containsForms 检查响应是否包含表单
func (d *CSRFDetector) containsForms(response string) bool {
	responseLower := strings.ToLower(response)
	return strings.Contains(responseLower, "<form")
}

// checkCSRFTokenInResponse 检查响应中是否包含CSRF令牌
func (d *CSRFDetector) checkCSRFTokenInResponse(response string) bool {
	responseLower := strings.ToLower(response)

	// 检查CSRF令牌模式
	for _, pattern := range d.csrfTokenPatterns {
		if strings.Contains(responseLower, strings.ToLower(pattern)) {
			return true
		}
	}

	// 检查表单模式
	for _, pattern := range d.formPatterns {
		if pattern.MatchString(response) {
			// 如果匹配到包含token的input，认为有CSRF保护
			match := pattern.FindString(response)
			if strings.Contains(strings.ToLower(match), "token") ||
			   strings.Contains(strings.ToLower(match), "csrf") ||
			   strings.Contains(strings.ToLower(match), "xsrf") {
				return true
			}
		}
	}

	return false
}

// checkFormCSRFProtection 检查表单是否有CSRF保护
func (d *CSRFDetector) checkFormCSRFProtection(form plugins.FormInfo) bool {
	// 检查表单字段中是否包含CSRF令牌
	for fieldName := range form.Fields {
		fieldNameLower := strings.ToLower(fieldName)
		for _, pattern := range d.csrfTokenPatterns {
			if strings.Contains(fieldNameLower, strings.ToLower(pattern)) {
				return true
			}
		}
	}
	return false
}

// extractFormEvidence 提取表单证据
func (d *CSRFDetector) extractFormEvidence(response string) string {
	lines := strings.Split(response, "\n")
	var formLines []string
	inForm := false

	for _, line := range lines {
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if strings.Contains(lineLower, "<form") {
			inForm = true
		}
		if inForm {
			formLines = append(formLines, line)
		}
		if strings.Contains(lineLower, "</form>") {
			inForm = false
			break
		}
	}

	if len(formLines) > 0 {
		return strings.Join(formLines, "\n")
	}
	return "未找到表单内容"
}

// extractTokenEvidence 提取令牌证据
func (d *CSRFDetector) extractTokenEvidence(response string) string {
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		lineLower := strings.ToLower(line)
		for _, pattern := range d.csrfTokenPatterns {
			if strings.Contains(lineLower, strings.ToLower(pattern)) {
				return strings.TrimSpace(line)
			}
		}
	}
	return "找到CSRF令牌"
}

// compareResponses 比较两个响应是否相似
func (d *CSRFDetector) compareResponses(resp1, resp2 string) bool {
	// 简单的响应比较：检查长度差异
	lengthDiff := len(resp1) - len(resp2)
	if lengthDiff < 0 {
		lengthDiff = -lengthDiff
	}

	// 如果长度差异小于5%，认为响应相似
	threshold := len(resp1) / 20 // 5%
	return lengthDiff <= threshold
}
