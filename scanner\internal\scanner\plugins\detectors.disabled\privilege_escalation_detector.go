package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// PrivilegeEscalationDetector 权限提升检测器
// 支持水平权限提升、垂直权限提升、认证绕过、访问控制缺陷等多种权限提升检测
type PrivilegeEscalationDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	adminPaths           []string         // 管理员路径
	userPaths            []string         // 用户路径
	apiEndpoints         []string         // API端点
	authBypassPayloads   []string         // 认证绕过载荷
	privilegePatterns    []*regexp.Regexp // 权限模式
	accessDeniedPatterns []*regexp.Regexp // 访问拒绝模式
	successPatterns      []*regexp.Regexp // 成功访问模式
	httpClient           *http.Client
}

// NewPrivilegeEscalationDetector 创建权限提升检测器
func NewPrivilegeEscalationDetector() *PrivilegeEscalationDetector {
	detector := &PrivilegeEscalationDetector{
		id:          "privilege-escalation-comprehensive",
		name:        "权限提升漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // 权限提升是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-269", "CWE-284", "CWE-285", "CWE-862", "CWE-863"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测权限提升漏洞，包括水平权限提升、垂直权限提升、认证绕过、访问控制缺陷等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // 权限提升检测需要较长时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，响应内容适中
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializePaths()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *PrivilegeEscalationDetector) GetID() string            { return d.id }
func (d *PrivilegeEscalationDetector) GetName() string          { return d.name }
func (d *PrivilegeEscalationDetector) GetCategory() string      { return d.category }
func (d *PrivilegeEscalationDetector) GetSeverity() string      { return d.severity }
func (d *PrivilegeEscalationDetector) GetCVE() []string         { return d.cve }
func (d *PrivilegeEscalationDetector) GetCWE() []string         { return d.cwe }
func (d *PrivilegeEscalationDetector) GetVersion() string       { return d.version }
func (d *PrivilegeEscalationDetector) GetAuthor() string        { return d.author }
func (d *PrivilegeEscalationDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *PrivilegeEscalationDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *PrivilegeEscalationDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *PrivilegeEscalationDetector) GetRequiredPorts() []int  { return []int{80, 443, 8080, 8443} }
func (d *PrivilegeEscalationDetector) GetRequiredServices() []string {
	return []string{"http", "https"}
}
func (d *PrivilegeEscalationDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *PrivilegeEscalationDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *PrivilegeEscalationDetector) GetDependencies() []string         { return []string{} }
func (d *PrivilegeEscalationDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *PrivilegeEscalationDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *PrivilegeEscalationDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *PrivilegeEscalationDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *PrivilegeEscalationDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *PrivilegeEscalationDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *PrivilegeEscalationDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.adminPaths) == 0 {
		return fmt.Errorf("管理员路径列表不能为空")
	}
	if len(d.privilegePatterns) == 0 {
		return fmt.Errorf("权限模式不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *PrivilegeEscalationDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 权限提升检测适用于有认证功能的Web应用
	// 检查是否有登录表单、认证相关的URL或头信息
	if d.hasAuthenticationFeatures(target) {
		return true
	}

	// 对于API端点，也适用权限提升检测
	if strings.Contains(target.URL, "/api/") {
		return true
	}

	// 对于管理相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	for _, adminPath := range []string{"admin", "manage", "dashboard", "panel", "control"} {
		if strings.Contains(targetLower, adminPath) {
			return true
		}
	}

	return false
}

// hasAuthenticationFeatures 检查是否有认证功能
func (d *PrivilegeEscalationDetector) hasAuthenticationFeatures(target *plugins.ScanTarget) bool {
	// 检查表单中是否有密码字段
	for _, form := range target.Forms {
		for fieldName, fieldType := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)
			fieldTypeLower := strings.ToLower(fieldType)
			if fieldTypeLower == "password" ||
				strings.Contains(fieldNameLower, "password") ||
				strings.Contains(fieldNameLower, "passwd") ||
				strings.Contains(fieldNameLower, "pwd") {
				return true
			}
		}
	}

	// 检查Cookie中是否有认证相关信息
	for _, cookie := range target.Cookies {
		cookieNameLower := strings.ToLower(cookie.Name)
		if strings.Contains(cookieNameLower, "session") ||
			strings.Contains(cookieNameLower, "auth") ||
			strings.Contains(cookieNameLower, "token") ||
			strings.Contains(cookieNameLower, "login") {
			return true
		}
	}

	// 检查Headers中是否有认证相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)
		if keyLower == "authorization" ||
			strings.Contains(valueLower, "bearer") ||
			strings.Contains(valueLower, "basic") ||
			strings.Contains(valueLower, "token") {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *PrivilegeEscalationDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种权限提升检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 垂直权限提升检测
	verticalEvidence, verticalConfidence, verticalPayload, verticalRequest, verticalResponse := d.detectVerticalPrivilegeEscalation(ctx, target)
	if verticalConfidence > maxConfidence {
		maxConfidence = verticalConfidence
		vulnerablePayload = verticalPayload
		vulnerableRequest = verticalRequest
		vulnerableResponse = verticalResponse
	}
	evidence = append(evidence, verticalEvidence...)

	// 2. 水平权限提升检测
	horizontalEvidence, horizontalConfidence, horizontalPayload, horizontalRequest, horizontalResponse := d.detectHorizontalPrivilegeEscalation(ctx, target)
	if horizontalConfidence > maxConfidence {
		maxConfidence = horizontalConfidence
		vulnerablePayload = horizontalPayload
		vulnerableRequest = horizontalRequest
		vulnerableResponse = horizontalResponse
	}
	evidence = append(evidence, horizontalEvidence...)

	// 3. 认证绕过检测
	authBypassEvidence, authBypassConfidence, authBypassPayload, authBypassRequest, authBypassResponse := d.detectAuthenticationBypass(ctx, target)
	if authBypassConfidence > maxConfidence {
		maxConfidence = authBypassConfidence
		vulnerablePayload = authBypassPayload
		vulnerableRequest = authBypassRequest
		vulnerableResponse = authBypassResponse
	}
	evidence = append(evidence, authBypassEvidence...)

	// 4. 访问控制检测
	accessControlEvidence, accessControlConfidence, accessControlPayload, accessControlRequest, accessControlResponse := d.detectAccessControlFlaws(ctx, target)
	if accessControlConfidence > maxConfidence {
		maxConfidence = accessControlConfidence
		vulnerablePayload = accessControlPayload
		vulnerableRequest = accessControlRequest
		vulnerableResponse = accessControlResponse
	}
	evidence = append(evidence, accessControlEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "权限提升漏洞",
		Description:       "检测到权限提升漏洞，可能允许攻击者获得未授权的访问权限",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "实施适当的访问控制机制，验证用户权限，使用基于角色的访问控制(RBAC)",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Privilege_Escalation", "https://cwe.mitre.org/data/definitions/269.html"},
		Tags:              []string{"privilege-escalation", "access-control", "web", "high"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *PrivilegeEscalationDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的路径进行验证
	verificationPaths := []string{
		"/admin",
		"/admin/users",
		"/api/admin/config",
		"/manage",
		"/dashboard",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, path := range verificationPaths {
		// 构造验证URL
		verifyURL := d.buildPrivilegeURL(target.URL, path)

		// 发送验证请求
		resp, err := d.sendPrivilegeRequest(ctx, verifyURL)
		if err != nil {
			continue
		}

		// 检查权限提升响应特征
		responseConfidence := d.checkVerticalPrivilegeEscalation(resp, path)
		if responseConfidence > 0.6 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证路径触发了权限提升响应"),
				Content:     resp,
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "privilege-response-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用权限响应分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *PrivilegeEscalationDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("privilege_escalation_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *PrivilegeEscalationDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (权限提升通常是高风险)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializePaths 初始化路径列表
func (d *PrivilegeEscalationDetector) initializePaths() {
	// 管理员路径
	d.adminPaths = []string{
		// 基础管理路径
		"/admin",
		"/admin/",
		"/admin/index",
		"/admin/home",
		"/admin/dashboard",
		"/admin/panel",
		"/administrator",
		"/administrator/",
		"/administration",
		"/manage",
		"/manage/",
		"/management",
		"/manager",
		"/control",
		"/control/",
		"/console",
		"/console/",

		// 用户管理
		"/admin/users",
		"/admin/user",
		"/admin/accounts",
		"/admin/account",
		"/admin/members",
		"/admin/member",
		"/manage/users",
		"/manage/accounts",

		// 系统管理
		"/admin/settings",
		"/admin/config",
		"/admin/configuration",
		"/admin/system",
		"/admin/logs",
		"/admin/log",
		"/admin/reports",
		"/admin/report",
		"/manage/settings",
		"/manage/config",

		// API管理
		"/api/admin",
		"/api/admin/",
		"/api/admin/users",
		"/api/admin/settings",
		"/api/admin/config",
		"/api/admin/logs",
		"/api/management",
		"/api/manage",
		"/admin/api",

		// 特定应用管理
		"/wp-admin",
		"/wp-admin/",
		"/phpmyadmin",
		"/phpmyadmin/",
		"/webmail/admin",
		"/mail/admin",
		"/cpanel",
		"/plesk",
		"/directadmin",

		// 其他管理路径
		"/backend",
		"/backend/",
		"/backoffice",
		"/office",
		"/staff",
		"/employee",
		"/internal",
		"/private",
		"/secure",
		"/restricted",
	}

	// 用户路径模板
	d.userPaths = []string{
		"/user/{id}",
		"/users/{id}",
		"/profile/{id}",
		"/profiles/{id}",
		"/account/{id}",
		"/accounts/{id}",
		"/member/{id}",
		"/members/{id}",
		"/customer/{id}",
		"/customers/{id}",
		"/api/user/{id}",
		"/api/users/{id}",
		"/api/profile/{id}",
		"/api/account/{id}",
		"/v1/user/{id}",
		"/v1/users/{id}",
		"/v2/user/{id}",
		"/v2/users/{id}",
	}

	// API端点
	d.apiEndpoints = []string{
		"/api/admin",
		"/api/admin/users",
		"/api/admin/settings",
		"/api/admin/config",
		"/api/admin/logs",
		"/api/admin/reports",
		"/api/management",
		"/api/manage",
		"/api/control",
		"/api/console",
		"/admin/api",
		"/manage/api",
		"/v1/admin",
		"/v2/admin",
		"/rest/admin",
		"/graphql/admin",
	}

	// 认证绕过载荷
	d.authBypassPayloads = []string{
		// SQL注入绕过
		"' OR '1'='1",
		"' OR 1=1--",
		"admin'--",
		"admin' OR '1'='1'--",
		"' UNION SELECT 1,1,1--",

		// NoSQL注入绕过
		"[$ne]=null",
		"[$regex]=.*",
		"[$exists]=true",
		"[$gt]=",
		"[$lt]=999999",

		// 逻辑绕过
		"true",
		"false",
		"1",
		"0",
		"null",
		"undefined",
		"[]",
		"{}",

		// 编码绕过
		"%27%20OR%20%271%27%3D%271",
		"%22%20OR%20%221%22%3D%221",
		"JTI3JTIwT1IlMjAlMjcxJTI3JTNEJTI3MQ==", // base64编码

		// 特殊字符绕过
		"admin\x00",
		"admin\r\n",
		"admin\t",
		"admin ",
		" admin",
		"ADMIN",
		"Admin",
		"aDmIn",
	}
}

// initializePatterns 初始化检测模式
func (d *PrivilegeEscalationDetector) initializePatterns() {
	// 权限模式 - 检测权限相关的响应内容
	privilegePatternStrings := []string{
		// 管理员界面指示器
		`(?i)(admin\s+panel|administration\s+panel|管理面板|管理后台)`,
		`(?i)(dashboard|control\s+panel|控制面板|仪表板)`,
		`(?i)(welcome\s+admin|欢迎管理员|admin\s+home)`,
		`(?i)(user\s+management|用户管理|账户管理)`,
		`(?i)(system\s+settings|系统设置|系统配置)`,
		`(?i)(admin\s+menu|管理菜单|后台菜单)`,

		// 权限功能指示器
		`(?i)(create\s+user|创建用户|添加用户)`,
		`(?i)(delete\s+user|删除用户|移除用户)`,
		`(?i)(edit\s+user|编辑用户|修改用户)`,
		`(?i)(manage\s+permissions|权限管理|角色管理)`,
		`(?i)(system\s+logs|系统日志|操作日志)`,
		`(?i)(backup\s+database|数据库备份|系统备份)`,

		// 用户信息指示器
		`(?i)(user\s+profile|用户资料|个人信息)`,
		`(?i)(account\s+details|账户详情|账户信息)`,
		`(?i)(personal\s+data|个人数据|私人信息)`,
		`(?i)(contact\s+information|联系信息|联系方式)`,
		`(?i)(billing\s+information|账单信息|付费信息)`,
		`(?i)(order\s+history|订单历史|购买记录)`,

		// API响应指示器
		`(?i)("role":\s*"admin"|"role":\s*"administrator")`,
		`(?i)("is_admin":\s*true|"isAdmin":\s*true)`,
		`(?i)("permissions":\s*\[|"privileges":\s*\[)`,
		`(?i)("user_type":\s*"admin"|"userType":\s*"admin")`,
		`(?i)("access_level":\s*[5-9]|"accessLevel":\s*[5-9])`,

		// 成功访问指示器
		`(?i)(access\s+granted|访问授权|权限通过)`,
		`(?i)(login\s+successful|登录成功|认证成功)`,
		`(?i)(welcome\s+back|欢迎回来|登录欢迎)`,
		`(?i)(session\s+created|会话创建|会话建立)`,
	}

	d.privilegePatterns = make([]*regexp.Regexp, 0, len(privilegePatternStrings))
	for _, pattern := range privilegePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.privilegePatterns = append(d.privilegePatterns, compiled)
		}
	}

	// 访问拒绝模式 - 检测访问被拒绝的响应
	accessDeniedPatternStrings := []string{
		// 英文拒绝指示器
		`(?i)(access\s+denied|permission\s+denied|unauthorized)`,
		`(?i)(forbidden|not\s+authorized|not\s+permitted)`,
		`(?i)(login\s+required|authentication\s+required)`,
		`(?i)(invalid\s+credentials|wrong\s+password|incorrect\s+password)`,
		`(?i)(session\s+expired|token\s+expired|please\s+login)`,
		`(?i)(insufficient\s+privileges|insufficient\s+permissions)`,

		// 中文拒绝指示器
		`(?i)(访问被拒绝|权限不足|未授权访问)`,
		`(?i)(禁止访问|无权限|需要登录)`,
		`(?i)(认证失败|登录失败|密码错误)`,
		`(?i)(会话过期|令牌过期|请重新登录)`,
		`(?i)(权限不够|访问受限|需要管理员权限)`,

		// HTTP状态指示器
		`(?i)(401\s+unauthorized|403\s+forbidden)`,
		`(?i)(error\s+401|error\s+403|status\s+401|status\s+403)`,

		// 重定向指示器
		`(?i)(redirect.*login|location.*login|href.*login)`,
		`(?i)(redirect.*auth|location.*auth|href.*auth)`,
	}

	d.accessDeniedPatterns = make([]*regexp.Regexp, 0, len(accessDeniedPatternStrings))
	for _, pattern := range accessDeniedPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.accessDeniedPatterns = append(d.accessDeniedPatterns, compiled)
		}
	}

	// 成功访问模式 - 检测成功访问的响应
	successPatternStrings := []string{
		// 成功登录指示器
		`(?i)(welcome|dashboard|home|profile)`,
		`(?i)(logout|sign\s+out|注销|退出)`,
		`(?i)(settings|preferences|配置|设置)`,
		`(?i)(account|profile|个人|账户)`,

		// 管理功能指示器
		`(?i)(users|members|customers|用户|成员)`,
		`(?i)(reports|statistics|logs|报告|统计|日志)`,
		`(?i)(configuration|management|管理|配置)`,
		`(?i)(database|backup|数据库|备份)`,

		// 页面结构指示器
		`(?i)(<title>.*admin|<title>.*管理|<title>.*dashboard)`,
		`(?i)(<h1>.*admin|<h1>.*管理|<h1>.*dashboard)`,
		`(?i)(class="admin|class="dashboard|class="管理)`,
		`(?i)(id="admin|id="dashboard|id="管理)`,

		// JavaScript指示器
		`(?i)(admin\.js|dashboard\.js|管理\.js)`,
		`(?i)(var\s+isAdmin\s*=\s*true|var\s+role\s*=\s*"admin")`,
		`(?i)(window\.admin|window\.dashboard)`,
	}

	d.successPatterns = make([]*regexp.Regexp, 0, len(successPatternStrings))
	for _, pattern := range successPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.successPatterns = append(d.successPatterns, compiled)
		}
	}
}
