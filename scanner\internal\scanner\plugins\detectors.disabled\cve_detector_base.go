package detectors

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"scanner/internal/scanner/plugins"
)

// CVEDetectorBase CVE检测器基类
// 为特定CVE检测器提供通用功能和标准化接口
type CVEDetectorBase struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// HTTP客户端
	httpClient *http.Client
}

// CVEDetectorInterface CVE检测器专用接口
// 扩展基础VulnerabilityDetector接口，添加CVE特定功能
type CVEDetectorInterface interface {
	plugins.VulnerabilityDetector

	// CVE特定方法
	GetCVEID() string                                    // 获取主要CVE ID
	GetAffectedProducts() []string                       // 获取受影响产品
	GetAffectedVersions() []string                       // 获取受影响版本
	GetPoCAvailable() bool                               // 是否有PoC可用
	GetExploitAvailable() bool                           // 是否有公开利用代码
	
	// 检测方法
	DetectCVE(ctx context.Context, target *plugins.ScanTarget) (*CVEDetectionResult, error)
	VerifyCVE(ctx context.Context, target *plugins.ScanTarget, result *CVEDetectionResult) (*CVEVerificationResult, error)
	
	// PoC执行
	ExecutePoC(ctx context.Context, target *plugins.ScanTarget) (*PoCExecutionResult, error)
}

// CVEDetectionResult CVE检测结果
type CVEDetectionResult struct {
	*plugins.DetectionResult
	
	// CVE特定字段
	CVEID              string                 `json:"cve_id"`
	CVSSScore          float64                `json:"cvss_score"`
	CVSSVector         string                 `json:"cvss_vector"`
	AffectedComponent  string                 `json:"affected_component"`
	ComponentVersion   string                 `json:"component_version"`
	ExploitComplexity  string                 `json:"exploit_complexity"`
	RequiredPrivileges string                 `json:"required_privileges"`
	UserInteraction    string                 `json:"user_interaction"`
	PoCExecuted        bool                   `json:"poc_executed"`
	ExploitCode        string                 `json:"exploit_code,omitempty"`
}

// CVEVerificationResult CVE验证结果
type CVEVerificationResult struct {
	*plugins.VerificationResult
	
	// CVE特定验证信息
	VerificationMethod string                 `json:"verification_method"`
	PoCResult          *PoCExecutionResult    `json:"poc_result,omitempty"`
	ExploitSuccess     bool                   `json:"exploit_success"`
	ImpactConfirmed    bool                   `json:"impact_confirmed"`
}

// PoCExecutionResult PoC执行结果
type PoCExecutionResult struct {
	Success        bool                   `json:"success"`
	ExecutionTime  time.Duration          `json:"execution_time"`
	ResponseCode   int                    `json:"response_code"`
	ResponseBody   string                 `json:"response_body"`
	ErrorMessage   string                 `json:"error_message,omitempty"`
	Evidence       []string               `json:"evidence"`
	Metadata       map[string]interface{} `json:"metadata"`
	ExecutedAt     time.Time              `json:"executed_at"`
}

// NewCVEDetectorBase 创建CVE检测器基类实例
func NewCVEDetectorBase(cveID, name, description string) *CVEDetectorBase {
	return &CVEDetectorBase{
		id:          cveID,
		name:        name,
		description: description,
		cve:         []string{cveID},
		version:     "1.0",
		author:      "Security Scanner",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// 实现VulnerabilityDetector接口的基础方法
func (d *CVEDetectorBase) GetID() string                    { return d.id }
func (d *CVEDetectorBase) GetName() string                  { return d.name }
func (d *CVEDetectorBase) GetCategory() string              { return d.category }
func (d *CVEDetectorBase) GetSeverity() string              { return d.severity }
func (d *CVEDetectorBase) GetCVE() []string                 { return d.cve }
func (d *CVEDetectorBase) GetCWE() []string                 { return d.cwe }
func (d *CVEDetectorBase) GetVersion() string               { return d.version }
func (d *CVEDetectorBase) GetAuthor() string                { return d.author }
func (d *CVEDetectorBase) GetCreatedAt() time.Time          { return d.createdAt }
func (d *CVEDetectorBase) GetUpdatedAt() time.Time          { return d.updatedAt }
func (d *CVEDetectorBase) GetDescription() string           { return d.description }
func (d *CVEDetectorBase) IsEnabled() bool                  { return d.enabled }
func (d *CVEDetectorBase) SetEnabled(enabled bool)          { d.enabled = enabled }
func (d *CVEDetectorBase) GetConfiguration() *plugins.DetectorConfig { return d.config }

func (d *CVEDetectorBase) SetConfiguration(config *plugins.DetectorConfig) error {
	d.config = config
	return nil
}

func (d *CVEDetectorBase) GetDependencies() []string {
	return []string{} // 大多数CVE检测器无依赖
}

func (d *CVEDetectorBase) Initialize() error {
	// 基础初始化逻辑
	if d.httpClient == nil {
		d.httpClient = &http.Client{
			Timeout: 30 * time.Second,
		}
	}
	return nil
}

func (d *CVEDetectorBase) Cleanup() error {
	// 基础清理逻辑
	return nil
}

func (d *CVEDetectorBase) Validate() error {
	if d.id == "" {
		return fmt.Errorf("CVE检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("CVE检测器名称不能为空")
	}
	if len(d.cve) == 0 {
		return fmt.Errorf("CVE检测器必须关联至少一个CVE")
	}
	return nil
}

// 默认目标适用性检查
func (d *CVEDetectorBase) GetTargetTypes() []string {
	return []string{"http", "https"}
}

func (d *CVEDetectorBase) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443}
}

func (d *CVEDetectorBase) GetRequiredServices() []string {
	return []string{"http"}
}

func (d *CVEDetectorBase) GetRequiredHeaders() []string {
	return []string{}
}

func (d *CVEDetectorBase) GetRequiredTechnologies() []string {
	return []string{}
}

func (d *CVEDetectorBase) IsApplicable(target *plugins.ScanTarget) bool {
	// 基础适用性检查：HTTP/HTTPS目标
	return target.Type == "http" || target.Type == "https"
}

// 默认检测方法（子类应重写）
func (d *CVEDetectorBase) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	return &plugins.DetectionResult{
		VulnerabilityID: d.generateVulnID(target),
		DetectorID:      d.id,
		IsVulnerable:    false,
		Confidence:      0.0,
		Severity:        d.severity,
		Message:         "基础CVE检测器，需要子类实现具体检测逻辑",
		DetectedAt:      time.Now(),
	}, nil
}

// 默认验证方法（子类应重写）
func (d *CVEDetectorBase) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	return &plugins.VerificationResult{
		IsVerified: false,
		Confidence: 0.0,
		Method:     "base-verification",
		Notes:      "基础CVE验证器，需要子类实现具体验证逻辑",
		VerifiedAt: time.Now(),
	}, nil
}

// 生成漏洞ID
func (d *CVEDetectorBase) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("%s_%s_%d", d.id, target.Host, time.Now().Unix())
}

// 工具方法：发送HTTP请求
func (d *CVEDetectorBase) makeRequest(ctx context.Context, url string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return "", err
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body := make([]byte, 4096)
	n, _ := resp.Body.Read(body)
	return string(body[:n]), nil
}
