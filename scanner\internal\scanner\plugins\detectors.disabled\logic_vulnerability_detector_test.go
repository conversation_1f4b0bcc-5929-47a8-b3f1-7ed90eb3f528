package detectors

import (
	"testing"
	"time"

	"scanner/internal/scanner/plugins"
)

// TestLogicVulnerabilityDetectorBasicFunctionality 测试逻辑漏洞检测器基本功能
func TestLogicVulnerabilityDetectorBasicFunctionality(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	// 测试基本信息
	if detector.GetID() != "logic-vulnerability-comprehensive" {
		t.<PERSON>("Expected ID 'logic-vulnerability-comprehensive', got '%s'", detector.GetID())
	}

	if detector.GetName() != "逻辑漏洞综合检测器" {
		t.<PERSON><PERSON><PERSON>("Expected name '逻辑漏洞综合检测器', got '%s'", detector.GetName())
	}

	if detector.GetCategory() != "web" {
		t.<PERSON>rf("Expected category 'web', got '%s'", detector.GetCategory())
	}

	if detector.GetSeverity() != "high" {
		t.<PERSON><PERSON><PERSON>("Expected severity 'high', got '%s'", detector.GetSeverity())
	}

	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled by default")
	}

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	expectedTypes := []string{"http", "https"}
	if len(targetTypes) != len(expectedTypes) {
		t.Errorf("Expected %d target types, got %d", len(expectedTypes), len(targetTypes))
	}

	// 测试端口
	ports := detector.GetRequiredPorts()
	if len(ports) == 0 {
		t.Error("Expected some required ports")
	}

	// 测试服务
	services := detector.GetRequiredServices()
	expectedServices := []string{"http", "https", "web", "api"}
	if len(services) != len(expectedServices) {
		t.Errorf("Expected %d services, got %d", len(expectedServices), len(services))
	}
}

// TestLogicVulnerabilityDetectorApplicability 测试逻辑漏洞检测器适用性
func TestLogicVulnerabilityDetectorApplicability(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "HTTP URL目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有逻辑漏洞关键词的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com/login/admin",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有业务关键词的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com/order/payment",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有表单的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Forms: []plugins.FormInfo{
					{
						Action: "/login",
						Method: "POST",
						Fields: map[string]string{"username": "text", "password": "password"},
					},
				},
			},
			expected: true,
		},
		{
			name: "有业务技术栈的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Technologies: []plugins.TechnologyInfo{
					{Name: "Spring", Version: "5.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "非Web目标",
			target: &plugins.ScanTarget{
				Type:     "ip",
				Protocol: "tcp",
				Port:     22,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.IsApplicable(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestLogicVulnerabilityDetectorConfiguration 测试逻辑漏洞检测器配置
func TestLogicVulnerabilityDetectorConfiguration(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	if config.Timeout != 20*time.Second {
		t.Errorf("Expected timeout 20s, got %v", config.Timeout)
	}

	if config.MaxRetries != 2 {
		t.Errorf("Expected max retries 2, got %d", config.MaxRetries)
	}

	if config.Concurrency != 5 {
		t.Errorf("Expected concurrency 5, got %d", config.Concurrency)
	}

	if config.RateLimit != 5 {
		t.Errorf("Expected rate limit 5, got %d", config.RateLimit)
	}

	if !config.FollowRedirects {
		t.Error("Expected FollowRedirects to be true")
	}

	if config.VerifySSL {
		t.Error("Expected VerifySSL to be false")
	}

	if config.MaxResponseSize != 2*1024*1024 {
		t.Errorf("Expected max response size 2MB, got %d", config.MaxResponseSize)
	}

	// 测试配置更新
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     8,
		RateLimit:       8,
		FollowRedirects: false,
		VerifySSL:       true,
		MaxResponseSize: 5 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	if err != nil {
		t.Errorf("Failed to set configuration: %v", err)
	}

	updatedConfig := detector.GetConfiguration()
	if updatedConfig.Timeout != 30*time.Second {
		t.Errorf("Expected updated timeout 30s, got %v", updatedConfig.Timeout)
	}
}

// TestLogicVulnerabilityDetectorBusinessLogicPayloads 测试业务逻辑载荷
func TestLogicVulnerabilityDetectorBusinessLogicPayloads(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	if len(detector.businessLogicPayloads) == 0 {
		t.Error("Expected some business logic payloads")
	}

	// 检查是否包含关键的业务逻辑载荷
	expectedPayloads := []string{
		"price=-1",
		"price=0",
		"amount=-100",
		"quantity=-1",
		"role=admin",
		"user_id=1",
		"order_status=completed",
		"limit=999999",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.businessLogicPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find business logic payload '%s'", expected)
		}
	}
}

// TestLogicVulnerabilityDetectorPermissionPayloads 测试权限控制载荷
func TestLogicVulnerabilityDetectorPermissionPayloads(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	if len(detector.permissionPayloads) == 0 {
		t.Error("Expected some permission payloads")
	}

	// 检查是否包含关键的权限控制载荷
	expectedPayloads := []string{
		"../admin",
		"user=admin",
		"username=admin",
		"admin=1",
		"access_level=admin",
		"session_role=admin",
		"read=true",
		"write=true",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.permissionPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find permission payload '%s'", expected)
		}
	}
}

// TestLogicVulnerabilityDetectorDataValidationPayloads 测试数据验证载荷
func TestLogicVulnerabilityDetectorDataValidationPayloads(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	if len(detector.dataValidationPayloads) == 0 {
		t.Error("Expected some data validation payloads")
	}

	// 检查是否包含关键的数据验证载荷
	expectedPayloads := []string{
		"age=-1",
		"age=999",
		"month=13",
		"email=invalid",
		"phone=invalid",
		"id=string",
		"score=-100",
		"required=",
		"username=admin",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.dataValidationPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find data validation payload '%s'", expected)
		}
	}
}

// TestLogicVulnerabilityDetectorProcessControlPayloads 测试流程控制载荷
func TestLogicVulnerabilityDetectorProcessControlPayloads(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	if len(detector.processControlPayloads) == 0 {
		t.Error("Expected some process control payloads")
	}

	// 检查是否包含关键的流程控制载荷
	expectedPayloads := []string{
		"step=skip",
		"stage=bypass",
		"status=completed",
		"approval=auto",
		"wait=0",
		"if=true",
		"loop=break",
		"error=ignore",
		"auth_step=skip",
		"payment_step=skip",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.processControlPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find process control payload '%s'", expected)
		}
	}
}

// TestLogicVulnerabilityDetectorTestParameters 测试测试参数
func TestLogicVulnerabilityDetectorTestParameters(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	if len(detector.testParameters) == 0 {
		t.Error("Expected some test parameters")
	}

	// 检查是否包含关键的测试参数
	expectedParams := []string{
		"logic", "business", "process", "workflow", "permission", "access",
		"role", "user", "admin", "order", "payment", "price", "amount",
		"data", "validate", "step", "status", "control", "逻辑", "业务",
		"权限", "用户", "订单", "支付", "数据", "验证", "状态", "控制",
	}

	for _, expected := range expectedParams {
		found := false
		for _, param := range detector.testParameters {
			if param == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find test parameter '%s'", expected)
		}
	}
}

// TestLogicVulnerabilityDetectorPatterns 测试检测模式
func TestLogicVulnerabilityDetectorPatterns(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	// 测试逻辑漏洞模式
	if len(detector.logicPatterns) == 0 {
		t.Error("Expected some logic patterns")
	}

	// 测试业务逻辑模式
	if len(detector.businessPatterns) == 0 {
		t.Error("Expected some business patterns")
	}

	// 测试权限控制模式
	if len(detector.permissionPatterns) == 0 {
		t.Error("Expected some permission patterns")
	}

	// 测试数据验证模式
	if len(detector.validationPatterns) == 0 {
		t.Error("Expected some validation patterns")
	}
}

// TestLogicVulnerabilityDetectorLogicVulnerabilityFeatures 测试逻辑漏洞功能检查
func TestLogicVulnerabilityDetectorLogicVulnerabilityFeatures(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "有认证头部的目标",
			target: &plugins.ScanTarget{
				Headers: map[string]string{
					"Authorization": "Bearer token",
				},
			},
			expected: true,
		},
		{
			name: "有业务技术栈的目标",
			target: &plugins.ScanTarget{
				Technologies: []plugins.TechnologyInfo{
					{Name: "Spring", Version: "5.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有逻辑漏洞链接的目标",
			target: &plugins.ScanTarget{
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/admin", Text: "Admin Panel"},
				},
			},
			expected: true,
		},
		{
			name: "有逻辑漏洞字段的表单目标",
			target: &plugins.ScanTarget{
				Forms: []plugins.FormInfo{
					{
						Fields: map[string]string{"role": "text", "permission": "text"},
					},
				},
			},
			expected: true,
		},
		{
			name: "普通目标",
			target: &plugins.ScanTarget{
				URL: "https://example.com",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.hasLogicVulnerabilityFeatures(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestLogicVulnerabilityDetectorRiskScore 测试风险评分
func TestLogicVulnerabilityDetectorRiskScore(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	testCases := []struct {
		confidence float64
		expected   float64
	}{
		{0.0, 0.0},
		{0.5, 4.0},
		{0.8, 6.4},
		{1.0, 8.0},
	}

	for _, tc := range testCases {
		score := detector.calculateRiskScore(tc.confidence)
		// 使用浮点数比较的容差
		if score < tc.expected-0.01 || score > tc.expected+0.01 {
			t.Errorf("Expected risk score %.1f for confidence %.1f, got %.1f", tc.expected, tc.confidence, score)
		}
	}
}

// TestLogicVulnerabilityDetectorLifecycle 测试检测器生命周期
func TestLogicVulnerabilityDetectorLifecycle(t *testing.T) {
	detector := NewLogicVulnerabilityDetector()

	// 测试初始化
	err := detector.Initialize()
	if err != nil {
		t.Errorf("Failed to initialize detector: %v", err)
	}

	// 测试验证
	err = detector.Validate()
	if err != nil {
		t.Errorf("Detector validation failed: %v", err)
	}

	// 测试启用/禁用
	detector.SetEnabled(false)
	if detector.IsEnabled() {
		t.Error("Expected detector to be disabled")
	}

	detector.SetEnabled(true)
	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled")
	}

	// 测试清理
	err = detector.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup detector: %v", err)
	}
}
