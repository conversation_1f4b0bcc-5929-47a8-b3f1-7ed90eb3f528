package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectBusinessLogicVulnerability 检测业务逻辑漏洞
func (d *LogicVulnerabilityDetector) detectBusinessLogicVulnerability(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试业务逻辑载荷
	for _, payload := range d.businessLogicPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送业务逻辑测试请求
		response, err := d.sendLogicVulnerabilityRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在业务逻辑漏洞
		confidence := d.checkBusinessLogicVulnerability(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("业务逻辑漏洞: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "business-logic-vulnerability",
				Description: fmt.Sprintf("发现业务逻辑漏洞: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractLogicVulnerabilityEvidence(response, payload, "business-logic"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 100)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPermissionVulnerability 检测权限控制漏洞
func (d *LogicVulnerabilityDetector) detectPermissionVulnerability(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试权限控制载荷
	for _, payload := range d.permissionPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送权限控制测试请求
		response, err := d.sendLogicVulnerabilityRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在权限控制漏洞
		confidence := d.checkPermissionVulnerability(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("权限控制漏洞: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "permission-vulnerability",
				Description: fmt.Sprintf("发现权限控制漏洞: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractLogicVulnerabilityEvidence(response, payload, "permission"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 120)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDataValidationVulnerability 检测数据验证漏洞
func (d *LogicVulnerabilityDetector) detectDataValidationVulnerability(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试数据验证载荷
	for _, payload := range d.dataValidationPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送数据验证测试请求
		response, err := d.sendLogicVulnerabilityRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在数据验证漏洞
		confidence := d.checkDataValidationVulnerability(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("数据验证漏洞: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "data-validation-vulnerability",
				Description: fmt.Sprintf("发现数据验证漏洞: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractLogicVulnerabilityEvidence(response, payload, "data-validation"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectProcessControlVulnerability 检测流程控制漏洞
func (d *LogicVulnerabilityDetector) detectProcessControlVulnerability(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试流程控制载荷
	for _, payload := range d.processControlPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送流程控制测试请求
		response, err := d.sendLogicVulnerabilityRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在流程控制漏洞
		confidence := d.checkProcessControlVulnerability(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("流程控制漏洞: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "process-control-vulnerability",
				Description: fmt.Sprintf("发现流程控制漏洞: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractLogicVulnerabilityEvidence(response, payload, "process-control"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 180)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendLogicVulnerabilityRequest 发送逻辑漏洞测试请求
func (d *LogicVulnerabilityDetector) sendLogicVulnerabilityRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendLogicGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST表单注入
	postResp, err := d.sendLogicPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 如果有POST响应（即使有错误），也返回
	if postResp != "" {
		return postResp, nil
	}

	return "", fmt.Errorf("所有逻辑漏洞请求都失败")
}

// sendLogicGETRequest 发送逻辑漏洞GET请求
func (d *LogicVulnerabilityDetector) sendLogicGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// sendLogicPOSTRequest 发送逻辑漏洞POST请求
func (d *LogicVulnerabilityDetector) sendLogicPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// checkBusinessLogicVulnerability 检查业务逻辑漏洞
func (d *LogicVulnerabilityDetector) checkBusinessLogicVulnerability(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查业务逻辑相关的响应模式
	for _, pattern := range d.businessPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查价格操作相关响应
	if strings.Contains(payloadLower, "price") || strings.Contains(payloadLower, "amount") {
		if strings.Contains(responseLower, "success") ||
			strings.Contains(responseLower, "accepted") ||
			strings.Contains(responseLower, "valid") ||
			strings.Contains(responseLower, "成功") ||
			strings.Contains(responseLower, "接受") ||
			strings.Contains(responseLower, "有效") {
			confidence += 0.4
		}
	}

	// 检查权限提升相关响应
	if strings.Contains(payloadLower, "admin") || strings.Contains(payloadLower, "role") {
		if strings.Contains(responseLower, "admin") ||
			strings.Contains(responseLower, "administrator") ||
			strings.Contains(responseLower, "管理") ||
			strings.Contains(responseLower, "权限") {
			confidence += 0.3
		}
	}

	// 检查订单状态相关响应
	if strings.Contains(payloadLower, "order") || strings.Contains(payloadLower, "status") {
		if strings.Contains(responseLower, "completed") ||
			strings.Contains(responseLower, "approved") ||
			strings.Contains(responseLower, "完成") ||
			strings.Contains(responseLower, "批准") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkPermissionVulnerability 检查权限控制漏洞
func (d *LogicVulnerabilityDetector) checkPermissionVulnerability(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查权限控制相关的响应模式
	for _, pattern := range d.permissionPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查权限提升相关响应
	if strings.Contains(payloadLower, "admin") || strings.Contains(payloadLower, "root") {
		if strings.Contains(responseLower, "admin") ||
			strings.Contains(responseLower, "administrator") ||
			strings.Contains(responseLower, "root") ||
			strings.Contains(responseLower, "superuser") ||
			strings.Contains(responseLower, "管理员") ||
			strings.Contains(responseLower, "超级用户") {
			confidence += 0.4
		}
	}

	// 检查访问控制绕过响应
	if strings.Contains(payloadLower, "access") || strings.Contains(payloadLower, "permission") {
		if strings.Contains(responseLower, "granted") ||
			strings.Contains(responseLower, "allowed") ||
			strings.Contains(responseLower, "authorized") ||
			strings.Contains(responseLower, "permitted") ||
			strings.Contains(responseLower, "授权") ||
			strings.Contains(responseLower, "允许") {
			confidence += 0.3
		}
	}

	// 检查角色切换相关响应
	if strings.Contains(payloadLower, "role") || strings.Contains(payloadLower, "user") {
		if strings.Contains(responseLower, "switched") ||
			strings.Contains(responseLower, "changed") ||
			strings.Contains(responseLower, "updated") ||
			strings.Contains(responseLower, "切换") ||
			strings.Contains(responseLower, "更改") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkDataValidationVulnerability 检查数据验证漏洞
func (d *LogicVulnerabilityDetector) checkDataValidationVulnerability(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查数据验证相关的响应模式
	for _, pattern := range d.validationPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查无效数据被接受的响应
	if strings.Contains(payloadLower, "invalid") || strings.Contains(payloadLower, "-1") {
		if strings.Contains(responseLower, "success") ||
			strings.Contains(responseLower, "accepted") ||
			strings.Contains(responseLower, "valid") ||
			strings.Contains(responseLower, "成功") ||
			strings.Contains(responseLower, "接受") {
			confidence += 0.4
		}
	}

	// 检查长度验证绕过响应
	if len(payload) > 500 {
		if strings.Contains(responseLower, "success") ||
			strings.Contains(responseLower, "saved") ||
			strings.Contains(responseLower, "stored") ||
			strings.Contains(responseLower, "成功") ||
			strings.Contains(responseLower, "保存") {
			confidence += 0.3
		}
	}

	// 检查格式验证绕过响应
	if strings.Contains(payloadLower, "@") && !strings.Contains(payloadLower, ".") {
		if strings.Contains(responseLower, "valid") ||
			strings.Contains(responseLower, "correct") ||
			strings.Contains(responseLower, "有效") ||
			strings.Contains(responseLower, "正确") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkProcessControlVulnerability 检查流程控制漏洞
func (d *LogicVulnerabilityDetector) checkProcessControlVulnerability(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查逻辑漏洞相关的响应模式
	for _, pattern := range d.logicPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查流程跳过相关响应
	if strings.Contains(payloadLower, "skip") || strings.Contains(payloadLower, "bypass") {
		if strings.Contains(responseLower, "completed") ||
			strings.Contains(responseLower, "finished") ||
			strings.Contains(responseLower, "done") ||
			strings.Contains(responseLower, "完成") ||
			strings.Contains(responseLower, "结束") {
			confidence += 0.4
		}
	}

	// 检查状态操作相关响应
	if strings.Contains(payloadLower, "status") || strings.Contains(payloadLower, "state") {
		if strings.Contains(responseLower, "updated") ||
			strings.Contains(responseLower, "changed") ||
			strings.Contains(responseLower, "modified") ||
			strings.Contains(responseLower, "更新") ||
			strings.Contains(responseLower, "修改") {
			confidence += 0.3
		}
	}

	// 检查审批绕过相关响应
	if strings.Contains(payloadLower, "approval") || strings.Contains(payloadLower, "review") {
		if strings.Contains(responseLower, "approved") ||
			strings.Contains(responseLower, "authorized") ||
			strings.Contains(responseLower, "granted") ||
			strings.Contains(responseLower, "批准") ||
			strings.Contains(responseLower, "授权") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// extractLogicVulnerabilityEvidence 提取逻辑漏洞证据
func (d *LogicVulnerabilityDetector) extractLogicVulnerabilityEvidence(response, payload, vulnType string) string {
	// 限制证据长度
	maxLength := 1000
	evidence := fmt.Sprintf("逻辑漏洞证据 (%s):\n", vulnType)

	// 添加载荷信息
	evidence += fmt.Sprintf("载荷: %s\n", payload)
	evidence += fmt.Sprintf("漏洞类型: %s\n", vulnType)

	// 检查响应中的逻辑漏洞相关模式
	evidence += "\n逻辑模式匹配:\n"
	for _, pattern := range d.logicPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现逻辑模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的业务逻辑相关模式
	evidence += "\n业务逻辑模式匹配:\n"
	for _, pattern := range d.businessPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现业务逻辑模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的权限控制相关模式
	evidence += "\n权限控制模式匹配:\n"
	for _, pattern := range d.permissionPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现权限控制模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的数据验证相关模式
	evidence += "\n数据验证模式匹配:\n"
	for _, pattern := range d.validationPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现数据验证模式: %s\n", pattern.String())
		}
	}

	// 添加响应摘要
	if len(response) > 200 {
		evidence += fmt.Sprintf("\n响应摘要: %s...\n", response[:200])
	} else {
		evidence += fmt.Sprintf("\n响应内容: %s\n", response)
	}

	// 分析载荷类型
	evidence += d.analyzeLogicPayloadType(payload, vulnType)

	// 限制证据长度
	if len(evidence) > maxLength {
		evidence = evidence[:maxLength] + "..."
	}

	return evidence
}

// analyzeLogicPayloadType 分析逻辑载荷类型
func (d *LogicVulnerabilityDetector) analyzeLogicPayloadType(payload, vulnType string) string {
	analysis := "\n载荷分析:\n"
	payloadLower := strings.ToLower(payload)

	// 业务逻辑载荷分析
	if vulnType == "business-logic" {
		analysis += "- 载荷类型: 业务逻辑漏洞\n"

		if strings.Contains(payloadLower, "price") || strings.Contains(payloadLower, "amount") {
			analysis += "- 攻击目标: 价格操作\n"
		} else if strings.Contains(payloadLower, "quantity") || strings.Contains(payloadLower, "count") {
			analysis += "- 攻击目标: 数量操作\n"
		} else if strings.Contains(payloadLower, "order") || strings.Contains(payloadLower, "payment") {
			analysis += "- 攻击目标: 订单操作\n"
		} else if strings.Contains(payloadLower, "limit") || strings.Contains(payloadLower, "restriction") {
			analysis += "- 攻击目标: 限制绕过\n"
		}
	}

	// 权限控制载荷分析
	if vulnType == "permission" {
		analysis += "- 载荷类型: 权限控制漏洞\n"

		if strings.Contains(payloadLower, "admin") || strings.Contains(payloadLower, "root") {
			analysis += "- 攻击目标: 权限提升\n"
		} else if strings.Contains(payloadLower, "user") || strings.Contains(payloadLower, "switch") {
			analysis += "- 攻击目标: 用户切换\n"
		} else if strings.Contains(payloadLower, "role") || strings.Contains(payloadLower, "group") {
			analysis += "- 攻击目标: 角色绕过\n"
		} else if strings.Contains(payloadLower, "access") || strings.Contains(payloadLower, "permission") {
			analysis += "- 攻击目标: 访问控制\n"
		}
	}

	// 数据验证载荷分析
	if vulnType == "data-validation" {
		analysis += "- 载荷类型: 数据验证漏洞\n"

		if strings.Contains(payloadLower, "age") || strings.Contains(payloadLower, "year") {
			analysis += "- 攻击目标: 数值验证\n"
		} else if len(payload) > 500 {
			analysis += "- 攻击目标: 长度验证\n"
		} else if strings.Contains(payloadLower, "email") || strings.Contains(payloadLower, "@") {
			analysis += "- 攻击目标: 格式验证\n"
		} else if strings.Contains(payloadLower, "required") || strings.Contains(payloadLower, "mandatory") {
			analysis += "- 攻击目标: 必填验证\n"
		}
	}

	// 流程控制载荷分析
	if vulnType == "process-control" {
		analysis += "- 载荷类型: 流程控制漏洞\n"

		if strings.Contains(payloadLower, "skip") || strings.Contains(payloadLower, "bypass") {
			analysis += "- 攻击目标: 流程跳过\n"
		} else if strings.Contains(payloadLower, "status") || strings.Contains(payloadLower, "state") {
			analysis += "- 攻击目标: 状态操作\n"
		} else if strings.Contains(payloadLower, "approval") || strings.Contains(payloadLower, "review") {
			analysis += "- 攻击目标: 审批绕过\n"
		} else if strings.Contains(payloadLower, "wait") || strings.Contains(payloadLower, "delay") {
			analysis += "- 攻击目标: 时间控制\n"
		}
	}

	// 检查载荷特征
	if strings.Contains(payload, "-1") || strings.Contains(payload, "0") {
		analysis += "- 载荷特征: 边界值测试\n"
	} else if strings.Contains(payload, "admin") || strings.Contains(payload, "root") {
		analysis += "- 载荷特征: 权限提升测试\n"
	} else if strings.Contains(payload, "true") || strings.Contains(payload, "false") {
		analysis += "- 载荷特征: 布尔值测试\n"
	} else if strings.Contains(payload, "skip") || strings.Contains(payload, "bypass") {
		analysis += "- 载荷特征: 绕过测试\n"
	}

	return analysis
}
