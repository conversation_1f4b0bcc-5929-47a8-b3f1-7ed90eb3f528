package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectXMLEntityInjection 检测XML实体注入
func (d *XMLInjectionDetector) detectXMLEntityInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试XML实体注入载荷
	for _, payload := range d.xmlEntityPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送XML实体注入请求
		resp, err := d.sendXMLRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查XML实体注入响应
		confidence := d.checkXMLEntityInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("XML实体注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "xml-entity-injection",
				Description: fmt.Sprintf("发现XML实体注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractXMLEvidence(resp, "xml-entity-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 250)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectXPathInjection 检测XPath注入
func (d *XMLInjectionDetector) detectXPathInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试XPath注入载荷
	for _, payload := range d.xpathPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送XPath注入请求
		resp, err := d.sendXMLRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查XPath注入响应
		confidence := d.checkXPathInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("XPath注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "xpath-injection",
				Description: fmt.Sprintf("发现XPath注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractXMLEvidence(resp, "xpath-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 250)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectXMLStructureInjection 检测XML结构注入
func (d *XMLInjectionDetector) detectXMLStructureInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试XML结构注入载荷
	for _, payload := range d.xmlStructurePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送XML结构注入请求
		resp, err := d.sendXMLRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查XML结构注入响应
		confidence := d.checkXMLStructureInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("XML结构注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "xml-structure-injection",
				Description: fmt.Sprintf("发现XML结构注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractXMLEvidence(resp, "xml-structure-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 250)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectSOAPInjection 检测SOAP注入
func (d *XMLInjectionDetector) detectSOAPInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试SOAP注入载荷
	for _, payload := range d.soapPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送SOAP注入请求
		resp, err := d.sendSOAPRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查SOAP注入响应
		confidence := d.checkSOAPInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("SOAP注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "soap-injection",
				Description: fmt.Sprintf("发现SOAP注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractXMLEvidence(resp, "soap-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 250)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendXMLRequest 发送XML注入请求
func (d *XMLInjectionDetector) sendXMLRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendXMLGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST参数注入
	postResp, err := d.sendXMLPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 尝试XML载荷注入
	xmlResp, err := d.sendXMLBodyRequest(ctx, targetURL, payload)
	if err == nil && xmlResp != "" {
		return xmlResp, nil
	}

	// 返回GET响应（即使有错误）
	if getResp != "" {
		return getResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendXMLGETRequest 发送XML注入GET请求
func (d *XMLInjectionDetector) sendXMLGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/xml,text/xml,application/xhtml+xml,text/html,application/json,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendXMLPOSTRequest 发送XML注入POST请求
func (d *XMLInjectionDetector) sendXMLPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造POST数据
	postData := url.Values{}

	for _, param := range d.testParameters {
		postData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/xml,text/xml,application/xhtml+xml,text/html,application/json,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendXMLBodyRequest 发送XML载荷请求
func (d *XMLInjectionDetector) sendXMLBodyRequest(ctx context.Context, targetURL, payload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(payload))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/xml,text/xml,application/xhtml+xml,text/html,application/json,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/xml")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendSOAPRequest 发送SOAP请求
func (d *XMLInjectionDetector) sendSOAPRequest(ctx context.Context, targetURL, payload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(payload))
	if err != nil {
		return "", err
	}

	// 设置SOAP请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/soap+xml,application/xml,text/xml,*/*;q=0.8")
	req.Header.Set("Content-Type", "text/xml; charset=utf-8")
	req.Header.Set("SOAPAction", "\"\"")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkXMLEntityInjectionResponse 检查XML实体注入响应
func (d *XMLInjectionDetector) checkXMLEntityInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示注入错误
	}

	// 检查XML模式匹配
	for _, pattern := range d.xmlPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查XML实体特定错误
	xmlEntityErrors := []string{
		"entity not found", "entity reference", "external entity",
		"parameter entity", "recursive entity", "entity error",
		"dtd error", "parsing error", "xml error", "malformed xml",
		"实体错误", "解析错误", "xml错误", "格式错误",
	}

	for _, error := range xmlEntityErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // XML实体特定错误的强指示器
			break
		}
	}

	// 检查文件内容泄露
	fileContents := []string{
		"root:", "daemon:", "bin:", "sys:", "localhost", "127.0.0.1",
		"windows", "system32", "drivers", "etc", "hosts", "passwd",
	}

	for _, content := range fileContents {
		if strings.Contains(response, content) {
			confidence += 0.8 // 文件内容泄露的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkXPathInjectionResponse 检查XPath注入响应
func (d *XMLInjectionDetector) checkXPathInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 400") ||
		strings.Contains(response, "status: 500") {
		confidence += 0.4 // 错误响应可能表示注入错误
	}

	// 检查XPath模式匹配
	for _, pattern := range d.xpathPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查XPath特定错误
	xpathErrors := []string{
		"xpath error", "xpath syntax error", "invalid xpath",
		"xpath expression", "node not found", "axis not supported",
		"unknown function", "function not found", "invalid function",
		"xpath错误", "路径错误", "节点错误", "表达式错误",
	}

	for _, error := range xpathErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // XPath特定错误的强指示器
			break
		}
	}

	// 检查XPath函数
	xpathFunctions := []string{
		"string-length", "substring", "contains", "starts-with",
		"normalize-space", "translate", "concat", "position",
		"last", "count", "sum", "boolean", "number", "string",
	}

	for _, function := range xpathFunctions {
		if strings.Contains(response, function) {
			confidence += 0.4 // XPath函数的指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkXMLStructureInjectionResponse 检查XML结构注入响应
func (d *XMLInjectionDetector) checkXMLStructureInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 400") ||
		strings.Contains(response, "status: 500") {
		confidence += 0.4 // 错误响应可能表示注入错误
	}

	// 检查XML模式匹配
	for _, pattern := range d.xmlPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查XML结构特定错误
	xmlStructureErrors := []string{
		"malformed xml", "invalid xml", "xml syntax error",
		"unexpected end of file", "parsing error", "format error",
		"tag mismatch", "unclosed tag", "invalid character",
		"xml格式错误", "解析错误", "标签错误", "字符错误",
	}

	for _, error := range xmlStructureErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // XML结构特定错误的强指示器
			break
		}
	}

	// 检查注入的XML标签
	injectedTags := []string{
		"<injected>", "</injected>", "<script>", "</script>",
		"<test>", "</test>", "<evil>", "</evil>", "malicious",
	}

	for _, tag := range injectedTags {
		if strings.Contains(response, tag) {
			confidence += 0.7 // 注入标签的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkSOAPInjectionResponse 检查SOAP注入响应
func (d *XMLInjectionDetector) checkSOAPInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // SOAP错误通常返回500
	}

	// 检查SOAP模式匹配
	for _, pattern := range d.soapPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查SOAP特定错误
	soapErrors := []string{
		"soap fault", "soap error", "faultcode", "faultstring",
		"faultactor", "detail", "soap:fault", "soapenv:fault",
		"soap错误", "服务错误", "操作错误", "方法错误",
	}

	for _, error := range soapErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // SOAP特定错误的强指示器
			break
		}
	}

	// 检查SOAP结构
	soapStructures := []string{
		"soap:envelope", "soap:header", "soap:body", "soapenv:envelope",
		"xmlns:soap", "schemas.xmlsoap.org", "soap action", "wsdl",
	}

	for _, structure := range soapStructures {
		if strings.Contains(response, structure) {
			confidence += 0.4 // SOAP结构的指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// extractXMLEvidence 提取XML注入证据
func (d *XMLInjectionDetector) extractXMLEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var xmlLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "xml-entity-injection":
			if strings.Contains(lineLower, "xml") ||
				strings.Contains(lineLower, "entity") ||
				strings.Contains(lineLower, "dtd") ||
				strings.Contains(lineLower, "parsing") ||
				strings.Contains(lineLower, "root:") ||
				strings.Contains(lineLower, "daemon:") ||
				strings.Contains(lineLower, "localhost") {
				xmlLines = append(xmlLines, line)
			}
		case "xpath-injection":
			if strings.Contains(lineLower, "xpath") ||
				strings.Contains(lineLower, "node") ||
				strings.Contains(lineLower, "axis") ||
				strings.Contains(lineLower, "function") ||
				strings.Contains(lineLower, "expression") ||
				strings.Contains(lineLower, "string-length") ||
				strings.Contains(lineLower, "substring") {
				xmlLines = append(xmlLines, line)
			}
		case "xml-structure-injection":
			if strings.Contains(lineLower, "xml") ||
				strings.Contains(lineLower, "tag") ||
				strings.Contains(lineLower, "malformed") ||
				strings.Contains(lineLower, "syntax") ||
				strings.Contains(lineLower, "injected") ||
				strings.Contains(lineLower, "script") ||
				strings.Contains(lineLower, "test") {
				xmlLines = append(xmlLines, line)
			}
		case "soap-injection":
			if strings.Contains(lineLower, "soap") ||
				strings.Contains(lineLower, "fault") ||
				strings.Contains(lineLower, "envelope") ||
				strings.Contains(lineLower, "header") ||
				strings.Contains(lineLower, "body") ||
				strings.Contains(lineLower, "wsdl") ||
				strings.Contains(lineLower, "binding") {
				xmlLines = append(xmlLines, line)
			}
		default:
			if strings.Contains(lineLower, "xml") ||
				strings.Contains(lineLower, "soap") ||
				strings.Contains(lineLower, "xpath") ||
				strings.Contains(lineLower, "entity") ||
				strings.Contains(lineLower, "parsing") ||
				strings.Contains(lineLower, "injection") {
				xmlLines = append(xmlLines, line)
			}
		}

		if len(xmlLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(xmlLines) > 0 {
		return strings.Join(xmlLines, "\n")
	}

	// 如果没有找到特定的XML信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
