package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// initializePatterns 初始化检测模式
func (d *SSRFDetector) initializePatterns() {
	// 响应模式 - 检测SSRF成功的响应特征
	responsePatterns := []string{
		// 网络错误模式
		`connection refused`,
		`connection timeout`,
		`connection timed out`,
		`no route to host`,
		`network unreachable`,
		`permission denied`,
		`host unreachable`,
		`network is unreachable`,

		// 服务响应模式
		`ssh-\d+\.\d+`,            // SSH服务响应
		`220 .*ftp`,               // FTP服务响应
		`http/1\.[01]`,            // HTTP响应
		`server:\s*nginx`,         // Nginx服务器
		`server:\s*apache`,        // Apache服务器
		`server:\s*microsoft-iis`, // IIS服务器

		// HTML响应模式
		`<!doctype html`,
		`<html[^>]*>`,
		`<title[^>]*>`,
		`<meta[^>]*>`,

		// 云服务元数据模式
		`ami-[a-f0-9]+`,    // AWS AMI ID
		`i-[a-f0-9]+`,      // AWS实例ID
		`instance-id`,      // 实例ID
		`security-groups`,  // 安全组
		`metadata`,         // 元数据关键字
		`computeMetadata`,  // Google Cloud元数据
		`latest/meta-data`, // AWS元数据路径

		// 数据库响应模式
		`mysql_native_password`, // MySQL认证
		`postgresql`,            // PostgreSQL
		`redis_version`,         // Redis版本信息
		`mongodb`,               // MongoDB

		// 应用服务响应模式
		`tomcat`,    // Tomcat
		`jetty`,     // Jetty
		`websphere`, // WebSphere
		`weblogic`,  // WebLogic
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatterns))
	for _, pattern := range responsePatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}

	// 服务识别模式
	d.servicePatterns = make(map[string]*regexp.Regexp)
	servicePatterns := map[string]string{
		"ssh":        `ssh-\d+\.\d+`,
		"ftp":        `220 .*ftp`,
		"http":       `http/1\.[01]`,
		"mysql":      `mysql_native_password`,
		"postgresql": `postgresql`,
		"redis":      `redis_version`,
		"mongodb":    `mongodb`,
		"nginx":      `server:\s*nginx`,
		"apache":     `server:\s*apache`,
		"tomcat":     `tomcat`,
	}

	for service, pattern := range servicePatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.servicePatterns[service] = compiled
		}
	}
}

// detectInternalNetworkAccess 检测内网访问
func (d *SSRFDetector) detectInternalNetworkAccess(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试内网载荷
	for i, payload := range d.internalPayloads {
		if i >= 15 { // 限制载荷数量避免过多请求
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造测试URL
		testURL := d.injectPayload(target.URL, payload)

		// 发送请求
		resp, err := d.makeRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查SSRF响应
		confidence := d.checkSSRFResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = testURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "internal-access",
				Description: fmt.Sprintf("内网载荷 %s 触发了SSRF响应 (置信度: %.2f)", payload, confidence),
				Content:     d.extractSSRFEvidence(resp, payload),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCloudMetadataAccess 检测云服务元数据访问
func (d *SSRFDetector) detectCloudMetadataAccess(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试云服务载荷
	for _, payload := range d.cloudPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造测试URL
		testURL := d.injectPayload(target.URL, payload)

		// 发送请求
		resp, err := d.makeRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查云服务元数据响应
		confidence := d.checkCloudMetadataResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = testURL
			vulnerableResponse = resp
		}

		if confidence > 0.7 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "cloud-metadata",
				Description: fmt.Sprintf("云服务载荷 %s 成功访问元数据 (置信度: %.2f)", payload, confidence),
				Content:     d.extractCloudEvidence(resp, payload),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectProtocolBypass 检测协议绕过
func (d *SSRFDetector) detectProtocolBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试协议绕过载荷
	for i, payload := range d.protocolPayloads {
		if i >= 10 { // 限制载荷数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造测试URL
		testURL := d.injectPayload(target.URL, payload)

		// 发送请求
		resp, err := d.makeRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查协议绕过响应
		confidence := d.checkProtocolBypassResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = testURL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "protocol-bypass",
				Description: fmt.Sprintf("协议绕过载荷 %s 成功执行 (置信度: %.2f)", payload, confidence),
				Content:     d.extractProtocolEvidence(resp, payload),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// injectPayload 将载荷注入到URL中
func (d *SSRFDetector) injectPayload(targetURL, payload string) string {
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return targetURL
	}

	// 如果URL已有参数，在现有参数中注入
	if parsedURL.RawQuery != "" {
		values, err := url.ParseQuery(parsedURL.RawQuery)
		if err == nil {
			// 寻找可能的URL参数
			urlParams := []string{"url", "link", "redirect", "callback", "target", "goto", "next", "return"}
			for _, param := range urlParams {
				if values.Has(param) {
					values.Set(param, payload)
					parsedURL.RawQuery = values.Encode()
					return parsedURL.String()
				}
			}

			// 如果没有找到URL参数，在第一个参数中注入
			for key := range values {
				values.Set(key, payload)
				break
			}
			parsedURL.RawQuery = values.Encode()
			return parsedURL.String()
		}
	}

	// 如果没有参数，添加测试参数
	if parsedURL.RawQuery == "" {
		parsedURL.RawQuery = "url=" + url.QueryEscape(payload)
	}

	return parsedURL.String()
}

// makeRequest 发送HTTP请求
func (d *SSRFDetector) makeRequest(ctx context.Context, targetURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置常见的浏览器头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		// 网络错误也可能是SSRF的证据
		return err.Error(), nil
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkSSRFResponse 检查SSRF响应
func (d *SSRFDetector) checkSSRFResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查响应模式
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查服务识别模式
	for service, pattern := range d.servicePatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			// 特定服务的额外加分
			if service == "ssh" || service == "mysql" || service == "redis" {
				confidence += 0.2
			}
			break
		}
	}

	// 检查网络错误（也是SSRF的证据）
	networkErrors := []string{
		"connection refused",
		"connection timeout",
		"no route to host",
		"network unreachable",
		"permission denied",
	}

	for _, errorMsg := range networkErrors {
		if strings.Contains(response, errorMsg) {
			confidence += 0.5
			break
		}
	}

	// 检查内网IP响应
	if strings.Contains(payload, "127.0.0.1") || strings.Contains(payload, "localhost") {
		if strings.Contains(response, "http/1.") || strings.Contains(response, "server:") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkCloudMetadataResponse 检查云服务元数据响应
func (d *SSRFDetector) checkCloudMetadataResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// AWS元数据特征
	awsIndicators := []string{
		"ami-",
		"instance-id",
		"security-groups",
		"iam/security-credentials",
		"placement/availability-zone",
		"public-hostname",
		"public-ipv4",
		"local-hostname",
		"local-ipv4",
	}

	for _, indicator := range awsIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
		}
	}

	// Google Cloud元数据特征
	gcpIndicators := []string{
		"computemetadata",
		"metadata-flavor",
		"google",
		"project/project-id",
		"instance/zone",
		"instance/machine-type",
	}

	for _, indicator := range gcpIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
		}
	}

	// Azure元数据特征
	azureIndicators := []string{
		"compute/vmid",
		"compute/name",
		"compute/resourcegroupname",
		"compute/subscriptionid",
		"network/interface",
	}

	for _, indicator := range azureIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
		}
	}

	// 阿里云/腾讯云元数据特征
	chinaCloudIndicators := []string{
		"instance-id",
		"image-id",
		"region-id",
		"zone-id",
	}

	for _, indicator := range chinaCloudIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.3
		}
	}

	// JSON格式的元数据响应
	if strings.Contains(response, "{") && strings.Contains(response, "}") {
		confidence += 0.2
	}

	// 检查是否是元数据URL
	if strings.Contains(payload, "***************") ||
		strings.Contains(payload, "metadata.google.internal") ||
		strings.Contains(payload, "***************") ||
		strings.Contains(payload, "metadata.tencentyun.com") {
		confidence += 0.3
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkProtocolBypassResponse 检查协议绕过响应
func (d *SSRFDetector) checkProtocolBypassResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 文件协议响应特征
	if strings.HasPrefix(payload, "file://") {
		fileIndicators := []string{
			"root:",
			"daemon:",
			"bin:",
			"sys:",
			"# /etc/passwd",
			"127.0.0.1",
			"localhost",
			"# hosts file",
			"windows",
			"system32",
		}

		for _, indicator := range fileIndicators {
			if strings.Contains(response, indicator) {
				confidence += 0.5
				break
			}
		}
	}

	// Gopher协议响应特征
	if strings.HasPrefix(payload, "gopher://") {
		gopherIndicators := []string{
			"redis_version",
			"redis_mode",
			"info server",
			"mysql",
			"postgresql",
			"smtp",
			"helo",
		}

		for _, indicator := range gopherIndicators {
			if strings.Contains(response, indicator) {
				confidence += 0.6
				break
			}
		}
	}

	// Dict协议响应特征
	if strings.HasPrefix(payload, "dict://") {
		dictIndicators := []string{
			"stats",
			"version",
			"memcached",
			"redis",
		}

		for _, indicator := range dictIndicators {
			if strings.Contains(response, indicator) {
				confidence += 0.5
				break
			}
		}
	}

	// FTP协议响应特征
	if strings.HasPrefix(payload, "ftp://") {
		ftpIndicators := []string{
			"220",
			"ftp",
			"welcome",
			"ready",
		}

		for _, indicator := range ftpIndicators {
			if strings.Contains(response, indicator) {
				confidence += 0.5
				break
			}
		}
	}

	// LDAP协议响应特征
	if strings.HasPrefix(payload, "ldap://") || strings.HasPrefix(payload, "ldaps://") {
		ldapIndicators := []string{
			"ldap",
			"directory",
			"bind",
			"search",
		}

		for _, indicator := range ldapIndicators {
			if strings.Contains(response, indicator) {
				confidence += 0.5
				break
			}
		}
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// extractSSRFEvidence 提取SSRF证据
func (d *SSRFDetector) extractSSRFEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 0 && (strings.Contains(line, "ssh-") ||
			strings.Contains(line, "http/1.") ||
			strings.Contains(line, "server:") ||
			strings.Contains(line, "connection") ||
			strings.Contains(line, "refused") ||
			strings.Contains(line, "timeout")) {
			return line
		}
	}
	return "检测到SSRF响应特征"
}

// extractCloudEvidence 提取云服务证据
func (d *SSRFDetector) extractCloudEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 0 && (strings.Contains(line, "ami-") ||
			strings.Contains(line, "instance-id") ||
			strings.Contains(line, "metadata") ||
			strings.Contains(line, "compute") ||
			strings.Contains(line, "security-groups")) {
			return line
		}
	}
	return "检测到云服务元数据访问"
}

// extractProtocolEvidence 提取协议证据
func (d *SSRFDetector) extractProtocolEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 0 && (strings.Contains(line, "root:") ||
			strings.Contains(line, "daemon:") ||
			strings.Contains(line, "redis_version") ||
			strings.Contains(line, "220") ||
			strings.Contains(line, "stats")) {
			return line
		}
	}
	return "检测到协议绕过成功"
}
