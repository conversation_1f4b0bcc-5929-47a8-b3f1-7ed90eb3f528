# 简单服务状态检查

Write-Host "检查后端服务..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "后端服务状态: OK - $($response.StatusCode)"
    Write-Host "响应内容: $($response.Content)"
} catch {
    Write-Host "后端服务状态: ERROR - $($_.Exception.Message)"
}

Write-Host ""
Write-Host "检查前端服务..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 5
    Write-Host "前端服务状态: OK - $($response.StatusCode)"
} catch {
    Write-Host "前端服务状态: ERROR - $($_.Exception.Message)"
}

Write-Host ""
Write-Host "访问地址:"
Write-Host "前端: http://localhost:3000"
Write-Host "后端: http://localhost:8080"
