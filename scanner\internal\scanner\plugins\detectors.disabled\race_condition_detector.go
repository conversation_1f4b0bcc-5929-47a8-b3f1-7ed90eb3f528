package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// RaceConditionDetector 竞态条件检测器
// 支持时间窗口攻击、资源竞争、状态不一致、并发控制等多种竞态条件漏洞检测
type RaceConditionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	timeWindowPayloads   []string         // 时间窗口攻击载荷
	resourceRacePayloads []string         // 资源竞争载荷
	stateRacePayloads    []string         // 状态竞争载荷
	concurrencyPayloads  []string         // 并发控制载荷
	testParameters       []string         // 测试参数
	racePatterns         []*regexp.Regexp // 竞态条件模式
	timePatterns         []*regexp.Regexp // 时间相关模式
	resourcePatterns     []*regexp.Regexp // 资源竞争模式
	statePatterns        []*regexp.Regexp // 状态不一致模式
	concurrencyPatterns  []*regexp.Regexp // 并发控制模式
	httpClient           *http.Client
}

// NewRaceConditionDetector 创建竞态条件检测器
func NewRaceConditionDetector() *RaceConditionDetector {
	detector := &RaceConditionDetector{
		id:          "race-condition-comprehensive",
		name:        "竞态条件漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-34527", "CVE-2020-1472", "CVE-2019-0708", "CVE-2018-8120"},
		cwe:         []string{"CWE-362", "CWE-367", "CWE-366", "CWE-364", "CWE-365"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测竞态条件漏洞，包括时间窗口攻击、资源竞争、状态不一致、并发控制等多种竞态条件安全问题",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // 竞态条件检测需要较长时间
		MaxRetries:      1,                // 竞态条件检测不适合重试
		Concurrency:     10,               // 高并发是竞态条件检测的关键
		RateLimit:       10,               // 高速率用于触发竞态条件
		FollowRedirects: true,             // 跟随重定向检查竞态条件
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，竞态条件响应通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeTimeWindowPayloads()
	detector.initializeResourceRacePayloads()
	detector.initializeStateRacePayloads()
	detector.initializeConcurrencyPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *RaceConditionDetector) GetID() string            { return d.id }
func (d *RaceConditionDetector) GetName() string          { return d.name }
func (d *RaceConditionDetector) GetCategory() string      { return d.category }
func (d *RaceConditionDetector) GetSeverity() string      { return d.severity }
func (d *RaceConditionDetector) GetCVE() []string         { return d.cve }
func (d *RaceConditionDetector) GetCWE() []string         { return d.cwe }
func (d *RaceConditionDetector) GetVersion() string       { return d.version }
func (d *RaceConditionDetector) GetAuthor() string        { return d.author }
func (d *RaceConditionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *RaceConditionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *RaceConditionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *RaceConditionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *RaceConditionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *RaceConditionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *RaceConditionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *RaceConditionDetector) GetDependencies() []string         { return []string{} }
func (d *RaceConditionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *RaceConditionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *RaceConditionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *RaceConditionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *RaceConditionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 1
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 10
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *RaceConditionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *RaceConditionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.timeWindowPayloads) == 0 {
		return fmt.Errorf("时间窗口载荷不能为空")
	}
	if len(d.resourceRacePayloads) == 0 {
		return fmt.Errorf("资源竞争载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *RaceConditionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 竞态条件检测适用于有并发处理功能的Web应用
	// 检查是否有竞态条件相关的特征
	if d.hasRaceConditionFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于竞态条件相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	raceKeywords := []string{
		"race", "concurrent", "parallel", "async", "thread", "process",
		"lock", "mutex", "semaphore", "atomic", "sync", "queue",
		"order", "sequence", "batch", "bulk", "multi", "simultaneous",
		"transaction", "session", "state", "status", "counter", "balance",
		"竞态", "并发", "并行", "异步", "线程", "进程", "锁", "互斥",
		"信号量", "原子", "同步", "队列", "顺序", "序列", "批量",
		"多重", "同时", "事务", "会话", "状态", "计数", "余额",
	}

	for _, keyword := range raceKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 竞态条件是通用Web漏洞，默认适用于所有Web目标
}

// hasRaceConditionFeatures 检查是否有竞态条件功能
func (d *RaceConditionDetector) hasRaceConditionFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有竞态条件相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "concurrent") ||
			strings.Contains(keyLower, "parallel") ||
			strings.Contains(keyLower, "async") ||
			strings.Contains(keyLower, "thread") ||
			strings.Contains(keyLower, "process") ||
			strings.Contains(valueLower, "concurrent") ||
			strings.Contains(valueLower, "parallel") ||
			strings.Contains(valueLower, "async") ||
			strings.Contains(valueLower, "thread") ||
			strings.Contains(valueLower, "process") {
			return true
		}
	}

	// 检查技术栈中是否有并发处理相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		concurrentTechnologies := []string{
			"java", "go", "rust", "erlang", "elixir", "scala", "kotlin",
			"node.js", "python", "ruby", "php", "c#", "c++",
			"spring", "akka", "netty", "vertx", "reactor", "rxjava",
			"express", "koa", "fastify", "tornado", "flask", "django",
			"rails", "sinatra", "laravel", "symfony", "phalcon",
			"nginx", "apache", "tomcat", "jetty", "undertow", "gunicorn",
			"redis", "memcached", "hazelcast", "ignite", "coherence",
			"kafka", "rabbitmq", "activemq", "zeromq", "nats",
			"并发", "并行", "异步", "多线程", "多进程",
		}

		for _, concurrentTech := range concurrentTechnologies {
			if strings.Contains(techNameLower, concurrentTech) {
				return true
			}
		}
	}

	// 检查链接中是否有竞态条件相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "concurrent") ||
			strings.Contains(linkURLLower, "parallel") ||
			strings.Contains(linkURLLower, "async") ||
			strings.Contains(linkURLLower, "thread") ||
			strings.Contains(linkURLLower, "process") ||
			strings.Contains(linkTextLower, "concurrent") ||
			strings.Contains(linkTextLower, "parallel") ||
			strings.Contains(linkTextLower, "async") ||
			strings.Contains(linkTextLower, "thread") ||
			strings.Contains(linkTextLower, "并发") ||
			strings.Contains(linkTextLower, "并行") ||
			strings.Contains(linkTextLower, "异步") {
			return true
		}
	}

	// 检查表单中是否有竞态条件相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			raceFields := []string{
				"race", "concurrent", "parallel", "async", "thread", "process",
				"lock", "mutex", "semaphore", "atomic", "sync", "queue",
				"order", "sequence", "batch", "bulk", "multi", "simultaneous",
				"transaction", "session", "state", "status", "counter", "balance",
				"竞态", "并发", "并行", "异步", "线程", "进程", "锁", "互斥",
				"信号量", "原子", "同步", "队列", "顺序", "序列", "批量",
				"多重", "同时", "事务", "会话", "状态", "计数", "余额",
			}

			for _, raceField := range raceFields {
				if strings.Contains(fieldNameLower, raceField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *RaceConditionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种竞态条件检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 时间窗口攻击检测
	timeEvidence, timeConfidence, timePayload, timeRequest, timeResponse := d.detectTimeWindowRace(ctx, target)
	if timeConfidence > maxConfidence {
		maxConfidence = timeConfidence
		vulnerablePayload = timePayload
		vulnerableRequest = timeRequest
		vulnerableResponse = timeResponse
	}
	evidence = append(evidence, timeEvidence...)

	// 2. 资源竞争检测
	resourceEvidence, resourceConfidence, resourcePayload, resourceRequest, resourceResponse := d.detectResourceRace(ctx, target)
	if resourceConfidence > maxConfidence {
		maxConfidence = resourceConfidence
		vulnerablePayload = resourcePayload
		vulnerableRequest = resourceRequest
		vulnerableResponse = resourceResponse
	}
	evidence = append(evidence, resourceEvidence...)

	// 3. 状态竞争检测
	stateEvidence, stateConfidence, statePayload, stateRequest, stateResponse := d.detectStateRace(ctx, target)
	if stateConfidence > maxConfidence {
		maxConfidence = stateConfidence
		vulnerablePayload = statePayload
		vulnerableRequest = stateRequest
		vulnerableResponse = stateResponse
	}
	evidence = append(evidence, stateEvidence...)

	// 4. 并发控制检测
	concurrencyEvidence, concurrencyConfidence, concurrencyPayload, concurrencyRequest, concurrencyResponse := d.detectConcurrencyRace(ctx, target)
	if concurrencyConfidence > maxConfidence {
		maxConfidence = concurrencyConfidence
		vulnerablePayload = concurrencyPayload
		vulnerableRequest = concurrencyRequest
		vulnerableResponse = concurrencyResponse
	}
	evidence = append(evidence, concurrencyEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "竞态条件漏洞",
		Description:       "检测到竞态条件漏洞，可能导致数据不一致、权限绕过或拒绝服务攻击",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "实施适当的并发控制机制，使用锁、信号量或原子操作，确保关键操作的原子性",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Race_condition", "https://cwe.mitre.org/data/definitions/362.html", "https://cwe.mitre.org/data/definitions/367.html"},
		Tags:              []string{"race", "condition", "concurrent", "parallel", "thread", "process", "lock", "mutex", "atomic"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *RaceConditionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"time-window-verification",
		"resource-race-verification",
		"state-race-verification",
		"concurrency-verification",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyRaceConditionMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了竞态条件漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "race-condition-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用竞态条件验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *RaceConditionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("race_condition_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *RaceConditionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (竞态条件通常是高风险漏洞)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyRaceConditionMethod 验证竞态条件方法
func (d *RaceConditionDetector) verifyRaceConditionMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "time-window-verification":
		return d.verifyTimeWindow(ctx, target)
	case "resource-race-verification":
		return d.verifyResourceRace(ctx, target)
	case "state-race-verification":
		return d.verifyStateRace(ctx, target)
	case "concurrency-verification":
		return d.verifyConcurrency(ctx, target)
	default:
		return 0.0
	}
}

// verifyTimeWindow 验证时间窗口
func (d *RaceConditionDetector) verifyTimeWindow(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的时间窗口验证
	if d.hasRaceConditionFeatures(target) {
		return 0.7 // 有竞态条件特征的目标可能有时间窗口问题
	}
	return 0.3
}

// verifyResourceRace 验证资源竞争
func (d *RaceConditionDetector) verifyResourceRace(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的资源竞争验证
	if d.hasRaceConditionFeatures(target) {
		return 0.6 // 有竞态条件特征的目标可能有资源竞争
	}
	return 0.2
}

// verifyStateRace 验证状态竞争
func (d *RaceConditionDetector) verifyStateRace(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的状态竞争验证
	if d.hasRaceConditionFeatures(target) {
		return 0.6 // 有竞态条件特征的目标可能有状态竞争
	}
	return 0.2
}

// verifyConcurrency 验证并发控制
func (d *RaceConditionDetector) verifyConcurrency(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的并发控制验证
	if d.hasRaceConditionFeatures(target) {
		return 0.5 // 有竞态条件特征的目标可能有并发控制问题
	}
	return 0.1
}

// initializeTimeWindowPayloads 初始化时间窗口攻击载荷列表
func (d *RaceConditionDetector) initializeTimeWindowPayloads() {
	d.timeWindowPayloads = []string{
		// 时间延迟载荷
		"sleep(1)", "sleep(2)", "sleep(3)", "sleep(5)", "sleep(10)",
		"usleep(1000000)", "usleep(2000000)", "usleep(3000000)", "usleep(5000000)",
		"Thread.sleep(1000)", "Thread.sleep(2000)", "Thread.sleep(3000)", "Thread.sleep(5000)",
		"time.sleep(1)", "time.sleep(2)", "time.sleep(3)", "time.sleep(5)",
		"setTimeout(1000)", "setTimeout(2000)", "setTimeout(3000)", "setTimeout(5000)",
		"delay(1000)", "delay(2000)", "delay(3000)", "delay(5000)",

		// 时间窗口操作
		"WAITFOR DELAY '00:00:01'", "WAITFOR DELAY '00:00:02'", "WAITFOR DELAY '00:00:03'",
		"WAITFOR DELAY '00:00:05'", "WAITFOR DELAY '00:00:10'",
		"pg_sleep(1)", "pg_sleep(2)", "pg_sleep(3)", "pg_sleep(5)",
		"BENCHMARK(1000000,MD5(1))", "BENCHMARK(2000000,MD5(1))", "BENCHMARK(5000000,MD5(1))",

		// 时间竞争载荷
		"race_window", "time_window", "timing_attack", "time_race", "window_race",
		"concurrent_access", "parallel_access", "simultaneous_access",
		"time_sensitive", "timing_sensitive", "window_sensitive",
		"race_condition", "timing_condition", "window_condition",

		// 时间戳操作
		"timestamp", "time_stamp", "current_time", "now()", "sysdate()",
		"gettime()", "microtime()", "unixtime()", "epoch_time",
		"time_diff", "time_delta", "time_interval", "time_span",

		// 时间相关函数
		"strftime", "strptime", "mktime", "gmtime", "localtime",
		"date_format", "date_parse", "date_diff", "date_add", "date_sub",
		"datetime", "timestamp", "timeofday", "clock", "timer",

		// 中文时间载荷
		"时间窗口", "时间竞争", "时间攻击", "时间差", "时间间隔",
		"时间戳", "当前时间", "系统时间", "本地时间", "格林威治时间",
		"延迟", "等待", "暂停", "休眠", "睡眠",
	}
}

// initializeResourceRacePayloads 初始化资源竞争载荷列表
func (d *RaceConditionDetector) initializeResourceRacePayloads() {
	d.resourceRacePayloads = []string{
		// 文件资源竞争
		"file_lock", "file_unlock", "flock", "fcntl", "lockf",
		"exclusive_lock", "shared_lock", "read_lock", "write_lock",
		"file_access", "file_open", "file_close", "file_read", "file_write",
		"file_create", "file_delete", "file_rename", "file_move", "file_copy",

		// 数据库资源竞争
		"table_lock", "row_lock", "page_lock", "database_lock",
		"select_for_update", "lock_in_share_mode", "exclusive_mode",
		"transaction_lock", "deadlock", "lock_timeout", "lock_wait",
		"isolation_level", "read_committed", "repeatable_read", "serializable",

		// 内存资源竞争
		"memory_lock", "memory_unlock", "malloc", "free", "realloc",
		"shared_memory", "memory_map", "memory_pool", "memory_barrier",
		"cache_lock", "cache_unlock", "cache_invalidate", "cache_flush",

		// 网络资源竞争
		"socket_lock", "connection_lock", "port_lock", "address_lock",
		"bind_lock", "listen_lock", "accept_lock", "connect_lock",
		"send_lock", "recv_lock", "close_lock", "shutdown_lock",

		// 进程资源竞争
		"process_lock", "pid_lock", "signal_lock", "semaphore_lock",
		"mutex_lock", "mutex_unlock", "spinlock", "rwlock",
		"condition_variable", "barrier", "latch", "gate",

		// 线程资源竞争
		"thread_lock", "thread_unlock", "thread_join", "thread_detach",
		"thread_create", "thread_destroy", "thread_suspend", "thread_resume",
		"thread_priority", "thread_affinity", "thread_local", "thread_safe",

		// 队列资源竞争
		"queue_lock", "queue_unlock", "enqueue", "dequeue",
		"priority_queue", "blocking_queue", "concurrent_queue",
		"producer_consumer", "buffer_full", "buffer_empty",

		// 资源池竞争
		"pool_lock", "pool_unlock", "connection_pool", "thread_pool",
		"object_pool", "resource_pool", "pool_acquire", "pool_release",
		"pool_timeout", "pool_exhausted", "pool_overflow",

		// 中文资源载荷
		"资源竞争", "资源锁", "文件锁", "数据库锁", "内存锁",
		"网络锁", "进程锁", "线程锁", "队列锁", "连接池",
		"资源池", "互斥锁", "读写锁", "信号量", "条件变量",
		"死锁", "锁超时", "锁等待", "资源耗尽", "资源溢出",
	}
}

// initializeStateRacePayloads 初始化状态竞争载荷列表
func (d *RaceConditionDetector) initializeStateRacePayloads() {
	d.stateRacePayloads = []string{
		// 状态变量竞争
		"state_variable", "global_state", "shared_state", "session_state",
		"application_state", "system_state", "user_state", "object_state",
		"state_change", "state_update", "state_modify", "state_set",
		"state_get", "state_read", "state_write", "state_check",

		// 计数器竞争
		"counter", "increment", "decrement", "atomic_increment", "atomic_decrement",
		"reference_count", "usage_count", "access_count", "hit_count",
		"sequence_number", "serial_number", "order_number", "ticket_number",

		// 标志位竞争
		"flag", "boolean_flag", "status_flag", "control_flag", "sync_flag",
		"ready_flag", "done_flag", "error_flag", "warning_flag", "debug_flag",
		"flag_set", "flag_clear", "flag_toggle", "flag_check", "flag_wait",

		// 状态机竞争
		"state_machine", "finite_state", "state_transition", "state_diagram",
		"current_state", "next_state", "previous_state", "initial_state", "final_state",
		"state_enter", "state_exit", "state_action", "state_guard", "state_event",

		// 会话状态竞争
		"session_id", "session_data", "session_timeout", "session_expire",
		"session_create", "session_destroy", "session_update", "session_validate",
		"login_state", "logout_state", "authenticated", "authorized", "permission",

		// 事务状态竞争
		"transaction_state", "transaction_id", "transaction_begin", "transaction_commit",
		"transaction_rollback", "transaction_abort", "transaction_timeout",
		"isolation_level", "consistency", "durability", "atomicity",

		// 缓存状态竞争
		"cache_state", "cache_valid", "cache_invalid", "cache_dirty", "cache_clean",
		"cache_hit", "cache_miss", "cache_expire", "cache_refresh", "cache_evict",

		// 连接状态竞争
		"connection_state", "connected", "disconnected", "connecting", "disconnecting",
		"connection_pool", "connection_timeout", "connection_retry", "connection_error",

		// 中文状态载荷
		"状态竞争", "状态变量", "全局状态", "共享状态", "会话状态",
		"应用状态", "系统状态", "用户状态", "对象状态", "状态改变",
		"状态更新", "状态修改", "状态设置", "状态获取", "状态检查",
		"计数器", "增量", "减量", "原子操作", "引用计数",
		"标志位", "布尔标志", "状态标志", "控制标志", "同步标志",
		"状态机", "有限状态", "状态转换", "当前状态", "下一状态",
		"会话标识", "会话数据", "会话超时", "登录状态", "注销状态",
		"事务状态", "事务标识", "事务开始", "事务提交", "事务回滚",
		"缓存状态", "缓存有效", "缓存无效", "缓存命中", "缓存失效",
		"连接状态", "已连接", "已断开", "正在连接", "正在断开",
	}
}

// initializeConcurrencyPayloads 初始化并发控制载荷列表
func (d *RaceConditionDetector) initializeConcurrencyPayloads() {
	d.concurrencyPayloads = []string{
		// 并发控制原语
		"mutex", "semaphore", "monitor", "barrier", "latch", "gate",
		"lock", "unlock", "trylock", "timedlock", "spinlock", "rwlock",
		"read_lock", "write_lock", "shared_lock", "exclusive_lock",
		"condition_variable", "wait", "notify", "notify_all", "signal", "broadcast",

		// 原子操作
		"atomic", "atomic_add", "atomic_sub", "atomic_inc", "atomic_dec",
		"atomic_and", "atomic_or", "atomic_xor", "atomic_not", "atomic_swap",
		"compare_and_swap", "compare_and_set", "test_and_set", "fetch_and_add",
		"load_acquire", "store_release", "memory_fence", "memory_barrier",

		// 线程同步
		"thread_sync", "thread_join", "thread_detach", "thread_yield",
		"thread_sleep", "thread_wait", "thread_notify", "thread_interrupt",
		"thread_priority", "thread_affinity", "thread_local", "thread_safe",

		// 进程同步
		"process_sync", "ipc", "pipe", "fifo", "message_queue", "shared_memory",
		"signal", "sigwait", "sigaction", "kill", "waitpid", "fork", "exec",
		"process_group", "session", "daemon", "zombie", "orphan",

		// 并发数据结构
		"concurrent_queue", "concurrent_map", "concurrent_list", "concurrent_set",
		"lock_free_queue", "lock_free_stack", "lock_free_list", "wait_free",
		"blocking_queue", "bounded_queue", "unbounded_queue", "priority_queue",

		// 并发模式
		"producer_consumer", "reader_writer", "dining_philosophers", "sleeping_barber",
		"cigarette_smokers", "bridge_crossing", "elevator", "parking_lot",
		"resource_allocation", "banker_algorithm", "deadlock_detection", "deadlock_prevention",

		// 并发框架
		"actor_model", "message_passing", "event_driven", "reactive", "async_await",
		"future_promise", "callback", "continuation", "coroutine", "fiber",
		"green_thread", "virtual_thread", "lightweight_thread", "user_thread",

		// 并发控制策略
		"optimistic_locking", "pessimistic_locking", "lock_free", "wait_free",
		"non_blocking", "blocking", "timeout", "retry", "backoff", "exponential_backoff",
		"linear_backoff", "random_backoff", "jitter", "circuit_breaker",

		// 并发性能
		"throughput", "latency", "scalability", "contention", "bottleneck",
		"load_balancing", "work_stealing", "work_sharing", "task_scheduling",
		"thread_pool", "executor", "scheduler", "dispatcher", "coordinator",

		// 中文并发载荷
		"并发控制", "互斥锁", "信号量", "监视器", "屏障", "门闩",
		"锁", "解锁", "尝试锁", "定时锁", "自旋锁", "读写锁",
		"读锁", "写锁", "共享锁", "排他锁", "条件变量", "等待",
		"通知", "全部通知", "信号", "广播", "原子操作", "原子加",
		"原子减", "原子增", "原子减", "原子与", "原子或", "原子异或",
		"原子非", "原子交换", "比较交换", "比较设置", "测试设置",
		"获取加", "加载获取", "存储释放", "内存栅栏", "内存屏障",
		"线程同步", "线程连接", "线程分离", "线程让步", "线程睡眠",
		"线程等待", "线程通知", "线程中断", "线程优先级", "线程亲和性",
		"线程本地", "线程安全", "进程同步", "进程间通信", "管道",
		"先进先出", "消息队列", "共享内存", "信号", "信号等待",
		"信号动作", "杀死", "等待进程", "分叉", "执行", "进程组",
		"会话", "守护进程", "僵尸进程", "孤儿进程", "并发队列",
		"并发映射", "并发列表", "并发集合", "无锁队列", "无锁栈",
		"无锁列表", "无等待", "阻塞队列", "有界队列", "无界队列",
		"优先队列", "生产者消费者", "读者写者", "哲学家就餐", "理发师睡觉",
		"吸烟者", "桥梁穿越", "电梯", "停车场", "资源分配",
		"银行家算法", "死锁检测", "死锁预防", "演员模型", "消息传递",
		"事件驱动", "反应式", "异步等待", "未来承诺", "回调",
		"继续", "协程", "纤程", "绿色线程", "虚拟线程",
		"轻量级线程", "用户线程", "乐观锁", "悲观锁", "无锁",
		"无等待", "非阻塞", "阻塞", "超时", "重试", "退避",
		"指数退避", "线性退避", "随机退避", "抖动", "断路器",
		"吞吐量", "延迟", "可扩展性", "争用", "瓶颈", "负载均衡",
		"工作窃取", "工作共享", "任务调度", "线程池", "执行器",
		"调度器", "分发器", "协调器",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *RaceConditionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 竞态条件相关参数
		"race", "condition", "concurrent", "parallel", "async", "thread",
		"process", "lock", "mutex", "semaphore", "atomic", "sync",
		"race_condition", "race_test", "concurrent_test", "parallel_test",
		"thread_test", "process_test", "lock_test", "mutex_test",

		// 时间相关参数
		"time", "timing", "timestamp", "delay", "sleep", "wait",
		"timeout", "interval", "duration", "period", "window",
		"time_window", "timing_attack", "time_race", "window_race",
		"time_sensitive", "timing_sensitive", "window_sensitive",

		// 资源相关参数
		"resource", "file", "database", "memory", "network", "socket",
		"connection", "pool", "queue", "buffer", "cache", "session",
		"resource_lock", "file_lock", "database_lock", "memory_lock",
		"network_lock", "connection_lock", "pool_lock", "queue_lock",

		// 状态相关参数
		"state", "status", "flag", "counter", "sequence", "order",
		"number", "id", "identifier", "reference", "handle", "token",
		"state_variable", "status_flag", "counter_value", "sequence_number",
		"order_number", "reference_count", "handle_count", "token_count",

		// 并发控制参数
		"control", "synchronization", "coordination", "barrier", "latch",
		"gate", "monitor", "condition", "signal", "notify", "broadcast",
		"sync_control", "async_control", "thread_control", "process_control",
		"lock_control", "mutex_control", "semaphore_control", "atomic_control",

		// 操作相关参数
		"operation", "action", "command", "request", "response", "call",
		"invoke", "execute", "run", "start", "stop", "pause", "resume",
		"create", "destroy", "open", "close", "read", "write", "update",
		"delete", "insert", "select", "commit", "rollback", "abort",

		// 性能相关参数
		"performance", "throughput", "latency", "scalability", "load",
		"stress", "benchmark", "test", "measure", "metric", "monitor",
		"profile", "trace", "debug", "log", "report", "statistics",

		// 错误相关参数
		"error", "exception", "fault", "failure", "crash", "hang",
		"deadlock", "livelock", "starvation", "contention", "bottleneck",
		"race_error", "concurrent_error", "thread_error", "lock_error",

		// 中文参数
		"竞态", "条件", "并发", "并行", "异步", "线程", "进程",
		"锁", "互斥", "信号量", "原子", "同步", "竞态条件",
		"竞态测试", "并发测试", "并行测试", "线程测试", "进程测试",
		"时间", "时机", "时间戳", "延迟", "睡眠", "等待",
		"超时", "间隔", "持续时间", "周期", "窗口", "时间窗口",
		"时间攻击", "时间竞争", "窗口竞争", "时间敏感", "时机敏感",
		"资源", "文件", "数据库", "内存", "网络", "套接字",
		"连接", "池", "队列", "缓冲区", "缓存", "会话",
		"资源锁", "文件锁", "数据库锁", "内存锁", "网络锁",
		"状态", "状态", "标志", "计数器", "序列", "顺序",
		"编号", "标识", "标识符", "引用", "句柄", "令牌",
		"状态变量", "状态标志", "计数器值", "序列号", "顺序号",
		"控制", "同步", "协调", "屏障", "门闩", "门",
		"监视器", "条件", "信号", "通知", "广播", "同步控制",
		"操作", "动作", "命令", "请求", "响应", "调用",
		"调用", "执行", "运行", "开始", "停止", "暂停",
		"性能", "吞吐量", "延迟", "可扩展性", "负载", "压力",
		"错误", "异常", "故障", "失败", "崩溃", "挂起",
		"死锁", "活锁", "饥饿", "争用", "瓶颈", "竞态错误",
	}
}

// initializePatterns 初始化检测模式
func (d *RaceConditionDetector) initializePatterns() {
	// 竞态条件检测模式 - 检测竞态条件相关的响应内容
	racePatternStrings := []string{
		// 通用竞态条件模式
		`(?i)race\s+condition`,
		`(?i)concurrent\s+(access|modification|update)`,
		`(?i)parallel\s+(access|modification|update)`,
		`(?i)simultaneous\s+(access|modification|update)`,
		`(?i)thread\s+(conflict|contention|race)`,
		`(?i)process\s+(conflict|contention|race)`,

		// 锁相关模式
		`(?i)lock\s+(timeout|contention|conflict)`,
		`(?i)mutex\s+(timeout|contention|conflict)`,
		`(?i)semaphore\s+(timeout|contention|conflict)`,
		`(?i)deadlock\s+(detected|occurred)`,
		`(?i)livelock\s+(detected|occurred)`,
		`(?i)starvation\s+(detected|occurred)`,

		// 资源竞争模式
		`(?i)resource\s+(conflict|contention|busy)`,
		`(?i)file\s+(locked|busy|in_use)`,
		`(?i)database\s+(locked|busy|timeout)`,
		`(?i)connection\s+(busy|timeout|unavailable)`,
		`(?i)pool\s+(exhausted|timeout|busy)`,
		`(?i)queue\s+(full|overflow|timeout)`,

		// 状态不一致模式
		`(?i)state\s+(inconsistent|invalid|corrupted)`,
		`(?i)data\s+(inconsistent|invalid|corrupted)`,
		`(?i)transaction\s+(conflict|rollback|abort)`,
		`(?i)isolation\s+(violation|error)`,
		`(?i)consistency\s+(violation|error)`,
		`(?i)integrity\s+(violation|error)`,

		// 时间相关模式
		`(?i)timing\s+(attack|issue|problem)`,
		`(?i)time\s+(window|race|sensitive)`,
		`(?i)temporal\s+(attack|issue|problem)`,
		`(?i)synchronization\s+(error|failure)`,
		`(?i)coordination\s+(error|failure)`,

		// 中文竞态条件模式
		`(?i)(竞态|竞争).*条件`,
		`(?i)(并发|并行).*冲突`,
		`(?i)(线程|进程).*竞争`,
		`(?i)(锁|互斥).*冲突`,
		`(?i)(资源|文件|数据库).*冲突`,
		`(?i)(状态|数据).*不一致`,
		`(?i)(事务|交易).*冲突`,
		`(?i)(时间|时机).*竞争`,
		`(?i)(同步|协调).*错误`,
	}

	d.racePatterns = make([]*regexp.Regexp, 0, len(racePatternStrings))
	for _, pattern := range racePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.racePatterns = append(d.racePatterns, compiled)
		}
	}

	// 时间相关检测模式 - 检测时间窗口攻击相关的响应内容
	timePatternStrings := []string{
		// 时间延迟模式
		`(?i)sleep\s*\(\s*\d+\s*\)`,
		`(?i)usleep\s*\(\s*\d+\s*\)`,
		`(?i)thread\.sleep\s*\(\s*\d+\s*\)`,
		`(?i)time\.sleep\s*\(\s*\d+\s*\)`,
		`(?i)settimeout\s*\(\s*\d+\s*\)`,
		`(?i)delay\s*\(\s*\d+\s*\)`,

		// 时间函数模式
		`(?i)waitfor\s+delay`,
		`(?i)pg_sleep\s*\(\s*\d+\s*\)`,
		`(?i)benchmark\s*\(\s*\d+`,
		`(?i)now\s*\(\s*\)`,
		`(?i)sysdate\s*\(\s*\)`,
		`(?i)current_time`,
		`(?i)current_timestamp`,

		// 时间戳模式
		`(?i)timestamp\s*[:=]\s*\d+`,
		`(?i)time_stamp\s*[:=]\s*\d+`,
		`(?i)epoch_time\s*[:=]\s*\d+`,
		`(?i)unix_time\s*[:=]\s*\d+`,
		`(?i)microtime\s*\(\s*\)`,
		`(?i)gettime\s*\(\s*\)`,

		// 时间差异模式
		`(?i)time_diff\s*[:=]`,
		`(?i)time_delta\s*[:=]`,
		`(?i)time_interval\s*[:=]`,
		`(?i)time_span\s*[:=]`,
		`(?i)duration\s*[:=]`,
		`(?i)elapsed\s*[:=]`,

		// 时间窗口模式
		`(?i)time_window\s*[:=]`,
		`(?i)timing_window\s*[:=]`,
		`(?i)window_size\s*[:=]`,
		`(?i)race_window\s*[:=]`,
		`(?i)attack_window\s*[:=]`,

		// 中文时间模式
		`(?i)(时间|时机).*窗口`,
		`(?i)(时间|时机).*差异`,
		`(?i)(时间|时机).*间隔`,
		`(?i)(时间|时机).*戳`,
		`(?i)(延迟|等待|暂停).*时间`,
		`(?i)(当前|系统|本地).*时间`,
	}

	d.timePatterns = make([]*regexp.Regexp, 0, len(timePatternStrings))
	for _, pattern := range timePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.timePatterns = append(d.timePatterns, compiled)
		}
	}

	// 资源竞争检测模式 - 检测资源竞争相关的响应内容
	resourcePatternStrings := []string{
		// 文件资源模式
		`(?i)file\s+(lock|unlock|locked|busy)`,
		`(?i)flock\s*\(\s*`,
		`(?i)fcntl\s*\(\s*`,
		`(?i)lockf\s*\(\s*`,
		`(?i)exclusive_lock`,
		`(?i)shared_lock`,
		`(?i)read_lock`,
		`(?i)write_lock`,

		// 数据库资源模式
		`(?i)table_lock`,
		`(?i)row_lock`,
		`(?i)page_lock`,
		`(?i)database_lock`,
		`(?i)select\s+for\s+update`,
		`(?i)lock\s+in\s+share\s+mode`,
		`(?i)exclusive\s+mode`,
		`(?i)transaction_lock`,

		// 内存资源模式
		`(?i)memory_lock`,
		`(?i)memory_unlock`,
		`(?i)malloc\s*\(\s*`,
		`(?i)free\s*\(\s*`,
		`(?i)realloc\s*\(\s*`,
		`(?i)shared_memory`,
		`(?i)memory_map`,
		`(?i)memory_pool`,

		// 网络资源模式
		`(?i)socket_lock`,
		`(?i)connection_lock`,
		`(?i)port_lock`,
		`(?i)address_lock`,
		`(?i)bind_lock`,
		`(?i)listen_lock`,
		`(?i)accept_lock`,
		`(?i)connect_lock`,

		// 进程资源模式
		`(?i)process_lock`,
		`(?i)pid_lock`,
		`(?i)signal_lock`,
		`(?i)semaphore_lock`,
		`(?i)mutex_lock`,
		`(?i)mutex_unlock`,
		`(?i)spinlock`,
		`(?i)rwlock`,

		// 队列资源模式
		`(?i)queue_lock`,
		`(?i)queue_unlock`,
		`(?i)enqueue\s*\(\s*`,
		`(?i)dequeue\s*\(\s*`,
		`(?i)priority_queue`,
		`(?i)blocking_queue`,
		`(?i)concurrent_queue`,

		// 中文资源模式
		`(?i)(资源|文件|数据库).*锁`,
		`(?i)(内存|网络|进程).*锁`,
		`(?i)(队列|连接|线程).*锁`,
		`(?i)(互斥|读写|共享).*锁`,
		`(?i)(资源|连接|线程).*池`,
		`(?i)(死锁|活锁|饥饿)`,
		`(?i)(争用|冲突|竞争)`,
	}

	d.resourcePatterns = make([]*regexp.Regexp, 0, len(resourcePatternStrings))
	for _, pattern := range resourcePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.resourcePatterns = append(d.resourcePatterns, compiled)
		}
	}

	// 状态不一致检测模式 - 检测状态竞争相关的响应内容
	statePatternStrings := []string{
		// 状态变量模式
		`(?i)state\s*[:=]\s*[^,}]+`,
		`(?i)status\s*[:=]\s*[^,}]+`,
		`(?i)flag\s*[:=]\s*[^,}]+`,
		`(?i)counter\s*[:=]\s*\d+`,
		`(?i)sequence\s*[:=]\s*\d+`,
		`(?i)order\s*[:=]\s*\d+`,

		// 状态操作模式
		`(?i)state_(change|update|modify|set)`,
		`(?i)status_(change|update|modify|set)`,
		`(?i)flag_(set|clear|toggle|check)`,
		`(?i)counter_(increment|decrement|reset)`,
		`(?i)sequence_(next|current|reset)`,

		// 计数器模式
		`(?i)atomic_(increment|decrement)`,
		`(?i)reference_count`,
		`(?i)usage_count`,
		`(?i)access_count`,
		`(?i)hit_count`,
		`(?i)serial_number`,
		`(?i)ticket_number`,

		// 状态机模式
		`(?i)state_machine`,
		`(?i)finite_state`,
		`(?i)state_transition`,
		`(?i)current_state`,
		`(?i)next_state`,
		`(?i)initial_state`,
		`(?i)final_state`,

		// 会话状态模式
		`(?i)session_id\s*[:=]`,
		`(?i)session_data\s*[:=]`,
		`(?i)login_state\s*[:=]`,
		`(?i)logout_state\s*[:=]`,
		`(?i)authenticated\s*[:=]`,
		`(?i)authorized\s*[:=]`,

		// 事务状态模式
		`(?i)transaction_state\s*[:=]`,
		`(?i)transaction_id\s*[:=]`,
		`(?i)isolation_level\s*[:=]`,
		`(?i)consistency\s*[:=]`,
		`(?i)durability\s*[:=]`,
		`(?i)atomicity\s*[:=]`,

		// 中文状态模式
		`(?i)(状态|状态).*[:=]`,
		`(?i)(标志|标记).*[:=]`,
		`(?i)(计数器|计数).*[:=]`,
		`(?i)(序列|顺序).*[:=]`,
		`(?i)(会话|登录|注销).*状态`,
		`(?i)(事务|交易).*状态`,
		`(?i)(一致性|完整性|原子性)`,
	}

	d.statePatterns = make([]*regexp.Regexp, 0, len(statePatternStrings))
	for _, pattern := range statePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.statePatterns = append(d.statePatterns, compiled)
		}
	}

	// 并发控制检测模式 - 检测并发控制相关的响应内容
	concurrencyPatternStrings := []string{
		// 并发控制原语模式
		`(?i)mutex\s*[:=]`,
		`(?i)semaphore\s*[:=]`,
		`(?i)monitor\s*[:=]`,
		`(?i)barrier\s*[:=]`,
		`(?i)latch\s*[:=]`,
		`(?i)gate\s*[:=]`,
		`(?i)condition_variable\s*[:=]`,

		// 原子操作模式
		`(?i)atomic_\w+\s*\(\s*`,
		`(?i)compare_and_swap\s*\(\s*`,
		`(?i)compare_and_set\s*\(\s*`,
		`(?i)test_and_set\s*\(\s*`,
		`(?i)fetch_and_add\s*\(\s*`,
		`(?i)load_acquire\s*\(\s*`,
		`(?i)store_release\s*\(\s*`,

		// 线程同步模式
		`(?i)thread_sync`,
		`(?i)thread_join\s*\(\s*`,
		`(?i)thread_detach\s*\(\s*`,
		`(?i)thread_yield\s*\(\s*`,
		`(?i)thread_sleep\s*\(\s*`,
		`(?i)thread_wait\s*\(\s*`,
		`(?i)thread_notify\s*\(\s*`,

		// 进程同步模式
		`(?i)process_sync`,
		`(?i)ipc\s*[:=]`,
		`(?i)pipe\s*\(\s*`,
		`(?i)message_queue`,
		`(?i)shared_memory`,
		`(?i)sigwait\s*\(\s*`,
		`(?i)sigaction\s*\(\s*`,

		// 并发数据结构模式
		`(?i)concurrent_\w+`,
		`(?i)lock_free_\w+`,
		`(?i)wait_free_\w+`,
		`(?i)blocking_\w+`,
		`(?i)bounded_\w+`,
		`(?i)unbounded_\w+`,

		// 并发模式
		`(?i)producer_consumer`,
		`(?i)reader_writer`,
		`(?i)actor_model`,
		`(?i)message_passing`,
		`(?i)event_driven`,
		`(?i)async_await`,
		`(?i)future_promise`,

		// 中文并发模式
		`(?i)(并发|并行).*控制`,
		`(?i)(互斥|信号量|监视器).*[:=]`,
		`(?i)(屏障|门闩|条件变量).*[:=]`,
		`(?i)(原子|比较交换|测试设置).*操作`,
		`(?i)(线程|进程).*同步`,
		`(?i)(生产者消费者|读者写者)`,
		`(?i)(演员模型|消息传递|事件驱动)`,
	}

	d.concurrencyPatterns = make([]*regexp.Regexp, 0, len(concurrencyPatternStrings))
	for _, pattern := range concurrencyPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.concurrencyPatterns = append(d.concurrencyPatterns, compiled)
		}
	}
}
