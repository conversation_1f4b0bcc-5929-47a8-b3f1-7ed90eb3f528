package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectParameterRedirect 检测参数重定向
func (d *OpenRedirectDetector) detectParameterRedirect(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试参数重定向载荷
	for _, payload := range d.parameterPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送参数重定向请求
		resp, err := d.sendRedirectRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查参数重定向响应
		confidence := d.checkParameterRedirectResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("参数重定向: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "parameter-redirect",
				Description: fmt.Sprintf("发现参数重定向: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractRedirectEvidence(resp, "parameter-redirect"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectHeaderRedirect 检测头部重定向
func (d *OpenRedirectDetector) detectHeaderRedirect(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试头部重定向载荷
	for _, payload := range d.headerPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送头部重定向请求
		resp, err := d.sendHeaderRedirectRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查头部重定向响应
		confidence := d.checkHeaderRedirectResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("头部重定向: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "header-redirect",
				Description: fmt.Sprintf("发现头部重定向: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractRedirectEvidence(resp, "header-redirect"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectJavascriptRedirect 检测JavaScript重定向
func (d *OpenRedirectDetector) detectJavascriptRedirect(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试JavaScript重定向载荷
	for _, payload := range d.javascriptPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送JavaScript重定向请求
		resp, err := d.sendRedirectRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查JavaScript重定向响应
		confidence := d.checkJavascriptRedirectResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("JavaScript重定向: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "javascript-redirect",
				Description: fmt.Sprintf("发现JavaScript重定向: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractRedirectEvidence(resp, "javascript-redirect"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectProtocolRedirect 检测协议重定向
func (d *OpenRedirectDetector) detectProtocolRedirect(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试协议重定向载荷
	for _, payload := range d.protocolPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送协议重定向请求
		resp, err := d.sendRedirectRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查协议重定向响应
		confidence := d.checkProtocolRedirectResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("协议重定向: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "protocol-redirect",
				Description: fmt.Sprintf("发现协议重定向: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractRedirectEvidence(resp, "protocol-redirect"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendRedirectRequest 发送重定向请求
func (d *OpenRedirectDetector) sendRedirectRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendRedirectGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST参数注入
	postResp, err := d.sendRedirectPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 返回GET响应（即使有错误）
	if getResp != "" {
		return getResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendRedirectGETRequest 发送重定向GET请求
func (d *OpenRedirectDetector) sendRedirectGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendRedirectPOSTRequest 发送重定向POST请求
func (d *OpenRedirectDetector) sendRedirectPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造POST数据
	postData := url.Values{}

	for _, param := range d.testParameters {
		postData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendHeaderRedirectRequest 发送头部重定向请求
func (d *OpenRedirectDetector) sendHeaderRedirectRequest(ctx context.Context, targetURL, payload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")

	// 添加重定向头部载荷
	if strings.Contains(payload, "Location:") {
		req.Header.Set("X-Forwarded-Host", strings.TrimPrefix(payload, "Location: "))
	} else if strings.Contains(payload, "Refresh:") {
		req.Header.Set("X-Forwarded-Proto", strings.TrimPrefix(payload, "Refresh: "))
	} else {
		req.Header.Set("X-Original-URL", payload)
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkParameterRedirectResponse 检查参数重定向响应
func (d *OpenRedirectDetector) checkParameterRedirectResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP重定向状态码
	if strings.Contains(response, "status: 301") ||
		strings.Contains(response, "status: 302") ||
		strings.Contains(response, "status: 303") ||
		strings.Contains(response, "status: 307") ||
		strings.Contains(response, "status: 308") {
		confidence += 0.5
	}

	// 检查Location头部
	if strings.Contains(response, "location:") {
		confidence += 0.4

		// 检查Location头部是否包含载荷
		payloadLower := strings.ToLower(payload)
		if payloadLower != "" && strings.Contains(response, payloadLower) {
			confidence += 0.4
		}
	}

	// 检查重定向模式匹配
	for _, pattern := range d.redirectPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 根据载荷类型调整置信度
	if strings.Contains(payload, "evil.com") || strings.Contains(payload, "malicious") {
		if confidence > 0.5 {
			confidence += 0.2 // 恶意域名载荷更危险
		}
	} else if strings.HasPrefix(payload, "//") {
		if confidence > 0.4 {
			confidence += 0.3 // 协议相对重定向更危险
		}
	} else if strings.Contains(payload, "javascript:") {
		if confidence > 0.3 {
			confidence += 0.4 // JavaScript协议更危险
		}
	}

	return confidence
}

// checkHeaderRedirectResponse 检查头部重定向响应
func (d *OpenRedirectDetector) checkHeaderRedirectResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP重定向状态码
	if strings.Contains(response, "status: 301") ||
		strings.Contains(response, "status: 302") ||
		strings.Contains(response, "status: 303") ||
		strings.Contains(response, "status: 307") ||
		strings.Contains(response, "status: 308") {
		confidence += 0.4
	}

	// 检查重定向头部
	redirectHeaders := []string{
		"location:", "refresh:", "x-redirect:", "x-location:", "redirect-to:",
	}

	for _, header := range redirectHeaders {
		if strings.Contains(response, header) {
			confidence += 0.3
			break
		}
	}

	// 检查重定向模式匹配
	for _, pattern := range d.redirectPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkJavascriptRedirectResponse 检查JavaScript重定向响应
func (d *OpenRedirectDetector) checkJavascriptRedirectResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2
	}

	// 检查JavaScript模式匹配
	for _, pattern := range d.javascriptPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查重定向模式匹配
	for _, pattern := range d.redirectPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查JavaScript重定向特定指示器
	jsIndicators := []string{
		"location.href", "window.location", "document.location",
		"location.replace", "location.assign", "window.open",
		"javascript:", "data:text/html", "vbscript:",
		"位置", "地址", "跳转", "重定向",
	}

	for _, indicator := range jsIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.3
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	// 根据载荷类型调整置信度
	if strings.Contains(payload, "javascript:") {
		if confidence > 0.4 {
			confidence += 0.3 // JavaScript协议载荷更危险
		}
	} else if strings.Contains(payload, "data:") {
		if confidence > 0.3 {
			confidence += 0.2 // Data URI载荷更危险
		}
	}

	return confidence
}

// checkProtocolRedirectResponse 检查协议重定向响应
func (d *OpenRedirectDetector) checkProtocolRedirectResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2
	} else if strings.Contains(response, "status: 301") ||
		strings.Contains(response, "status: 302") ||
		strings.Contains(response, "status: 303") ||
		strings.Contains(response, "status: 307") ||
		strings.Contains(response, "status: 308") {
		confidence += 0.3
	}

	// 检查重定向模式匹配
	for _, pattern := range d.redirectPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查协议特定指示器
	protocolIndicators := []string{
		"file:", "ftp:", "mailto:", "tel:", "sms:",
		"skype:", "steam:", "discord:", "custom:",
		"文件:", "邮件:", "电话:", "短信:",
	}

	for _, indicator := range protocolIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.3
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	// 根据载荷类型调整置信度
	if strings.Contains(payload, "file:") {
		if confidence > 0.4 {
			confidence += 0.4 // 文件协议载荷更危险
		}
	} else if strings.Contains(payload, "ftp:") {
		if confidence > 0.3 {
			confidence += 0.3 // FTP协议载荷更危险
		}
	} else if strings.Contains(payload, "mailto:") {
		if confidence > 0.2 {
			confidence += 0.2 // 邮件协议载荷中等危险
		}
	}

	return confidence
}

// extractRedirectEvidence 提取重定向证据
func (d *OpenRedirectDetector) extractRedirectEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var redirectLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "parameter-redirect":
			if strings.Contains(lineLower, "location:") ||
				strings.Contains(lineLower, "refresh:") ||
				strings.Contains(lineLower, "redirect") ||
				strings.Contains(lineLower, "jump") ||
				strings.Contains(lineLower, "forward") ||
				strings.Contains(lineLower, "goto") ||
				strings.Contains(lineLower, "evil.com") ||
				strings.Contains(lineLower, "malicious") ||
				strings.Contains(lineLower, "重定向") ||
				strings.Contains(lineLower, "跳转") ||
				strings.Contains(lineLower, "转向") ||
				strings.Contains(lineLower, "恶意") {
				redirectLines = append(redirectLines, line)
			}
		case "header-redirect":
			if strings.Contains(lineLower, "location:") ||
				strings.Contains(lineLower, "refresh:") ||
				strings.Contains(lineLower, "x-redirect:") ||
				strings.Contains(lineLower, "x-location:") ||
				strings.Contains(lineLower, "redirect-to:") ||
				strings.Contains(lineLower, "x-forwarded") ||
				strings.Contains(lineLower, "x-original") ||
				strings.Contains(lineLower, "重定向") ||
				strings.Contains(lineLower, "跳转") {
				redirectLines = append(redirectLines, line)
			}
		case "javascript-redirect":
			if strings.Contains(lineLower, "location.href") ||
				strings.Contains(lineLower, "window.location") ||
				strings.Contains(lineLower, "document.location") ||
				strings.Contains(lineLower, "location.replace") ||
				strings.Contains(lineLower, "location.assign") ||
				strings.Contains(lineLower, "window.open") ||
				strings.Contains(lineLower, "javascript:") ||
				strings.Contains(lineLower, "data:text/html") ||
				strings.Contains(lineLower, "vbscript:") ||
				strings.Contains(lineLower, "位置") ||
				strings.Contains(lineLower, "地址") ||
				strings.Contains(lineLower, "跳转") ||
				strings.Contains(lineLower, "重定向") {
				redirectLines = append(redirectLines, line)
			}
		case "protocol-redirect":
			if strings.Contains(lineLower, "file:") ||
				strings.Contains(lineLower, "ftp:") ||
				strings.Contains(lineLower, "mailto:") ||
				strings.Contains(lineLower, "tel:") ||
				strings.Contains(lineLower, "sms:") ||
				strings.Contains(lineLower, "skype:") ||
				strings.Contains(lineLower, "steam:") ||
				strings.Contains(lineLower, "discord:") ||
				strings.Contains(lineLower, "custom:") ||
				strings.Contains(lineLower, "文件:") ||
				strings.Contains(lineLower, "邮件:") ||
				strings.Contains(lineLower, "电话:") ||
				strings.Contains(lineLower, "短信:") {
				redirectLines = append(redirectLines, line)
			}
		default:
			if strings.Contains(lineLower, "redirect") ||
				strings.Contains(lineLower, "location") ||
				strings.Contains(lineLower, "jump") ||
				strings.Contains(lineLower, "forward") ||
				strings.Contains(lineLower, "goto") ||
				strings.Contains(lineLower, "refresh") ||
				strings.Contains(lineLower, "重定向") ||
				strings.Contains(lineLower, "跳转") ||
				strings.Contains(lineLower, "转向") {
				redirectLines = append(redirectLines, line)
			}
		}

		if len(redirectLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(redirectLines) > 0 {
		return strings.Join(redirectLines, "\n")
	}

	// 如果没有找到特定的重定向信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
