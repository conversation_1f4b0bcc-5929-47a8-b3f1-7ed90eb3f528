package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestHTTPHeaderInjectionDetectorBasicFunctionality 测试HTTP头部注入检测器基础功能
func TestHTTPHeaderInjectionDetectorBasicFunctionality(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "http-header-injection-comprehensive", detector.GetID())
	assert.Equal(t, "HTTP头部注入漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-113")
	assert.Contains(t, detector.GetCVE(), "CVE-2021-44228")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestHTTPHeaderInjectionDetectorApplicability 测试HTTP头部注入检测器适用性
func TestHTTPHeaderInjectionDetectorApplicability(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 测试有HTTP功能的目标
	httpTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/redirect",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Content-Type": "text/html",
			"Server":       "Apache/2.4.41",
			"Location":     "http://example.com/new",
		},
	}
	assert.True(t, detector.IsApplicable(httpTarget))

	// 测试有HTTP技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "Apache", Version: "2.4.41", Confidence: 0.9},
			{Name: "Nginx", Version: "1.18.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有HTTP链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/home",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/redirect", Text: "Redirect"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有HTTP表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/form",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"redirect": "text", "location": "hidden"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search?q=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试API相关URL
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/redirect",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试普通Web目标（HTTP头部注入是通用问题）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestHTTPHeaderInjectionDetectorConfiguration 测试HTTP头部注入检测器配置
func TestHTTPHeaderInjectionDetectorConfiguration(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 15*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.False(t, config.FollowRedirects) // HTTP头部注入不跟随重定向
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       4,
		FollowRedirects: false,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 4, updatedConfig.Concurrency)
}

// TestHTTPHeaderInjectionDetectorCRLFPayloads 测试CRLF载荷
func TestHTTPHeaderInjectionDetectorCRLFPayloads(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 检查CRLF载荷列表
	assert.NotEmpty(t, detector.crlfPayloads)
	assert.GreaterOrEqual(t, len(detector.crlfPayloads), 20)

	// 检查基础CRLF注入
	assert.Contains(t, detector.crlfPayloads, "test\r\nSet-Cookie: injected=true")
	assert.Contains(t, detector.crlfPayloads, "test\r\nLocation: http://evil.com")
	assert.Contains(t, detector.crlfPayloads, "test\r\nContent-Type: text/html")
	assert.Contains(t, detector.crlfPayloads, "test\r\nX-Injected: true")

	// 检查URL编码CRLF注入
	assert.Contains(t, detector.crlfPayloads, "test%0d%0aSet-Cookie: injected=true")
	assert.Contains(t, detector.crlfPayloads, "test%0d%0aLocation: http://evil.com")
	assert.Contains(t, detector.crlfPayloads, "test%0d%0aContent-Type: text/html")

	// 检查双重编码CRLF注入
	assert.Contains(t, detector.crlfPayloads, "test%250d%250aSet-Cookie: injected=true")
	assert.Contains(t, detector.crlfPayloads, "test%250d%250aLocation: http://evil.com")

	// 检查Unicode编码CRLF注入
	assert.Contains(t, detector.crlfPayloads, "test\u000d\u000aSet-Cookie: injected=true")
	assert.Contains(t, detector.crlfPayloads, "test\u000d\u000aLocation: http://evil.com")

	// 检查混合编码CRLF注入
	assert.Contains(t, detector.crlfPayloads, "test\r%0aSet-Cookie: injected=true")
	assert.Contains(t, detector.crlfPayloads, "test%0d\nLocation: http://evil.com")

	// 检查空格绕过CRLF注入
	assert.Contains(t, detector.crlfPayloads, "test \r\nSet-Cookie: injected=true")
	assert.Contains(t, detector.crlfPayloads, "test\t\r\nLocation: http://evil.com")

	// 检查中文CRLF载荷
	assert.Contains(t, detector.crlfPayloads, "测试\r\nSet-Cookie: 注入=真")
	assert.Contains(t, detector.crlfPayloads, "测试\r\nLocation: http://恶意.com")
}

// TestHTTPHeaderInjectionDetectorResponseSplittingPayloads 测试响应分割载荷
func TestHTTPHeaderInjectionDetectorResponseSplittingPayloads(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 检查响应分割载荷列表
	assert.NotEmpty(t, detector.responseSpittingPayloads)
	assert.GreaterOrEqual(t, len(detector.responseSpittingPayloads), 15)

	// 检查基础响应分割
	assert.Contains(t, detector.responseSpittingPayloads, "test\r\n\r\n<script>alert('XSS')</script>")
	assert.Contains(t, detector.responseSpittingPayloads, "test\r\n\r\n<html><body>Injected Content</body></html>")
	assert.Contains(t, detector.responseSpittingPayloads, "test\r\n\r\nHTTP/1.1 200 OK\r\nContent-Type: text/html\r\n\r\n<script>alert('XSS')</script>")

	// 检查URL编码响应分割
	assert.Contains(t, detector.responseSpittingPayloads, "test%0d%0a%0d%0a<script>alert('XSS')</script>")
	assert.Contains(t, detector.responseSpittingPayloads, "test%0d%0a%0d%0a<html><body>Injected Content</body></html>")

	// 检查双重编码响应分割
	assert.Contains(t, detector.responseSpittingPayloads, "test%250d%250a%250d%250a<script>alert('XSS')</script>")
	assert.Contains(t, detector.responseSpittingPayloads, "test%250d%250a%250d%250a<html><body>Injected Content</body></html>")

	// 检查混合编码响应分割
	assert.Contains(t, detector.responseSpittingPayloads, "test\r\n%0d%0a<script>alert('XSS')</script>")
	assert.Contains(t, detector.responseSpittingPayloads, "test%0d%0a\r\n<html><body>Injected Content</body></html>")

	// 检查缓存投毒响应分割
	assert.Contains(t, detector.responseSpittingPayloads, "test\r\n\r\nHTTP/1.1 200 OK\r\nCache-Control: max-age=3600\r\nContent-Type: text/html\r\n\r\n<script>alert('Cache Poisoning')</script>")

	// 检查中文响应分割载荷
	assert.Contains(t, detector.responseSpittingPayloads, "测试\r\n\r\n<script>alert('注入')</script>")
	assert.Contains(t, detector.responseSpittingPayloads, "测试\r\n\r\n<html><body>注入内容</body></html>")
}

// TestHTTPHeaderInjectionDetectorHeaderInjectionPayloads 测试头部注入载荷
func TestHTTPHeaderInjectionDetectorHeaderInjectionPayloads(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 检查头部注入载荷列表
	assert.NotEmpty(t, detector.headerInjectionPayloads)
	assert.GreaterOrEqual(t, len(detector.headerInjectionPayloads), 25)

	// 检查基础头部注入
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nX-Forwarded-For: 127.0.0.1")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nX-Real-IP: 127.0.0.1")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nX-Originating-IP: 127.0.0.1")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nX-Remote-IP: 127.0.0.1")

	// 检查认证绕过头部注入
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nX-Forwarded-User: admin")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nX-Remote-User: admin")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nX-User: admin")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nAuthorization: Bearer admin_token")

	// 检查缓存控制头部注入
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nCache-Control: no-cache")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nCache-Control: max-age=0")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nPragma: no-cache")

	// 检查安全头部注入
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nX-Frame-Options: DENY")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nX-XSS-Protection: 0")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nX-Content-Type-Options: nosniff")

	// 检查重定向头部注入
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nLocation: http://evil.com")
	assert.Contains(t, detector.headerInjectionPayloads, "test\r\nRefresh: 0; url=http://evil.com")

	// 检查中文头部注入载荷
	assert.Contains(t, detector.headerInjectionPayloads, "测试\r\nX-Forwarded-For: 127.0.0.1")
	assert.Contains(t, detector.headerInjectionPayloads, "测试\r\nX-用户: 管理员")
}

// TestHTTPHeaderInjectionDetectorCachePoisoningPayloads 测试缓存投毒载荷
func TestHTTPHeaderInjectionDetectorCachePoisoningPayloads(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 检查缓存投毒载荷列表
	assert.NotEmpty(t, detector.cachePoisoningPayloads)
	assert.GreaterOrEqual(t, len(detector.cachePoisoningPayloads), 15)

	// 检查基础缓存投毒
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nX-Forwarded-Host: evil.com")
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nHost: evil.com")
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nX-Original-Host: evil.com")
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nX-Host: evil.com")

	// 检查缓存键投毒
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nX-Forwarded-Proto: https")
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nX-Forwarded-Scheme: https")
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nX-Scheme: https")

	// 检查缓存控制投毒
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nVary: X-Forwarded-Host")
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nVary: Host")
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nVary: X-Original-Host")

	// 检查Web缓存欺骗
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nX-Cache-Key: evil_key")
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nX-Cache-Tag: evil_tag")

	// 检查CDN缓存投毒
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nX-Forwarded-Server: evil.com")
	assert.Contains(t, detector.cachePoisoningPayloads, "test\r\nX-Server-Name: evil.com")

	// 检查中文缓存投毒载荷
	assert.Contains(t, detector.cachePoisoningPayloads, "测试\r\nX-Forwarded-Host: 恶意.com")
	assert.Contains(t, detector.cachePoisoningPayloads, "测试\r\nHost: 恶意.com")
}

// TestHTTPHeaderInjectionDetectorHostHeaderPayloads 测试Host头载荷
func TestHTTPHeaderInjectionDetectorHostHeaderPayloads(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 检查Host头载荷列表
	assert.NotEmpty(t, detector.hostHeaderPayloads)
	assert.GreaterOrEqual(t, len(detector.hostHeaderPayloads), 20)

	// 检查基础Host头注入
	assert.Contains(t, detector.hostHeaderPayloads, "evil.com")
	assert.Contains(t, detector.hostHeaderPayloads, "attacker.com")
	assert.Contains(t, detector.hostHeaderPayloads, "malicious.com")
	assert.Contains(t, detector.hostHeaderPayloads, "127.0.0.1")
	assert.Contains(t, detector.hostHeaderPayloads, "localhost")

	// 检查端口Host头注入
	assert.Contains(t, detector.hostHeaderPayloads, "evil.com:80")
	assert.Contains(t, detector.hostHeaderPayloads, "evil.com:443")
	assert.Contains(t, detector.hostHeaderPayloads, "evil.com:8080")

	// 检查子域名Host头注入
	assert.Contains(t, detector.hostHeaderPayloads, "admin.evil.com")
	assert.Contains(t, detector.hostHeaderPayloads, "api.evil.com")
	assert.Contains(t, detector.hostHeaderPayloads, "www.evil.com")

	// 检查特殊字符Host头注入
	assert.Contains(t, detector.hostHeaderPayloads, "evil.com/")
	assert.Contains(t, detector.hostHeaderPayloads, "evil.com\\")
	assert.Contains(t, detector.hostHeaderPayloads, "evil.com#")

	// 检查编码Host头注入
	assert.Contains(t, detector.hostHeaderPayloads, "evil%2ecom")
	assert.Contains(t, detector.hostHeaderPayloads, "evil%252ecom")

	// 检查中文Host头载荷
	assert.Contains(t, detector.hostHeaderPayloads, "恶意.com")
	assert.Contains(t, detector.hostHeaderPayloads, "攻击者.com")
}

// TestHTTPHeaderInjectionDetectorXFFPayloads 测试XFF载荷
func TestHTTPHeaderInjectionDetectorXFFPayloads(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 检查XFF载荷列表
	assert.NotEmpty(t, detector.xffPayloads)
	assert.GreaterOrEqual(t, len(detector.xffPayloads), 15)

	// 检查基础XFF注入
	assert.Contains(t, detector.xffPayloads, "127.0.0.1")
	assert.Contains(t, detector.xffPayloads, "localhost")
	assert.Contains(t, detector.xffPayloads, "0.0.0.0")
	assert.Contains(t, detector.xffPayloads, "***********")
	assert.Contains(t, detector.xffPayloads, "********")

	// 检查多IP XFF注入
	assert.Contains(t, detector.xffPayloads, "127.0.0.1, ***********")
	assert.Contains(t, detector.xffPayloads, "evil.com, 127.0.0.1")
	assert.Contains(t, detector.xffPayloads, "***********, localhost")

	// 检查特殊XFF注入
	assert.Contains(t, detector.xffPayloads, "admin")
	assert.Contains(t, detector.xffPayloads, "root")
	assert.Contains(t, detector.xffPayloads, "administrator")
	assert.Contains(t, detector.xffPayloads, "system")

	// 检查绕过XFF注入
	assert.Contains(t, detector.xffPayloads, "127.0.0.1\r\nX-Real-IP: 127.0.0.1")
	assert.Contains(t, detector.xffPayloads, "localhost\r\nX-Originating-IP: localhost")

	// 检查中文XFF载荷
	assert.Contains(t, detector.xffPayloads, "本地主机")
	assert.Contains(t, detector.xffPayloads, "内部")
	assert.Contains(t, detector.xffPayloads, "管理员")
}

// TestHTTPHeaderInjectionDetectorTestParameters 测试参数列表
func TestHTTPHeaderInjectionDetectorTestParameters(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.GreaterOrEqual(t, len(detector.testParameters), 80)

	// 检查HTTP相关参数
	assert.Contains(t, detector.testParameters, "redirect")
	assert.Contains(t, detector.testParameters, "forward")
	assert.Contains(t, detector.testParameters, "proxy")
	assert.Contains(t, detector.testParameters, "cache")
	assert.Contains(t, detector.testParameters, "location")
	assert.Contains(t, detector.testParameters, "header")

	// 检查重定向相关参数
	assert.Contains(t, detector.testParameters, "redirect_url")
	assert.Contains(t, detector.testParameters, "redirect_uri")
	assert.Contains(t, detector.testParameters, "return_url")
	assert.Contains(t, detector.testParameters, "callback_url")

	// 检查代理相关参数
	assert.Contains(t, detector.testParameters, "proxy_url")
	assert.Contains(t, detector.testParameters, "proxy_host")
	assert.Contains(t, detector.testParameters, "upstream_url")
	assert.Contains(t, detector.testParameters, "backend_url")

	// 检查缓存相关参数
	assert.Contains(t, detector.testParameters, "cache_key")
	assert.Contains(t, detector.testParameters, "cache_tag")
	assert.Contains(t, detector.testParameters, "cache_control")
	assert.Contains(t, detector.testParameters, "cache_policy")

	// 检查头部相关参数
	assert.Contains(t, detector.testParameters, "x_forwarded_for")
	assert.Contains(t, detector.testParameters, "x_real_ip")
	assert.Contains(t, detector.testParameters, "x_forwarded_host")
	assert.Contains(t, detector.testParameters, "x_forwarded_proto")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "重定向")
	assert.Contains(t, detector.testParameters, "转发")
	assert.Contains(t, detector.testParameters, "代理")
	assert.Contains(t, detector.testParameters, "缓存")
}

// TestHTTPHeaderInjectionDetectorPatterns 测试模式
func TestHTTPHeaderInjectionDetectorPatterns(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 检查CRLF模式
	assert.NotEmpty(t, detector.crlfPatterns)
	assert.GreaterOrEqual(t, len(detector.crlfPatterns), 15)

	// 检查头部模式
	assert.NotEmpty(t, detector.headerPatterns)
	assert.GreaterOrEqual(t, len(detector.headerPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.GreaterOrEqual(t, len(detector.responsePatterns), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.GreaterOrEqual(t, len(detector.errorPatterns), 15)
}

// TestHTTPHeaderInjectionDetectorHTTPFeatures 测试HTTP功能检查
func TestHTTPHeaderInjectionDetectorHTTPFeatures(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 测试有HTTP头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Location": "http://example.com/redirect",
			"Server":   "Apache/2.4.41",
		},
	}
	assert.True(t, detector.hasHTTPFeatures(headerTarget))

	// 测试有HTTP技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "Apache", Version: "2.4.41", Confidence: 0.9},
			{Name: "Nginx", Version: "1.18.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasHTTPFeatures(techTarget))

	// 测试有HTTP链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/redirect", Text: "Redirect"},
		},
	}
	assert.True(t, detector.hasHTTPFeatures(linkTarget))

	// 测试有HTTP表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"redirect": "text", "location": "hidden"}},
		},
	}
	assert.True(t, detector.hasHTTPFeatures(formTarget))

	// 测试无HTTP功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasHTTPFeatures(simpleTarget))
}

// TestHTTPHeaderInjectionDetectorRiskScore 测试风险评分计算
func TestHTTPHeaderInjectionDetectorRiskScore(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 4.5)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 2.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestHTTPHeaderInjectionDetectorLifecycle 测试检测器生命周期
func TestHTTPHeaderInjectionDetectorLifecycle(t *testing.T) {
	detector := NewHTTPHeaderInjectionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
