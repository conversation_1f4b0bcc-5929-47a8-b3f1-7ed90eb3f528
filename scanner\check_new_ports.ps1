# 检查新端口的服务状态

Write-Host "检查后端服务 (8080)..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "后端服务: OK - $($response.StatusCode)"
} catch {
    Write-Host "后端服务: ERROR"
}

Write-Host "检查前端服务 (3001)..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001" -UseBasicParsing -TimeoutSec 5
    Write-Host "前端服务: OK - $($response.StatusCode)"
} catch {
    Write-Host "前端服务: ERROR"
}

Write-Host ""
Write-Host "访问地址:"
Write-Host "前端: http://localhost:3001"
Write-Host "后端: http://localhost:8080"
