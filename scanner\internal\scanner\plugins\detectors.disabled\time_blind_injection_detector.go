package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// TimeBlindInjectionDetector 时间盲注检测器
// 支持SQL时间盲注、NoSQL时间盲注、命令时间盲注、代码时间盲注等多种时间盲注漏洞检测
type TimeBlindInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	sqlTimePayloads     []string         // SQL时间盲注载荷
	nosqlTimePayloads   []string         // NoSQL时间盲注载荷
	commandTimePayloads []string         // 命令时间盲注载荷
	codeTimePayloads    []string         // 代码时间盲注载荷
	testParameters      []string         // 测试参数
	timePatterns        []*regexp.Regexp // 时间相关模式
	delayPatterns       []*regexp.Regexp // 延迟模式
	injectionPatterns   []*regexp.Regexp // 注入模式
	httpClient          *http.Client
	baselineTime        time.Duration // 基准响应时间
	delayThreshold      time.Duration // 延迟阈值
}

// NewTimeBlindInjectionDetector 创建时间盲注检测器
func NewTimeBlindInjectionDetector() *TimeBlindInjectionDetector {
	detector := &TimeBlindInjectionDetector{
		id:          "time-blind-injection-comprehensive",
		name:        "时间盲注漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-0708", "CVE-2018-8120"},
		cwe:         []string{"CWE-89", "CWE-78", "CWE-94", "CWE-943", "CWE-77"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测时间盲注漏洞，包括SQL时间盲注、NoSQL时间盲注、命令时间盲注、代码时间盲注等多种时间盲注安全问题",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second, // 时间盲注检测需要较长时间
		MaxRetries:      1,                // 时间盲注检测不适合重试
		Concurrency:     1,                // 时间盲注需要串行检测
		RateLimit:       1,                // 低速率避免影响时间测量
		FollowRedirects: true,             // 跟随重定向检查时间盲注
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，时间盲注响应通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化时间参数
	detector.baselineTime = 0
	detector.delayThreshold = 3 * time.Second // 默认3秒延迟阈值

	// 初始化检测数据
	detector.initializeSQLTimePayloads()
	detector.initializeNoSQLTimePayloads()
	detector.initializeCommandTimePayloads()
	detector.initializeCodeTimePayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *TimeBlindInjectionDetector) GetID() string            { return d.id }
func (d *TimeBlindInjectionDetector) GetName() string          { return d.name }
func (d *TimeBlindInjectionDetector) GetCategory() string      { return d.category }
func (d *TimeBlindInjectionDetector) GetSeverity() string      { return d.severity }
func (d *TimeBlindInjectionDetector) GetCVE() []string         { return d.cve }
func (d *TimeBlindInjectionDetector) GetCWE() []string         { return d.cwe }
func (d *TimeBlindInjectionDetector) GetVersion() string       { return d.version }
func (d *TimeBlindInjectionDetector) GetAuthor() string        { return d.author }
func (d *TimeBlindInjectionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *TimeBlindInjectionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *TimeBlindInjectionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *TimeBlindInjectionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *TimeBlindInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *TimeBlindInjectionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *TimeBlindInjectionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *TimeBlindInjectionDetector) GetDependencies() []string         { return []string{} }
func (d *TimeBlindInjectionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *TimeBlindInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *TimeBlindInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *TimeBlindInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *TimeBlindInjectionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 30 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 1
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 1
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *TimeBlindInjectionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *TimeBlindInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.sqlTimePayloads) == 0 {
		return fmt.Errorf("SQL时间盲注载荷不能为空")
	}
	if len(d.nosqlTimePayloads) == 0 {
		return fmt.Errorf("NoSQL时间盲注载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *TimeBlindInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 时间盲注检测适用于有数据库交互功能的Web应用
	// 检查是否有时间盲注相关的特征
	if d.hasTimeBlindInjectionFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于时间盲注相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	timeKeywords := []string{
		"time", "delay", "sleep", "wait", "timeout", "benchmark",
		"slow", "pause", "interval", "duration", "latency",
		"sql", "query", "search", "filter", "sort", "order",
		"login", "auth", "user", "admin", "data", "info",
		"时间", "延迟", "睡眠", "等待", "超时", "基准",
		"慢", "暂停", "间隔", "持续", "延迟", "查询",
		"搜索", "过滤", "排序", "登录", "认证", "数据",
	}

	for _, keyword := range timeKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 时间盲注是通用Web漏洞，默认适用于所有Web目标
}

// hasTimeBlindInjectionFeatures 检查是否有时间盲注功能
func (d *TimeBlindInjectionDetector) hasTimeBlindInjectionFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有时间盲注相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "time") ||
			strings.Contains(keyLower, "delay") ||
			strings.Contains(keyLower, "timeout") ||
			strings.Contains(valueLower, "time") ||
			strings.Contains(valueLower, "delay") ||
			strings.Contains(valueLower, "timeout") {
			return true
		}
	}

	// 检查技术栈中是否有数据库相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		databaseTechnologies := []string{
			"mysql", "postgresql", "oracle", "mssql", "sqlite", "mongodb",
			"redis", "memcached", "cassandra", "elasticsearch", "solr",
			"mariadb", "percona", "cockroachdb", "influxdb", "neo4j",
			"php", "java", "python", "ruby", "nodejs", "asp.net",
			"spring", "django", "rails", "express", "laravel", "symfony",
			"数据库", "查询", "存储", "缓存",
		}

		for _, dbTech := range databaseTechnologies {
			if strings.Contains(techNameLower, dbTech) {
				return true
			}
		}
	}

	// 检查链接中是否有时间盲注相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "time") ||
			strings.Contains(linkURLLower, "delay") ||
			strings.Contains(linkURLLower, "sql") ||
			strings.Contains(linkURLLower, "query") ||
			strings.Contains(linkTextLower, "time") ||
			strings.Contains(linkTextLower, "delay") ||
			strings.Contains(linkTextLower, "sql") ||
			strings.Contains(linkTextLower, "query") ||
			strings.Contains(linkTextLower, "时间") ||
			strings.Contains(linkTextLower, "延迟") ||
			strings.Contains(linkTextLower, "查询") {
			return true
		}
	}

	// 检查表单中是否有时间盲注相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			timeFields := []string{
				"time", "delay", "sleep", "wait", "timeout", "benchmark",
				"sql", "query", "search", "filter", "sort", "order",
				"login", "auth", "user", "password", "data", "info",
				"时间", "延迟", "睡眠", "等待", "超时", "基准",
				"查询", "搜索", "过滤", "排序", "登录", "认证",
				"用户", "密码", "数据", "信息",
			}

			for _, timeField := range timeFields {
				if strings.Contains(fieldNameLower, timeField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *TimeBlindInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 首先建立基准响应时间
	err := d.establishBaseline(ctx, target)
	if err != nil {
		return nil, fmt.Errorf("建立基准时间失败: %v", err)
	}

	// 执行多种时间盲注检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. SQL时间盲注检测
	sqlEvidence, sqlConfidence, sqlPayload, sqlRequest, sqlResponse := d.detectSQLTimeBlindInjection(ctx, target)
	if sqlConfidence > maxConfidence {
		maxConfidence = sqlConfidence
		vulnerablePayload = sqlPayload
		vulnerableRequest = sqlRequest
		vulnerableResponse = sqlResponse
	}
	evidence = append(evidence, sqlEvidence...)

	// 2. NoSQL时间盲注检测
	nosqlEvidence, nosqlConfidence, nosqlPayload, nosqlRequest, nosqlResponse := d.detectNoSQLTimeBlindInjection(ctx, target)
	if nosqlConfidence > maxConfidence {
		maxConfidence = nosqlConfidence
		vulnerablePayload = nosqlPayload
		vulnerableRequest = nosqlRequest
		vulnerableResponse = nosqlResponse
	}
	evidence = append(evidence, nosqlEvidence...)

	// 3. 命令时间盲注检测
	commandEvidence, commandConfidence, commandPayload, commandRequest, commandResponse := d.detectCommandTimeBlindInjection(ctx, target)
	if commandConfidence > maxConfidence {
		maxConfidence = commandConfidence
		vulnerablePayload = commandPayload
		vulnerableRequest = commandRequest
		vulnerableResponse = commandResponse
	}
	evidence = append(evidence, commandEvidence...)

	// 4. 代码时间盲注检测
	codeEvidence, codeConfidence, codePayload, codeRequest, codeResponse := d.detectCodeTimeBlindInjection(ctx, target)
	if codeConfidence > maxConfidence {
		maxConfidence = codeConfidence
		vulnerablePayload = codePayload
		vulnerableRequest = codeRequest
		vulnerableResponse = codeResponse
	}
	evidence = append(evidence, codeEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "时间盲注漏洞",
		Description:       "检测到时间盲注漏洞，攻击者可能通过时间延迟获取敏感信息或执行恶意操作",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "使用参数化查询、输入验证、输出编码，避免直接拼接用户输入到查询语句中",
		References:        []string{"https://owasp.org/www-community/attacks/Blind_SQL_Injection", "https://cwe.mitre.org/data/definitions/89.html", "https://cwe.mitre.org/data/definitions/78.html"},
		Tags:              []string{"time", "blind", "injection", "sql", "nosql", "command", "code", "delay"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *TimeBlindInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"sql-time-verification",
		"nosql-time-verification",
		"command-time-verification",
		"code-time-verification",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyTimeBlindInjectionMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了时间盲注漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "time-blind-injection-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用时间盲注验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *TimeBlindInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("time_blind_injection_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *TimeBlindInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (时间盲注通常是高风险漏洞)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyTimeBlindInjectionMethod 验证时间盲注方法
func (d *TimeBlindInjectionDetector) verifyTimeBlindInjectionMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "sql-time-verification":
		return d.verifySQLTime(ctx, target)
	case "nosql-time-verification":
		return d.verifyNoSQLTime(ctx, target)
	case "command-time-verification":
		return d.verifyCommandTime(ctx, target)
	case "code-time-verification":
		return d.verifyCodeTime(ctx, target)
	default:
		return 0.0
	}
}

// verifySQLTime 验证SQL时间盲注
func (d *TimeBlindInjectionDetector) verifySQLTime(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的SQL时间盲注验证
	if d.hasTimeBlindInjectionFeatures(target) {
		return 0.7 // 有时间盲注特征的目标可能有SQL时间盲注
	}
	return 0.3
}

// verifyNoSQLTime 验证NoSQL时间盲注
func (d *TimeBlindInjectionDetector) verifyNoSQLTime(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的NoSQL时间盲注验证
	if d.hasTimeBlindInjectionFeatures(target) {
		return 0.6 // 有时间盲注特征的目标可能有NoSQL时间盲注
	}
	return 0.2
}

// verifyCommandTime 验证命令时间盲注
func (d *TimeBlindInjectionDetector) verifyCommandTime(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的命令时间盲注验证
	if d.hasTimeBlindInjectionFeatures(target) {
		return 0.6 // 有时间盲注特征的目标可能有命令时间盲注
	}
	return 0.2
}

// verifyCodeTime 验证代码时间盲注
func (d *TimeBlindInjectionDetector) verifyCodeTime(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的代码时间盲注验证
	if d.hasTimeBlindInjectionFeatures(target) {
		return 0.5 // 有时间盲注特征的目标可能有代码时间盲注
	}
	return 0.1
}

// initializeSQLTimePayloads 初始化SQL时间盲注载荷列表
func (d *TimeBlindInjectionDetector) initializeSQLTimePayloads() {
	d.sqlTimePayloads = []string{
		// MySQL时间盲注载荷
		"' AND SLEEP(5)--",
		"' AND SLEEP(5)#",
		"' OR SLEEP(5)--",
		"' OR SLEEP(5)#",
		"'; WAITFOR DELAY '00:00:05'--",
		"' AND (SELECT SLEEP(5))--",
		"' AND (SELECT SLEEP(5) FROM DUAL)--",
		"' UNION SELECT SLEEP(5)--",
		"' AND BENCHMARK(5000000,MD5(1))--",
		"' AND BENCHMARK(5000000,SHA1(1))--",

		// PostgreSQL时间盲注载荷
		"' AND pg_sleep(5)--",
		"' OR pg_sleep(5)--",
		"'; SELECT pg_sleep(5)--",
		"' AND (SELECT pg_sleep(5))--",
		"' UNION SELECT pg_sleep(5)--",

		// SQL Server时间盲注载荷
		"'; WAITFOR DELAY '00:00:05'--",
		"' AND WAITFOR DELAY '00:00:05'--",
		"' OR WAITFOR DELAY '00:00:05'--",
		"'; WAITFOR TIME '00:00:05'--",
		"' AND WAITFOR TIME '00:00:05'--",

		// Oracle时间盲注载荷
		"' AND DBMS_LOCK.SLEEP(5)--",
		"' OR DBMS_LOCK.SLEEP(5)--",
		"' AND (SELECT DBMS_LOCK.SLEEP(5) FROM DUAL)--",
		"' UNION SELECT DBMS_LOCK.SLEEP(5) FROM DUAL--",

		// SQLite时间盲注载荷
		"' AND (SELECT COUNT(*) FROM (SELECT * FROM sqlite_master UNION SELECT * FROM sqlite_master UNION SELECT * FROM sqlite_master UNION SELECT * FROM sqlite_master UNION SELECT * FROM sqlite_master))--",

		// 通用时间盲注载荷
		"1' AND SLEEP(5)--",
		"1' OR SLEEP(5)--",
		"1 AND SLEEP(5)--",
		"1 OR SLEEP(5)--",
		"admin' AND SLEEP(5)--",
		"admin' OR SLEEP(5)--",
		"test' AND SLEEP(5)--",
		"test' OR SLEEP(5)--",

		// 数字型时间盲注载荷
		"1 AND SLEEP(5)",
		"1 OR SLEEP(5)",
		"1; WAITFOR DELAY '00:00:05'",
		"1 AND pg_sleep(5)",
		"1 OR pg_sleep(5)",
		"1 AND BENCHMARK(5000000,MD5(1))",
		"1 OR BENCHMARK(5000000,MD5(1))",

		// 布尔型时间盲注载荷
		"true AND SLEEP(5)",
		"false OR SLEEP(5)",
		"1=1 AND SLEEP(5)",
		"1=2 OR SLEEP(5)",

		// 中文时间盲注载荷
		"' AND SLEEP(5)-- 时间盲注",
		"' OR SLEEP(5)-- 延迟注入",
		"'; WAITFOR DELAY '00:00:05'-- 等待延迟",
		"' AND pg_sleep(5)-- PostgreSQL延迟",
		"' AND BENCHMARK(5000000,MD5(1))-- 基准测试",
	}
}

// initializeNoSQLTimePayloads 初始化NoSQL时间盲注载荷列表
func (d *TimeBlindInjectionDetector) initializeNoSQLTimePayloads() {
	d.nosqlTimePayloads = []string{
		// MongoDB时间盲注载荷
		`{"$where": "sleep(5000)"}`,
		`{"$where": "this.sleep(5000)"}`,
		`{"$where": "function(){sleep(5000)}"}`,
		`{"$where": "function(){return sleep(5000)}"}`,
		`{"$where": "function(){var start = new Date(); while((new Date() - start) < 5000);}"}`,
		`{"username": {"$where": "sleep(5000)"}}`,
		`{"password": {"$where": "sleep(5000)"}}`,

		// Redis时间盲注载荷
		`EVAL "redis.call('DEBUG', 'SLEEP', 5)" 0`,
		`DEBUG SLEEP 5`,
		`SCRIPT LOAD "redis.call('DEBUG', 'SLEEP', 5)"`,

		// CouchDB时间盲注载荷
		`function(doc) { var start = new Date(); while((new Date() - start) < 5000); emit(doc._id, doc); }`,

		// Elasticsearch时间盲注载荷
		`{"script": {"source": "Thread.sleep(5000)"}}`,
		`{"script": {"source": "java.lang.Thread.sleep(5000)"}}`,

		// 通用NoSQL时间盲注载荷
		`sleep(5000)`,
		`this.sleep(5000)`,
		`function(){sleep(5000)}`,
		`function(){return sleep(5000)}`,
		`function(){var start = new Date(); while((new Date() - start) < 5000);}`,

		// 中文NoSQL时间盲注载荷
		`{"$where": "sleep(5000) // 时间盲注"}`,
		`{"$where": "function(){sleep(5000)} // 延迟注入"}`,
		`function(doc) { /* 时间盲注 */ var start = new Date(); while((new Date() - start) < 5000); emit(doc._id, doc); }`,
	}
}

// initializeCommandTimePayloads 初始化命令时间盲注载荷列表
func (d *TimeBlindInjectionDetector) initializeCommandTimePayloads() {
	d.commandTimePayloads = []string{
		// Linux/Unix命令时间盲注载荷
		"; sleep 5",
		"| sleep 5",
		"& sleep 5",
		"&& sleep 5",
		"|| sleep 5",
		"`sleep 5`",
		"$(sleep 5)",
		"; sleep 5 #",
		"| sleep 5 #",
		"& sleep 5 #",

		// Windows命令时间盲注载荷
		"; timeout 5",
		"| timeout 5",
		"& timeout 5",
		"&& timeout 5",
		"|| timeout 5",
		"; ping -n 6 127.0.0.1",
		"| ping -n 6 127.0.0.1",
		"& ping -n 6 127.0.0.1",
		"&& ping -n 6 127.0.0.1",
		"|| ping -n 6 127.0.0.1",

		// PowerShell命令时间盲注载荷
		"; Start-Sleep 5",
		"| Start-Sleep 5",
		"& Start-Sleep 5",
		"&& Start-Sleep 5",
		"|| Start-Sleep 5",
		"; Start-Sleep -Seconds 5",
		"| Start-Sleep -Seconds 5",
		"& Start-Sleep -Seconds 5",

		// 通用命令时间盲注载荷
		"test; sleep 5",
		"test| sleep 5",
		"test& sleep 5",
		"test&& sleep 5",
		"test|| sleep 5",
		"admin; sleep 5",
		"admin| sleep 5",
		"admin& sleep 5",

		// 中文命令时间盲注载荷
		"; sleep 5 # 时间盲注",
		"| sleep 5 # 延迟注入",
		"& sleep 5 # 命令注入",
		"; timeout 5 # Windows延迟",
		"| timeout 5 # 超时命令",
	}
}

// initializeCodeTimePayloads 初始化代码时间盲注载荷列表
func (d *TimeBlindInjectionDetector) initializeCodeTimePayloads() {
	d.codeTimePayloads = []string{
		// PHP代码时间盲注载荷
		"sleep(5)",
		"usleep(5000000)",
		"time_sleep_until(time() + 5)",
		"<?php sleep(5); ?>",
		"<?php usleep(5000000); ?>",
		"<?php time_sleep_until(time() + 5); ?>",
		"eval('sleep(5);')",
		"system('sleep 5')",
		"exec('sleep 5')",
		"shell_exec('sleep 5')",

		// Python代码时间盲注载荷
		"time.sleep(5)",
		"import time; time.sleep(5)",
		"__import__('time').sleep(5)",
		"exec('import time; time.sleep(5)')",
		"eval('__import__(\"time\").sleep(5)')",
		"os.system('sleep 5')",
		"subprocess.call(['sleep', '5'])",

		// Java代码时间盲注载荷
		"Thread.sleep(5000)",
		"java.lang.Thread.sleep(5000)",
		"TimeUnit.SECONDS.sleep(5)",
		"java.util.concurrent.TimeUnit.SECONDS.sleep(5)",
		"try{Thread.sleep(5000);}catch(Exception e){}",
		"Runtime.getRuntime().exec(\"sleep 5\")",

		// JavaScript代码时间盲注载荷
		"setTimeout(function(){}, 5000)",
		"setInterval(function(){}, 5000)",
		"new Promise(resolve => setTimeout(resolve, 5000))",
		"await new Promise(resolve => setTimeout(resolve, 5000))",
		"require('child_process').exec('sleep 5')",
		"process.exit(0)",

		// Ruby代码时间盲注载荷
		"sleep(5)",
		"Kernel.sleep(5)",
		"system('sleep 5')",
		"`sleep 5`",
		"exec('sleep 5')",
		"IO.popen('sleep 5')",

		// C#代码时间盲注载荷
		"Thread.Sleep(5000)",
		"System.Threading.Thread.Sleep(5000)",
		"Task.Delay(5000)",
		"System.Threading.Tasks.Task.Delay(5000)",
		"Process.Start(\"sleep\", \"5\")",
		"System.Diagnostics.Process.Start(\"sleep\", \"5\")",

		// Go代码时间盲注载荷
		"time.Sleep(5 * time.Second)",
		"time.Sleep(time.Duration(5) * time.Second)",
		"<-time.After(5 * time.Second)",
		"exec.Command(\"sleep\", \"5\").Run()",

		// Perl代码时间盲注载荷
		"sleep(5)",
		"select(undef, undef, undef, 5)",
		"system('sleep 5')",
		"`sleep 5`",
		"exec('sleep 5')",

		// 通用代码时间盲注载荷
		"delay(5000)",
		"wait(5000)",
		"pause(5000)",
		"timeout(5000)",
		"interval(5000)",

		// 中文代码时间盲注载荷
		"sleep(5) // 时间盲注",
		"time.sleep(5) # 延迟注入",
		"Thread.sleep(5000) /* 代码注入 */",
		"setTimeout(function(){}, 5000) // JavaScript延迟",
		"System.Threading.Thread.Sleep(5000) // C#延迟",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *TimeBlindInjectionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 时间盲注相关参数
		"time", "delay", "sleep", "wait", "timeout", "benchmark",
		"slow", "pause", "interval", "duration", "latency", "timing",
		"time_delay", "time_sleep", "time_wait", "time_timeout",
		"delay_time", "sleep_time", "wait_time", "timeout_time",

		// SQL相关参数
		"sql", "query", "search", "filter", "sort", "order", "where",
		"select", "insert", "update", "delete", "union", "join",
		"database", "table", "column", "field", "record", "row",
		"sql_query", "sql_search", "sql_filter", "sql_sort",

		// 用户相关参数
		"user", "username", "userid", "uid", "login", "auth", "password",
		"pass", "pwd", "email", "name", "account", "profile", "session",
		"user_id", "user_name", "user_login", "user_auth", "user_pass",

		// 数据相关参数
		"data", "info", "content", "text", "value", "input", "output",
		"param", "parameter", "arg", "argument", "var", "variable",
		"data_query", "data_search", "data_filter", "data_sort",

		// 命令相关参数
		"cmd", "command", "exec", "execute", "run", "system", "shell",
		"script", "code", "eval", "function", "method", "call",
		"cmd_exec", "cmd_run", "cmd_system", "cmd_shell",

		// 文件相关参数
		"file", "filename", "filepath", "path", "dir", "directory",
		"upload", "download", "read", "write", "open", "close",
		"file_path", "file_name", "file_upload", "file_download",

		// API相关参数
		"api", "endpoint", "service", "method", "action", "operation",
		"request", "response", "json", "xml", "rest", "soap",
		"api_key", "api_token", "api_call", "api_request",

		// 中文参数
		"时间", "延迟", "睡眠", "等待", "超时", "基准", "慢", "暂停",
		"间隔", "持续", "延迟", "时机", "查询", "搜索", "过滤",
		"排序", "用户", "登录", "认证", "密码", "数据", "信息",
		"内容", "文本", "值", "输入", "输出", "参数", "变量",
		"命令", "执行", "运行", "系统", "脚本", "代码", "函数",
		"文件", "路径", "目录", "上传", "下载", "读取", "写入",
	}
}

// initializePatterns 初始化检测模式
func (d *TimeBlindInjectionDetector) initializePatterns() {
	// 时间相关检测模式 - 检测时间盲注相关的响应内容
	timePatternStrings := []string{
		// 时间延迟模式
		`(?i)sleep\s*\(\s*\d+\s*\)`,
		`(?i)usleep\s*\(\s*\d+\s*\)`,
		`(?i)time\.sleep\s*\(\s*\d+\s*\)`,
		`(?i)thread\.sleep\s*\(\s*\d+\s*\)`,
		`(?i)settimeout\s*\(\s*[^,]+,\s*\d+\s*\)`,
		`(?i)delay\s*\(\s*\d+\s*\)`,
		`(?i)wait\s*\(\s*\d+\s*\)`,
		`(?i)pause\s*\(\s*\d+\s*\)`,

		// SQL时间函数模式
		`(?i)waitfor\s+delay\s+['"][^'"]*['"]`,
		`(?i)pg_sleep\s*\(\s*\d+\s*\)`,
		`(?i)benchmark\s*\(\s*\d+\s*,`,
		`(?i)dbms_lock\.sleep\s*\(\s*\d+\s*\)`,
		`(?i)sleep\s*\(\s*\d+\s*\)\s*--`,
		`(?i)sleep\s*\(\s*\d+\s*\)\s*#`,

		// 时间戳模式
		`(?i)timestamp\s*[:=]\s*\d+`,
		`(?i)time_stamp\s*[:=]\s*\d+`,
		`(?i)current_time\s*[:=]`,
		`(?i)now\s*\(\s*\)`,
		`(?i)sysdate\s*\(\s*\)`,
		`(?i)gettime\s*\(\s*\)`,
		`(?i)microtime\s*\(\s*\)`,

		// 延迟响应模式
		`(?i)response\s+time\s*[:=]\s*\d+`,
		`(?i)execution\s+time\s*[:=]\s*\d+`,
		`(?i)query\s+time\s*[:=]\s*\d+`,
		`(?i)processing\s+time\s*[:=]\s*\d+`,
		`(?i)elapsed\s+time\s*[:=]\s*\d+`,

		// 中文时间模式
		`(?i)(时间|延迟|睡眠|等待|暂停).*\d+`,
		`(?i)(超时|间隔|持续|基准).*\d+`,
		`(?i)(当前时间|系统时间|时间戳)`,
		`(?i)(响应时间|执行时间|查询时间)`,
		`(?i)(处理时间|耗时|用时)`,
	}

	d.timePatterns = make([]*regexp.Regexp, 0, len(timePatternStrings))
	for _, pattern := range timePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.timePatterns = append(d.timePatterns, compiled)
		}
	}

	// 延迟检测模式 - 检测延迟相关的响应内容
	delayPatternStrings := []string{
		// 延迟指示器模式
		`(?i)delay\s*[:=]\s*\d+`,
		`(?i)timeout\s*[:=]\s*\d+`,
		`(?i)interval\s*[:=]\s*\d+`,
		`(?i)duration\s*[:=]\s*\d+`,
		`(?i)latency\s*[:=]\s*\d+`,
		`(?i)slow\s*[:=]\s*(true|1|yes)`,

		// 延迟错误模式
		`(?i)timeout\s+(error|exception)`,
		`(?i)connection\s+timeout`,
		`(?i)read\s+timeout`,
		`(?i)write\s+timeout`,
		`(?i)query\s+timeout`,
		`(?i)execution\s+timeout`,

		// 延迟警告模式
		`(?i)slow\s+(query|request|response)`,
		`(?i)long\s+(running|execution)`,
		`(?i)performance\s+(warning|issue)`,
		`(?i)high\s+(latency|delay)`,

		// 中文延迟模式
		`(?i)(延迟|超时|间隔|持续|延迟).*\d+`,
		`(?i)(慢查询|长时间|性能问题)`,
		`(?i)(高延迟|高耗时|响应慢)`,
		`(?i)(连接超时|读取超时|写入超时)`,
		`(?i)(查询超时|执行超时|处理超时)`,
	}

	d.delayPatterns = make([]*regexp.Regexp, 0, len(delayPatternStrings))
	for _, pattern := range delayPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.delayPatterns = append(d.delayPatterns, compiled)
		}
	}

	// 注入检测模式 - 检测注入相关的响应内容
	injectionPatternStrings := []string{
		// SQL注入模式
		`(?i)sql\s+(syntax|error|exception)`,
		`(?i)mysql\s+(error|exception)`,
		`(?i)postgresql\s+(error|exception)`,
		`(?i)oracle\s+(error|exception)`,
		`(?i)mssql\s+(error|exception)`,
		`(?i)sqlite\s+(error|exception)`,

		// 命令注入模式
		`(?i)command\s+(not\s+found|error|exception)`,
		`(?i)shell\s+(error|exception)`,
		`(?i)exec\s+(error|exception)`,
		`(?i)system\s+(error|exception)`,

		// 代码注入模式
		`(?i)eval\s+(error|exception)`,
		`(?i)parse\s+(error|exception)`,
		`(?i)syntax\s+(error|exception)`,
		`(?i)runtime\s+(error|exception)`,

		// NoSQL注入模式
		`(?i)mongodb\s+(error|exception)`,
		`(?i)redis\s+(error|exception)`,
		`(?i)couchdb\s+(error|exception)`,
		`(?i)elasticsearch\s+(error|exception)`,

		// 中文注入模式
		`(?i)(SQL|数据库).*错误`,
		`(?i)(命令|系统).*错误`,
		`(?i)(代码|脚本).*错误`,
		`(?i)(语法|解析).*错误`,
		`(?i)(运行时|执行).*错误`,
	}

	d.injectionPatterns = make([]*regexp.Regexp, 0, len(injectionPatternStrings))
	for _, pattern := range injectionPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.injectionPatterns = append(d.injectionPatterns, compiled)
		}
	}
}
