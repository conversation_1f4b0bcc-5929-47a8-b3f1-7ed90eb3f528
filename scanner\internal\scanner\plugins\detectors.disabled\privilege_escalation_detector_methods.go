package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectVerticalPrivilegeEscalation 检测垂直权限提升
func (d *PrivilegeEscalationDetector) detectVerticalPrivilegeEscalation(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试管理员路径
	for i, path := range d.adminPaths {
		if i >= 15 { // 限制路径数量避免过多请求
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造管理员URL
		adminURL := d.buildPrivilegeURL(target.URL, path)

		// 发送管理员路径请求
		resp, err := d.sendPrivilegeRequest(ctx, adminURL)
		if err != nil {
			continue
		}

		// 检查垂直权限提升响应
		confidence := d.checkVerticalPrivilegeEscalation(resp, path)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = path
			vulnerableRequest = adminURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "vertical-privilege-escalation",
				Description: fmt.Sprintf("发现垂直权限提升: %s (置信度: %.2f)", path, confidence),
				Content:     d.extractPrivilegeEvidence(resp, path),
				Location:    adminURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectHorizontalPrivilegeEscalation 检测水平权限提升
func (d *PrivilegeEscalationDetector) detectHorizontalPrivilegeEscalation(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试用户ID枚举
	testUserIDs := []string{"1", "2", "3", "100", "1000", "admin", "test", "guest"}

	for i, userPath := range d.userPaths {
		if i >= 8 { // 限制路径数量
			break
		}

		for j, userID := range testUserIDs {
			if j >= 5 { // 限制ID数量
				break
			}

			select {
			case <-ctx.Done():
				return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
			default:
			}

			// 替换用户ID
			actualPath := strings.Replace(userPath, "{id}", userID, -1)
			userURL := d.buildPrivilegeURL(target.URL, actualPath)

			// 发送用户路径请求
			resp, err := d.sendPrivilegeRequest(ctx, userURL)
			if err != nil {
				continue
			}

			// 检查水平权限提升响应
			confidence := d.checkHorizontalPrivilegeEscalation(resp, actualPath, userID)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = actualPath
				vulnerableRequest = userURL
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "horizontal-privilege-escalation",
					Description: fmt.Sprintf("发现水平权限提升: %s (置信度: %.2f)", actualPath, confidence),
					Content:     d.extractPrivilegeEvidence(resp, actualPath),
					Location:    userURL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 500)
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectAuthenticationBypass 检测认证绕过
func (d *PrivilegeEscalationDetector) detectAuthenticationBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试认证绕过载荷
	for i, payload := range d.authBypassPayloads {
		if i >= 10 { // 限制载荷数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送认证绕过请求
		resp, err := d.sendAuthBypassRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查认证绕过响应
		confidence := d.checkAuthenticationBypass(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "authentication-bypass",
				Description: fmt.Sprintf("发现认证绕过: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractPrivilegeEvidence(resp, payload),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Second * 1)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectAccessControlFlaws 检测访问控制缺陷
func (d *PrivilegeEscalationDetector) detectAccessControlFlaws(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试API端点
	for i, endpoint := range d.apiEndpoints {
		if i >= 10 { // 限制端点数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造API URL
		apiURL := d.buildPrivilegeURL(target.URL, endpoint)

		// 发送API请求
		resp, err := d.sendPrivilegeRequest(ctx, apiURL)
		if err != nil {
			continue
		}

		// 检查访问控制缺陷
		confidence := d.checkAccessControlFlaws(resp, endpoint)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = endpoint
			vulnerableRequest = apiURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "access-control-flaw",
				Description: fmt.Sprintf("发现访问控制缺陷: %s (置信度: %.2f)", endpoint, confidence),
				Content:     d.extractPrivilegeEvidence(resp, endpoint),
				Location:    apiURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendPrivilegeRequest 发送权限请求
func (d *PrivilegeEscalationDetector) sendPrivilegeRequest(ctx context.Context, requestURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", requestURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendAuthBypassRequest 发送认证绕过请求
func (d *PrivilegeEscalationDetector) sendAuthBypassRequest(ctx context.Context, baseURL, payload string) (string, error) {
	// 构造POST数据
	data := url.Values{}
	data.Set("username", payload)
	data.Set("password", payload)
	data.Set("user", payload)
	data.Set("pass", payload)

	req, err := http.NewRequestWithContext(ctx, "POST", baseURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// buildPrivilegeURL 构造权限URL
func (d *PrivilegeEscalationDetector) buildPrivilegeURL(baseURL, path string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + path
	}

	// 确保路径以/开头
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}

	parsedURL.Path = path
	parsedURL.RawQuery = ""
	return parsedURL.String()
}

// checkVerticalPrivilegeEscalation 检查垂直权限提升
func (d *PrivilegeEscalationDetector) checkVerticalPrivilegeEscalation(response, path string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.4
	} else if strings.Contains(response, "status: 302") || strings.Contains(response, "status: 301") {
		confidence += 0.2 // 重定向可能表示需要认证
	} else {
		return 0.0 // 其他状态码通常表示访问失败
	}

	// 检查权限模式
	for _, pattern := range d.privilegePatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查成功访问模式
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查是否有访问拒绝指示器（如果有，降低置信度）
	for _, pattern := range d.accessDeniedPatterns {
		if pattern.MatchString(response) {
			confidence -= 0.5
			break
		}
	}

	// 检查路径特定的指示器
	pathLower := strings.ToLower(path)
	if strings.Contains(pathLower, "admin") && strings.Contains(response, "admin") {
		confidence += 0.2
	}
	if strings.Contains(pathLower, "manage") && strings.Contains(response, "manage") {
		confidence += 0.2
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}
	if confidence < 0.0 {
		confidence = 0.0
	}

	return confidence
}

// checkHorizontalPrivilegeEscalation 检查水平权限提升
func (d *PrivilegeEscalationDetector) checkHorizontalPrivilegeEscalation(response, path, userID string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.5
	} else {
		return 0.0
	}

	// 检查用户信息泄露
	userIDLower := strings.ToLower(userID)
	if strings.Contains(response, userIDLower) {
		confidence += 0.3
	}

	// 检查权限模式（用户相关）
	userPatterns := []string{
		"user profile", "account details", "personal data",
		"contact information", "billing information", "order history",
		"用户资料", "账户详情", "个人信息", "联系信息", "订单历史",
	}

	for _, pattern := range userPatterns {
		if strings.Contains(response, pattern) {
			confidence += 0.2
			break
		}
	}

	// 检查是否有访问拒绝指示器
	for _, pattern := range d.accessDeniedPatterns {
		if pattern.MatchString(response) {
			confidence -= 0.4
			break
		}
	}

	// 检查敏感信息泄露
	sensitivePatterns := []string{
		"email", "phone", "address", "credit card", "ssn",
		"邮箱", "电话", "地址", "信用卡", "身份证",
	}

	for _, pattern := range sensitivePatterns {
		if strings.Contains(response, pattern) {
			confidence += 0.2
			break
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkAuthenticationBypass 检查认证绕过
func (d *PrivilegeEscalationDetector) checkAuthenticationBypass(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查成功登录指示器
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查权限模式
	for _, pattern := range d.privilegePatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查重定向（可能表示成功登录）
	if strings.Contains(response, "status: 302") || strings.Contains(response, "status: 301") {
		if strings.Contains(response, "location:") {
			confidence += 0.3
		}
	}

	// 检查Cookie设置（可能表示会话建立）
	if strings.Contains(response, "set-cookie:") {
		if strings.Contains(response, "session") || strings.Contains(response, "auth") {
			confidence += 0.3
		}
	}

	// 检查是否有认证失败指示器
	for _, pattern := range d.accessDeniedPatterns {
		if pattern.MatchString(response) {
			confidence -= 0.4
			break
		}
	}

	// 检查载荷特定的响应
	payloadLower := strings.ToLower(payload)
	if strings.Contains(payloadLower, "sql") && strings.Contains(response, "error") {
		confidence -= 0.2 // SQL错误可能表示注入失败
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}
	if confidence < 0.0 {
		confidence = 0.0
	}

	return confidence
}

// checkAccessControlFlaws 检查访问控制缺陷
func (d *PrivilegeEscalationDetector) checkAccessControlFlaws(response, endpoint string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.5
	} else {
		return 0.0
	}

	// 检查API响应格式
	if strings.Contains(response, "application/json") ||
		strings.Contains(response, "{") ||
		strings.Contains(response, "[") {
		confidence += 0.2
	}

	// 检查权限相关的API响应
	apiPatterns := []string{
		`"role":`, `"permissions":`, `"privileges":`, `"access_level":`,
		`"is_admin":`, `"user_type":`, `"admin":`, `"管理员":`,
	}

	for _, pattern := range apiPatterns {
		if strings.Contains(response, pattern) {
			confidence += 0.3
			break
		}
	}

	// 检查敏感数据泄露
	sensitiveAPIPatterns := []string{
		`"password":`, `"secret":`, `"token":`, `"key":`,
		`"密码":`, `"密钥":`, `"令牌":`, `"秘钥":`,
	}

	for _, pattern := range sensitiveAPIPatterns {
		if strings.Contains(response, pattern) {
			confidence += 0.4
			break
		}
	}

	// 检查端点特定的指示器
	endpointLower := strings.ToLower(endpoint)
	if strings.Contains(endpointLower, "admin") && strings.Contains(response, "admin") {
		confidence += 0.2
	}
	if strings.Contains(endpointLower, "user") && strings.Contains(response, "user") {
		confidence += 0.2
	}

	return confidence
}

// extractPrivilegeEvidence 提取权限证据
func (d *PrivilegeEscalationDetector) extractPrivilegeEvidence(response, path string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 查找包含权限相关信息的行
	var privilegeLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)
		if strings.Contains(lineLower, "admin") ||
			strings.Contains(lineLower, "manage") ||
			strings.Contains(lineLower, "dashboard") ||
			strings.Contains(lineLower, "user") ||
			strings.Contains(lineLower, "profile") ||
			strings.Contains(lineLower, "权限") ||
			strings.Contains(lineLower, "管理") {
			privilegeLines = append(privilegeLines, line)
			if len(privilegeLines) >= 5 { // 只取前5行
				break
			}
		}
	}

	if len(privilegeLines) > 0 {
		return strings.Join(privilegeLines, "\n")
	}

	// 如果没有找到特定的权限信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
