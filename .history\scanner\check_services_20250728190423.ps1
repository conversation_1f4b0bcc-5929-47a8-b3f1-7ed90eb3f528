#!/usr/bin/env pwsh
# 漏洞扫描器服务状态检查脚本

Write-Host "==================================" -ForegroundColor Green
Write-Host "    漏洞扫描器服务状态检查" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host ""

# 检查后端服务
Write-Host "检查后端服务 (http://localhost:8080)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 后端服务运行正常" -ForegroundColor Green
        Write-Host "   状态码: $($response.StatusCode)" -ForegroundColor Gray
        Write-Host "   响应: $($response.Content)" -ForegroundColor Gray
    } else {
        Write-Host "❌ 后端服务响应异常" -ForegroundColor Red
        Write-Host "   状态码: $($response.StatusCode)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 后端服务无法访问" -ForegroundColor Red
    Write-Host "   错误: $($_.Exception.Message)" -ForegroundColor Gray
}

Write-Host ""

# 检查前端服务
Write-Host "检查前端服务 (http://localhost:3000)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 前端服务运行正常" -ForegroundColor Green
        Write-Host "   状态码: $($response.StatusCode)" -ForegroundColor Gray
    } else {
        Write-Host "❌ 前端服务响应异常" -ForegroundColor Red
        Write-Host "   状态码: $($response.StatusCode)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 前端服务无法访问" -ForegroundColor Red
    Write-Host "   错误: $($_.Exception.Message)" -ForegroundColor Gray
}

Write-Host ""

# 检查端口占用
Write-Host "检查端口占用情况..." -ForegroundColor Yellow
$ports = @(8080, 3000)
foreach ($port in $ports) {
    try {
        $connections = Get-NetTCPConnection -LocalPort $port -State Listen -ErrorAction SilentlyContinue
        if ($connections) {
            Write-Host "✅ 端口 $port 正在监听" -ForegroundColor Green
            foreach ($conn in $connections) {
                Write-Host "   地址: $($conn.LocalAddress):$($conn.LocalPort)" -ForegroundColor Gray
            }
        } else {
            Write-Host "❌ 端口 $port 未在监听" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 无法检查端口 $port" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "==================================" -ForegroundColor Green
Write-Host "访问地址:" -ForegroundColor Green
Write-Host "前端应用: http://localhost:3000" -ForegroundColor Cyan
Write-Host "后端API:  http://localhost:8080" -ForegroundColor Cyan
Write-Host "健康检查: http://localhost:8080/health" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Green
