# 插件化架构扩充实战示例

## 🎯 **实战案例：添加API速率限制检测器**

本文档通过一个完整的实战案例，展示如何为插件化架构添加新的检测器。

---

## 📋 **案例背景**

### **需求分析**
- **目标**：检测API端点是否缺乏速率限制保护
- **风险**：可能导致DoS攻击或资源滥用
- **检测方法**：发送连续请求，分析响应模式
- **适用范围**：API端点、REST服务、GraphQL等

### **技术要求**
- 实现`VulnerabilityDetector`接口
- 支持智能目标适用性检查
- 提供完整的测试覆盖
- 集成到现有扫描引擎

---

## 🛠️ **实现步骤详解**

### **步骤1：创建检测器主文件**

<function_calls>
<invoke name="view">
<parameter name="path">scanner/internal/scanner/plugins/detectors/api_rate_limiting_detector.go
