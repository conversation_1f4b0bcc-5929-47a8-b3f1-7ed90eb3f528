package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestPathTraversalDetectorBasicFunctionality 测试路径遍历检测器基础功能
func TestPathTraversalDetectorBasicFunctionality(t *testing.T) {
	detector := NewPathTraversalDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "path-traversal-comprehensive", detector.GetID())
	assert.Equal(t, "路径遍历漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-22")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestPathTraversalDetectorApplicability 测试路径遍历检测器适用性
func TestPathTraversalDetectorApplicability(t *testing.T) {
	detector := NewPathTraversalDetector()

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/download",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/download",
				Method: "POST",
				Fields: map[string]string{
					"file": "text",
					"path": "text",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的URL
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/view?file=test.txt&path=/uploads",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试文件操作相关的URL
	fileTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/files/download",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(fileTarget))

	// 测试包含路径的URL
	pathTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/include/header.php",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(pathTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（路径遍历检测适用于所有Web目标）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(simpleTarget))
}

// TestPathTraversalDetectorConfiguration 测试路径遍历检测器配置
func TestPathTraversalDetectorConfiguration(t *testing.T) {
	detector := NewPathTraversalDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 15*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.True(t, config.FollowRedirects) // 路径遍历检测跟随重定向

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       8,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestPathTraversalDetectorPayloads 测试路径遍历检测器载荷
func TestPathTraversalDetectorPayloads(t *testing.T) {
	detector := NewPathTraversalDetector()

	// 检查路径遍历载荷
	assert.NotEmpty(t, detector.traversalPayloads)
	assert.Greater(t, len(detector.traversalPayloads), 25)

	// 检查文件包含载荷
	assert.NotEmpty(t, detector.fileInclusionPayloads)
	assert.Greater(t, len(detector.fileInclusionPayloads), 15)

	// 检查路径绕过载荷
	assert.NotEmpty(t, detector.bypassPayloads)
	assert.Greater(t, len(detector.bypassPayloads), 10)

	// 检查敏感文件列表
	assert.NotEmpty(t, detector.sensitiveFiles)
	assert.Greater(t, len(detector.sensitiveFiles), 20)

	// 检查文件指示器
	assert.NotEmpty(t, detector.fileIndicators)
	assert.Greater(t, len(detector.fileIndicators), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.Greater(t, len(detector.errorPatterns), 10)

	// 检查成功模式
	assert.NotEmpty(t, detector.successPatterns)
	assert.Greater(t, len(detector.successPatterns), 20)

	// 检查特定的载荷
	assert.Contains(t, detector.traversalPayloads, "../")
	assert.Contains(t, detector.traversalPayloads, "..\\")
	assert.Contains(t, detector.fileInclusionPayloads, "../etc/passwd")
	assert.Contains(t, detector.bypassPayloads, "./")
}

// TestPathTraversalDetectorTraversalResponse 测试路径遍历响应检查
func TestPathTraversalDetectorTraversalResponse(t *testing.T) {
	detector := NewPathTraversalDetector()

	// 测试Unix /etc/passwd响应
	passwdResponse := `root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin`
	passwdPayload := "../etc/passwd"
	confidence := detector.checkTraversalResponse(passwdResponse, passwdPayload)
	assert.Greater(t, confidence, 0.7)

	// 测试Windows win.ini响应
	winIniResponse := `[fonts]
[extensions]
[mci extensions]
[files]
[Mail]
MAPI=1`
	winIniPayload := "..\\windows\\win.ini"
	confidence2 := detector.checkTraversalResponse(winIniResponse, winIniPayload)
	assert.Greater(t, confidence2, 0.6)

	// 测试hosts文件响应
	hostsResponse := `# Copyright (c) 1993-2009 Microsoft Corp.
#
# This is a sample HOSTS file used by Microsoft TCP/IP for Windows.
#
127.0.0.1       localhost
::1             localhost`
	hostsPayload := "../etc/hosts"
	confidence3 := detector.checkTraversalResponse(hostsResponse, hostsPayload)
	assert.Greater(t, confidence3, 0.6)

	// 测试无路径遍历特征的响应
	normalResponse := "Hello World"
	normalPayload := "../etc/passwd"
	confidence4 := detector.checkTraversalResponse(normalResponse, normalPayload)
	assert.Equal(t, 0.0, confidence4)
}

// TestPathTraversalDetectorFileInclusionResponse 测试文件包含响应检查
func TestPathTraversalDetectorFileInclusionResponse(t *testing.T) {
	detector := NewPathTraversalDetector()

	// 测试PHP配置文件响应
	phpConfigResponse := `<?php
define('DB_NAME', 'database_name');
define('DB_USER', 'username');
define('DB_PASSWORD', 'password');
define('DB_HOST', 'localhost');
?>`
	phpConfigPayload := "../config/config.php"
	confidence := detector.checkFileInclusionResponse(phpConfigResponse, phpConfigPayload)
	assert.Greater(t, confidence, 0.7)

	// 测试.env文件响应
	envResponse := `APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:abcdefghijklmnopqrstuvwxyz
APP_DEBUG=true
APP_URL=http://localhost

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=secret`
	envPayload := "../.env"
	confidence2 := detector.checkFileInclusionResponse(envResponse, envPayload)
	assert.Greater(t, confidence2, 0.6)

	// 测试无文件包含特征的响应
	normalResponse := "Page not found"
	normalPayload := "../config/database.yml"
	confidence3 := detector.checkFileInclusionResponse(normalResponse, normalPayload)
	assert.Equal(t, 0.0, confidence3)
}

// TestPathTraversalDetectorPathBypassResponse 测试路径绕过响应检查
func TestPathTraversalDetectorPathBypassResponse(t *testing.T) {
	detector := NewPathTraversalDetector()

	// 测试成功绕过的响应（200状态码 + 管理内容）
	successResponse := `Status: 200 OK
Content-Type: text/html

<html>
<head><title>Admin Panel</title></head>
<body>
<h1>Administrator Dashboard</h1>
<p>Welcome to the management interface</p>
</body>
</html>`
	successPayload := "../admin/"
	confidence := detector.checkPathBypassResponse(successResponse, successPayload)
	assert.Greater(t, confidence, 0.4)

	// 测试被阻止的响应（403状态码）
	blockedResponse := `Status: 403 Forbidden
Content-Type: text/html

<html>
<head><title>Access Denied</title></head>
<body>
<h1>403 Forbidden</h1>
<p>You don't have permission to access this resource.</p>
</body>
</html>`
	blockedPayload := "../admin/"
	confidence2 := detector.checkPathBypassResponse(blockedResponse, blockedPayload)
	assert.Greater(t, confidence2, 0.0)
	assert.Less(t, confidence2, 0.3)

	// 测试普通响应
	normalResponse := `Status: 200 OK
Content-Type: text/html

<html>
<head><title>Home</title></head>
<body>
<h1>Welcome</h1>
<p>This is the home page</p>
</body>
</html>`
	normalPayload := "./"
	confidence3 := detector.checkPathBypassResponse(normalResponse, normalPayload)
	assert.Greater(t, confidence3, 0.0)
	assert.Less(t, confidence3, 0.4)
}

// TestPathTraversalDetectorRiskScore 测试风险评分计算
func TestPathTraversalDetectorRiskScore(t *testing.T) {
	detector := NewPathTraversalDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 4.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestPathTraversalDetectorLifecycle 测试检测器生命周期
func TestPathTraversalDetectorLifecycle(t *testing.T) {
	detector := NewPathTraversalDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkPathTraversalDetectorTraversalCheck 基准测试路径遍历检查性能
func BenchmarkPathTraversalDetectorTraversalCheck(b *testing.B) {
	detector := NewPathTraversalDetector()
	response := "root:x:0:0:root:/root:/bin/bash\ndaemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin"
	payload := "../etc/passwd"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkTraversalResponse(response, payload)
	}
}

// BenchmarkPathTraversalDetectorFileInclusionCheck 基准测试文件包含检查性能
func BenchmarkPathTraversalDetectorFileInclusionCheck(b *testing.B) {
	detector := NewPathTraversalDetector()
	response := "<?php\ndefine('DB_NAME', 'database');\ndefine('DB_USER', 'user');\n?>"
	payload := "../config/config.php"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkFileInclusionResponse(response, payload)
	}
}
