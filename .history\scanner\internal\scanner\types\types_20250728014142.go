package types

import (
	"context"
	"scanner/internal/services"
	"time"
)

// ScanEngine 扫描引擎接口
// 定义了所有扫描引擎必须实现的基本方法
type ScanEngine interface {
	// GetName 获取引擎名称
	GetName() string

	// GetType 获取引擎类型
	GetType() string

	// GetSupportedTargets 获取支持的目标类型
	GetSupportedTargets() []string

	// Validate 验证扫描配置是否有效
	Validate(config *ScanConfig) error

	// Scan 执行扫描
	Scan(ctx context.Context, target *ScanTarget, config *ScanConfig, progress chan<- *ScanProgress) (*ScanResult, error)

	// Stop 停止扫描
	Stop(ctx context.Context, taskID string) error

	// IsEnabled 检查引擎是否启用
	IsEnabled() bool

	// SetLogService 设置日志服务
	SetLogService(logService *services.ScanLogService)
}

// ScanTarget 扫描目标
type ScanTarget struct {
	ID       string            `json:"id"`
	Type     string            `json:"type"`     // url, ip, domain, cidr
	Value    string            `json:"value"`    // 目标值
	Port     int               `json:"port"`     // 端口（可选）
	Protocol string            `json:"protocol"` // 协议（可选）
	Metadata map[string]string `json:"metadata"` // 额外元数据
}

// ScanConfig 扫描配置
type ScanConfig struct {
	// 基础配置
	TaskID    string            `json:"task_id"`
	Depth     int               `json:"depth"`      // 扫描深度 1-浅度 2-中度 3-深度
	Timeout   time.Duration     `json:"timeout"`    // 超时时间
	Threads   int               `json:"threads"`    // 并发线程数
	UserAgent string            `json:"user_agent"` // User-Agent
	Headers   map[string]string `json:"headers"`    // 自定义请求头
	Cookies   map[string]string `json:"cookies"`    // Cookie

	// Web扫描配置
	WebConfig *WebScanConfig `json:"web_config,omitempty"`

	// 网络扫描配置
	NetworkConfig *NetworkScanConfig `json:"network_config,omitempty"`

	// 主机扫描配置
	HostConfig *HostScanConfig `json:"host_config,omitempty"`

	// API扫描配置
	APIConfig *APIScanConfig `json:"api_config,omitempty"`

	// CVE扫描配置
	CVEConfig *CVEScanConfig `json:"cve_config,omitempty"`

	// 云安全扫描配置
	CloudConfig *CloudScanConfig `json:"cloud_config,omitempty"`
}

// WebScanConfig Web扫描配置
type WebScanConfig struct {
	CheckSQLi         bool     `json:"check_sqli"`          // SQL注入检测
	CheckXSS          bool     `json:"check_xss"`           // XSS检测
	CheckCSRF         bool     `json:"check_csrf"`          // CSRF检测
	CheckFileUpload   bool     `json:"check_file_upload"`   // 文件上传漏洞
	CheckDirTraversal bool     `json:"check_dir_traversal"` // 目录遍历
	CheckWeakAuth     bool     `json:"check_weak_auth"`     // 弱认证检测
	CrawlDepth        int      `json:"crawl_depth"`         // 爬虫深度
	ExcludePaths      []string `json:"exclude_paths"`       // 排除路径
	IncludePaths      []string `json:"include_paths"`       // 包含路径
}

// NetworkScanConfig 网络扫描配置
type NetworkScanConfig struct {
	PortRange        string `json:"port_range"`        // 端口范围 "1-1000"
	ScanTCP          bool   `json:"scan_tcp"`          // TCP扫描
	ScanUDP          bool   `json:"scan_udp"`          // UDP扫描
	ServiceDetection bool   `json:"service_detection"` // 服务检测
	OSDetection      bool   `json:"os_detection"`      // 操作系统检测
	BannerGrab       bool   `json:"banner_grab"`       // Banner抓取
	ExcludePorts     []int  `json:"exclude_ports"`     // 排除端口
}

// HostScanConfig 主机扫描配置
type HostScanConfig struct {
	CheckSystemInfo bool     `json:"check_system_info"` // 系统信息检查
	CheckProcesses  bool     `json:"check_processes"`   // 进程检查
	CheckServices   bool     `json:"check_services"`    // 服务检查
	CheckFiles      bool     `json:"check_files"`       // 文件检查
	CheckUsers      bool     `json:"check_users"`       // 用户检查
	CheckPatches    bool     `json:"check_patches"`     // 补丁检查
	CheckPorts      []string `json:"check_ports"`       // 检查的端口列表
}

// APIScanConfig API扫描配置
type APIScanConfig struct {
	CheckAuthBypass bool   `json:"check_auth_bypass"` // 认证绕过
	CheckInjection  bool   `json:"check_injection"`   // 注入攻击
	CheckRateLimit  bool   `json:"check_rate_limit"`  // 速率限制
	CheckDataLeak   bool   `json:"check_data_leak"`   // 数据泄露
	CheckBizLogic   bool   `json:"check_biz_logic"`   // 业务逻辑
	APIDoc          string `json:"api_doc"`           // API文档URL
	AuthToken       string `json:"auth_token"`        // 认证令牌
}

// CVEScanConfig CVE扫描配置
type CVEScanConfig struct {
	// 历史扫描配置
	EnableHistoricalScan bool `json:"enable_historical_scan"` // 启用历史扫描
	StartYear            int  `json:"start_year"`             // 开始年份
	EndYear              int  `json:"end_year"`               // 结束年份
	MaxCVEsPerTarget     int  `json:"max_cves_per_target"`    // 每个目标最大CVE数

	// 扫描策略
	ScanStrategy        string   `json:"scan_strategy"`        // 扫描策略: comprehensive, targeted, fast
	ConfidenceThreshold float64  `json:"confidence_threshold"` // 置信度阈值
	SeverityFilter      []string `json:"severity_filter"`      // 严重程度过滤

	// PoC验证配置
	EnablePoCVerification bool `json:"enable_poc_verification"` // 启用PoC验证
	PoCTimeout            int  `json:"poc_timeout"`             // PoC超时时间(秒)
	MaxPoCAttempts        int  `json:"max_poc_attempts"`        // 最大PoC尝试次数

	// 利用检测配置
	EnableExploitDetection bool     `json:"enable_exploit_detection"` // 启用利用检测
	ExploitSources         []string `json:"exploit_sources"`          // 利用代码源

	// 性能配置
	ConcurrentScans int  `json:"concurrent_scans"` // 并发扫描数
	CacheEnabled    bool `json:"cache_enabled"`    // 启用缓存
	CacheTTL        int  `json:"cache_ttl"`        // 缓存TTL(秒)
}

// ScanProgress 扫描进度
type ScanProgress struct {
	TaskID      string    `json:"task_id"`
	Stage       string    `json:"stage"`        // 当前阶段
	Progress    int       `json:"progress"`     // 进度百分比 0-100
	Message     string    `json:"message"`      // 进度消息
	CurrentItem string    `json:"current_item"` // 当前处理项
	Timestamp   time.Time `json:"timestamp"`
}

// ScanResult 扫描结果
type ScanResult struct {
	TaskID          string                 `json:"task_id"`
	TargetID        string                 `json:"target_id"`
	StartTime       time.Time              `json:"start_time"`
	EndTime         time.Time              `json:"end_time"`
	Duration        time.Duration          `json:"duration"`
	Status          string                 `json:"status"`          // success, failed, stopped
	Progress        int                    `json:"progress"`        // 最终进度
	Vulnerabilities []*Vulnerability       `json:"vulnerabilities"` // 发现的漏洞
	Statistics      *ScanStatistics        `json:"statistics"`      // 扫描统计
	Metadata        map[string]interface{} `json:"metadata"`        // 额外元数据
	Errors          []string               `json:"errors"`          // 错误信息
}

// Vulnerability 漏洞信息
type Vulnerability struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`        // 漏洞类型
	Name        string                 `json:"name"`        // 漏洞名称
	Description string                 `json:"description"` // 漏洞描述
	Severity    string                 `json:"severity"`    // 严重程度 critical, high, medium, low, info
	Category    string                 `json:"category"`    // 漏洞分类：Web安全、API安全、合规检查、主机安全等
	Scanner     string                 `json:"scanner"`     // 扫描器类型：web_scanner, api_scanner, compliance_scanner等
	CVSS        float64                `json:"cvss"`        // CVSS评分
	CVE         string                 `json:"cve"`         // CVE编号
	URL         string                 `json:"url"`         // 漏洞URL
	Method      string                 `json:"method"`      // HTTP方法
	Parameter   string                 `json:"parameter"`   // 参数名
	Payload     string                 `json:"payload"`     // 攻击载荷
	Evidence    string                 `json:"evidence"`    // 证据
	Response    string                 `json:"response"`    // 响应内容
	Solution    string                 `json:"solution"`    // 解决方案
	References  []string               `json:"references"`  // 参考链接
	Metadata    map[string]interface{} `json:"metadata"`    // 额外元数据
	CreatedAt   time.Time              `json:"created_at"`
}

// ScanStatistics 扫描统计
type ScanStatistics struct {
	TotalTargets    int `json:"total_targets"`    // 总目标数
	ScannedTargets  int `json:"scanned_targets"`  // 已扫描目标数
	TotalVulns      int `json:"total_vulns"`      // 总漏洞数
	CriticalVulns   int `json:"critical_vulns"`   // 严重漏洞数
	HighVulns       int `json:"high_vulns"`       // 高危漏洞数
	MediumVulns     int `json:"medium_vulns"`     // 中危漏洞数
	LowVulns        int `json:"low_vulns"`        // 低危漏洞数
	InfoVulns       int `json:"info_vulns"`       // 信息漏洞数
	TotalRequests   int `json:"total_requests"`   // 总请求数
	SuccessRequests int `json:"success_requests"` // 成功请求数
	FailedRequests  int `json:"failed_requests"`  // 失败请求数
}

// ServiceInfo 服务信息
type ServiceInfo struct {
	Name       string `json:"name"`        // 服务名称，如 http, ssh, mysql
	Product    string `json:"product"`     // 产品名称，如 Apache httpd, OpenSSH
	Version    string `json:"version"`     // 版本号
	ExtraInfo  string `json:"extra_info"`  // 额外信息
	CPE        string `json:"cpe"`         // Common Platform Enumeration
	Vendor     string `json:"vendor"`      // 厂商
	DeviceType string `json:"device_type"` // 设备类型
	OSType     string `json:"os_type"`     // 操作系统类型
}

// CloudScanConfig 云安全扫描配置
type CloudScanConfig struct {
	// 云平台配置
	Platforms []string `json:"platforms"` // 云平台列表: aws, azure, alicloud, tencentcloud

	// 扫描范围
	ResourceTypes []string `json:"resource_types"` // 资源类型: ec2, s3, rds, vpc, iam等
	Regions       []string `json:"regions"`        // 扫描区域

	// 合规标准
	ComplianceStandards []string `json:"compliance_standards"` // 合规标准: cis, nist, iso27001

	// 过滤条件
	TagFilters       map[string]string `json:"tag_filters"`       // 标签过滤
	ExcludeResources []string          `json:"exclude_resources"` // 排除资源

	// 扫描配置
	MaxConcurrency int  `json:"max_concurrency"` // 最大并发数
	EnableCache    bool `json:"enable_cache"`    // 启用缓存
}

// DetectionTarget 检测目标
type DetectionTarget struct {
	URL      string                 `json:"url"`      // 目标URL
	Method   string                 `json:"method"`   // HTTP方法
	Headers  map[string]string      `json:"headers"`  // 请求头
	Body     string                 `json:"body"`     // 请求体
	Payloads []string               `json:"payloads"` // 载荷列表
	Config   map[string]interface{} `json:"config"`   // 配置参数
}

// DetectionResult 检测结果
type DetectionResult struct {
	ID          string                 `json:"id"`          // 结果ID
	VulnType    string                 `json:"vuln_type"`   // 漏洞类型
	Severity    string                 `json:"severity"`    // 严重程度
	Confidence  float64                `json:"confidence"`  // 置信度
	Payload     string                 `json:"payload"`     // 使用的载荷
	Evidence    string                 `json:"evidence"`    // 证据
	URL         string                 `json:"url"`         // 漏洞URL
	Method      string                 `json:"method"`      // HTTP方法
	Parameter   string                 `json:"parameter"`   // 参数
	Description string                 `json:"description"` // 描述
	Solution    string                 `json:"solution"`    // 解决方案
	References  []string               `json:"references"`  // 参考链接
	Metadata    map[string]interface{} `json:"metadata"`    // 元数据
	CreatedAt   time.Time              `json:"created_at"`  // 创建时间
}
