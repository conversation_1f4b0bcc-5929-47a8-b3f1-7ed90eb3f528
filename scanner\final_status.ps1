Write-Host "=== 漏洞扫描器服务状态 ===" -ForegroundColor Green

# 检查后端
Write-Host "后端服务 (8080): " -NoNewline
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -UseBasicParsing -TimeoutSec 3
    Write-Host "运行正常" -ForegroundColor Green
} catch {
    Write-Host "无法访问" -ForegroundColor Red
}

# 检查前端 3000
Write-Host "前端服务 (3000): " -NoNewline
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 3
    Write-Host "运行正常" -ForegroundColor Green
} catch {
    Write-Host "无法访问" -ForegroundColor Yellow
}

# 检查前端 3001
Write-Host "前端服务 (3001): " -NoNewline
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001" -UseBasicParsing -TimeoutSec 3
    Write-Host "运行正常" -ForegroundColor Green
} catch {
    Write-Host "无法访问" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "访问地址:" -ForegroundColor Cyan
Write-Host "  后端API: http://localhost:8080" -ForegroundColor White
Write-Host "  前端界面: http://localhost:3000 或 http://localhost:3001" -ForegroundColor White
Write-Host "  健康检查: http://localhost:8080/health" -ForegroundColor White
