package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// DirectoryBrowsingDetector 目录浏览漏洞检测器
// 检测网站目录列表暴露、配置缺陷等安全问题
type DirectoryBrowsingDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	directoryPaths        []string         // 常见目录路径
	sensitiveDirectories  []string         // 敏感目录列表
	directoryIndicators   []string         // 目录列表指示器
	serverSignatures      []string         // 服务器特征
	listingPatterns       []*regexp.Regexp // 目录列表模式
	configErrorPatterns   []*regexp.Regexp // 配置错误模式
	sensitiveFilePatterns []*regexp.Regexp // 敏感文件模式
	httpClient            *http.Client
}

// NewDirectoryBrowsingDetector 创建目录浏览漏洞检测器
func NewDirectoryBrowsingDetector() *DirectoryBrowsingDetector {
	detector := &DirectoryBrowsingDetector{
		id:          "directory-browsing-detector",
		name:        "目录浏览漏洞检测器",
		category:    "information-disclosure",
		severity:    "Medium",
		cve:         []string{"CVE-2019-17671", "CVE-2020-5902"},
		cwe:         []string{"CWE-200", "CWE-548", "CWE-16"},
		version:     "1.0.0",
		author:      "Security Scanner Team",
		description: "检测网站目录列表暴露、配置缺陷等目录浏览漏洞",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
		config: &plugins.DetectorConfig{
			Enabled:         true,
			Timeout:         30 * time.Second,
			MaxRetries:      3,
			Concurrency:     5,
			RateLimit:       10,
			FollowRedirects: false,
			VerifySSL:       false,
			MaxResponseSize: 1024 * 1024, // 1MB
			Priority:        5,
		},
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				// 不跟随重定向，直接返回响应
				return http.ErrUseLastResponse
			},
		},
	}

	// 初始化检测规则
	detector.initializeRules()

	return detector
}

// initializeRules 初始化检测规则
func (d *DirectoryBrowsingDetector) initializeRules() {
	// 常见目录路径
	d.directoryPaths = []string{
		"/", "/admin/", "/backup/", "/config/", "/data/", "/db/",
		"/docs/", "/files/", "/images/", "/includes/", "/js/",
		"/css/", "/uploads/", "/downloads/", "/temp/", "/tmp/",
		"/logs/", "/log/", "/cache/", "/assets/", "/static/",
		"/public/", "/private/", "/secure/", "/test/", "/dev/",
		"/api/", "/app/", "/application/", "/bin/", "/lib/",
		"/vendor/", "/node_modules/", "/src/", "/source/",
		"/www/", "/web/", "/html/", "/htdocs/", "/public_html/",
		"/wp-content/", "/wp-admin/", "/wp-includes/",
		"/administrator/", "/manager/", "/phpmyadmin/",
		"/webmail/", "/mail/", "/email/", "/ftp/", "/sftp/",
		"/database/", "/mysql/", "/sql/", "/oracle/",
		"/backup/", "/backups/", "/bak/", "/old/", "/archive/",
		"/install/", "/setup/", "/update/", "/upgrade/",
		"/debug/", "/error/", "/errors/", "/exception/",
		"/system/", "/sys/", "/proc/", "/etc/", "/var/",
		"/usr/", "/opt/", "/home/", "/root/", "/boot/",
	}

	// 敏感目录列表
	d.sensitiveDirectories = []string{
		"/admin/", "/administrator/", "/manager/", "/control/",
		"/panel/", "/dashboard/", "/console/", "/backend/",
		"/config/", "/configuration/", "/settings/", "/setup/",
		"/install/", "/installation/", "/installer/", "/update/",
		"/backup/", "/backups/", "/bak/", "/dump/", "/dumps/",
		"/database/", "/db/", "/data/", "/sql/", "/mysql/",
		"/logs/", "/log/", "/logfiles/", "/access_log/", "/error_log/",
		"/temp/", "/tmp/", "/temporary/", "/cache/", "/session/",
		"/private/", "/secure/", "/protected/", "/restricted/",
		"/internal/", "/confidential/", "/secret/", "/hidden/",
		"/test/", "/testing/", "/debug/", "/dev/", "/development/",
		"/staging/", "/beta/", "/alpha/", "/demo/", "/sample/",
		"/.git/", "/.svn/", "/.hg/", "/.bzr/", "/CVS/",
		"/.env/", "/.config/", "/.ssh/", "/.aws/", "/.docker/",
		"/wp-config/", "/wp-admin/", "/wp-content/uploads/",
		"/phpmyadmin/", "/phpinfo/", "/info/", "/status/",
		"/server-status/", "/server-info/", "/stats/", "/statistics/",
	}

	// 目录列表指示器
	d.directoryIndicators = []string{
		"Index of", "Directory Listing", "Directory listing for",
		"Parent Directory", "Name", "Last modified", "Size", "Description",
		"[DIR]", "[   ]", "[TXT]", "[IMG]", "[ICO]", "[SND]", "[VID]",
		"<title>Index of", "<h1>Index of", "Directory contents",
		"File listing", "Folder listing", "Browse folder",
		"Apache", "nginx", "IIS", "Lighttpd", "Cherokee",
		"目录列表", "文件列表", "索引", "浏览目录", "文件夹",
		"上级目录", "父目录", "返回上级", "文件名", "修改时间",
		"文件大小", "文件类型", "权限", "所有者",
	}

	// 服务器特征
	d.serverSignatures = []string{
		"Apache", "nginx", "IIS", "Lighttpd", "Cherokee", "Caddy",
		"Tomcat", "Jetty", "WebLogic", "WebSphere", "JBoss",
		"Node.js", "Express", "Kestrel", "Gunicorn", "uWSGI",
		"Microsoft-IIS", "Apache-Coyote", "GWS", "cloudflare",
	}

	// 编译正则表达式模式
	d.compilePatterns()
}

// compilePatterns 编译正则表达式模式
func (d *DirectoryBrowsingDetector) compilePatterns() {
	// 目录列表模式
	listingPatterns := []string{
		`(?i)<title[^>]*>.*?index\s+of.*?</title>`,
		`(?i)<h1[^>]*>.*?index\s+of.*?</h1>`,
		`(?i)directory\s+listing\s+for`,
		`(?i)parent\s+directory`,
		`(?i)<a\s+href="\.\./?"[^>]*>\s*\[?\s*parent\s+directory\s*\]?\s*</a>`,
		`(?i)<a\s+href="[^"]*/"[^>]*>\s*\[[^\]]*DIR[^\]]*\]\s*</a>`,
		`(?i)<pre[^>]*>.*?<a\s+href="\.\./?"[^>]*>.*?</pre>`,
		`(?i)<table[^>]*>.*?<th[^>]*>.*?name.*?</th>.*?<th[^>]*>.*?size.*?</th>`,
		`(?i)<ul[^>]*>.*?<li[^>]*>.*?<a\s+href="[^"]*/"[^>]*>.*?</li>`,
		`(?i)apache.*?server.*?port\s+\d+`,
		`(?i)nginx.*?directory\s+index`,
		`(?i)iis.*?directory\s+browsing`,
		`(?i)lighttpd.*?directory\s+listing`,
		`(?i)file\s+listing.*?directory`,
		`(?i)browse\s+folder.*?contents`,
	}

	for _, pattern := range listingPatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.listingPatterns = append(d.listingPatterns, compiled)
		}
	}

	// 配置错误模式
	configErrorPatterns := []string{
		`(?i)directory\s+browsing\s+enabled`,
		`(?i)autoindex\s+on`,
		`(?i)options\s+indexes`,
		`(?i)directoryindex\s+disabled`,
		`(?i)default\s+document\s+not\s+found`,
		`(?i)no\s+default\s+page\s+configured`,
		`(?i)directory\s+listing\s+is\s+enabled`,
		`(?i)indexing\s+is\s+enabled`,
		`(?i)folder\s+browsing\s+allowed`,
		`(?i)file\s+listing\s+enabled`,
	}

	for _, pattern := range configErrorPatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.configErrorPatterns = append(d.configErrorPatterns, compiled)
		}
	}

	// 敏感文件模式
	sensitiveFilePatterns := []string{
		`(?i)\.env`,
		`(?i)\.git`,
		`(?i)\.svn`,
		`(?i)config\.php`,
		`(?i)wp-config\.php`,
		`(?i)database\.php`,
		`(?i)\.htaccess`,
		`(?i)\.htpasswd`,
		`(?i)web\.config`,
		`(?i)backup\.sql`,
		`(?i)dump\.sql`,
		`(?i)phpinfo\.php`,
		`(?i)info\.php`,
		`(?i)test\.php`,
		`(?i)debug\.php`,
		`(?i)admin\.php`,
		`(?i)login\.php`,
		`(?i)password\.txt`,
		`(?i)passwords\.txt`,
		`(?i)users\.txt`,
		`(?i)accounts\.txt`,
		`(?i)credentials\.txt`,
		`(?i)secret\.txt`,
		`(?i)private\.key`,
		`(?i)id_rsa`,
		`(?i)id_dsa`,
		`(?i)\.pem`,
		`(?i)\.p12`,
		`(?i)\.pfx`,
		`(?i)\.jks`,
		`(?i)\.keystore`,
	}

	for _, pattern := range sensitiveFilePatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.sensitiveFilePatterns = append(d.sensitiveFilePatterns, compiled)
		}
	}
}

// GetID 获取检测器ID
func (d *DirectoryBrowsingDetector) GetID() string {
	return d.id
}

// GetName 获取检测器名称
func (d *DirectoryBrowsingDetector) GetName() string {
	return d.name
}

// GetCategory 获取检测器分类
func (d *DirectoryBrowsingDetector) GetCategory() string {
	return d.category
}

// GetSeverity 获取严重程度
func (d *DirectoryBrowsingDetector) GetSeverity() string {
	return d.severity
}

// GetCVE 获取相关CVE
func (d *DirectoryBrowsingDetector) GetCVE() []string {
	return d.cve
}

// GetCWE 获取相关CWE
func (d *DirectoryBrowsingDetector) GetCWE() []string {
	return d.cwe
}

// GetVersion 获取版本
func (d *DirectoryBrowsingDetector) GetVersion() string {
	return d.version
}

// GetAuthor 获取作者
func (d *DirectoryBrowsingDetector) GetAuthor() string {
	return d.author
}

// GetDescription 获取描述
func (d *DirectoryBrowsingDetector) GetDescription() string {
	return d.description
}

// GetCreatedAt 获取创建时间
func (d *DirectoryBrowsingDetector) GetCreatedAt() time.Time {
	return d.createdAt
}

// GetUpdatedAt 获取更新时间
func (d *DirectoryBrowsingDetector) GetUpdatedAt() time.Time {
	return d.updatedAt
}

// IsEnabled 检查是否启用
func (d *DirectoryBrowsingDetector) IsEnabled() bool {
	return d.enabled
}

// SetEnabled 设置启用状态
func (d *DirectoryBrowsingDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

// GetConfig 获取配置
func (d *DirectoryBrowsingDetector) GetConfig() *plugins.DetectorConfig {
	return d.config
}

// SetConfig 设置配置
func (d *DirectoryBrowsingDetector) SetConfig(config *plugins.DetectorConfig) {
	d.config = config
	d.updatedAt = time.Now()
}

// IsApplicable 检查是否适用于目标
func (d *DirectoryBrowsingDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Type != "web" {
		return false
	}

	// 检查URL协议
	if !strings.HasPrefix(target.URL, "http://") && !strings.HasPrefix(target.URL, "https://") {
		return false
	}

	return true
}

// generateVulnID 生成漏洞ID
func (d *DirectoryBrowsingDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("%s-%s-%d", d.id, target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *DirectoryBrowsingDetector) calculateRiskScore(confidence float64) float64 {
	baseScore := 5.0 // 中等风险基础分

	// 根据置信度调整分数
	riskScore := baseScore * confidence

	// 确保分数在合理范围内
	if riskScore > 10.0 {
		riskScore = 10.0
	} else if riskScore < 0.0 {
		riskScore = 0.0
	}

	return riskScore
}

// Detect 执行目录浏览漏洞检测
func (d *DirectoryBrowsingDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	logger.Infof("开始目录浏览漏洞检测: %s", target.URL)

	// 执行多种目录浏览检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 根目录检测
	rootEvidence, rootConfidence, rootPayload, rootRequest, rootResponse := d.detectRootDirectoryBrowsing(ctx, target)
	if rootConfidence > maxConfidence {
		maxConfidence = rootConfidence
		vulnerablePayload = rootPayload
		vulnerableRequest = rootRequest
		vulnerableResponse = rootResponse
	}
	evidence = append(evidence, rootEvidence...)

	// 2. 常见目录检测
	dirEvidence, dirConfidence, dirPayload, dirRequest, dirResponse := d.detectCommonDirectoryBrowsing(ctx, target)
	if dirConfidence > maxConfidence {
		maxConfidence = dirConfidence
		vulnerablePayload = dirPayload
		vulnerableRequest = dirRequest
		vulnerableResponse = dirResponse
	}
	evidence = append(evidence, dirEvidence...)

	// 3. 敏感目录检测
	sensitiveEvidence, sensitiveConfidence, sensitivePayload, sensitiveRequest, sensitiveResponse := d.detectSensitiveDirectoryBrowsing(ctx, target)
	if sensitiveConfidence > maxConfidence {
		maxConfidence = sensitiveConfidence
		vulnerablePayload = sensitivePayload
		vulnerableRequest = sensitiveRequest
		vulnerableResponse = sensitiveResponse
	}
	evidence = append(evidence, sensitiveEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "目录浏览漏洞",
		Description:       "检测到目录浏览漏洞，攻击者可能能够浏览服务器目录结构并访问敏感文件",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "禁用目录浏览功能，配置默认首页文件，限制目录访问权限",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Directory_indexing", "https://cwe.mitre.org/data/definitions/548.html"},
		Tags:              []string{"directory-browsing", "information-disclosure", "web", "configuration"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *DirectoryBrowsingDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	logger.Infof("开始验证目录浏览漏洞: %s", target.URL)

	// 验证文件列表
	verificationPaths := []string{
		"/",
		"/admin/",
		"/backup/",
		"/config/",
		"/uploads/",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, path := range verificationPaths {
		// 发送验证请求
		resp, err := d.sendDirectoryRequest(ctx, target.URL, path)
		if err != nil {
			continue
		}

		// 检查目录浏览响应特征
		responseConfidence := d.checkDirectoryBrowsingResponse(resp, path)
		if responseConfidence > 0.5 {
			verificationConfidence += 0.3
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证路径触发了目录浏览响应"),
				Content:     resp,
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	// 计算最终验证置信度
	finalConfidence := verificationConfidence
	if finalConfidence > 1.0 {
		finalConfidence = 1.0
	}

	isVerified := finalConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: finalConfidence,
		Method:     "directory-browsing-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("目录浏览漏洞验证完成，置信度: %.2f", finalConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// GetTargetTypes 获取支持的目标类型
func (d *DirectoryBrowsingDetector) GetTargetTypes() []string {
	return []string{"http", "https"}
}

// GetRequiredPorts 获取需要的端口
func (d *DirectoryBrowsingDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443}
}

// GetRequiredServices 获取需要的服务
func (d *DirectoryBrowsingDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web"}
}

// GetRequiredHeaders 获取需要的HTTP头
func (d *DirectoryBrowsingDetector) GetRequiredHeaders() []string {
	return []string{}
}

// GetRequiredTechnologies 获取需要的技术栈
func (d *DirectoryBrowsingDetector) GetRequiredTechnologies() []string {
	return []string{}
}

// GetDependencies 获取依赖的其他检测器ID
func (d *DirectoryBrowsingDetector) GetDependencies() []string {
	return []string{}
}

// GetConfiguration 获取检测器配置
func (d *DirectoryBrowsingDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

// SetConfiguration 设置检测器配置
func (d *DirectoryBrowsingDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

// Validate 验证检测器有效性
func (d *DirectoryBrowsingDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if d.config == nil {
		return fmt.Errorf("检测器配置不能为空")
	}
	return nil
}

// Initialize 初始化检测器
func (d *DirectoryBrowsingDetector) Initialize() error {
	// 验证配置
	if err := d.Validate(); err != nil {
		return err
	}

	// 更新HTTP客户端配置
	if d.config.Timeout > 0 {
		d.httpClient.Timeout = d.config.Timeout
	}

	return nil
}

// Cleanup 清理资源
func (d *DirectoryBrowsingDetector) Cleanup() error {
	// 关闭HTTP客户端连接
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}
