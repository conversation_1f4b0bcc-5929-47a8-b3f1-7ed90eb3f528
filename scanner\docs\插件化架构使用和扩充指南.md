# 插件化架构使用和扩充指南

## 🎯 **插件化架构概览**

### 📊 **当前架构状态**

**已实现组件：**
- ✅ **核心接口**：`VulnerabilityDetector`接口
- ✅ **管理器**：`DetectorManager`和`CVEDetectorManager`
- ✅ **检测器生态**：40+专业检测器
- ✅ **集成机制**：已融入Web扫描引擎
- ✅ **测试框架**：完整的测试覆盖

**架构层次：**
```
┌─────────────────────────────────────────┐
│           扫描引擎层                      │
│  (Web/Network/Host/API扫描引擎)          │
├─────────────────────────────────────────┤
│           插件管理层                      │
│  (DetectorManager/CVEDetectorManager)   │
├─────────────────────────────────────────┤
│           检测器插件层                    │
│  (40+专业检测器 + CVE检测器)             │
├─────────────────────────────────────────┤
│           基础接口层                      │
│  (VulnerabilityDetector接口)            │
└─────────────────────────────────────────┘
```

---

## 🚀 **如何使用插件化架构**

### 1. **在扫描引擎中使用**

#### **Web引擎集成示例**
```go
// 1. 初始化检测器管理器
engine.detectorManager = plugins.NewDetectorManager(config, logger)

// 2. 注册内置检测器
engine.registerBuiltinDetectors()

// 3. 在扫描过程中使用
func (e *WebEngine) vulnerabilityDetection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
    // 构造插件化目标
    pluginTarget := e.convertToPluginTarget(targetURL, taskID)
    
    // 智能选择检测器
    selectedDetectors, err := e.detectorManager.SelectDetectors(pluginTarget, &plugins.SelectionOptions{
        EnabledOnly:      true,
        Categories:       []string{"web"},
        MaxDetectors:     25,
        Priority:         "severity",
    })
    
    // 并发执行检测器
    detectionResults, err := e.detectorManager.ExecuteDetectors(ctx, pluginTarget, selectedDetectors)
    
    // 处理结果
    for _, result := range detectionResults {
        if result.IsVulnerable {
            vuln := e.convertPluginResultToVulnerability(result, taskID)
            scanResult.Vulnerabilities = append(scanResult.Vulnerabilities, vuln)
        }
    }
}
```

### 2. **检测器选择策略**

#### **智能选择选项**
```go
type SelectionOptions struct {
    EnabledOnly      bool     // 只选择启用的检测器
    Categories       []string // 按分类过滤 ["web", "network", "host"]
    Severities       []string // 按严重程度过滤 ["critical", "high", "medium", "low"]
    MaxDetectors     int      // 最大检测器数量
    SkipDependencies bool     // 跳过依赖检查
    Priority         string   // 排序优先级 ["severity", "confidence", "speed"]
    TargetTypes      []string // 目标类型过滤 ["http", "https", "tcp", "udp"]
    Technologies     []string // 技术栈过滤 ["java", "php", "nodejs"]
}
```

#### **使用示例**
```go
// 高危漏洞快速扫描
options := &plugins.SelectionOptions{
    EnabledOnly:   true,
    Severities:    []string{"critical", "high"},
    MaxDetectors:  10,
    Priority:      "severity",
}

// 特定技术栈深度扫描
options := &plugins.SelectionOptions{
    EnabledOnly:   true,
    Categories:    []string{"web"},
    Technologies:  []string{"java", "spring"},
    MaxDetectors:  50,
    Priority:      "confidence",
}
```

### 3. **CVE检测器使用**

#### **CVE特定检测**
```go
// 初始化CVE检测器管理器
cveManager := plugins.NewCVEDetectorManager(config)

// CVE检测请求
request := &plugins.CVEDetectionRequest{
    Target:          pluginTarget,
    CVEFilters:      []string{"CVE-2021-44228", "CVE-2021-26855"},
    SeverityFilters: []string{"critical", "high"},
    EnablePoC:       true,
    MaxDetectors:    20,
}

// 执行CVE检测
response, err := cveManager.DetectCVEs(ctx, request)
```

---

## 🔧 **如何扩充插件化架构**

### 1. **添加新的通用检测器**

#### **步骤1：创建检测器文件**
```go
// scanner/internal/scanner/plugins/detectors/new_vulnerability_detector.go
package detectors

import (
    "context"
    "time"
    "scanner/internal/scanner/plugins"
)

// NewVulnerabilityDetector 新漏洞检测器
type NewVulnerabilityDetector struct {
    id          string
    name        string
    category    string
    severity    string
    description string
    enabled     bool
    config      *plugins.DetectorConfig
}

// NewNewVulnerabilityDetector 创建新漏洞检测器实例
func NewNewVulnerabilityDetector() *NewVulnerabilityDetector {
    return &NewVulnerabilityDetector{
        id:          "new-vulnerability-detector",
        name:        "新漏洞检测器",
        category:    "web",
        severity:    "medium",
        description: "检测新发现的漏洞类型",
        enabled:     true,
    }
}

// 实现VulnerabilityDetector接口
func (d *NewVulnerabilityDetector) GetID() string { return d.id }
func (d *NewVulnerabilityDetector) GetName() string { return d.name }
func (d *NewVulnerabilityDetector) GetCategory() string { return d.category }
func (d *NewVulnerabilityDetector) GetSeverity() string { return d.severity }
func (d *NewVulnerabilityDetector) GetDescription() string { return d.description }
func (d *NewVulnerabilityDetector) IsEnabled() bool { return d.enabled }
func (d *NewVulnerabilityDetector) SetEnabled(enabled bool) { d.enabled = enabled }

// 适用性检查
func (d *NewVulnerabilityDetector) IsApplicable(target *plugins.ScanTarget) bool {
    // 检查目标是否适用于此检测器
    return target.Type == "url" && (target.Protocol == "http" || target.Protocol == "https")
}

// 核心检测逻辑
func (d *NewVulnerabilityDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
    result := &plugins.DetectionResult{
        VulnerabilityID: d.generateVulnID(target),
        DetectorID:      d.id,
        IsVulnerable:    false,
        Confidence:      0.0,
        Severity:        d.severity,
        Title:           "新漏洞检测",
        DetectedAt:      time.Now(),
    }

    // 实现具体的检测逻辑
    if d.performDetection(ctx, target) {
        result.IsVulnerable = true
        result.Confidence = 0.8
        result.Description = "检测到新漏洞"
        result.Evidence = []plugins.Evidence{
            {
                Type:        "response",
                Description: "漏洞证据",
                Content:     "具体证据内容",
                Timestamp:   time.Now(),
            },
        }
        result.Remediation = "修复建议"
        result.References = []string{"https://example.com/advisory"}
    }

    return result, nil
}

// 具体检测实现
func (d *NewVulnerabilityDetector) performDetection(ctx context.Context, target *plugins.ScanTarget) bool {
    // 实现具体的检测逻辑
    // 例如：发送HTTP请求、分析响应、检查漏洞特征等
    return false // 示例返回
}

// 其他必需方法的实现...
```

#### **步骤2：注册检测器**
```go
// 在 web_engine.go 的 registerBuiltinDetectors 方法中添加
func (e *WebEngine) registerBuiltinDetectors() {
    // ... 现有检测器注册 ...
    
    // 注册新漏洞检测器
    newVulnDetector := detectors.NewNewVulnerabilityDetector()
    if err := e.detectorManager.RegisterDetector(newVulnDetector); err != nil {
        logger.Errorf("注册新漏洞检测器失败: %v", err)
    }
}
```

#### **步骤3：创建测试文件**
```go
// scanner/internal/scanner/plugins/detectors/new_vulnerability_detector_test.go
package detectors

import (
    "context"
    "testing"
    "scanner/internal/scanner/plugins"
    "github.com/stretchr/testify/assert"
)

func TestNewVulnerabilityDetector(t *testing.T) {
    detector := NewNewVulnerabilityDetector()
    
    // 测试基本属性
    assert.Equal(t, "new-vulnerability-detector", detector.GetID())
    assert.Equal(t, "新漏洞检测器", detector.GetName())
    assert.Equal(t, "web", detector.GetCategory())
    
    // 测试适用性检查
    target := &plugins.ScanTarget{
        Type:     "url",
        Protocol: "http",
        URL:      "http://example.com",
    }
    assert.True(t, detector.IsApplicable(target))
    
    // 测试检测功能
    ctx := context.Background()
    result, err := detector.Detect(ctx, target)
    assert.NoError(t, err)
    assert.NotNil(t, result)
}
```

### 2. **添加CVE特定检测器**

#### **使用CVE检测器基类**
```go
// scanner/internal/scanner/plugins/detectors/cve_2024_12345_detector.go
package detectors

import (
    "context"
    "scanner/internal/scanner/plugins"
)

// CVE202412345Detector 新CVE检测器
type CVE202412345Detector struct {
    *CVEDetectorBase
}

// NewCVE202412345Detector 创建新CVE检测器
func NewCVE202412345Detector() *CVE202412345Detector {
    base := NewCVEDetectorBase(
        "CVE-2024-12345",
        "示例CVE漏洞检测器",
        "检测CVE-2024-12345漏洞",
    )
    
    base.category = "remote_code_execution"
    base.severity = "critical"
    base.cwe = []string{"CWE-78"}
    
    return &CVE202412345Detector{
        CVEDetectorBase: base,
    }
}

// 实现CVE特定接口
func (d *CVE202412345Detector) GetAffectedProducts() []string {
    return []string{"Example Product", "Another Product"}
}

func (d *CVE202412345Detector) GetAffectedVersions() []string {
    return []string{"1.0.0 - 1.2.3", "2.0.0 - 2.1.0"}
}

// 实现具体的CVE检测逻辑
func (d *CVE202412345Detector) DetectCVE(ctx context.Context, target *plugins.ScanTarget) (*CVEDetectionResult, error) {
    // 实现CVE特定的检测逻辑
    // ...
}
```

### 3. **扩展检测器管理器**

#### **添加新的管理器功能**
```go
// 扩展DetectorManager
func (dm *DetectorManager) GetDetectorsByTechnology(technology string) []VulnerabilityDetector {
    dm.mutex.RLock()
    defer dm.mutex.RUnlock()
    
    var result []VulnerabilityDetector
    for _, detector := range dm.detectors {
        for _, tech := range detector.GetRequiredTechnologies() {
            if tech == technology {
                result = append(result, detector)
                break
            }
        }
    }
    return result
}

// 添加检测器性能统计
func (dm *DetectorManager) GetDetectorPerformanceStats() map[string]*DetectorPerformanceStats {
    // 实现性能统计逻辑
}
```

### 4. **添加新的扫描引擎支持**

#### **为其他引擎添加插件化支持**
```go
// scanner/internal/scanner/engines/network_engine.go
func (e *NetworkEngine) initializeDetectorManager() {
    config := &plugins.DetectorManagerConfig{
        MaxConcurrency:    10,
        DefaultTimeout:    30 * time.Second,
        EnableValidation:  true,
        EnableStatistics:  true,
    }
    
    e.detectorManager = plugins.NewDetectorManager(config, logger.GetLogger())
    e.registerNetworkDetectors()
}

func (e *NetworkEngine) registerNetworkDetectors() {
    // 注册网络相关检测器
    portScanDetector := detectors.NewPortScanDetector()
    e.detectorManager.RegisterDetector(portScanDetector)
    
    serviceDetector := detectors.NewServiceDetector()
    e.detectorManager.RegisterDetector(serviceDetector)
}
```

---

## 📈 **扩充策略和最佳实践**

### 1. **检测器开发最佳实践**

#### **设计原则**
- ✅ **单一职责**：每个检测器专注于一种漏洞类型
- ✅ **高内聚低耦合**：检测器之间相互独立
- ✅ **可配置性**：支持配置参数调整
- ✅ **错误处理**：优雅处理异常情况
- ✅ **性能优化**：避免阻塞和资源泄露

#### **代码规范**
```go
// 1. 命名规范
type SQLInjectionDetector struct {} // 检测器名称
func NewSQLInjectionDetector() *SQLInjectionDetector {} // 构造函数

// 2. 错误处理
func (d *Detector) Detect(ctx context.Context, target *ScanTarget) (*DetectionResult, error) {
    defer func() {
        if r := recover(); r != nil {
            logger.Errorf("检测器 %s 发生panic: %v", d.GetID(), r)
        }
    }()
    
    // 检测逻辑
}

// 3. 超时控制
ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
defer cancel()

// 4. 资源清理
defer response.Body.Close()
```

### 2. **性能优化策略**

#### **并发控制**
```go
// 使用信号量控制并发数
semaphore := make(chan struct{}, maxConcurrency)

for _, detector := range detectors {
    go func(d VulnerabilityDetector) {
        semaphore <- struct{}{} // 获取信号量
        defer func() { <-semaphore }() // 释放信号量
        
        result, err := d.Detect(ctx, target)
        // 处理结果
    }(detector)
}
```

#### **缓存机制**
```go
// 结果缓存
type DetectorCache struct {
    cache map[string]*DetectionResult
    mutex sync.RWMutex
    ttl   time.Duration
}

func (c *DetectorCache) Get(key string) (*DetectionResult, bool) {
    c.mutex.RLock()
    defer c.mutex.RUnlock()
    
    result, exists := c.cache[key]
    return result, exists
}
```

### 3. **扩展方向建议**

#### **短期扩展（1-3个月）**
1. **更多CVE检测器**
   - 添加2023-2024年高危CVE检测器
   - 实现PoC验证框架
   - 集成漏洞数据库

2. **检测器优化**
   - 性能基准测试
   - 误报率优化
   - 检测精度提升

#### **中期扩展（3-6个月）**
1. **多引擎支持**
   - Network引擎插件化
   - Host引擎插件化
   - API引擎插件化

2. **高级功能**
   - 检测器依赖管理
   - 动态插件加载
   - 插件市场机制

#### **长期扩展（6-12个月）**
1. **AI增强**
   - 机器学习检测器
   - 智能载荷生成
   - 自适应检测策略

2. **云原生支持**
   - 容器化检测器
   - 微服务架构
   - 分布式扫描

---

## 🎯 **总结**

### ✅ **当前能力**
- **40+专业检测器**：覆盖主要Web漏洞类型
- **智能管理系统**：多维度索引和选择
- **完整集成**：已融入Web扫描引擎
- **扩展框架**：CVE检测器基类和管理器

### 🚀 **扩充优势**
- **模块化设计**：新检测器独立开发和测试
- **热插拔支持**：运行时动态加载检测器
- **统一接口**：标准化的检测器接口
- **性能优化**：并发执行和智能选择

### 💡 **使用建议**
1. **新检测器开发**：遵循接口规范，实现完整测试
2. **性能调优**：合理设置并发数和超时时间
3. **错误处理**：实现优雅降级和错误恢复
4. **持续优化**：定期评估检测器性能和准确性

**插件化架构为漏洞扫描器提供了强大的扩展能力，支持快速添加新的检测能力，是构建商业级扫描器的核心基础！**
