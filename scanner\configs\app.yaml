app:
  name: "漏洞扫描器"
  version: "1.0.0"
  mode: "development"  # development, production
  debug: true

server:
  host: "0.0.0.0"
  port: 8082
  ipv6: true          # 启用IPv6支持
  dual_stack: true    # 启用IPv4/IPv6双栈
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

database:
  type: "sqlite"
  dsn: "data/scanner.db"
  max_open_conn: 10
  max_idle_conn: 5
  conn_max_lifetime: 36000s
  log_level: "info"
  auto_migrate: true

security:
  jwt_secret: "your-jwt-secret-key-change-in-production"
  jwt_expire: 24h
  password_salt: "your-password-salt"
  bcrypt_cost: 12
  rate_limit: 100

scan:
  max_concurrent: 10
  default_timeout: 7200s  # 增加到2小时，适合复杂Web应用扫描
  worker_pool_size: 20
  max_targets_per_task: 1000
  result_retention_days: 90

  # 分类型超时配置
  web_scan_timeout: 7200s      # Web扫描超时：2小时
  network_scan_timeout: 3600s  # 网络扫描超时：1小时
  host_scan_timeout: 2700s     # 主机扫描超时：45分钟
  api_scan_timeout: 2700s      # API扫描超时：45分钟
  compliance_scan_timeout: 1800s # 合规扫描超时：30分钟

  # 增强监控配置
  monitoring:
    check_interval: 30s              # 监控检查间隔
    progress_timeout: 1800s          # 进度更新超时（30分钟）
    progress_check_interval: 300s    # 进度检查间隔（5分钟）
    stale_progress_timeout: 1800s    # 进度停滞超时（30分钟，从15分钟增加）
    enable_adaptive_timeout: true    # 启用自适应超时
    min_timeout: 1800s              # 最小超时时间（30分钟，从10分钟增加）
    max_timeout: 14400s             # 最大超时时间（4小时）
    timeout_multiplier: 1.5         # 超时倍数

    # 分类型监控超时配置
    web_scan_progress_timeout: 1800s    # Web扫描进度超时（30分钟）
    network_scan_progress_timeout: 900s # 网络扫描进度超时（15分钟）
    host_scan_progress_timeout: 1200s   # 主机扫描进度超时（20分钟）
    api_scan_progress_timeout: 1200s    # API扫描进度超时（20分钟）

  # 重试配置
  retry:
    enable_retry: true              # 启用重试
    max_retries: 3                 # 最大重试次数
    initial_delay: 1s              # 初始延迟
    max_delay: 30s                 # 最大延迟
    backoff_factor: 2.0            # 退避因子
    enable_jitter: true            # 启用随机抖动

  # 网络请求配置
  network:
    request_timeout: 60s           # 单个请求超时（增加到60秒）
    max_concurrency: 3             # 最大并发数（降低）
    request_interval: 200ms        # 请求间隔（增加）
    enable_retry: true             # 启用网络重试
    max_network_retries: 5         # 最大网络重试次数

  # 目录扫描优化配置
  directory_scan:
    max_concurrency: 3             # 目录扫描最大并发数
    request_interval: 200ms        # 目录扫描请求间隔
    request_timeout: 60s           # 目录扫描请求超时（增加到60秒）
    enable_retry: true             # 启用重试
    max_retries: 3                # 最大重试次数
    follow_redirects: false       # 不跟随重定向

  # 命令注入检测优化配置
  command_injection:
    enable_cache: true            # 启用结果缓存
    cache_expiry: 1800s          # 缓存过期时间（30分钟）
    max_payloads: 15             # 最大payload数量（减少）
    request_timeout: 15s         # 请求超时
    skip_similar_params: true    # 跳过相似参数
    enable_smart_detection: true # 启用智能检测
    response_size_limit: 1048576 # 响应大小限制（1MB）

  # 网络扫描智能超时配置（保持兼容性）
  network_scan_adaptive_timeout: true  # 启用自适应超时
  network_scan_base_timeout: 1800s     # 基础超时：30分钟
  network_scan_per_target_timeout: 2s  # 每个目标额外超时：2秒

ai:
  enabled: true
  provider: "local"
  model: "scanner-ai"
  max_tokens: 2048
  timeout: 30s

logging:
  level: "info"
  format: "json"
  file: "logs/scanner.log"
  max_size: 100
  max_backups: 5
  max_age: 30
  compress: true

storage:
  data_dir: "data"
  logs_dir: "logs"
  reports_dir: "reports"
  temp_dir: "temp"
  max_file_size: 104857600
