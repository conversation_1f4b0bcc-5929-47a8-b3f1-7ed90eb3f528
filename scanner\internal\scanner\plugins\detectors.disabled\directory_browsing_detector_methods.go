package detectors

import (
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// detectRootDirectoryBrowsing 检测根目录浏览
func (d *DirectoryBrowsingDetector) detectRootDirectoryBrowsing(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测根目录浏览...")

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 测试根目录
	rootPaths := []string{
		"/",
		"/index/",
		"/home/",
		"/main/",
		"/default/",
	}

	for _, path := range rootPaths {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送目录请求
		resp, err := d.sendDirectoryRequest(ctx, target.URL, path)
		if err != nil {
			continue
		}

		// 检查目录浏览响应
		confidence := d.checkDirectoryBrowsingResponse(resp, path)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = path
			vulnerableRequest = d.buildDirectoryURL(target.URL, path)
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("根目录浏览检测 (置信度: %.2f)", confidence),
				Content:     d.extractDirectoryBrowsingEvidence(resp, path),
				Location:    d.buildDirectoryURL(target.URL, path),
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCommonDirectoryBrowsing 检测常见目录浏览
func (d *DirectoryBrowsingDetector) detectCommonDirectoryBrowsing(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测常见目录浏览...")

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 测试常见目录（限制数量避免过多请求）
	for i, path := range d.directoryPaths {
		if i >= 20 { // 限制目录数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送目录请求
		resp, err := d.sendDirectoryRequest(ctx, target.URL, path)
		if err != nil {
			continue
		}

		// 检查目录浏览响应
		confidence := d.checkDirectoryBrowsingResponse(resp, path)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = path
			vulnerableRequest = d.buildDirectoryURL(target.URL, path)
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("常见目录浏览检测 (置信度: %.2f)", confidence),
				Content:     d.extractDirectoryBrowsingEvidence(resp, path),
				Location:    d.buildDirectoryURL(target.URL, path),
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectSensitiveDirectoryBrowsing 检测敏感目录浏览
func (d *DirectoryBrowsingDetector) detectSensitiveDirectoryBrowsing(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测敏感目录浏览...")

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 测试敏感目录（限制数量避免过多请求）
	for i, path := range d.sensitiveDirectories {
		if i >= 15 { // 限制敏感目录数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送目录请求
		resp, err := d.sendDirectoryRequest(ctx, target.URL, path)
		if err != nil {
			continue
		}

		// 检查目录浏览响应
		confidence := d.checkDirectoryBrowsingResponse(resp, path)
		
		// 敏感目录的置信度加权
		if confidence > 0.3 {
			confidence += 0.2 // 敏感目录额外加权
		}

		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = path
			vulnerableRequest = d.buildDirectoryURL(target.URL, path)
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("敏感目录浏览检测 (置信度: %.2f)", confidence),
				Content:     d.extractDirectoryBrowsingEvidence(resp, path),
				Location:    d.buildDirectoryURL(target.URL, path),
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendDirectoryRequest 发送目录请求
func (d *DirectoryBrowsingDetector) sendDirectoryRequest(ctx context.Context, baseURL, path string) (string, error) {
	// 构建完整URL
	fullURL := d.buildDirectoryURL(baseURL, path)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	// 发送请求
	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查状态码
	if resp.StatusCode != 200 {
		return "", fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	return string(body), nil
}

// buildDirectoryURL 构建目录URL
func (d *DirectoryBrowsingDetector) buildDirectoryURL(baseURL, path string) string {
	// 解析基础URL
	u, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + path
	}

	// 确保路径以/开头
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}

	// 构建完整URL
	u.Path = path
	return u.String()
}

// checkDirectoryBrowsingResponse 检查目录浏览响应
func (d *DirectoryBrowsingDetector) checkDirectoryBrowsingResponse(response, path string) float64 {
	if response == "" {
		return 0.0
	}

	confidence := 0.0
	responseLower := strings.ToLower(response)

	// 1. 检查目录列表模式
	for _, pattern := range d.listingPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			logger.Debugf("匹配目录列表模式: %s", pattern.String())
		}
	}

	// 2. 检查目录指示器
	for _, indicator := range d.directoryIndicators {
		if strings.Contains(responseLower, strings.ToLower(indicator)) {
			confidence += 0.2
			logger.Debugf("发现目录指示器: %s", indicator)
		}
	}

	// 3. 检查配置错误模式
	for _, pattern := range d.configErrorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			logger.Debugf("匹配配置错误模式: %s", pattern.String())
		}
	}

	// 4. 检查敏感文件模式
	for _, pattern := range d.sensitiveFilePatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			logger.Debugf("发现敏感文件: %s", pattern.String())
		}
	}

	// 5. 检查服务器特征
	for _, signature := range d.serverSignatures {
		if strings.Contains(responseLower, strings.ToLower(signature)) {
			confidence += 0.1
			logger.Debugf("发现服务器特征: %s", signature)
		}
	}

	// 6. 检查HTML结构特征
	if d.checkHTMLStructure(response) {
		confidence += 0.2
		logger.Debugf("发现目录列表HTML结构")
	}

	// 7. 检查文件链接特征
	if d.checkFileLinks(response) {
		confidence += 0.2
		logger.Debugf("发现文件链接特征")
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkHTMLStructure 检查HTML结构特征
func (d *DirectoryBrowsingDetector) checkHTMLStructure(response string) bool {
	responseLower := strings.ToLower(response)

	// 检查典型的目录列表HTML结构
	htmlIndicators := []string{
		"<pre>",
		"<table",
		"<ul>",
		"<ol>",
		"<dir>",
		"<menu>",
	}

	linkIndicators := []string{
		"<a href=",
		"href=\"../\"",
		"href=\"./\"",
		"[dir]",
		"[txt]",
		"[img]",
	}

	htmlCount := 0
	linkCount := 0

	for _, indicator := range htmlIndicators {
		if strings.Contains(responseLower, indicator) {
			htmlCount++
		}
	}

	for _, indicator := range linkIndicators {
		if strings.Contains(responseLower, indicator) {
			linkCount++
		}
	}

	// 如果同时包含HTML结构和链接特征，可能是目录列表
	return htmlCount > 0 && linkCount > 1
}

// checkFileLinks 检查文件链接特征
func (d *DirectoryBrowsingDetector) checkFileLinks(response string) bool {
	responseLower := strings.ToLower(response)

	// 检查文件扩展名链接
	fileExtensions := []string{
		".txt", ".log", ".conf", ".config", ".ini", ".xml",
		".json", ".yml", ".yaml", ".properties", ".env",
		".php", ".asp", ".jsp", ".html", ".htm", ".js",
		".css", ".sql", ".bak", ".backup", ".old", ".tmp",
		".zip", ".tar", ".gz", ".rar", ".7z", ".pdf",
		".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
	}

	linkCount := 0
	for _, ext := range fileExtensions {
		if strings.Contains(responseLower, ext) {
			linkCount++
		}
	}

	// 如果包含多种文件扩展名，可能是目录列表
	return linkCount >= 3
}

// extractDirectoryBrowsingEvidence 提取目录浏览证据
func (d *DirectoryBrowsingDetector) extractDirectoryBrowsingEvidence(response, path string) string {
	evidence := fmt.Sprintf("目录路径: %s\n", path)

	// 提取关键证据
	lines := strings.Split(response, "\n")
	var evidenceLines []string

	for _, line := range lines {
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if len(lineLower) > 0 {
			// 检查是否包含目录浏览特征
			for _, indicator := range d.directoryIndicators {
				if strings.Contains(lineLower, strings.ToLower(indicator)) {
					evidenceLines = append(evidenceLines, strings.TrimSpace(line))
					break
				}
			}
			if len(evidenceLines) >= 5 {
				break
			}
		}
	}

	if len(evidenceLines) > 0 {
		evidence += "关键证据:\n" + strings.Join(evidenceLines, "\n")
	} else {
		// 如果没有找到特定证据，提取前几行
		if len(lines) > 5 {
			evidence += "响应内容:\n" + strings.Join(lines[:5], "\n")
		} else {
			evidence += "响应内容:\n" + response
		}
	}

	return evidence
}
