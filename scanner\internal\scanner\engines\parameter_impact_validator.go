package engines

import (
	"context"
	"crypto/md5"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/pkg/logger"
)

// ParameterImpactValidator 参数影响验证器
// 专门用于检测参数是否真正影响应用行为，避免参数忽略导致的误报
type ParameterImpactValidator struct {
	client         *http.Client
	config         *ParameterValidationConfig
	baselineCache  map[string]*ParameterBaseline
	requestHistory map[string][]*RequestRecord
}

// ParameterValidationConfig 参数验证配置
type ParameterValidationConfig struct {
	MaxValidationRequests int           `json:"max_validation_requests"`
	RequestTimeout        time.Duration `json:"request_timeout"`
	RequestInterval       time.Duration `json:"request_interval"`
	SimilarityThreshold   float64       `json:"similarity_threshold"`
	ImpactThreshold       float64       `json:"impact_threshold"`
	EnableDeepValidation  bool          `json:"enable_deep_validation"`
	EnableTimingAnalysis  bool          `json:"enable_timing_analysis"`
}

// ParameterBaseline 参数基线
type ParameterBaseline struct {
	Target             string                    `json:"target"`
	NormalResponse     *ResponseSignature        `json:"normal_response"`
	ParameterBehaviors map[string]*ParamBehavior `json:"parameter_behaviors"`
	DefaultResponses   []*ResponseSignature      `json:"default_responses"`
	ErrorResponses     []*ResponseSignature      `json:"error_responses"`
	CreatedAt          time.Time                 `json:"created_at"`
	UpdatedAt          time.Time                 `json:"updated_at"`
}

// ParamBehavior 参数行为
type ParamBehavior struct {
	Name                string    `json:"name"`
	IsProcessed         bool      `json:"is_processed"`
	HasImpact           bool      `json:"has_impact"`
	DefaultValue        string    `json:"default_value"`
	IgnoresInvalidInput bool      `json:"ignores_invalid_input"`
	ResponseVariation   float64   `json:"response_variation"`
	LastTested          time.Time `json:"last_tested"`
}

// ResponseSignature 响应签名
type ResponseSignature struct {
	StatusCode    int               `json:"status_code"`
	ContentLength int               `json:"content_length"`
	ContentHash   string            `json:"content_hash"`
	StructureHash string            `json:"structure_hash"`
	Headers       map[string]string `json:"headers"`
	ResponseTime  time.Duration     `json:"response_time"`
	KeyElements   []string          `json:"key_elements"`
	Timestamp     time.Time         `json:"timestamp"`
}

// RequestRecord 请求记录
type RequestRecord struct {
	URL           string             `json:"url"`
	Parameters    map[string]string  `json:"parameters"`
	Response      *ResponseSignature `json:"response"`
	RequestTime   time.Time          `json:"request_time"`
	ValidationTag string             `json:"validation_tag"`
}

// ParameterImpactResult 参数影响结果
type ParameterImpactResult struct {
	Target             string                      `json:"target"`
	TestedParameter    string                      `json:"tested_parameter"`
	HasRealImpact      bool                        `json:"has_real_impact"`
	ImpactScore        float64                     `json:"impact_score"`
	ParameterProcessed bool                        `json:"parameter_processed"`
	ResponseVariation  float64                     `json:"response_variation"`
	IsParameterIgnored bool                        `json:"is_parameter_ignored"`
	IsDefaultBehavior  bool                        `json:"is_default_behavior"`
	ValidationDetails  *ParameterValidationDetails `json:"validation_details"`
	Confidence         float64                     `json:"confidence"`
	RecommendedAction  string                      `json:"recommended_action"`
}

// ParameterValidationDetails 参数验证详情
type ParameterValidationDetails struct {
	BaselineComparison  *BaselineComparison  `json:"baseline_comparison"`
	ResponseConsistency *ResponseConsistency `json:"response_consistency"`
	ParameterInfluence  *ParameterInfluence  `json:"parameter_influence"`
	TimingAnalysis      *TimingAnalysis      `json:"timing_analysis"`
	ContentAnalysis     *ContentAnalysis     `json:"content_analysis"`
}

// BaselineComparison 基线比较
type BaselineComparison struct {
	MatchesNormalResponse  bool    `json:"matches_normal_response"`
	MatchesDefaultResponse bool    `json:"matches_default_response"`
	MatchesErrorResponse   bool    `json:"matches_error_response"`
	SimilarityScore        float64 `json:"similarity_score"`
	DeviationType          string  `json:"deviation_type"`
}

// ResponseConsistency 响应一致性
type ResponseConsistency struct {
	ConsistencyScore      float64 `json:"consistency_score"`
	VariationDetected     bool    `json:"variation_detected"`
	VariationPattern      string  `json:"variation_pattern"`
	UnexpectedConsistency bool    `json:"unexpected_consistency"`
}

// ParameterInfluence 参数影响
type ParameterInfluence struct {
	InfluenceScore         float64  `json:"influence_score"`
	InfluenceType          string   `json:"influence_type"`
	InfluenceEvidence      []string `json:"influence_evidence"`
	ParameterRelationships []string `json:"parameter_relationships"`
}

// TimingAnalysis 时间分析
type TimingAnalysis struct {
	BaselineTime     time.Duration `json:"baseline_time"`
	TestTime         time.Duration `json:"test_time"`
	TimingVariation  float64       `json:"timing_variation"`
	HasTimingAnomaly bool          `json:"has_timing_anomaly"`
	TimingPattern    string        `json:"timing_pattern"`
}

// ContentAnalysis 内容分析
type ContentAnalysis struct {
	ContentRelevance   float64  `json:"content_relevance"`
	DataFreshness      float64  `json:"data_freshness"`
	StructuralChanges  []string `json:"structural_changes"`
	SemanticChanges    []string `json:"semantic_changes"`
	SuspiciousPatterns []string `json:"suspicious_patterns"`
}

// NewParameterImpactValidator 创建参数影响验证器
func NewParameterImpactValidator(config *ParameterValidationConfig) *ParameterImpactValidator {
	if config == nil {
		config = &ParameterValidationConfig{
			MaxValidationRequests: 10,
			RequestTimeout:        30 * time.Second,
			RequestInterval:       200 * time.Millisecond,
			SimilarityThreshold:   0.95,
			ImpactThreshold:       0.3,
			EnableDeepValidation:  true,
			EnableTimingAnalysis:  true,
		}
	}

	return &ParameterImpactValidator{
		client: &http.Client{
			Timeout: config.RequestTimeout,
		},
		config:         config,
		baselineCache:  make(map[string]*ParameterBaseline),
		requestHistory: make(map[string][]*RequestRecord),
	}
}

// ValidateParameterImpact 验证参数影响
func (piv *ParameterImpactValidator) ValidateParameterImpact(
	ctx context.Context,
	targetURL string,
	paramName string,
	normalValue string,
	testValue string,
) (*ParameterImpactResult, error) {

	logger.Infof("开始验证参数影响: %s, 参数: %s", targetURL, paramName)

	result := &ParameterImpactResult{
		Target:            targetURL,
		TestedParameter:   paramName,
		ValidationDetails: &ParameterValidationDetails{},
	}

	// 1. 获取或建立基线
	baseline, err := piv.getOrCreateBaseline(ctx, targetURL)
	if err != nil {
		return nil, fmt.Errorf("获取基线失败: %v", err)
	}

	// 2. 执行多重验证测试
	testResults, err := piv.executeValidationTests(ctx, targetURL, paramName, normalValue, testValue)
	if err != nil {
		return nil, fmt.Errorf("执行验证测试失败: %v", err)
	}

	// 3. 基线比较分析
	result.ValidationDetails.BaselineComparison = piv.compareWithBaseline(testResults, baseline)

	// 4. 响应一致性分析
	result.ValidationDetails.ResponseConsistency = piv.analyzeResponseConsistency(testResults)

	// 5. 参数影响分析
	result.ValidationDetails.ParameterInfluence = piv.analyzeParameterInfluence(testResults, paramName)

	// 6. 时间分析（如果启用）
	if piv.config.EnableTimingAnalysis {
		result.ValidationDetails.TimingAnalysis = piv.analyzeTimingPatterns(testResults)
	}

	// 7. 内容分析
	result.ValidationDetails.ContentAnalysis = piv.analyzeContentChanges(testResults)

	// 8. 综合评估
	result.HasRealImpact = piv.evaluateRealImpact(result.ValidationDetails)
	result.ImpactScore = piv.calculateImpactScore(result.ValidationDetails)
	result.ParameterProcessed = piv.isParameterProcessed(result.ValidationDetails)
	result.IsParameterIgnored = piv.isParameterIgnored(result.ValidationDetails)
	result.IsDefaultBehavior = piv.isDefaultBehavior(result.ValidationDetails, baseline)
	result.Confidence = piv.calculateConfidence(result.ValidationDetails)
	result.RecommendedAction = piv.getRecommendedAction(result)

	logger.Infof("参数影响验证完成: 有影响=%v, 影响分数=%.2f, 置信度=%.2f",
		result.HasRealImpact, result.ImpactScore, result.Confidence)

	return result, nil
}

// getOrCreateBaseline 获取或创建基线
func (piv *ParameterImpactValidator) getOrCreateBaseline(ctx context.Context, targetURL string) (*ParameterBaseline, error) {
	// 检查缓存
	if baseline, exists := piv.baselineCache[targetURL]; exists {
		if time.Since(baseline.UpdatedAt) < 1*time.Hour {
			return baseline, nil
		}
	}

	logger.Debugf("创建新的参数基线: %s", targetURL)

	baseline := &ParameterBaseline{
		Target:             targetURL,
		ParameterBehaviors: make(map[string]*ParamBehavior),
		DefaultResponses:   make([]*ResponseSignature, 0),
		ErrorResponses:     make([]*ResponseSignature, 0),
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// 建立正常响应基线
	normalResponse, err := piv.captureNormalResponse(ctx, targetURL)
	if err != nil {
		return nil, fmt.Errorf("捕获正常响应失败: %v", err)
	}
	baseline.NormalResponse = normalResponse

	// 测试常见的默认行为
	err = piv.identifyDefaultBehaviors(ctx, targetURL, baseline)
	if err != nil {
		logger.Warnf("识别默认行为失败: %v", err)
	}

	// 缓存基线
	piv.baselineCache[targetURL] = baseline

	return baseline, nil
}

// captureNormalResponse 捕获正常响应
func (piv *ParameterImpactValidator) captureNormalResponse(ctx context.Context, targetURL string) (*ResponseSignature, error) {
	start := time.Now()
	resp, err := piv.client.Get(targetURL)
	responseTime := time.Since(start)

	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return piv.createResponseSignature(resp, body, responseTime), nil
}

// createResponseSignature 创建响应签名
func (piv *ParameterImpactValidator) createResponseSignature(resp *http.Response, body []byte, responseTime time.Duration) *ResponseSignature {
	// 计算内容哈希
	contentHash := fmt.Sprintf("%x", md5.Sum(body))

	// 计算结构哈希（基于HTML结构）
	structureHash := piv.calculateStructureHash(string(body))

	// 提取关键元素
	keyElements := piv.extractKeyElements(string(body))

	// 提取重要头部
	headers := make(map[string]string)
	importantHeaders := []string{"Content-Type", "Server", "Set-Cookie", "Location"}
	for _, header := range importantHeaders {
		if value := resp.Header.Get(header); value != "" {
			headers[header] = value
		}
	}

	return &ResponseSignature{
		StatusCode:    resp.StatusCode,
		ContentLength: len(body),
		ContentHash:   contentHash,
		StructureHash: structureHash,
		Headers:       headers,
		ResponseTime:  responseTime,
		KeyElements:   keyElements,
		Timestamp:     time.Now(),
	}
}

// calculateStructureHash 计算结构哈希
func (piv *ParameterImpactValidator) calculateStructureHash(content string) string {
	// 简化的结构哈希：移除内容，保留标签结构
	structure := strings.ReplaceAll(content, ">", ">\n")
	lines := strings.Split(structure, "\n")

	var structureLines []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "<") && strings.HasSuffix(line, ">") {
			// 移除标签内的属性值，保留结构
			if idx := strings.Index(line, " "); idx > 0 {
				line = line[:idx] + ">"
			}
			structureLines = append(structureLines, line)
		}
	}

	structureContent := strings.Join(structureLines, "")
	return fmt.Sprintf("%x", md5.Sum([]byte(structureContent)))
}

// extractKeyElements 提取关键元素
func (piv *ParameterImpactValidator) extractKeyElements(content string) []string {
	elements := make([]string, 0)

	// 提取标题
	if strings.Contains(content, "<title>") {
		start := strings.Index(content, "<title>") + 7
		end := strings.Index(content[start:], "</title>")
		if end > 0 {
			title := content[start : start+end]
			elements = append(elements, "title:"+title)
		}
	}

	// 提取表单数量
	formCount := strings.Count(content, "<form")
	if formCount > 0 {
		elements = append(elements, fmt.Sprintf("forms:%d", formCount))
	}

	// 提取链接数量
	linkCount := strings.Count(content, "<a ")
	if linkCount > 0 {
		elements = append(elements, fmt.Sprintf("links:%d", linkCount))
	}

	return elements
}

// ValidationTestResult 验证测试结果
type ValidationTestResult struct {
	TestName        string             `json:"test_name"`
	Parameters      map[string]string  `json:"parameters"`
	Response        *ResponseSignature `json:"response"`
	TestType        string             `json:"test_type"`
	ExpectedOutcome string             `json:"expected_outcome"`
	ActualOutcome   string             `json:"actual_outcome"`
}

// executeValidationTests 执行验证测试
func (piv *ParameterImpactValidator) executeValidationTests(
	ctx context.Context,
	targetURL string,
	paramName string,
	normalValue string,
	testValue string,
) ([]*ValidationTestResult, error) {

	var results []*ValidationTestResult

	// 1. 基线测试（无参数）
	baselineResult, err := piv.executeTest(ctx, targetURL, map[string]string{}, "baseline", "normal_response")
	if err == nil {
		results = append(results, baselineResult)
	}

	// 2. 正常参数测试
	normalParams := map[string]string{paramName: normalValue}
	normalResult, err := piv.executeTest(ctx, targetURL, normalParams, "normal_param", "normal_response")
	if err == nil {
		results = append(results, normalResult)
	}

	// 3. 测试参数测试
	testParams := map[string]string{paramName: testValue}
	testResult, err := piv.executeTest(ctx, targetURL, testParams, "test_param", "different_response")
	if err == nil {
		results = append(results, testResult)
	}

	// 4. 无关参数测试
	irrelevantParams := map[string]string{"irrelevant_param_xyz": testValue}
	irrelevantResult, err := piv.executeTest(ctx, targetURL, irrelevantParams, "irrelevant_param", "normal_response")
	if err == nil {
		results = append(results, irrelevantResult)
	}

	// 5. 多参数测试
	multiParams := map[string]string{
		paramName:               normalValue,
		"additional_param_test": testValue,
	}
	multiResult, err := piv.executeTest(ctx, targetURL, multiParams, "multi_param", "normal_response")
	if err == nil {
		results = append(results, multiResult)
	}

	// 6. 空值测试
	emptyParams := map[string]string{paramName: ""}
	emptyResult, err := piv.executeTest(ctx, targetURL, emptyParams, "empty_param", "normal_or_error")
	if err == nil {
		results = append(results, emptyResult)
	}

	// 7. 特殊字符测试
	specialParams := map[string]string{paramName: "!@#$%^&*()"}
	specialResult, err := piv.executeTest(ctx, targetURL, specialParams, "special_chars", "normal_or_filtered")
	if err == nil {
		results = append(results, specialResult)
	}

	return results, nil
}

// executeTest 执行单个测试
func (piv *ParameterImpactValidator) executeTest(
	ctx context.Context,
	targetURL string,
	params map[string]string,
	testType string,
	expectedOutcome string,
) (*ValidationTestResult, error) {

	// 构建URL
	u, err := url.Parse(targetURL)
	if err != nil {
		return nil, err
	}

	q := u.Query()
	for key, value := range params {
		q.Set(key, value)
	}
	u.RawQuery = q.Encode()

	// 发送请求
	start := time.Now()
	resp, err := piv.client.Get(u.String())
	responseTime := time.Since(start)

	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 创建响应签名
	responseSignature := piv.createResponseSignature(resp, body, responseTime)

	// 记录请求历史
	record := &RequestRecord{
		URL:           u.String(),
		Parameters:    params,
		Response:      responseSignature,
		RequestTime:   time.Now(),
		ValidationTag: testType,
	}
	piv.requestHistory[targetURL] = append(piv.requestHistory[targetURL], record)

	result := &ValidationTestResult{
		TestName:        fmt.Sprintf("%s_test", testType),
		Parameters:      params,
		Response:        responseSignature,
		TestType:        testType,
		ExpectedOutcome: expectedOutcome,
		ActualOutcome:   piv.determineActualOutcome(responseSignature),
	}

	// 添加请求间隔
	time.Sleep(piv.config.RequestInterval)

	return result, nil
}

// determineActualOutcome 确定实际结果
func (piv *ParameterImpactValidator) determineActualOutcome(response *ResponseSignature) string {
	if response.StatusCode >= 400 {
		return "error_response"
	}
	if response.StatusCode >= 300 {
		return "redirect_response"
	}
	if response.ContentLength == 0 {
		return "empty_response"
	}
	return "normal_response"
}

// compareWithBaseline 与基线比较
func (piv *ParameterImpactValidator) compareWithBaseline(
	testResults []*ValidationTestResult,
	baseline *ParameterBaseline,
) *BaselineComparison {

	if len(testResults) == 0 || baseline.NormalResponse == nil {
		return &BaselineComparison{
			SimilarityScore: 0.0,
			DeviationType:   "insufficient_data",
		}
	}

	// 找到测试参数的结果
	var testResponse *ResponseSignature
	for _, result := range testResults {
		if result.TestType == "test_param" {
			testResponse = result.Response
			break
		}
	}

	if testResponse == nil {
		return &BaselineComparison{
			SimilarityScore: 0.0,
			DeviationType:   "no_test_response",
		}
	}

	comparison := &BaselineComparison{}

	// 与正常响应比较
	normalSimilarity := piv.calculateResponseSimilarity(testResponse, baseline.NormalResponse)
	comparison.MatchesNormalResponse = normalSimilarity > piv.config.SimilarityThreshold
	comparison.SimilarityScore = normalSimilarity

	// 与默认响应比较
	for _, defaultResp := range baseline.DefaultResponses {
		defaultSimilarity := piv.calculateResponseSimilarity(testResponse, defaultResp)
		if defaultSimilarity > piv.config.SimilarityThreshold {
			comparison.MatchesDefaultResponse = true
			break
		}
	}

	// 与错误响应比较
	for _, errorResp := range baseline.ErrorResponses {
		errorSimilarity := piv.calculateResponseSimilarity(testResponse, errorResp)
		if errorSimilarity > piv.config.SimilarityThreshold {
			comparison.MatchesErrorResponse = true
			break
		}
	}

	// 确定偏差类型
	if comparison.MatchesNormalResponse {
		comparison.DeviationType = "no_deviation"
	} else if comparison.MatchesDefaultResponse {
		comparison.DeviationType = "default_behavior"
	} else if comparison.MatchesErrorResponse {
		comparison.DeviationType = "error_behavior"
	} else {
		comparison.DeviationType = "significant_deviation"
	}

	return comparison
}

// calculateResponseSimilarity 计算响应相似度
func (piv *ParameterImpactValidator) calculateResponseSimilarity(resp1, resp2 *ResponseSignature) float64 {
	score := 0.0

	// 状态码相似度 (权重: 0.2)
	if resp1.StatusCode == resp2.StatusCode {
		score += 0.2
	}

	// 内容哈希相似度 (权重: 0.4)
	if resp1.ContentHash == resp2.ContentHash {
		score += 0.4
	}

	// 结构哈希相似度 (权重: 0.2)
	if resp1.StructureHash == resp2.StructureHash {
		score += 0.2
	}

	// 内容长度相似度 (权重: 0.1)
	lengthDiff := math.Abs(float64(resp1.ContentLength - resp2.ContentLength))
	maxLength := math.Max(float64(resp1.ContentLength), float64(resp2.ContentLength))
	if maxLength > 0 {
		lengthSimilarity := 1.0 - (lengthDiff / maxLength)
		score += 0.1 * lengthSimilarity
	}

	// 关键元素相似度 (权重: 0.1)
	elementSimilarity := piv.calculateElementSimilarity(resp1.KeyElements, resp2.KeyElements)
	score += 0.1 * elementSimilarity

	return score
}

// calculateElementSimilarity 计算元素相似度
func (piv *ParameterImpactValidator) calculateElementSimilarity(elements1, elements2 []string) float64 {
	if len(elements1) == 0 && len(elements2) == 0 {
		return 1.0
	}

	if len(elements1) == 0 || len(elements2) == 0 {
		return 0.0
	}

	matches := 0
	for _, elem1 := range elements1 {
		for _, elem2 := range elements2 {
			if elem1 == elem2 {
				matches++
				break
			}
		}
	}

	maxElements := math.Max(float64(len(elements1)), float64(len(elements2)))
	return float64(matches) / maxElements
}

// analyzeResponseConsistency 分析响应一致性
func (piv *ParameterImpactValidator) analyzeResponseConsistency(testResults []*ValidationTestResult) *ResponseConsistency {
	if len(testResults) < 2 {
		return &ResponseConsistency{
			ConsistencyScore:      0.0,
			VariationDetected:     false,
			VariationPattern:      "insufficient_data",
			UnexpectedConsistency: false,
		}
	}

	// 计算所有响应之间的相似度
	var similarities []float64
	for i := 0; i < len(testResults); i++ {
		for j := i + 1; j < len(testResults); j++ {
			similarity := piv.calculateResponseSimilarity(testResults[i].Response, testResults[j].Response)
			similarities = append(similarities, similarity)
		}
	}

	// 计算平均相似度
	var totalSimilarity float64
	for _, sim := range similarities {
		totalSimilarity += sim
	}
	avgSimilarity := totalSimilarity / float64(len(similarities))

	// 检测变化模式
	variationPattern := "consistent"
	variationDetected := false

	// 检查是否有显著变化
	for _, sim := range similarities {
		if sim < 0.8 {
			variationDetected = true
			variationPattern = "significant_variation"
			break
		}
	}

	// 检查异常一致性（所有响应都完全相同，可能是参数被忽略）
	unexpectedConsistency := false
	if avgSimilarity > 0.98 && len(testResults) > 3 {
		// 检查是否包含了应该产生不同响应的测试
		hasVariationTest := false
		for _, result := range testResults {
			if result.TestType == "test_param" || result.TestType == "irrelevant_param" {
				hasVariationTest = true
				break
			}
		}
		if hasVariationTest {
			unexpectedConsistency = true
			variationPattern = "unexpected_consistency"
		}
	}

	return &ResponseConsistency{
		ConsistencyScore:      avgSimilarity,
		VariationDetected:     variationDetected,
		VariationPattern:      variationPattern,
		UnexpectedConsistency: unexpectedConsistency,
	}
}

// analyzeParameterInfluence 分析参数影响
func (piv *ParameterImpactValidator) analyzeParameterInfluence(testResults []*ValidationTestResult, paramName string) *ParameterInfluence {
	influence := &ParameterInfluence{
		InfluenceEvidence:      make([]string, 0),
		ParameterRelationships: make([]string, 0),
	}

	var baselineResponse, normalParamResponse, testParamResponse, irrelevantParamResponse *ResponseSignature

	// 提取不同测试的响应
	for _, result := range testResults {
		switch result.TestType {
		case "baseline":
			baselineResponse = result.Response
		case "normal_param":
			normalParamResponse = result.Response
		case "test_param":
			testParamResponse = result.Response
		case "irrelevant_param":
			irrelevantParamResponse = result.Response
		}
	}

	// 计算参数影响分数
	influenceScore := 0.0

	// 1. 检查正常参数是否有影响
	if baselineResponse != nil && normalParamResponse != nil {
		normalInfluence := 1.0 - piv.calculateResponseSimilarity(baselineResponse, normalParamResponse)
		if normalInfluence > 0.1 {
			influence.InfluenceEvidence = append(influence.InfluenceEvidence, "正常参数产生响应变化")
			influenceScore += normalInfluence * 0.3
		}
	}

	// 2. 检查测试参数是否有影响
	if normalParamResponse != nil && testParamResponse != nil {
		testInfluence := 1.0 - piv.calculateResponseSimilarity(normalParamResponse, testParamResponse)
		if testInfluence > 0.1 {
			influence.InfluenceEvidence = append(influence.InfluenceEvidence, "测试参数产生响应变化")
			influenceScore += testInfluence * 0.5
		}
	}

	// 3. 检查无关参数的影响（应该没有影响）
	if baselineResponse != nil && irrelevantParamResponse != nil {
		irrelevantInfluence := 1.0 - piv.calculateResponseSimilarity(baselineResponse, irrelevantParamResponse)
		if irrelevantInfluence < 0.05 {
			influence.InfluenceEvidence = append(influence.InfluenceEvidence, "无关参数未产生影响")
			influenceScore += 0.2
		} else {
			influence.InfluenceEvidence = append(influence.InfluenceEvidence, "无关参数意外产生影响")
			influenceScore -= 0.3 // 降低置信度
		}
	}

	// 确定影响类型
	if influenceScore > 0.5 {
		influence.InfluenceType = "significant_influence"
	} else if influenceScore > 0.2 {
		influence.InfluenceType = "moderate_influence"
	} else if influenceScore > 0.05 {
		influence.InfluenceType = "minimal_influence"
	} else {
		influence.InfluenceType = "no_influence"
	}

	influence.InfluenceScore = influenceScore

	return influence
}

// analyzeTimingPatterns 分析时间模式
func (piv *ParameterImpactValidator) analyzeTimingPatterns(testResults []*ValidationTestResult) *TimingAnalysis {
	if len(testResults) == 0 {
		return &TimingAnalysis{
			HasTimingAnomaly: false,
			TimingPattern:    "no_data",
		}
	}

	var baselineTime, testTime time.Duration
	var allTimes []time.Duration

	// 收集时间数据
	for _, result := range testResults {
		allTimes = append(allTimes, result.Response.ResponseTime)

		switch result.TestType {
		case "baseline":
			baselineTime = result.Response.ResponseTime
		case "test_param":
			testTime = result.Response.ResponseTime
		}
	}

	// 计算平均时间
	var totalTime time.Duration
	for _, t := range allTimes {
		totalTime += t
	}
	_ = totalTime / time.Duration(len(allTimes)) // avgTime 暂时未使用

	// 计算时间变化
	timingVariation := 0.0
	if baselineTime > 0 && testTime > 0 {
		timingVariation = math.Abs(float64(testTime-baselineTime)) / float64(baselineTime)
	}

	// 检测时间异常
	hasTimingAnomaly := false
	timingPattern := "normal"

	if timingVariation > 0.5 {
		hasTimingAnomaly = true
		if testTime > baselineTime {
			timingPattern = "significant_delay"
		} else {
			timingPattern = "significant_speedup"
		}
	} else if timingVariation > 0.2 {
		timingPattern = "moderate_variation"
	}

	return &TimingAnalysis{
		BaselineTime:     baselineTime,
		TestTime:         testTime,
		TimingVariation:  timingVariation,
		HasTimingAnomaly: hasTimingAnomaly,
		TimingPattern:    timingPattern,
	}
}

// analyzeContentChanges 分析内容变化
func (piv *ParameterImpactValidator) analyzeContentChanges(testResults []*ValidationTestResult) *ContentAnalysis {
	analysis := &ContentAnalysis{
		StructuralChanges:  make([]string, 0),
		SemanticChanges:    make([]string, 0),
		SuspiciousPatterns: make([]string, 0),
	}

	if len(testResults) < 2 {
		return analysis
	}

	var baselineResponse, testResponse *ResponseSignature

	// 找到基线和测试响应
	for _, result := range testResults {
		switch result.TestType {
		case "baseline":
			baselineResponse = result.Response
		case "test_param":
			testResponse = result.Response
		}
	}

	if baselineResponse == nil || testResponse == nil {
		return analysis
	}

	// 分析结构变化
	if baselineResponse.StructureHash != testResponse.StructureHash {
		analysis.StructuralChanges = append(analysis.StructuralChanges, "页面结构发生变化")
	}

	// 分析内容相关性
	contentRelevance := 0.5 // 默认中等相关性
	if baselineResponse.ContentHash == testResponse.ContentHash {
		contentRelevance = 1.0 // 完全相同
		analysis.SuspiciousPatterns = append(analysis.SuspiciousPatterns, "响应内容完全相同")
	} else {
		// 基于内容长度差异计算相关性
		lengthDiff := math.Abs(float64(baselineResponse.ContentLength - testResponse.ContentLength))
		maxLength := math.Max(float64(baselineResponse.ContentLength), float64(testResponse.ContentLength))
		if maxLength > 0 {
			contentRelevance = 1.0 - (lengthDiff / maxLength)
		}
	}

	// 分析数据新鲜度
	dataFreshness := 1.0 // 假设数据是新鲜的
	timeDiff := testResponse.Timestamp.Sub(baselineResponse.Timestamp)
	if timeDiff < 1*time.Second {
		analysis.SuspiciousPatterns = append(analysis.SuspiciousPatterns, "响应时间间隔过短")
		dataFreshness = 0.5
	}

	// 检查关键元素变化
	elementSimilarity := piv.calculateElementSimilarity(baselineResponse.KeyElements, testResponse.KeyElements)
	if elementSimilarity < 0.8 {
		analysis.SemanticChanges = append(analysis.SemanticChanges, "关键元素发生变化")
	}

	analysis.ContentRelevance = contentRelevance
	analysis.DataFreshness = dataFreshness

	return analysis
}

// evaluateRealImpact 评估真实影响
func (piv *ParameterImpactValidator) evaluateRealImpact(details *ParameterValidationDetails) bool {
	// 如果参数影响分数高，认为有真实影响
	if details.ParameterInfluence != nil && details.ParameterInfluence.InfluenceScore > piv.config.ImpactThreshold {
		return true
	}

	// 如果基线比较显示显著偏差，认为有真实影响
	if details.BaselineComparison != nil && details.BaselineComparison.DeviationType == "significant_deviation" {
		return true
	}

	// 如果检测到结构变化，认为有真实影响
	if details.ContentAnalysis != nil && len(details.ContentAnalysis.StructuralChanges) > 0 {
		return true
	}

	// 如果有时间异常，可能有真实影响
	if details.TimingAnalysis != nil && details.TimingAnalysis.HasTimingAnomaly {
		return true
	}

	// 如果响应一致性异常低，认为有真实影响
	if details.ResponseConsistency != nil && details.ResponseConsistency.VariationDetected && !details.ResponseConsistency.UnexpectedConsistency {
		return true
	}

	return false
}

// calculateImpactScore 计算影响分数
func (piv *ParameterImpactValidator) calculateImpactScore(details *ParameterValidationDetails) float64 {
	score := 0.0

	// 参数影响分数 (权重: 0.4)
	if details.ParameterInfluence != nil {
		score += details.ParameterInfluence.InfluenceScore * 0.4
	}

	// 基线偏差分数 (权重: 0.3)
	if details.BaselineComparison != nil {
		switch details.BaselineComparison.DeviationType {
		case "significant_deviation":
			score += 0.3
		case "error_behavior":
			score += 0.2
		case "default_behavior":
			score += 0.1
		}
	}

	// 内容相关性分数 (权重: 0.2)
	if details.ContentAnalysis != nil {
		relevanceScore := 1.0 - details.ContentAnalysis.ContentRelevance
		score += relevanceScore * 0.2
	}

	// 时间异常分数 (权重: 0.1)
	if details.TimingAnalysis != nil && details.TimingAnalysis.HasTimingAnomaly {
		score += details.TimingAnalysis.TimingVariation * 0.1
	}

	return math.Min(score, 1.0)
}

// isParameterProcessed 判断参数是否被处理
func (piv *ParameterImpactValidator) isParameterProcessed(details *ParameterValidationDetails) bool {
	// 如果有参数影响，说明被处理了
	if details.ParameterInfluence != nil && details.ParameterInfluence.InfluenceScore > 0.1 {
		return true
	}

	// 如果有显著的响应变化，说明被处理了
	if details.BaselineComparison != nil && details.BaselineComparison.DeviationType == "significant_deviation" {
		return true
	}

	// 如果有时间变化，可能被处理了
	if details.TimingAnalysis != nil && details.TimingAnalysis.TimingVariation > 0.2 {
		return true
	}

	return false
}

// isParameterIgnored 判断参数是否被忽略
func (piv *ParameterImpactValidator) isParameterIgnored(details *ParameterValidationDetails) bool {
	// 如果响应完全一致，可能被忽略了
	if details.ResponseConsistency != nil && details.ResponseConsistency.UnexpectedConsistency {
		return true
	}

	// 如果与基线完全匹配，可能被忽略了
	if details.BaselineComparison != nil && details.BaselineComparison.MatchesNormalResponse && details.BaselineComparison.SimilarityScore > 0.98 {
		return true
	}

	// 如果参数影响极低，可能被忽略了
	if details.ParameterInfluence != nil && details.ParameterInfluence.InfluenceScore < 0.05 {
		return true
	}

	return false
}

// isDefaultBehavior 判断是否为默认行为
func (piv *ParameterImpactValidator) isDefaultBehavior(details *ParameterValidationDetails, baseline *ParameterBaseline) bool {
	// 如果与默认响应匹配
	if details.BaselineComparison != nil && details.BaselineComparison.MatchesDefaultResponse {
		return true
	}

	// 如果基线比较显示为默认行为
	if details.BaselineComparison != nil && details.BaselineComparison.DeviationType == "default_behavior" {
		return true
	}

	return false
}

// calculateConfidence 计算置信度
func (piv *ParameterImpactValidator) calculateConfidence(details *ParameterValidationDetails) float64 {
	confidence := 0.0

	// 基于参数影响的置信度
	if details.ParameterInfluence != nil {
		confidence += details.ParameterInfluence.InfluenceScore * 0.3
	}

	// 基于基线比较的置信度
	if details.BaselineComparison != nil {
		confidence += (1.0 - details.BaselineComparison.SimilarityScore) * 0.3
	}

	// 基于响应一致性的置信度
	if details.ResponseConsistency != nil {
		if details.ResponseConsistency.VariationDetected && !details.ResponseConsistency.UnexpectedConsistency {
			confidence += 0.2
		}
	}

	// 基于内容分析的置信度
	if details.ContentAnalysis != nil {
		if len(details.ContentAnalysis.StructuralChanges) > 0 || len(details.ContentAnalysis.SemanticChanges) > 0 {
			confidence += 0.1
		}
	}

	// 基于时间分析的置信度
	if details.TimingAnalysis != nil && details.TimingAnalysis.HasTimingAnomaly {
		confidence += 0.1
	}

	return math.Min(confidence, 1.0)
}

// getRecommendedAction 获取推荐操作
func (piv *ParameterImpactValidator) getRecommendedAction(result *ParameterImpactResult) string {
	if result.HasRealImpact && result.Confidence > 0.7 {
		return "继续深度测试"
	}

	if result.IsParameterIgnored {
		return "参数被忽略，建议测试其他参数"
	}

	if result.IsDefaultBehavior {
		return "触发默认行为，建议验证业务逻辑"
	}

	if result.Confidence < 0.5 {
		return "结果不确定，建议人工验证"
	}

	return "建议进一步分析"
}

// identifyDefaultBehaviors 识别默认行为
func (piv *ParameterImpactValidator) identifyDefaultBehaviors(ctx context.Context, targetURL string, baseline *ParameterBaseline) error {
	// 测试一些常见的默认行为触发条件
	defaultTests := []map[string]string{
		{"invalid_param": "invalid_value"},
		{"nonexistent_param": "test"},
		{"empty_param": ""},
		{"null_param": "null"},
	}

	for _, params := range defaultTests {
		result, err := piv.executeTest(ctx, targetURL, params, "default_behavior", "default_response")
		if err == nil {
			baseline.DefaultResponses = append(baseline.DefaultResponses, result.Response)
		}
		time.Sleep(piv.config.RequestInterval)
	}

	return nil
}
