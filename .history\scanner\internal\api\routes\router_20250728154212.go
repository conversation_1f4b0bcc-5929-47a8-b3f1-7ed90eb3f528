package routes

import (
	"net/http"
	"time"

	// "scanner/internal/api/middleware"

	"scanner/internal/api/handlers"
	"scanner/internal/api/middleware"
	"scanner/internal/config"
	"scanner/internal/repository"
	"scanner/internal/scanner"
	"scanner/internal/scanner/engines"
	"scanner/internal/services"

	"scanner/internal/services/asset"
	"scanner/internal/services/auth"
	"scanner/internal/services/vulnerability"
	"scanner/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Setup 设置路由
func Setup(db *gorm.DB, cfg *config.Config) *gin.Engine {
	r := gin.New()

	// 中间件
	r.Use(gin.LoggerWithWriter(logger.GetLogger().Writer()))
	r.Use(gin.Recovery())
	r.Use(corsMiddleware())

	// 初始化服务
	userRepo := repository.NewUserRepository(db)
	assetRepo := repository.NewAssetRepository(db)

	authService := auth.NewService(userRepo, cfg.Security)
	scanService := services.NewScanService(db)
	scanLogService := services.NewScanLogService(db)
	assetService := asset.NewService(assetRepo, scanService, scanLogService)
	vulnService := vulnerability.NewService(db)
	systemConfigService := services.NewSystemConfigService(db)

	// 初始化定时扫描服务
	scheduledScanService := services.NewScheduledScanService(db, scanService)

	// 启动定时扫描服务
	if err := scheduledScanService.Start(); err != nil {
		logger.Errorf("启动定时扫描服务失败: %v", err)
	}

	// 初始化AI相关仓库和服务
	conversationRepo := repository.NewConversationRepository(db)
	aiService := services.NewAIService(db, vulnService, conversationRepo, systemConfigService)

	// 初始化默认系统配置
	if err := systemConfigService.InitializeDefaultConfigs(); err != nil {
		logger.Errorf("初始化默认系统配置失败: %v", err)
	} else {
		logger.Infof("✅ 系统配置初始化完成")
	}

	// 初始化扫描引擎（传递日志服务、系统配置服务和漏洞服务）
	engineManager := scanner.NewEngineManager(scanLogService, systemConfigService, vulnService)

	// 注册所有扫描引擎
	// 使用完整功能的Web扫描引擎（包含信息收集、漏洞检测等完整功能）
	// webEngine := engines.NewWebEngine() // 临时注释以解决循环导入
	networkEngine := engines.NewNetworkEngine()
	hostEngine := engines.NewHostEngine()
	apiEngine := engines.NewAPIEngine()
	domainEngine := engines.NewDomainEngine()

	if err := engineManager.RegisterEngine(webEngine); err != nil {
		logger.Errorf("注册完整功能Web扫描引擎失败: %v", err)
	} else {
		logger.Infof("✅ 已注册完整功能Web扫描引擎: %s (包含信息收集、漏洞检测、验证等功能)", webEngine.GetName())
		logger.Infof("   引擎类型: %s", webEngine.GetType())
		logger.Infof("   引擎实际类型: %T", webEngine)

		// 验证引擎是否正确注册
		if retrievedEngine, err := engineManager.GetEngine("web"); err != nil {
			logger.Errorf("❌ 验证失败：无法获取Web引擎: %v", err)
		} else {
			logger.Infof("✅ 验证成功：获取到Web引擎: %s", retrievedEngine.GetName())
			logger.Infof("   验证引擎类型: %s", retrievedEngine.GetType())
			logger.Infof("   验证引擎实际类型: %T", retrievedEngine)

			// 检查是否是完整功能引擎
			if _, ok := retrievedEngine.(*engines.WebEngine); ok {
				logger.Infof("✅ 确认是完整功能Web引擎")
				logger.Infof("   引擎功能: 信息收集、指纹识别、漏洞检测、漏洞验证、防护检测")
			} else {
				logger.Errorf("❌ 不是完整功能Web引擎，实际类型: %T", retrievedEngine)
			}
		}
	}
	if err := engineManager.RegisterEngine(networkEngine); err != nil {
		logger.Errorf("注册网络扫描引擎失败: %v", err)
	}
	if err := engineManager.RegisterEngine(hostEngine); err != nil {
		logger.Errorf("注册主机扫描引擎失败: %v", err)
	}
	if err := engineManager.RegisterEngine(apiEngine); err != nil {
		logger.Errorf("注册API扫描引擎失败: %v", err)
	}
	if err := engineManager.RegisterEngine(domainEngine); err != nil {
		logger.Errorf("注册域名发现引擎失败: %v", err)
	}

	taskScheduler := scanner.NewTaskScheduler(engineManager)

	// 初始化处理器
	authHandler := handlers.NewAuthHandler(authService)
	userHandler := handlers.NewUserHandler(authService)
	assetHandler := handlers.NewAssetHandler(assetService, scanService)
	scanHandler := handlers.NewScanHandler(scanService, scanLogService, scheduledScanService, engineManager, taskScheduler)
	scanLogHandler := handlers.NewScanLogHandler(scanLogService, scanService)
	vulnHandler := handlers.NewVulnerabilityHandler(vulnService)
	systemConfigHandler := handlers.NewSystemConfigHandler(systemConfigService)
	dashboardHandler := handlers.NewDashboardHandler(db, assetService, scanService, vulnService)
	aiHandler := handlers.NewAIHandler(aiService)

	// 初始化备份服务和处理器
	backupService := services.NewBackupService(db)
	backupHandler := handlers.NewBackupHandler(db, backupService)

	// 初始化中间件
	authMiddleware := middleware.NewAuthMiddleware(authService)

	// 初始化默认管理员
	if err := authService.InitDefaultAdmin(); err != nil {
		logger.Errorf("初始化默认管理员失败: %v", err)
	}

	// 初始化系统配置
	if err := systemConfigService.InitializeDefaultConfigs(); err != nil {
		logger.Errorf("初始化系统配置失败: %v", err)
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "漏洞扫描器运行正常",
			"version": cfg.App.Version,
		})
	})

	// 静态文件服务
	r.Static("/static", "./web/dist")
	r.StaticFile("/", "./web/dist/index.html")
	r.StaticFile("/favicon.ico", "./web/dist/favicon.ico")

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证路由
		auth := api.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.GET("/validate", authMiddleware.JWT(), authHandler.ValidateToken)
		}

		// 需要认证的路由
		authorized := api.Group("/")
		authorized.Use(authMiddleware.JWT())
		{
			// 认证相关
			authRoutes := authorized.Group("/auth")
			{
				authRoutes.POST("/logout", authHandler.Logout)
				authRoutes.GET("/permissions", authHandler.GetPermissions) // 获取用户权限
				authRoutes.GET("/profile", authHandler.GetProfile)         // 获取用户信息
			}

			// 仪表盘
			dashboard := authorized.Group("/dashboard")
			{
				dashboard.GET("/stats", dashboardHandler.GetDashboardStats) // 获取仪表盘统计数据
			}

			// 资产管理
			assets := authorized.Group("/assets")
			{
				assets.GET("", assetHandler.List)                            // 获取资产列表
				assets.POST("", assetHandler.Create)                         // 创建资产
				assets.GET("/:id", assetHandler.GetByID)                     // 获取资产详情
				assets.PUT("/:id", assetHandler.Update)                      // 更新资产
				assets.DELETE("/:id", assetHandler.Delete)                   // 删除资产
				assets.GET("/statistics", assetHandler.GetStatistics)        // 获取统计信息
				assets.POST("/discovery", assetHandler.Discovery)            // 资产发现
				assets.POST("/batch-import", assetHandler.BatchImport)       // 批量导入
				assets.POST("/batch-operation", assetHandler.BatchOperation) // 批量操作
				assets.GET("/export", assetHandler.Export)                   // 导出资产
			}

			// 用户管理
			users := authorized.Group("/users")
			{
				users.GET("", userHandler.GetUsers)          // 获取用户列表
				users.POST("", userHandler.CreateUser)       // 创建用户
				users.GET("/:id", userHandler.GetUser)       // 获取用户详情
				users.PUT("/:id", userHandler.UpdateUser)    // 更新用户
				users.DELETE("/:id", userHandler.DeleteUser) // 删除用户

				// 用户状态管理
				users.POST("/:id/enable", userHandler.EnableUser)            // 启用用户
				users.POST("/:id/disable", userHandler.DisableUser)          // 禁用用户
				users.POST("/:id/reset-password", userHandler.ResetPassword) // 重置密码

				// 角色管理
				users.GET("/roles", userHandler.GetRoles) // 获取角色列表
			}

			// 扫描管理
			scans := authorized.Group("/scans")
			{
				scans.GET("", scanHandler.GetTasks)                          // 获取扫描任务列表
				scans.POST("", scanHandler.CreateTask)                       // 创建扫描任务
				scans.GET("/stats", scanHandler.GetTaskStats)                // 获取扫描统计信息
				scans.GET("/:id", scanHandler.GetTaskByID)                   // 获取扫描任务详情
				scans.GET("/:id/target-info", scanHandler.GetTaskTargetInfo) // 获取扫描任务目标信息详情
				scans.GET("/:id/sitemap", scanHandler.GetTaskSiteMap)        // 获取扫描任务网站地图
				scans.PUT("/:id", scanHandler.UpdateTask)                    // 更新扫描任务
				scans.DELETE("/:id", scanHandler.DeleteTask)                 // 删除扫描任务
				scans.POST("/:id/start", scanHandler.StartTask)              // 启动扫描任务
				scans.POST("/:id/stop", scanHandler.StopTask)                // 停止扫描任务

				// 定时任务相关路由
				scans.GET("/scheduled", scanHandler.GetScheduledTasks)           // 获取定时任务列表
				scans.GET("/scheduled/stats", scanHandler.GetScheduledTaskStats) // 获取定时任务统计
				scans.DELETE("/scheduled/:id", scanHandler.RemoveScheduledTask)  // 移除定时任务

				// 扫描日志相关路由
				scans.GET("/:id/logs", scanLogHandler.GetScanLogs)                     // 获取扫描日志列表
				scans.GET("/:id/logs/latest", scanLogHandler.GetLatestScanLogs)        // 获取最新扫描日志
				scans.GET("/:id/logs/search", scanLogHandler.SearchScanLogs)           // 搜索扫描日志
				scans.GET("/:id/logs/stats", scanLogHandler.GetLogStats)               // 获取日志统计信息
				scans.GET("/:id/logs/progress", scanLogHandler.GetProgressHistory)     // 获取进度历史
				scans.GET("/:id/logs/summary", scanLogHandler.GetScanExecutionSummary) // 获取扫描执行摘要
				scans.GET("/:id/logs/export", scanLogHandler.ExportScanLogs)           // 导出扫描日志
				scans.GET("/:id/logs/stage", scanLogHandler.GetLogsByStage)            // 根据阶段获取日志

				// 扫描漏洞相关路由
				scans.GET("/:id/vulnerabilities", scanHandler.GetScanTaskVulnerabilities) // 获取扫描任务漏洞列表
			}

			// 漏洞管理
			vulns := authorized.Group("/vulnerabilities")
			{
				vulns.GET("", vulnHandler.GetVulnerabilities)                       // 获取漏洞列表
				vulns.GET("/:id", vulnHandler.GetVulnerabilityDetail)               // 获取漏洞详情
				vulns.PUT("/:id", vulnHandler.UpdateVulnerability)                  // 更新漏洞信息
				vulns.DELETE("/:id", vulnHandler.DeleteVulnerability)               // 删除漏洞
				vulns.POST("/batch-update", vulnHandler.BatchUpdateVulnerabilities) // 批量更新漏洞状态
				vulns.GET("/stats", vulnHandler.GetVulnerabilityStats)              // 获取漏洞统计信息
			}

			// AI分析
			ai := authorized.Group("/ai")
			{
				ai.POST("/chat", aiHandler.Chat)                                     // AI聊天
				ai.POST("/analyze-vulnerability", aiHandler.AnalyzeVulnerability)    // 漏洞智能分析
				ai.POST("/generate-fix-suggestion", aiHandler.GenerateFixSuggestion) // 生成修复建议
				ai.POST("/test-connection", aiHandler.TestConnection)                // 测试AI连接
				ai.GET("/conversations", aiHandler.GetConversations)                 // 获取对话历史
				ai.POST("/conversations", aiHandler.CreateConversation)              // 创建新对话
				ai.DELETE("/conversations/:id", aiHandler.DeleteConversation)        // 删除对话
				ai.POST("/security-qa", aiHandler.SecurityQA)                        // 安全知识问答
				ai.GET("/usage-stats", aiHandler.GetUsageStats)                      // 获取使用统计
			}

			// 工单管理
			// 创建邮件服务（暂时使用空配置，只记录日志）
			emailService := services.NewEmailService(nil)
			ticketHandler := handlers.NewTicketHandler(services.NewTicketService(db), emailService, db)
			tickets := authorized.Group("/tickets")
			{
				tickets.GET("", ticketHandler.GetTickets)           // 获取工单列表
				tickets.POST("", ticketHandler.CreateTicket)        // 创建工单
				tickets.GET("/stats", ticketHandler.GetTicketStats) // 获取工单统计信息
				tickets.GET("/:id", ticketHandler.GetTicketByID)    // 获取工单详情
				tickets.PUT("/:id", ticketHandler.UpdateTicket)     // 更新工单
				tickets.DELETE("/:id", ticketHandler.DeleteTicket)  // 删除工单
			}

			// 报告管理
			reportService := services.NewReportService(db)
			reportHandler := handlers.NewReportHandler(reportService, scanService, db)
			reports := authorized.Group("/reports")
			{
				reports.GET("", reportHandler.GetReports)                  // 获取报告列表
				reports.POST("", reportHandler.CreateReport)               // 创建报告
				reports.GET("/:id", reportHandler.GetReportByID)           // 获取报告详情
				reports.GET("/:id/download", reportHandler.DownloadReport) // 下载报告
				reports.DELETE("/:id", reportHandler.DeleteReport)         // 删除报告
			}

			// 系统设置
			settings := authorized.Group("/settings")
			{
				// 扫描配置
				settings.GET("/scan", systemConfigHandler.GetScanConfig)          // 获取扫描配置
				settings.PUT("/scan", systemConfigHandler.UpdateScanConfig)       // 更新扫描配置
				settings.POST("/scan/reset", systemConfigHandler.ResetScanConfig) // 重置扫描配置

				// 超时配置
				settings.GET("/scan/timeout", systemConfigHandler.GetScanTimeout)       // 获取扫描超时配置
				settings.PUT("/scan/timeout", systemConfigHandler.SetScanTimeout)       // 设置扫描超时配置
				settings.GET("/scan/timeout/test", systemConfigHandler.TestScanTimeout) // 测试超时配置

				// 通用配置
				settings.GET("/config/:category/:key", systemConfigHandler.GetConfig) // 获取指定配置
				settings.PUT("/config/:category/:key", systemConfigHandler.SetConfig) // 设置指定配置

				// 测试连接
				settings.POST("/notification/test-email", systemConfigHandler.TestEmailConnection) // 测试邮件连接
				settings.POST("/ai/test-connection", systemConfigHandler.TestAIConnection)         // 测试AI连接

				// 系统信息
				settings.GET("/system/info", systemConfigHandler.GetSystemInfo) // 获取系统信息

				// 备份恢复
				settings.GET("/backup/config", backupHandler.GetBackupConfig)        // 获取备份配置
				settings.PUT("/backup/config", backupHandler.UpdateBackupConfig)     // 更新备份配置
				settings.GET("/backup/stats", backupHandler.GetBackupStats)          // 获取备份统计
				settings.GET("/backups", backupHandler.GetBackupList)                // 获取备份列表
				settings.POST("/backup", backupHandler.CreateBackup)                 // 创建备份
				settings.POST("/backup/:id/restore", backupHandler.RestoreBackup)    // 恢复备份
				settings.DELETE("/backup/:id", backupHandler.DeleteBackup)           // 删除备份
				settings.GET("/backup/:id/download", backupHandler.DownloadBackup)   // 下载备份
				settings.POST("/backup/upload-restore", backupHandler.UploadRestore) // 上传并恢复备份
			}
		}
	}

	// 测试日志
	logger.Infof("🔧 到达任务监控服务初始化位置")

	// 初始化简化的任务状态监控服务
	logger.Infof("🔧 开始初始化任务状态监控服务...")
	simpleTaskMonitorConfig := &services.SimpleTaskMonitorConfig{
		CheckInterval: 5 * time.Minute,  // 每5分钟检查一次
		MaxStaleTime:  10 * time.Minute, // 10分钟无更新视为过期
	}
	logger.Infof("🔧 创建任务监控服务实例...")
	simpleTaskMonitorService := services.NewSimpleTaskMonitorService(
		db, scanService, scanLogService, simpleTaskMonitorConfig)

	// 启动任务状态监控服务
	logger.Infof("🔧 启动任务状态监控服务...")
	if err := simpleTaskMonitorService.Start(); err != nil {
		logger.Errorf("❌ 启动任务状态监控服务失败: %v", err)
	} else {
		logger.Infof("✅ 任务状态监控服务已启动，每5分钟检查一次运行中的任务状态")
	}

	return r
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置CORS头部
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Request-ID, X-Requested-With")
		c.Header("Access-Control-Expose-Headers", "Content-Length, X-Request-ID")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400") // 24小时预检缓存

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
