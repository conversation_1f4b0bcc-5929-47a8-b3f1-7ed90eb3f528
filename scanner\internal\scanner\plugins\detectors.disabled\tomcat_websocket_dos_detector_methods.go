package detectors

import (
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// detectTomcatService 检测Tomcat服务
func (d *TomcatWebSocketDoSDetector) detectTomcatService(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测Tomcat服务...")

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 构造测试URL
	testURL := target.URL

	// 发送请求检测Tomcat服务
	resp, err := d.sendTomcatRequest(ctx, testURL)
	if err != nil {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	// 检查Tomcat服务响应
	confidence := d.checkTomcatResponse(resp)
	if confidence > maxConfidence {
		maxConfidence = confidence
		vulnerablePayload = "Tomcat服务检测"
		vulnerableRequest = testURL
		vulnerableResponse = resp
	}

	if confidence > 0.5 {
		evidence = append(evidence, plugins.Evidence{
			Type:        "tomcat_service",
			Description: fmt.Sprintf("Tomcat服务检测 (置信度: %.2f)", confidence),
			Content:     d.extractTomcatEvidence(resp),
			Location:    testURL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectWebSocketEndpoints 检测WebSocket端点
func (d *TomcatWebSocketDoSDetector) detectWebSocketEndpoints(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测WebSocket端点...")

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 测试WebSocket路径
	for _, path := range d.websocketPaths {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造WebSocket测试URL
		testURL := d.buildWebSocketURL(target.URL, path)

		// 发送WebSocket升级请求
		resp, err := d.sendWebSocketRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查WebSocket响应
		confidence := d.checkWebSocketResponse(resp, path)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = path
			vulnerableRequest = testURL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "websocket_endpoint",
				Description: fmt.Sprintf("WebSocket端点检测 (路径: %s, 置信度: %.2f)", path, confidence),
				Content:     d.extractWebSocketEvidence(resp, path),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCVE202013935 检测CVE-2020-13935漏洞
func (d *TomcatWebSocketDoSDetector) detectCVE202013935(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测CVE-2020-13935漏洞...")

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// CVE-2020-13935特定测试
	cveTests := []struct {
		path        string
		description string
	}{
		{"/websocket", "WebSocket端点DoS测试"},
		{"/ws", "WebSocket简短路径测试"},
		{"/chat", "聊天WebSocket测试"},
		{"/echo", "回声WebSocket测试"},
	}

	for _, test := range cveTests {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造测试URL
		testURL := d.buildWebSocketURL(target.URL, test.path)

		// 执行CVE-2020-13935特定测试
		confidence := d.testCVE202013935DoS(ctx, testURL)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = test.path
			vulnerableRequest = testURL
			vulnerableResponse = fmt.Sprintf("CVE-2020-13935测试结果 (置信度: %.2f)", confidence)
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "cve_2020_13935",
				Description: fmt.Sprintf("%s (置信度: %.2f)", test.description, confidence),
				Content:     fmt.Sprintf("CVE-2020-13935 DoS测试: %s", test.path),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendTomcatRequest 发送Tomcat检测请求
func (d *TomcatWebSocketDoSDetector) sendTomcatRequest(ctx context.Context, testURL string) (string, error) {
	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Connection", "keep-alive")

	// 发送请求
	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 构造完整响应信息
	response := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			response += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	response += "\n" + string(body)

	return response, nil
}

// sendWebSocketRequest 发送WebSocket升级请求
func (d *TomcatWebSocketDoSDetector) sendWebSocketRequest(ctx context.Context, testURL string) (string, error) {
	// 创建WebSocket升级请求
	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置WebSocket升级头部
	req.Header.Set("Upgrade", "websocket")
	req.Header.Set("Connection", "Upgrade")
	req.Header.Set("Sec-WebSocket-Key", "dGhlIHNhbXBsZSBub25jZQ==")
	req.Header.Set("Sec-WebSocket-Version", "13")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 发送请求
	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 构造完整响应信息
	response := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			response += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	response += "\n" + string(body)

	return response, nil
}

// buildWebSocketURL 构造WebSocket测试URL
func (d *TomcatWebSocketDoSDetector) buildWebSocketURL(baseURL, path string) string {
	u, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + path
	}

	// 确保路径以/开头
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}

	u.Path = path
	return u.String()
}

// checkTomcatResponse 检查Tomcat服务响应
func (d *TomcatWebSocketDoSDetector) checkTomcatResponse(response string) float64 {
	if response == "" {
		return 0.0
	}

	confidence := 0.0
	responseLower := strings.ToLower(response)

	// 检查Tomcat指示器
	for _, indicator := range d.tomcatIndicators {
		if strings.Contains(responseLower, strings.ToLower(indicator)) {
			confidence += 0.2
			logger.Debugf("发现Tomcat指示器: %s", indicator)
		}
	}

	// 检查Tomcat响应模式
	for _, pattern := range d.tomcatPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			logger.Debugf("匹配Tomcat响应模式: %s", pattern.String())
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkWebSocketResponse 检查WebSocket响应
func (d *TomcatWebSocketDoSDetector) checkWebSocketResponse(response, path string) float64 {
	if response == "" {
		return 0.0
	}

	confidence := 0.0
	responseLower := strings.ToLower(response)

	// 检查WebSocket升级成功 (101 Switching Protocols)
	if strings.Contains(response, "101") && strings.Contains(responseLower, "switching") {
		confidence += 0.5
	}

	// 检查WebSocket头部
	for _, header := range d.websocketHeaders {
		if strings.Contains(responseLower, strings.ToLower(header)) {
			confidence += 0.2
			logger.Debugf("发现WebSocket头部: %s", header)
		}
	}

	// 检查WebSocket响应模式
	for _, pattern := range d.websocketPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			logger.Debugf("匹配WebSocket响应模式: %s", pattern.String())
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// testCVE202013935DoS 测试CVE-2020-13935 DoS漏洞
func (d *TomcatWebSocketDoSDetector) testCVE202013935DoS(ctx context.Context, testURL string) float64 {
	// 安全的DoS测试，不进行实际的攻击
	confidence := 0.0

	// 1. 测试WebSocket连接建立
	if d.testWebSocketConnection(ctx, testURL) {
		confidence += 0.3
	}

	// 2. 测试服务响应时间
	responseTime := d.measureResponseTime(ctx, testURL)
	if responseTime > 5*time.Second {
		confidence += 0.2 // 响应时间过长可能表示存在问题
	}

	// 3. 测试多次连接
	if d.testMultipleConnections(ctx, testURL) {
		confidence += 0.3
	}

	// 4. 检查错误响应
	if d.testErrorResponses(ctx, testURL) {
		confidence += 0.2
	}

	return confidence
}

// testWebSocketConnection 测试WebSocket连接
func (d *TomcatWebSocketDoSDetector) testWebSocketConnection(ctx context.Context, testURL string) bool {
	resp, err := d.sendWebSocketRequest(ctx, testURL)
	if err != nil {
		return false
	}

	// 检查是否成功建立WebSocket连接
	return strings.Contains(resp, "101") || strings.Contains(strings.ToLower(resp), "websocket")
}

// measureResponseTime 测量响应时间
func (d *TomcatWebSocketDoSDetector) measureResponseTime(ctx context.Context, testURL string) time.Duration {
	start := time.Now()
	_, err := d.sendWebSocketRequest(ctx, testURL)
	duration := time.Since(start)

	if err != nil {
		return 0
	}

	return duration
}

// testMultipleConnections 测试多次连接
func (d *TomcatWebSocketDoSDetector) testMultipleConnections(ctx context.Context, testURL string) bool {
	successCount := 0
	totalTests := 3

	for i := 0; i < totalTests; i++ {
		if d.testWebSocketConnection(ctx, testURL) {
			successCount++
		}
	}

	// 如果多次连接都成功，说明WebSocket端点可用
	return successCount >= 2
}

// testErrorResponses 测试错误响应
func (d *TomcatWebSocketDoSDetector) testErrorResponses(ctx context.Context, testURL string) bool {
	// 发送无效的WebSocket请求
	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return false
	}

	// 设置无效的WebSocket头部
	req.Header.Set("Upgrade", "invalid")
	req.Header.Set("Connection", "invalid")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	// 检查是否返回错误响应
	return resp.StatusCode >= 400
}

// extractTomcatEvidence 提取Tomcat证据
func (d *TomcatWebSocketDoSDetector) extractTomcatEvidence(response string) string {
	evidence := "Tomcat服务检测证据:\n"

	// 提取关键信息
	lines := strings.Split(response, "\n")
	var evidenceLines []string

	for _, line := range lines {
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if len(lineLower) > 0 {
			// 检查是否包含Tomcat特征
			for _, indicator := range d.tomcatIndicators {
				if strings.Contains(lineLower, strings.ToLower(indicator)) {
					evidenceLines = append(evidenceLines, strings.TrimSpace(line))
					break
				}
			}
			if len(evidenceLines) >= 3 {
				break
			}
		}
	}

	if len(evidenceLines) > 0 {
		evidence += strings.Join(evidenceLines, "\n")
	} else {
		// 如果没有找到特定证据，提取前几行
		if len(lines) > 3 {
			evidence += strings.Join(lines[:3], "\n")
		} else {
			evidence += response
		}
	}

	return evidence
}

// extractWebSocketEvidence 提取WebSocket证据
func (d *TomcatWebSocketDoSDetector) extractWebSocketEvidence(response, path string) string {
	evidence := fmt.Sprintf("WebSocket端点检测证据 (路径: %s):\n", path)

	// 提取WebSocket相关证据
	if strings.Contains(strings.ToLower(response), "websocket") {
		evidence += "发现WebSocket协议支持\n"
	}

	if strings.Contains(response, "101") {
		evidence += "WebSocket升级成功 (101 Switching Protocols)\n"
	}

	// 截取响应的前200个字符作为证据
	if len(response) > 200 {
		evidence += "响应摘要:\n" + response[:200] + "..."
	} else {
		evidence += "响应内容:\n" + response
	}

	return evidence
}

// verifyTomcatService 验证Tomcat服务
func (d *TomcatWebSocketDoSDetector) verifyTomcatService(ctx context.Context, target *plugins.ScanTarget) bool {
	resp, err := d.sendTomcatRequest(ctx, target.URL)
	if err != nil {
		return false
	}

	return d.checkTomcatResponse(resp) > 0.5
}

// verifyWebSocketEndpoints 验证WebSocket端点
func (d *TomcatWebSocketDoSDetector) verifyWebSocketEndpoints(ctx context.Context, target *plugins.ScanTarget) bool {
	// 验证关键WebSocket路径
	keyPaths := []string{"/websocket", "/ws", "/socket"}

	for _, path := range keyPaths {
		testURL := d.buildWebSocketURL(target.URL, path)
		if d.testWebSocketConnection(ctx, testURL) {
			return true
		}
	}

	return false
}

// verifyDoSVulnerability 验证DoS漏洞
func (d *TomcatWebSocketDoSDetector) verifyDoSVulnerability(ctx context.Context, target *plugins.ScanTarget) bool {
	// 验证关键WebSocket路径的DoS漏洞
	keyPaths := []string{"/websocket", "/ws"}

	for _, path := range keyPaths {
		testURL := d.buildWebSocketURL(target.URL, path)
		confidence := d.testCVE202013935DoS(ctx, testURL)
		if confidence > 0.6 {
			return true
		}
	}

	return false
}
