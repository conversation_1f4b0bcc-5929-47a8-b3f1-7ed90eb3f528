package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// LogicVulnerabilityDetector 逻辑漏洞检测器
// 支持业务逻辑漏洞、权限控制漏洞、数据验证漏洞、流程控制漏洞等多种逻辑漏洞检测
type LogicVulnerabilityDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	businessLogicPayloads  []string         // 业务逻辑载荷
	permissionPayloads     []string         // 权限控制载荷
	dataValidationPayloads []string         // 数据验证载荷
	processControlPayloads []string         // 流程控制载荷
	testParameters         []string         // 测试参数
	logicPatterns          []*regexp.Regexp // 逻辑漏洞模式
	businessPatterns       []*regexp.Regexp // 业务逻辑模式
	permissionPatterns     []*regexp.Regexp // 权限控制模式
	validationPatterns     []*regexp.Regexp // 数据验证模式
	httpClient             *http.Client
}

// NewLogicVulnerabilityDetector 创建逻辑漏洞检测器
func NewLogicVulnerabilityDetector() *LogicVulnerabilityDetector {
	detector := &LogicVulnerabilityDetector{
		id:          "logic-vulnerability-comprehensive",
		name:        "逻辑漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-34527", "CVE-2020-1472", "CVE-2019-19781", "CVE-2018-13379"},
		cwe:         []string{"CWE-284", "CWE-285", "CWE-862", "CWE-863", "CWE-639"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测逻辑漏洞，包括业务逻辑漏洞、权限控制漏洞、数据验证漏洞、流程控制漏洞等多种逻辑安全问题",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // 逻辑漏洞检测需要适中时间
		MaxRetries:      2,                // 逻辑漏洞检测可以重试
		Concurrency:     5,                // 中等并发数
		RateLimit:       5,                // 中等速率限制
		FollowRedirects: true,             // 跟随重定向检查逻辑流程
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，逻辑漏洞响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeBusinessLogicPayloads()
	detector.initializePermissionPayloads()
	detector.initializeDataValidationPayloads()
	detector.initializeProcessControlPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *LogicVulnerabilityDetector) GetID() string            { return d.id }
func (d *LogicVulnerabilityDetector) GetName() string          { return d.name }
func (d *LogicVulnerabilityDetector) GetCategory() string      { return d.category }
func (d *LogicVulnerabilityDetector) GetSeverity() string      { return d.severity }
func (d *LogicVulnerabilityDetector) GetCVE() []string         { return d.cve }
func (d *LogicVulnerabilityDetector) GetCWE() []string         { return d.cwe }
func (d *LogicVulnerabilityDetector) GetVersion() string       { return d.version }
func (d *LogicVulnerabilityDetector) GetAuthor() string        { return d.author }
func (d *LogicVulnerabilityDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *LogicVulnerabilityDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *LogicVulnerabilityDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *LogicVulnerabilityDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *LogicVulnerabilityDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *LogicVulnerabilityDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *LogicVulnerabilityDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *LogicVulnerabilityDetector) GetDependencies() []string         { return []string{} }
func (d *LogicVulnerabilityDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *LogicVulnerabilityDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *LogicVulnerabilityDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *LogicVulnerabilityDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *LogicVulnerabilityDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 5
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *LogicVulnerabilityDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *LogicVulnerabilityDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.businessLogicPayloads) == 0 {
		return fmt.Errorf("业务逻辑载荷不能为空")
	}
	if len(d.permissionPayloads) == 0 {
		return fmt.Errorf("权限控制载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *LogicVulnerabilityDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 逻辑漏洞检测适用于有业务功能的Web应用
	// 检查是否有逻辑漏洞相关的特征
	if d.hasLogicVulnerabilityFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于逻辑漏洞相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	logicKeywords := []string{
		"login", "auth", "user", "admin", "account", "profile", "setting",
		"order", "payment", "cart", "checkout", "purchase", "buy", "sell",
		"transfer", "withdraw", "deposit", "balance", "money", "price",
		"role", "permission", "access", "privilege", "right", "grant",
		"api", "service", "endpoint", "method", "function", "action",
		"logic", "business", "process", "workflow", "step", "stage",
		"登录", "认证", "用户", "管理", "账户", "配置", "设置",
		"订单", "支付", "购物车", "结账", "购买", "销售", "转账",
		"提现", "存款", "余额", "金钱", "价格", "角色", "权限",
		"访问", "特权", "授权", "逻辑", "业务", "流程", "工作流",
	}

	for _, keyword := range logicKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 逻辑漏洞是通用Web漏洞，默认适用于所有Web目标
}

// hasLogicVulnerabilityFeatures 检查是否有逻辑漏洞功能
func (d *LogicVulnerabilityDetector) hasLogicVulnerabilityFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有逻辑漏洞相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "auth") ||
			strings.Contains(keyLower, "user") ||
			strings.Contains(keyLower, "session") ||
			strings.Contains(keyLower, "permission") ||
			strings.Contains(valueLower, "auth") ||
			strings.Contains(valueLower, "user") ||
			strings.Contains(valueLower, "session") ||
			strings.Contains(valueLower, "permission") {
			return true
		}
	}

	// 检查技术栈中是否有业务应用相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		businessTechnologies := []string{
			"spring", "django", "rails", "express", "laravel", "symfony",
			"struts", "hibernate", "mybatis", "sequelize", "eloquent",
			"jwt", "oauth", "saml", "ldap", "active directory",
			"redis", "memcached", "session", "cookie", "token",
			"业务", "逻辑", "权限", "认证", "授权", "会话",
		}

		for _, bizTech := range businessTechnologies {
			if strings.Contains(techNameLower, bizTech) {
				return true
			}
		}
	}

	// 检查链接中是否有逻辑漏洞相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "login") ||
			strings.Contains(linkURLLower, "auth") ||
			strings.Contains(linkURLLower, "admin") ||
			strings.Contains(linkURLLower, "user") ||
			strings.Contains(linkURLLower, "order") ||
			strings.Contains(linkURLLower, "payment") ||
			strings.Contains(linkTextLower, "login") ||
			strings.Contains(linkTextLower, "auth") ||
			strings.Contains(linkTextLower, "admin") ||
			strings.Contains(linkTextLower, "user") ||
			strings.Contains(linkTextLower, "order") ||
			strings.Contains(linkTextLower, "payment") ||
			strings.Contains(linkTextLower, "登录") ||
			strings.Contains(linkTextLower, "认证") ||
			strings.Contains(linkTextLower, "管理") ||
			strings.Contains(linkTextLower, "用户") ||
			strings.Contains(linkTextLower, "订单") ||
			strings.Contains(linkTextLower, "支付") {
			return true
		}
	}

	// 检查表单中是否有逻辑漏洞相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			logicFields := []string{
				"login", "auth", "user", "username", "password", "email",
				"role", "permission", "access", "privilege", "admin",
				"order", "payment", "amount", "price", "money", "balance",
				"transfer", "withdraw", "deposit", "buy", "sell", "cart",
				"logic", "business", "process", "workflow", "step",
				"登录", "认证", "用户", "密码", "邮箱", "角色",
				"权限", "访问", "特权", "管理", "订单", "支付",
				"金额", "价格", "余额", "转账", "提现", "存款",
				"逻辑", "业务", "流程", "工作流", "步骤",
			}

			for _, logicField := range logicFields {
				if strings.Contains(fieldNameLower, logicField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *LogicVulnerabilityDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种逻辑漏洞检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 业务逻辑漏洞检测
	businessEvidence, businessConfidence, businessPayload, businessRequest, businessResponse := d.detectBusinessLogicVulnerability(ctx, target)
	if businessConfidence > maxConfidence {
		maxConfidence = businessConfidence
		vulnerablePayload = businessPayload
		vulnerableRequest = businessRequest
		vulnerableResponse = businessResponse
	}
	evidence = append(evidence, businessEvidence...)

	// 2. 权限控制漏洞检测
	permissionEvidence, permissionConfidence, permissionPayload, permissionRequest, permissionResponse := d.detectPermissionVulnerability(ctx, target)
	if permissionConfidence > maxConfidence {
		maxConfidence = permissionConfidence
		vulnerablePayload = permissionPayload
		vulnerableRequest = permissionRequest
		vulnerableResponse = permissionResponse
	}
	evidence = append(evidence, permissionEvidence...)

	// 3. 数据验证漏洞检测
	validationEvidence, validationConfidence, validationPayload, validationRequest, validationResponse := d.detectDataValidationVulnerability(ctx, target)
	if validationConfidence > maxConfidence {
		maxConfidence = validationConfidence
		vulnerablePayload = validationPayload
		vulnerableRequest = validationRequest
		vulnerableResponse = validationResponse
	}
	evidence = append(evidence, validationEvidence...)

	// 4. 流程控制漏洞检测
	processEvidence, processConfidence, processPayload, processRequest, processResponse := d.detectProcessControlVulnerability(ctx, target)
	if processConfidence > maxConfidence {
		maxConfidence = processConfidence
		vulnerablePayload = processPayload
		vulnerableRequest = processRequest
		vulnerableResponse = processResponse
	}
	evidence = append(evidence, processEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "逻辑漏洞",
		Description:       "检测到逻辑漏洞，攻击者可能绕过业务逻辑、权限控制或数据验证机制",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "加强业务逻辑验证、完善权限控制机制、增强数据验证规则、优化流程控制逻辑",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Business_logic_vulnerability", "https://cwe.mitre.org/data/definitions/284.html", "https://cwe.mitre.org/data/definitions/862.html"},
		Tags:              []string{"logic", "business", "permission", "validation", "process", "control"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *LogicVulnerabilityDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"business-logic-verification",
		"permission-control-verification",
		"data-validation-verification",
		"process-control-verification",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyLogicVulnerabilityMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了逻辑漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "logic-vulnerability-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用逻辑漏洞验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *LogicVulnerabilityDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("logic_vulnerability_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *LogicVulnerabilityDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (逻辑漏洞通常是高风险漏洞)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyLogicVulnerabilityMethod 验证逻辑漏洞方法
func (d *LogicVulnerabilityDetector) verifyLogicVulnerabilityMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "business-logic-verification":
		return d.verifyBusinessLogic(ctx, target)
	case "permission-control-verification":
		return d.verifyPermissionControl(ctx, target)
	case "data-validation-verification":
		return d.verifyDataValidation(ctx, target)
	case "process-control-verification":
		return d.verifyProcessControl(ctx, target)
	default:
		return 0.0
	}
}

// verifyBusinessLogic 验证业务逻辑
func (d *LogicVulnerabilityDetector) verifyBusinessLogic(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的业务逻辑验证
	if d.hasLogicVulnerabilityFeatures(target) {
		return 0.7 // 有逻辑漏洞特征的目标可能有业务逻辑漏洞
	}
	return 0.3
}

// verifyPermissionControl 验证权限控制
func (d *LogicVulnerabilityDetector) verifyPermissionControl(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的权限控制验证
	if d.hasLogicVulnerabilityFeatures(target) {
		return 0.6 // 有逻辑漏洞特征的目标可能有权限控制漏洞
	}
	return 0.2
}

// verifyDataValidation 验证数据验证
func (d *LogicVulnerabilityDetector) verifyDataValidation(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的数据验证验证
	if d.hasLogicVulnerabilityFeatures(target) {
		return 0.6 // 有逻辑漏洞特征的目标可能有数据验证漏洞
	}
	return 0.2
}

// verifyProcessControl 验证流程控制
func (d *LogicVulnerabilityDetector) verifyProcessControl(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的流程控制验证
	if d.hasLogicVulnerabilityFeatures(target) {
		return 0.5 // 有逻辑漏洞特征的目标可能有流程控制漏洞
	}
	return 0.1
}

// initializeBusinessLogicPayloads 初始化业务逻辑载荷列表
func (d *LogicVulnerabilityDetector) initializeBusinessLogicPayloads() {
	d.businessLogicPayloads = []string{
		// 价格操作载荷
		"price=-1", "price=0", "price=0.01", "amount=-100", "amount=0",
		"cost=-1", "cost=0", "total=-1", "total=0", "money=-1",
		"balance=-1000", "credit=-500", "discount=999", "discount=100%",
		"coupon=UNLIMITED", "voucher=FREE", "promo=100OFF",

		// 数量操作载荷
		"quantity=-1", "quantity=0", "qty=-10", "count=-1", "num=-5",
		"items=-1", "products=-1", "goods=-1", "stock=-100",
		"inventory=-1000", "limit=-1", "max=-1", "min=-1",

		// 用户权限载荷
		"role=admin", "role=administrator", "role=root", "role=superuser",
		"permission=all", "access=full", "privilege=admin", "level=999",
		"group=admin", "type=admin", "status=admin", "rank=admin",
		"is_admin=true", "is_root=true", "is_super=true", "admin=1",

		// 用户ID操作载荷
		"user_id=1", "user_id=0", "user_id=-1", "userid=admin", "uid=0",
		"id=1", "id=0", "id=-1", "account_id=1", "customer_id=1",
		"member_id=1", "profile_id=1", "session_id=admin",

		// 订单操作载荷
		"order_id=1", "order_id=0", "order_status=completed", "order_status=paid",
		"payment_status=success", "payment_status=completed", "status=approved",
		"state=finished", "stage=complete", "step=final", "phase=done",

		// 时间操作载荷
		"date=1970-01-01", "time=00:00:00", "timestamp=0", "expire=never",
		"valid_until=2099-12-31", "start_date=1900-01-01", "end_date=2099-12-31",
		"created_at=1970-01-01", "updated_at=2099-12-31",

		// 业务流程载荷
		"step=skip", "stage=bypass", "process=complete", "workflow=finish",
		"action=approve", "operation=confirm", "method=auto", "mode=fast",
		"type=instant", "category=priority", "priority=urgent", "level=high",

		// 限制绕过载荷
		"limit=999999", "max_limit=unlimited", "rate_limit=0", "throttle=off",
		"restriction=none", "constraint=bypass", "validation=skip", "check=false",
		"verify=false", "confirm=auto", "approve=auto", "review=skip",

		// 中文业务逻辑载荷
		"价格=-1", "金额=0", "数量=-1", "角色=管理员", "权限=全部",
		"用户ID=1", "订单状态=已完成", "支付状态=成功", "审核=跳过",
		"验证=关闭", "确认=自动", "批准=自动", "限制=无",
	}
}

// initializePermissionPayloads 初始化权限控制载荷列表
func (d *LogicVulnerabilityDetector) initializePermissionPayloads() {
	d.permissionPayloads = []string{
		// 权限提升载荷
		"../admin", "../../admin", "../../../admin", "/admin", "admin/",
		"../root", "../../root", "../../../root", "/root", "root/",
		"../user/admin", "../admin/user", "/user/admin", "/admin/user",

		// 用户切换载荷
		"user=admin", "user=administrator", "user=root", "user=superuser",
		"username=admin", "login=admin", "account=admin", "profile=admin",
		"switch_user=admin", "su=admin", "sudo=admin", "impersonate=admin",

		// 角色绕过载荷
		"role[]=admin", "role[0]=admin", "roles[]=admin", "roles[0]=admin",
		"permission[]=all", "permissions[]=all", "access[]=full", "privileges[]=all",
		"group[]=admin", "groups[]=admin", "level[]=999", "levels[]=999",

		// 权限参数载荷
		"admin=1", "admin=true", "is_admin=1", "is_admin=true",
		"root=1", "root=true", "is_root=1", "is_root=true",
		"super=1", "super=true", "is_super=1", "is_super=true",
		"privileged=1", "privileged=true", "elevated=1", "elevated=true",

		// 访问控制载荷
		"access_level=admin", "access_type=full", "access_mode=unrestricted",
		"permission_level=high", "security_level=low", "auth_level=bypass",
		"acl=bypass", "rbac=disable", "policy=ignore", "rule=skip",

		// 会话操作载荷
		"session_role=admin", "session_user=admin", "session_level=high",
		"token_role=admin", "jwt_role=admin", "cookie_role=admin",
		"auth_role=admin", "login_role=admin", "user_role=admin",

		// 组织结构载荷
		"department=admin", "division=management", "team=security",
		"organization=root", "company=admin", "branch=headquarters",
		"office=main", "location=central", "region=global", "zone=unrestricted",

		// 功能权限载荷
		"read=true", "write=true", "execute=true", "delete=true",
		"create=true", "update=true", "modify=true", "admin=true",
		"manage=true", "control=true", "configure=true", "maintain=true",

		// 中文权限载荷
		"角色=管理员", "用户=管理员", "权限=全部", "访问=完全",
		"级别=最高", "等级=管理", "身份=管理员", "类型=管理",
		"组织=管理", "部门=管理", "团队=安全", "职位=管理员",
	}
}

// initializeDataValidationPayloads 初始化数据验证载荷列表
func (d *LogicVulnerabilityDetector) initializeDataValidationPayloads() {
	d.dataValidationPayloads = []string{
		// 数值验证绕过载荷
		"age=-1", "age=999", "age=0", "year=1800", "year=2200",
		"month=13", "month=0", "day=32", "day=0", "hour=25", "hour=-1",
		"minute=61", "minute=-1", "second=61", "second=-1",

		// 长度验证绕过载荷
		"name=" + strings.Repeat("A", 1000), "title=" + strings.Repeat("B", 500),
		"description=" + strings.Repeat("C", 2000), "content=" + strings.Repeat("D", 5000),
		"comment=" + strings.Repeat("E", 1000), "message=" + strings.Repeat("F", 2000),

		// 格式验证绕过载荷
		"email=invalid", "email=@", "email=test@", "email=@test",
		"phone=invalid", "phone=123", "phone=abc", "url=invalid",
		"ip=invalid", "ip=999.999.999.999", "ip=256.256.256.256",

		// 类型验证绕过载荷
		"id=string", "count=text", "price=invalid", "amount=abc",
		"number=text", "integer=float", "boolean=maybe", "date=invalid",
		"time=invalid", "datetime=wrong", "timestamp=text",

		// 范围验证绕过载荷
		"score=-100", "score=1000", "rating=-5", "rating=15",
		"percentage=-10", "percentage=110", "level=-1", "level=999",
		"priority=-1", "priority=100", "weight=-1", "weight=1000",

		// 必填验证绕过载荷
		"required=", "mandatory=", "essential=", "necessary=",
		"important=", "critical=", "vital=", "key=",
		"primary=", "main=", "basic=", "fundamental=",

		// 唯一性验证绕过载荷
		"username=admin", "email=<EMAIL>", "phone=1234567890",
		"id=1", "code=DUPLICATE", "serial=SAME", "number=REPEAT",
		"reference=COPY", "identifier=CLONE", "key=DUPLICATE",

		// 业务规则验证绕过载荷
		"start_date=2099-01-01", "end_date=1900-01-01", "from=future", "to=past",
		"min_value=100", "max_value=1", "lower=high", "upper=low",
		"begin=end", "first=last", "initial=final", "start=finish",

		// 中文数据验证载荷
		"年龄=-1", "姓名=" + strings.Repeat("测", 100), "邮箱=无效",
		"电话=无效", "分数=-100", "必填=", "唯一=重复",
		"开始=结束", "最小=最大", "范围=超出", "格式=错误",
	}
}

// initializeProcessControlPayloads 初始化流程控制载荷列表
func (d *LogicVulnerabilityDetector) initializeProcessControlPayloads() {
	d.processControlPayloads = []string{
		// 流程跳过载荷
		"step=skip", "stage=bypass", "phase=jump", "process=shortcut",
		"workflow=fast", "procedure=quick", "method=direct", "path=straight",
		"route=express", "way=immediate", "mode=instant", "type=rapid",

		// 状态操作载荷
		"status=completed", "state=finished", "condition=done", "situation=ready",
		"position=final", "stage=last", "step=end", "phase=complete",
		"progress=100", "percent=100", "ratio=1.0", "fraction=1",

		// 审批绕过载荷
		"approval=auto", "review=skip", "check=bypass", "verify=ignore",
		"validate=false", "confirm=auto", "authorize=immediate", "permit=granted",
		"allow=true", "enable=auto", "activate=instant", "approve=yes",

		// 时间控制载荷
		"wait=0", "delay=0", "timeout=0", "interval=0",
		"duration=0", "period=0", "time=0", "timer=off",
		"schedule=now", "timing=immediate", "when=instant", "at=now",

		// 条件绕过载荷
		"if=true", "when=always", "unless=false", "condition=met",
		"requirement=satisfied", "prerequisite=fulfilled", "dependency=resolved",
		"constraint=removed", "limitation=lifted", "restriction=bypassed",

		// 循环控制载荷
		"loop=break", "iteration=stop", "repeat=end", "cycle=finish",
		"continue=false", "next=skip", "previous=ignore", "current=last",
		"index=max", "counter=limit", "number=final", "position=end",

		// 分支控制载荷
		"branch=main", "path=direct", "route=shortest", "direction=straight",
		"choice=best", "option=optimal", "selection=perfect", "decision=final",
		"alternative=primary", "variant=standard", "version=latest", "edition=final",

		// 异常处理载荷
		"error=ignore", "exception=skip", "failure=continue", "problem=bypass",
		"issue=resolve", "trouble=fix", "difficulty=overcome", "obstacle=remove",
		"barrier=break", "block=clear", "stop=continue", "halt=proceed",

		// 权限流程载荷
		"auth_step=skip", "login_required=false", "permission_check=bypass",
		"access_control=disable", "security_check=ignore", "validation_step=skip",
		"verification_process=bypass", "authorization_flow=shortcut",

		// 业务流程载荷
		"payment_step=skip", "shipping_step=bypass", "billing_step=ignore",
		"order_process=complete", "checkout_flow=finish", "purchase_step=final",
		"transaction_stage=done", "processing_step=end", "fulfillment_stage=complete",

		// 中文流程控制载荷
		"步骤=跳过", "阶段=绕过", "流程=完成", "状态=完成",
		"审批=自动", "检查=跳过", "验证=忽略", "确认=自动",
		"等待=0", "延迟=0", "条件=满足", "要求=达到",
		"循环=结束", "分支=主要", "异常=忽略", "错误=跳过",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *LogicVulnerabilityDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 逻辑漏洞相关参数
		"logic", "business", "process", "workflow", "procedure", "method",
		"operation", "action", "function", "feature", "capability", "service",
		"logic_check", "business_rule", "process_control", "workflow_step",

		// 权限相关参数
		"permission", "access", "privilege", "right", "authority", "power",
		"role", "group", "level", "rank", "grade", "class", "type",
		"user", "username", "userid", "uid", "account", "profile",
		"admin", "administrator", "root", "super", "manager", "owner",

		// 业务相关参数
		"order", "payment", "transaction", "purchase", "sale", "buy",
		"sell", "trade", "exchange", "transfer", "move", "send",
		"price", "amount", "cost", "fee", "charge", "rate", "value",
		"money", "cash", "credit", "debit", "balance", "fund", "budget",

		// 数据相关参数
		"data", "info", "information", "content", "text", "value",
		"input", "output", "param", "parameter", "arg", "argument",
		"field", "column", "attribute", "property", "variable", "element",
		"record", "row", "entry", "item", "object", "entity", "model",

		// 验证相关参数
		"validate", "verify", "check", "confirm", "approve", "authorize",
		"permit", "allow", "enable", "activate", "grant", "accept",
		"validation", "verification", "confirmation", "approval", "authorization",
		"permission", "allowance", "enablement", "activation", "grant", "acceptance",

		// 流程相关参数
		"step", "stage", "phase", "process", "procedure", "workflow",
		"flow", "sequence", "order", "series", "chain", "path",
		"route", "way", "method", "approach", "technique", "strategy",
		"plan", "scheme", "design", "pattern", "structure", "framework",

		// 状态相关参数
		"status", "state", "condition", "situation", "position", "stage",
		"phase", "step", "level", "grade", "rank", "class", "type",
		"mode", "style", "form", "shape", "format", "pattern", "structure",

		// 控制相关参数
		"control", "manage", "handle", "operate", "run", "execute",
		"perform", "conduct", "carry", "implement", "apply", "use",
		"controller", "manager", "handler", "operator", "runner", "executor",
		"performer", "conductor", "carrier", "implementer", "applier", "user",

		// 中文参数
		"逻辑", "业务", "流程", "工作流", "程序", "方法", "操作",
		"权限", "访问", "特权", "角色", "用户", "管理", "管理员",
		"订单", "支付", "交易", "购买", "销售", "价格", "金额",
		"数据", "信息", "内容", "参数", "字段", "属性", "变量",
		"验证", "确认", "批准", "授权", "允许", "启用", "激活",
		"步骤", "阶段", "流程", "顺序", "路径", "方式", "方法",
		"状态", "条件", "情况", "位置", "模式", "类型", "格式",
		"控制", "管理", "处理", "操作", "运行", "执行", "实施",
	}
}

// initializePatterns 初始化检测模式
func (d *LogicVulnerabilityDetector) initializePatterns() {
	// 逻辑漏洞检测模式 - 检测逻辑漏洞相关的响应内容
	logicPatternStrings := []string{
		// 通用逻辑漏洞模式
		`(?i)logic\s+(error|exception|vulnerability)`,
		`(?i)business\s+(logic|rule)\s+(error|violation)`,
		`(?i)validation\s+(error|failed|bypass)`,
		`(?i)permission\s+(denied|granted|bypass)`,
		`(?i)access\s+(denied|granted|control)`,
		`(?i)authorization\s+(failed|success|bypass)`,

		// 权限相关模式
		`(?i)admin\s+(panel|dashboard|interface)`,
		`(?i)administrator\s+(access|login|panel)`,
		`(?i)root\s+(access|login|privilege)`,
		`(?i)superuser\s+(access|login|privilege)`,
		`(?i)elevated\s+(privilege|access|permission)`,
		`(?i)privilege\s+(escalation|elevation)`,

		// 业务流程模式
		`(?i)workflow\s+(completed|skipped|bypassed)`,
		`(?i)process\s+(completed|skipped|bypassed)`,
		`(?i)step\s+(completed|skipped|bypassed)`,
		`(?i)stage\s+(completed|skipped|bypassed)`,
		`(?i)approval\s+(granted|bypassed|skipped)`,
		`(?i)review\s+(completed|skipped|bypassed)`,

		// 状态控制模式
		`(?i)status\s+(changed|updated|modified)`,
		`(?i)state\s+(changed|updated|modified)`,
		`(?i)condition\s+(met|satisfied|bypassed)`,
		`(?i)requirement\s+(met|satisfied|bypassed)`,

		// 中文逻辑模式
		`(?i)(逻辑|业务).*错误`,
		`(?i)(权限|访问).*错误`,
		`(?i)(验证|确认).*失败`,
		`(?i)(流程|步骤).*跳过`,
		`(?i)(状态|条件).*改变`,
	}

	d.logicPatterns = make([]*regexp.Regexp, 0, len(logicPatternStrings))
	for _, pattern := range logicPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.logicPatterns = append(d.logicPatterns, compiled)
		}
	}

	// 业务逻辑检测模式 - 检测业务逻辑相关的响应内容
	businessPatternStrings := []string{
		// 价格相关模式
		`(?i)price\s*[:=]\s*-?\d+`,
		`(?i)amount\s*[:=]\s*-?\d+`,
		`(?i)cost\s*[:=]\s*-?\d+`,
		`(?i)total\s*[:=]\s*-?\d+`,
		`(?i)balance\s*[:=]\s*-?\d+`,
		`(?i)discount\s*[:=]\s*\d+%?`,

		// 订单相关模式
		`(?i)order\s+(completed|approved|processed)`,
		`(?i)payment\s+(success|completed|approved)`,
		`(?i)transaction\s+(success|completed|approved)`,
		`(?i)purchase\s+(success|completed|approved)`,

		// 数量相关模式
		`(?i)quantity\s*[:=]\s*-?\d+`,
		`(?i)count\s*[:=]\s*-?\d+`,
		`(?i)stock\s*[:=]\s*-?\d+`,
		`(?i)inventory\s*[:=]\s*-?\d+`,

		// 业务规则模式
		`(?i)business\s+(rule|logic)\s+(passed|failed)`,
		`(?i)validation\s+(passed|failed|skipped)`,
		`(?i)constraint\s+(satisfied|violated|bypassed)`,
		`(?i)limit\s+(exceeded|bypassed|ignored)`,

		// 中文业务模式
		`(?i)(价格|金额|数量).*[:=]\s*-?\d+`,
		`(?i)(订单|支付|交易).*成功`,
		`(?i)(业务|规则|验证).*通过`,
		`(?i)(限制|约束).*绕过`,
	}

	d.businessPatterns = make([]*regexp.Regexp, 0, len(businessPatternStrings))
	for _, pattern := range businessPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.businessPatterns = append(d.businessPatterns, compiled)
		}
	}

	// 权限控制检测模式 - 检测权限控制相关的响应内容
	permissionPatternStrings := []string{
		// 权限提升模式
		`(?i)admin\s+(access|login|panel|dashboard)`,
		`(?i)administrator\s+(access|login|panel)`,
		`(?i)root\s+(access|login|shell|privilege)`,
		`(?i)superuser\s+(access|login|privilege)`,
		`(?i)elevated\s+(privilege|access|permission)`,

		// 角色相关模式
		`(?i)role\s*[:=]\s*(admin|administrator|root)`,
		`(?i)user\s+(role|type|level)\s*[:=]\s*admin`,
		`(?i)permission\s+(granted|allowed|authorized)`,
		`(?i)access\s+(granted|allowed|authorized)`,

		// 权限检查模式
		`(?i)permission\s+(check|validation)\s+(passed|failed|skipped)`,
		`(?i)access\s+(control|check)\s+(passed|failed|bypassed)`,
		`(?i)authorization\s+(check|validation)\s+(passed|failed|skipped)`,

		// 会话相关模式
		`(?i)session\s+(elevated|admin|privileged)`,
		`(?i)token\s+(admin|privileged|elevated)`,
		`(?i)cookie\s+(admin|privileged|elevated)`,

		// 中文权限模式
		`(?i)(管理员|管理|权限).*访问`,
		`(?i)(角色|用户).*管理员`,
		`(?i)(权限|访问).*授权`,
		`(?i)(会话|令牌).*提升`,
	}

	d.permissionPatterns = make([]*regexp.Regexp, 0, len(permissionPatternStrings))
	for _, pattern := range permissionPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.permissionPatterns = append(d.permissionPatterns, compiled)
		}
	}

	// 数据验证检测模式 - 检测数据验证相关的响应内容
	validationPatternStrings := []string{
		// 验证错误模式
		`(?i)validation\s+(error|failed|exception)`,
		`(?i)invalid\s+(input|data|format|value)`,
		`(?i)format\s+(error|invalid|incorrect)`,
		`(?i)type\s+(error|mismatch|invalid)`,
		`(?i)range\s+(error|exceeded|invalid)`,

		// 验证绕过模式
		`(?i)validation\s+(bypassed|skipped|ignored)`,
		`(?i)check\s+(bypassed|skipped|ignored)`,
		`(?i)constraint\s+(bypassed|violated|ignored)`,
		`(?i)rule\s+(bypassed|violated|ignored)`,

		// 数据接受模式
		`(?i)data\s+(accepted|saved|stored|processed)`,
		`(?i)input\s+(accepted|valid|processed)`,
		`(?i)value\s+(accepted|valid|processed)`,
		`(?i)field\s+(accepted|valid|updated)`,

		// 长度相关模式
		`(?i)length\s+(exceeded|invalid|too\s+long)`,
		`(?i)size\s+(exceeded|invalid|too\s+large)`,
		`(?i)limit\s+(exceeded|reached|bypassed)`,

		// 中文验证模式
		`(?i)(验证|检查).*失败`,
		`(?i)(格式|类型).*错误`,
		`(?i)(数据|输入).*接受`,
		`(?i)(长度|大小).*超出`,
	}

	d.validationPatterns = make([]*regexp.Regexp, 0, len(validationPatternStrings))
	for _, pattern := range validationPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.validationPatterns = append(d.validationPatterns, compiled)
		}
	}
}
