package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectHTTPMethodBypass 检测HTTP方法绕过
func (d *AuthenticationBypassDetector) detectHTTPMethodBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 首先发送标准GET请求作为基线
	baselineResp, err := d.sendAuthRequest(ctx, target.URL, "GET", nil)
	if err != nil {
		return evidence, 0.0, "", "", ""
	}

	// 测试不同HTTP方法
	for _, method := range d.httpMethods {
		if method == "GET" {
			continue // 跳过基线方法
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送HTTP方法请求
		resp, err := d.sendAuthRequest(ctx, target.URL, method, nil)
		if err != nil {
			continue
		}

		// 检查HTTP方法绕过响应
		confidence := d.checkHTTPMethodBypassResponse(resp, baselineResp, method)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = method
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "http-method-bypass",
				Description: fmt.Sprintf("发现HTTP方法绕过: %s (置信度: %.2f)", method, confidence),
				Content:     d.extractAuthEvidence(resp, method),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 800)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPathTraversalBypass 检测路径遍历绕过
func (d *AuthenticationBypassDetector) detectPathTraversalBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	for _, bypassPath := range d.bypassPaths {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造绕过URL
		bypassURL := d.buildBypassURL(target.URL, bypassPath)

		// 发送路径遍历请求
		resp, err := d.sendAuthRequest(ctx, bypassURL, "GET", nil)
		if err != nil {
			continue
		}

		// 检查路径遍历绕过响应
		confidence := d.checkPathTraversalBypassResponse(resp, bypassPath)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = bypassPath
			vulnerableRequest = bypassURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "path-traversal-bypass",
				Description: fmt.Sprintf("发现路径遍历绕过: %s (置信度: %.2f)", bypassPath, confidence),
				Content:     d.extractAuthEvidence(resp, bypassPath),
				Location:    bypassURL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 600)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectUnauthorizedAccess 检测未授权访问
func (d *AuthenticationBypassDetector) detectUnauthorizedAccess(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	for _, endpoint := range d.authEndpoints {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造认证端点URL
		authURL := d.buildAuthURL(target.URL, endpoint)

		// 发送未授权访问请求
		resp, err := d.sendAuthRequest(ctx, authURL, "GET", nil)
		if err != nil {
			continue
		}

		// 检查未授权访问响应
		confidence := d.checkUnauthorizedAccessResponse(resp, endpoint)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = endpoint
			vulnerableRequest = authURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "unauthorized-access",
				Description: fmt.Sprintf("发现未授权访问: %s (置信度: %.2f)", endpoint, confidence),
				Content:     d.extractAuthEvidence(resp, endpoint),
				Location:    authURL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 1000)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectJWTVulnerabilities 检测JWT漏洞
func (d *AuthenticationBypassDetector) detectJWTVulnerabilities(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 首先检查是否存在JWT令牌
	jwtToken := d.extractJWTToken(target)
	if jwtToken == "" {
		return evidence, 0.0, "", "", ""
	}

	// 测试JWT弱密钥
	for _, secret := range d.jwtSecrets {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 尝试使用弱密钥验证JWT
		if d.verifyJWTWithSecret(jwtToken, secret) {
			confidence := 0.9 // JWT弱密钥是高置信度漏洞

			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("JWT弱密钥: %s", secret)
				vulnerableRequest = target.URL
				vulnerableResponse = fmt.Sprintf("JWT令牌: %s", jwtToken)
			}

			evidence = append(evidence, plugins.Evidence{
				Type:        "jwt-weak-secret",
				Description: fmt.Sprintf("发现JWT弱密钥: %s (置信度: %.2f)", secret, confidence),
				Content:     fmt.Sprintf("JWT令牌可以使用弱密钥 '%s' 进行验证", secret),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 500)
	}

	// 测试JWT算法混淆攻击
	if d.testJWTAlgorithmConfusion(jwtToken) {
		confidence := 0.8

		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = "JWT算法混淆"
			vulnerableRequest = target.URL
			vulnerableResponse = fmt.Sprintf("JWT令牌: %s", jwtToken)
		}

		evidence = append(evidence, plugins.Evidence{
			Type:        "jwt-algorithm-confusion",
			Description: fmt.Sprintf("发现JWT算法混淆漏洞 (置信度: %.2f)", confidence),
			Content:     "JWT令牌可能存在算法混淆漏洞，允许攻击者绕过签名验证",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendAuthRequest 发送认证请求
func (d *AuthenticationBypassDetector) sendAuthRequest(ctx context.Context, targetURL, method string, headers map[string]string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, method, targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置默认请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	// 设置自定义头部
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// buildBypassURL 构造绕过URL
func (d *AuthenticationBypassDetector) buildBypassURL(baseURL, bypassPath string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + bypassPath
	}

	// 在路径前添加绕过路径
	if strings.HasSuffix(parsedURL.Path, "/") {
		parsedURL.Path = parsedURL.Path + bypassPath
	} else {
		parsedURL.Path = parsedURL.Path + "/" + bypassPath
	}

	return parsedURL.String()
}

// buildAuthURL 构造认证URL
func (d *AuthenticationBypassDetector) buildAuthURL(baseURL, endpoint string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + endpoint
	}

	// 替换路径为认证端点
	parsedURL.Path = endpoint

	return parsedURL.String()
}

// extractJWTToken 提取JWT令牌
func (d *AuthenticationBypassDetector) extractJWTToken(target *plugins.ScanTarget) string {
	// 从Authorization头中提取
	if auth, exists := target.Headers["Authorization"]; exists {
		if strings.HasPrefix(auth, "Bearer ") {
			token := strings.TrimPrefix(auth, "Bearer ")
			if d.isValidJWTFormat(token) {
				return token
			}
		}
	}

	// 从Cookie中提取
	for _, cookie := range target.Cookies {
		if strings.Contains(strings.ToLower(cookie.Name), "jwt") ||
			strings.Contains(strings.ToLower(cookie.Name), "token") {
			if d.isValidJWTFormat(cookie.Value) {
				return cookie.Value
			}
		}
	}

	return ""
}

// isValidJWTFormat 检查是否是有效的JWT格式
func (d *AuthenticationBypassDetector) isValidJWTFormat(token string) bool {
	parts := strings.Split(token, ".")
	return len(parts) == 3 && len(parts[0]) > 0 && len(parts[1]) > 0
}

// verifyJWTWithSecret 使用密钥验证JWT
func (d *AuthenticationBypassDetector) verifyJWTWithSecret(token, secret string) bool {
	// 这里应该实现实际的JWT验证逻辑
	// 为了简化，我们只检查令牌格式和密钥长度
	if !d.isValidJWTFormat(token) {
		return false
	}

	// 简单的启发式检查：如果密钥太短或太常见，可能是弱密钥
	if len(secret) < 8 {
		return true
	}

	commonSecrets := []string{"secret", "password", "123456", "admin", "test"}
	for _, common := range commonSecrets {
		if secret == common {
			return true
		}
	}

	return false
}

// testJWTAlgorithmConfusion 测试JWT算法混淆
func (d *AuthenticationBypassDetector) testJWTAlgorithmConfusion(token string) bool {
	// 这里应该实现实际的JWT算法混淆测试
	// 为了简化，我们只检查令牌是否使用了可能存在混淆的算法
	if !d.isValidJWTFormat(token) {
		return false
	}

	// 简单的启发式检查：检查头部是否包含可能的算法混淆指示器
	parts := strings.Split(token, ".")
	if len(parts) >= 1 {
		// 这里应该解码JWT头部并检查算法
		// 为了简化，我们假设某些情况下存在算法混淆
		return len(parts[0]) > 20 // 简单的启发式
	}

	return false
}

// checkHTTPMethodBypassResponse 检查HTTP方法绕过响应
func (d *AuthenticationBypassDetector) checkHTTPMethodBypassResponse(response, baseline, method string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)
	baseline = strings.ToLower(baseline)

	// 检查HTTP状态码变化
	if strings.Contains(response, "status: 200") && !strings.Contains(baseline, "status: 200") {
		confidence += 0.4
	} else if strings.Contains(response, "status: 302") && !strings.Contains(baseline, "status: 302") {
		confidence += 0.3
	}

	// 检查响应内容长度变化
	if len(response) > len(baseline)*2 {
		confidence += 0.2
	}

	// 检查认证模式匹配
	for _, pattern := range d.authPatterns {
		if pattern.MatchString(response) && !pattern.MatchString(baseline) {
			confidence += 0.3
			break
		}
	}

	// 检查绕过模式匹配
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查特定HTTP方法的绕过特征
	if method == "PUT" || method == "DELETE" || method == "PATCH" {
		if strings.Contains(response, "method not allowed") && strings.Contains(baseline, "unauthorized") {
			confidence += 0.3
		}
	}

	return confidence
}

// checkPathTraversalBypassResponse 检查路径遍历绕过响应
func (d *AuthenticationBypassDetector) checkPathTraversalBypassResponse(response, path string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.4
	}

	// 检查路径遍历成功指示器
	traversalIndicators := []string{
		"root:", "bin/", "etc/", "var/", "usr/", "home/",
		"windows", "system32", "program files", "users",
		"config", "passwd", "shadow", "hosts",
		"web.config", "app.config", "database.php",
	}

	for _, indicator := range traversalIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5
			break
		}
	}

	// 检查绕过模式匹配
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查特定路径的绕过特征
	if strings.Contains(path, "..") && strings.Contains(response, "directory") {
		confidence += 0.2
	}

	return confidence
}

// checkUnauthorizedAccessResponse 检查未授权访问响应
func (d *AuthenticationBypassDetector) checkUnauthorizedAccessResponse(response, endpoint string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.4
	} else if strings.Contains(response, "status: 302") {
		confidence += 0.3
	}

	// 检查未授权访问成功指示器
	accessIndicators := []string{
		"welcome", "dashboard", "admin panel", "control panel",
		"user list", "admin list", "configuration", "settings",
		"database", "api documentation", "system info",
		"欢迎", "仪表板", "管理面板", "控制面板", "用户列表",
	}

	for _, indicator := range accessIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
			break
		}
	}

	// 检查认证模式匹配
	for _, pattern := range d.authPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查绕过模式匹配
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查特定端点的访问特征
	if strings.Contains(endpoint, "admin") && strings.Contains(response, "admin") {
		confidence += 0.2
	}
	if strings.Contains(endpoint, "api") && strings.Contains(response, "api") {
		confidence += 0.2
	}

	return confidence
}

// checkAuthenticationBypassResponse 检查认证绕过响应（通用方法）
func (d *AuthenticationBypassDetector) checkAuthenticationBypassResponse(response, method string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查认证模式匹配
	for _, pattern := range d.authPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查绕过模式匹配
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	return confidence
}

// extractAuthEvidence 提取认证证据
func (d *AuthenticationBypassDetector) extractAuthEvidence(response, authInfo string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 查找包含认证相关信息的行
	var authLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)
		if strings.Contains(lineLower, "auth") ||
			strings.Contains(lineLower, "login") ||
			strings.Contains(lineLower, "session") ||
			strings.Contains(lineLower, "token") ||
			strings.Contains(lineLower, "admin") ||
			strings.Contains(lineLower, "user") ||
			strings.Contains(lineLower, "welcome") ||
			strings.Contains(lineLower, "dashboard") ||
			strings.Contains(lineLower, "认证") ||
			strings.Contains(lineLower, "登录") ||
			strings.Contains(lineLower, "用户") ||
			strings.Contains(lineLower, "管理") {
			authLines = append(authLines, line)
			if len(authLines) >= 5 { // 只取前5行
				break
			}
		}
	}

	if len(authLines) > 0 {
		return strings.Join(authLines, "\n")
	}

	// 如果没有找到特定的认证信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
