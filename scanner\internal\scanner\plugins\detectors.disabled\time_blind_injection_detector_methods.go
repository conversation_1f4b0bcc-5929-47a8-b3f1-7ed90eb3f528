package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// establishBaseline 建立基准响应时间
func (d *TimeBlindInjectionDetector) establishBaseline(ctx context.Context, target *plugins.ScanTarget) error {
	// 发送3次正常请求来建立基准时间
	var totalTime time.Duration
	successCount := 0

	for i := 0; i < 3; i++ {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		start := time.Now()
		_, err := d.sendNormalRequest(ctx, target.URL)
		elapsed := time.Since(start)

		if err == nil {
			totalTime += elapsed
			successCount++
		}

		// 添加小延迟避免过于频繁的请求
		time.Sleep(time.Millisecond * 100)
	}

	if successCount == 0 {
		return fmt.Errorf("无法建立基准时间，所有请求都失败")
	}

	// 计算平均基准时间
	d.baselineTime = totalTime / time.Duration(successCount)

	// 设置延迟阈值为基准时间的3倍，但至少3秒
	d.delayThreshold = d.baselineTime * 3
	if d.delayThreshold < 3*time.Second {
		d.delayThreshold = 3 * time.Second
	}

	return nil
}

// sendNormalRequest 发送正常请求
func (d *TimeBlindInjectionDetector) sendNormalRequest(ctx context.Context, targetURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// detectSQLTimeBlindInjection 检测SQL时间盲注
func (d *TimeBlindInjectionDetector) detectSQLTimeBlindInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试SQL时间盲注载荷
	for _, payload := range d.sqlTimePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送时间盲注请求
		response, responseTime, err := d.sendTimeBlindInjectionRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在时间延迟
		confidence := d.checkTimeDelay(responseTime, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("SQL时间盲注: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "sql-time-blind-injection",
				Description: fmt.Sprintf("发现SQL时间盲注: %s (响应时间: %v, 置信度: %.2f)", payload, responseTime, confidence),
				Content:     d.extractTimeBlindInjectionEvidence(response, payload, responseTime),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectNoSQLTimeBlindInjection 检测NoSQL时间盲注
func (d *TimeBlindInjectionDetector) detectNoSQLTimeBlindInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试NoSQL时间盲注载荷
	for _, payload := range d.nosqlTimePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送时间盲注请求
		response, responseTime, err := d.sendTimeBlindInjectionRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在时间延迟
		confidence := d.checkTimeDelay(responseTime, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("NoSQL时间盲注: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "nosql-time-blind-injection",
				Description: fmt.Sprintf("发现NoSQL时间盲注: %s (响应时间: %v, 置信度: %.2f)", payload, responseTime, confidence),
				Content:     d.extractTimeBlindInjectionEvidence(response, payload, responseTime),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 250)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCommandTimeBlindInjection 检测命令时间盲注
func (d *TimeBlindInjectionDetector) detectCommandTimeBlindInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试命令时间盲注载荷
	for _, payload := range d.commandTimePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送时间盲注请求
		response, responseTime, err := d.sendTimeBlindInjectionRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在时间延迟
		confidence := d.checkTimeDelay(responseTime, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("命令时间盲注: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "command-time-blind-injection",
				Description: fmt.Sprintf("发现命令时间盲注: %s (响应时间: %v, 置信度: %.2f)", payload, responseTime, confidence),
				Content:     d.extractTimeBlindInjectionEvidence(response, payload, responseTime),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCodeTimeBlindInjection 检测代码时间盲注
func (d *TimeBlindInjectionDetector) detectCodeTimeBlindInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试代码时间盲注载荷
	for _, payload := range d.codeTimePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送时间盲注请求
		response, responseTime, err := d.sendTimeBlindInjectionRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在时间延迟
		confidence := d.checkTimeDelay(responseTime, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("代码时间盲注: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "code-time-blind-injection",
				Description: fmt.Sprintf("发现代码时间盲注: %s (响应时间: %v, 置信度: %.2f)", payload, responseTime, confidence),
				Content:     d.extractTimeBlindInjectionEvidence(response, payload, responseTime),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 350)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendTimeBlindInjectionRequest 发送时间盲注请求
func (d *TimeBlindInjectionDetector) sendTimeBlindInjectionRequest(ctx context.Context, targetURL, payload string) (string, time.Duration, error) {
	// 记录开始时间
	start := time.Now()

	// 尝试GET参数注入
	getResp, err := d.sendTimeBlindGETRequest(ctx, targetURL, payload)
	elapsed := time.Since(start)
	if err == nil && getResp != "" {
		return getResp, elapsed, nil
	}

	// 重新记录时间用于POST请求
	start = time.Now()

	// 尝试POST表单注入
	postResp, err := d.sendTimeBlindPOSTRequest(ctx, targetURL, payload)
	elapsed = time.Since(start)
	if err == nil && postResp != "" {
		return postResp, elapsed, nil
	}

	// 如果有POST响应（即使有错误），也返回
	if postResp != "" {
		return postResp, elapsed, nil
	}

	return "", elapsed, fmt.Errorf("所有时间盲注请求都失败")
}

// sendTimeBlindGETRequest 发送时间盲注GET请求
func (d *TimeBlindInjectionDetector) sendTimeBlindGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// sendTimeBlindPOSTRequest 发送时间盲注POST请求
func (d *TimeBlindInjectionDetector) sendTimeBlindPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// checkTimeDelay 检查时间延迟
func (d *TimeBlindInjectionDetector) checkTimeDelay(responseTime time.Duration, payload string) float64 {
	// 基础置信度
	confidence := 0.0

	// 检查响应时间是否超过延迟阈值
	if responseTime >= d.delayThreshold {
		// 计算延迟倍数
		delayMultiplier := float64(responseTime) / float64(d.baselineTime)

		// 根据延迟倍数计算置信度
		if delayMultiplier >= 10.0 {
			confidence = 0.9 // 非常高的置信度
		} else if delayMultiplier >= 5.0 {
			confidence = 0.8 // 高置信度
		} else if delayMultiplier >= 3.0 {
			confidence = 0.7 // 中等置信度
		} else if delayMultiplier >= 2.0 {
			confidence = 0.6 // 较低置信度
		} else {
			confidence = 0.5 // 最低置信度
		}
	}

	// 检查载荷类型，调整置信度
	payloadLower := strings.ToLower(payload)

	// SQL时间盲注载荷通常更可靠
	if strings.Contains(payloadLower, "sleep") ||
		strings.Contains(payloadLower, "waitfor") ||
		strings.Contains(payloadLower, "pg_sleep") ||
		strings.Contains(payloadLower, "benchmark") {
		confidence += 0.1
	}

	// 命令时间盲注载荷
	if strings.Contains(payloadLower, "sleep ") ||
		strings.Contains(payloadLower, "timeout") ||
		strings.Contains(payloadLower, "ping") {
		confidence += 0.05
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}
	if confidence < 0.0 {
		confidence = 0.0
	}

	return confidence
}

// extractTimeBlindInjectionEvidence 提取时间盲注证据
func (d *TimeBlindInjectionDetector) extractTimeBlindInjectionEvidence(response, payload string, responseTime time.Duration) string {
	// 限制证据长度
	maxLength := 1000
	evidence := fmt.Sprintf("时间盲注证据:\n")

	// 添加时间信息
	evidence += fmt.Sprintf("载荷: %s\n", payload)
	evidence += fmt.Sprintf("响应时间: %v\n", responseTime)
	evidence += fmt.Sprintf("基准时间: %v\n", d.baselineTime)
	evidence += fmt.Sprintf("延迟阈值: %v\n", d.delayThreshold)

	// 计算延迟倍数
	if d.baselineTime > 0 {
		delayMultiplier := float64(responseTime) / float64(d.baselineTime)
		evidence += fmt.Sprintf("延迟倍数: %.2fx\n", delayMultiplier)
	}

	// 检查响应中的时间相关模式
	evidence += "\n时间模式匹配:\n"
	for _, pattern := range d.timePatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现时间模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的延迟相关模式
	evidence += "\n延迟模式匹配:\n"
	for _, pattern := range d.delayPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现延迟模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的注入相关模式
	evidence += "\n注入模式匹配:\n"
	for _, pattern := range d.injectionPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现注入模式: %s\n", pattern.String())
		}
	}

	// 添加响应摘要
	if len(response) > 200 {
		evidence += fmt.Sprintf("\n响应摘要: %s...\n", response[:200])
	} else {
		evidence += fmt.Sprintf("\n响应内容: %s\n", response)
	}

	// 分析载荷类型
	evidence += d.analyzePayloadType(payload)

	// 限制证据长度
	if len(evidence) > maxLength {
		evidence = evidence[:maxLength] + "..."
	}

	return evidence
}

// analyzePayloadType 分析载荷类型
func (d *TimeBlindInjectionDetector) analyzePayloadType(payload string) string {
	analysis := "\n载荷分析:\n"
	payloadLower := strings.ToLower(payload)

	// SQL时间盲注分析
	if strings.Contains(payloadLower, "sleep") ||
		strings.Contains(payloadLower, "waitfor") ||
		strings.Contains(payloadLower, "pg_sleep") ||
		strings.Contains(payloadLower, "benchmark") ||
		strings.Contains(payloadLower, "dbms_lock") {
		analysis += "- 载荷类型: SQL时间盲注\n"

		if strings.Contains(payloadLower, "mysql") || strings.Contains(payloadLower, "sleep(") {
			analysis += "- 数据库类型: MySQL\n"
		} else if strings.Contains(payloadLower, "pg_sleep") {
			analysis += "- 数据库类型: PostgreSQL\n"
		} else if strings.Contains(payloadLower, "waitfor") {
			analysis += "- 数据库类型: SQL Server\n"
		} else if strings.Contains(payloadLower, "dbms_lock") {
			analysis += "- 数据库类型: Oracle\n"
		}
	}

	// NoSQL时间盲注分析
	if strings.Contains(payloadLower, "$where") ||
		strings.Contains(payloadLower, "mongodb") ||
		strings.Contains(payloadLower, "redis") ||
		strings.Contains(payloadLower, "couchdb") ||
		strings.Contains(payloadLower, "elasticsearch") {
		analysis += "- 载荷类型: NoSQL时间盲注\n"

		if strings.Contains(payloadLower, "$where") || strings.Contains(payloadLower, "mongodb") {
			analysis += "- 数据库类型: MongoDB\n"
		} else if strings.Contains(payloadLower, "redis") {
			analysis += "- 数据库类型: Redis\n"
		} else if strings.Contains(payloadLower, "couchdb") {
			analysis += "- 数据库类型: CouchDB\n"
		} else if strings.Contains(payloadLower, "elasticsearch") {
			analysis += "- 数据库类型: Elasticsearch\n"
		}
	}

	// 命令时间盲注分析
	if strings.Contains(payloadLower, "sleep ") ||
		strings.Contains(payloadLower, "timeout") ||
		strings.Contains(payloadLower, "ping") ||
		strings.Contains(payloadLower, "start-sleep") {
		analysis += "- 载荷类型: 命令时间盲注\n"

		if strings.Contains(payloadLower, "sleep ") {
			analysis += "- 操作系统: Linux/Unix\n"
		} else if strings.Contains(payloadLower, "timeout") || strings.Contains(payloadLower, "ping") {
			analysis += "- 操作系统: Windows\n"
		} else if strings.Contains(payloadLower, "start-sleep") {
			analysis += "- Shell类型: PowerShell\n"
		}
	}

	// 代码时间盲注分析
	if strings.Contains(payloadLower, "time.sleep") ||
		strings.Contains(payloadLower, "thread.sleep") ||
		strings.Contains(payloadLower, "settimeout") ||
		strings.Contains(payloadLower, "usleep") {
		analysis += "- 载荷类型: 代码时间盲注\n"

		if strings.Contains(payloadLower, "time.sleep") {
			analysis += "- 编程语言: Python\n"
		} else if strings.Contains(payloadLower, "thread.sleep") {
			analysis += "- 编程语言: Java/C#\n"
		} else if strings.Contains(payloadLower, "settimeout") {
			analysis += "- 编程语言: JavaScript\n"
		} else if strings.Contains(payloadLower, "usleep") {
			analysis += "- 编程语言: PHP\n"
		}
	}

	// 检查注入点类型
	if strings.Contains(payload, "'") {
		analysis += "- 注入点类型: 字符串型\n"
	} else if strings.Contains(payload, "1 ") || strings.Contains(payload, "1;") {
		analysis += "- 注入点类型: 数字型\n"
	} else if strings.Contains(payload, "true") || strings.Contains(payload, "false") {
		analysis += "- 注入点类型: 布尔型\n"
	}

	// 检查注入方法
	if strings.Contains(payload, " AND ") {
		analysis += "- 注入方法: AND条件注入\n"
	} else if strings.Contains(payload, " OR ") {
		analysis += "- 注入方法: OR条件注入\n"
	} else if strings.Contains(payload, " UNION ") {
		analysis += "- 注入方法: UNION联合注入\n"
	} else if strings.Contains(payload, ";") {
		analysis += "- 注入方法: 堆叠查询注入\n"
	}

	return analysis
}
