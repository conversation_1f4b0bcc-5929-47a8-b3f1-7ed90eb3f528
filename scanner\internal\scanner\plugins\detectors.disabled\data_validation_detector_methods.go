package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectBoundaryValueVulnerabilities 检测边界值漏洞
func (d *DataValidationDetector) detectBoundaryValueVulnerabilities(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试边界值载荷
	for _, payload := range d.boundaryValuePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送边界值测试请求
		response, err := d.sendDataValidationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在边界值漏洞
		confidence := d.checkBoundaryValueResponse(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("边界值测试: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.4 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "boundary-value",
				Description: fmt.Sprintf("发现边界值漏洞: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractDataValidationEvidence(response, payload, "boundary-value"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 80)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectTypeConfusionVulnerabilities 检测类型混淆漏洞
func (d *DataValidationDetector) detectTypeConfusionVulnerabilities(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试类型混淆载荷
	for _, payload := range d.typeConfusionPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送类型混淆测试请求
		response, err := d.sendDataValidationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在类型混淆漏洞
		confidence := d.checkTypeConfusionResponse(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("类型混淆测试: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.4 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "type-confusion",
				Description: fmt.Sprintf("发现类型混淆漏洞: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractDataValidationEvidence(response, payload, "type-confusion"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 90)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectFormatBypassVulnerabilities 检测格式验证绕过漏洞
func (d *DataValidationDetector) detectFormatBypassVulnerabilities(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试格式验证绕过载荷
	for _, payload := range d.formatBypassPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送格式验证绕过测试请求
		response, err := d.sendDataValidationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在格式验证绕过漏洞
		confidence := d.checkFormatBypassResponse(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("格式验证绕过: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.4 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "format-bypass",
				Description: fmt.Sprintf("发现格式验证绕过漏洞: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractDataValidationEvidence(response, payload, "format-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 100)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectLengthBypassVulnerabilities 检测长度验证绕过漏洞
func (d *DataValidationDetector) detectLengthBypassVulnerabilities(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试长度验证绕过载荷
	for _, payload := range d.lengthBypassPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送长度验证绕过测试请求
		response, err := d.sendDataValidationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在长度验证绕过漏洞
		confidence := d.checkLengthBypassResponse(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("长度验证绕过: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.4 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "length-bypass",
				Description: fmt.Sprintf("发现长度验证绕过漏洞: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractDataValidationEvidence(response, payload, "length-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 110)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendDataValidationRequest 发送数据验证测试请求
func (d *DataValidationDetector) sendDataValidationRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendDataValidationGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST表单注入
	postResp, err := d.sendDataValidationPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 如果有POST响应（即使有错误），也返回
	if postResp != "" {
		return postResp, nil
	}

	return "", fmt.Errorf("所有数据验证请求都失败")
}

// sendDataValidationGETRequest 发送数据验证GET请求
func (d *DataValidationDetector) sendDataValidationGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// sendDataValidationPOSTRequest 发送数据验证POST请求
func (d *DataValidationDetector) sendDataValidationPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// checkBoundaryValueResponse 检查边界值响应
func (d *DataValidationDetector) checkBoundaryValueResponse(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查数据验证相关的响应模式
	for _, pattern := range d.validationPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查边界值相关的错误响应
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
		}
	}

	// 检查边界值成功的响应
	if strings.Contains(payloadLower, "2147483647") || strings.Contains(payloadLower, "-2147483648") {
		if strings.Contains(responseLower, "overflow") ||
			strings.Contains(responseLower, "underflow") ||
			strings.Contains(responseLower, "out of range") ||
			strings.Contains(responseLower, "溢出") ||
			strings.Contains(responseLower, "超出范围") {
			confidence += 0.4
		}
	}

	// 检查长字符串边界值
	if len(payload) > 1000 {
		if strings.Contains(responseLower, "too long") ||
			strings.Contains(responseLower, "length") ||
			strings.Contains(responseLower, "size") ||
			strings.Contains(responseLower, "太长") ||
			strings.Contains(responseLower, "长度") ||
			strings.Contains(responseLower, "大小") {
			confidence += 0.3
		}
	}

	// 检查空值边界值
	if payload == "" {
		if strings.Contains(responseLower, "required") ||
			strings.Contains(responseLower, "empty") ||
			strings.Contains(responseLower, "null") ||
			strings.Contains(responseLower, "必填") ||
			strings.Contains(responseLower, "空") ||
			strings.Contains(responseLower, "为空") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkTypeConfusionResponse 检查类型混淆响应
func (d *DataValidationDetector) checkTypeConfusionResponse(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查数据验证相关的响应模式
	for _, pattern := range d.validationPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查类型混淆相关的错误响应
	if strings.Contains(payloadLower, "true") || strings.Contains(payloadLower, "false") {
		if strings.Contains(responseLower, "boolean") ||
			strings.Contains(responseLower, "bool") ||
			strings.Contains(responseLower, "type") ||
			strings.Contains(responseLower, "布尔") ||
			strings.Contains(responseLower, "类型") {
			confidence += 0.4
		}
	}

	// 检查数字字符串混淆
	if strings.Contains(payloadLower, "123") {
		if strings.Contains(responseLower, "number") ||
			strings.Contains(responseLower, "integer") ||
			strings.Contains(responseLower, "numeric") ||
			strings.Contains(responseLower, "数字") ||
			strings.Contains(responseLower, "整数") {
			confidence += 0.3
		}
	}

	// 检查空值混淆
	if strings.Contains(payloadLower, "null") || strings.Contains(payloadLower, "undefined") {
		if strings.Contains(responseLower, "null") ||
			strings.Contains(responseLower, "undefined") ||
			strings.Contains(responseLower, "empty") ||
			strings.Contains(responseLower, "空") ||
			strings.Contains(responseLower, "未定义") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkFormatBypassResponse 检查格式验证绕过响应
func (d *DataValidationDetector) checkFormatBypassResponse(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查数据验证相关的响应模式
	for _, pattern := range d.validationPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查邮箱格式绕过
	if strings.Contains(payloadLower, "@") {
		if strings.Contains(responseLower, "email") ||
			strings.Contains(responseLower, "mail") ||
			strings.Contains(responseLower, "invalid") ||
			strings.Contains(responseLower, "format") ||
			strings.Contains(responseLower, "邮箱") ||
			strings.Contains(responseLower, "邮件") ||
			strings.Contains(responseLower, "格式") {
			confidence += 0.4
		}
	}

	// 检查URL格式绕过
	if strings.Contains(payloadLower, "http") {
		if strings.Contains(responseLower, "url") ||
			strings.Contains(responseLower, "uri") ||
			strings.Contains(responseLower, "link") ||
			strings.Contains(responseLower, "网址") ||
			strings.Contains(responseLower, "链接") {
			confidence += 0.3
		}
	}

	// 检查电话格式绕过
	if strings.Contains(payloadLower, "123") && strings.Contains(payloadLower, "-") {
		if strings.Contains(responseLower, "phone") ||
			strings.Contains(responseLower, "tel") ||
			strings.Contains(responseLower, "mobile") ||
			strings.Contains(responseLower, "电话") ||
			strings.Contains(responseLower, "手机") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkLengthBypassResponse 检查长度验证绕过响应
func (d *DataValidationDetector) checkLengthBypassResponse(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)

	// 检查数据验证相关的响应模式
	for _, pattern := range d.validationPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查长度相关的错误响应
	payloadLength := len(payload)
	if payloadLength > 1000 {
		if strings.Contains(responseLower, "length") ||
			strings.Contains(responseLower, "size") ||
			strings.Contains(responseLower, "long") ||
			strings.Contains(responseLower, "limit") ||
			strings.Contains(responseLower, "长度") ||
			strings.Contains(responseLower, "大小") ||
			strings.Contains(responseLower, "太长") ||
			strings.Contains(responseLower, "限制") {
			confidence += 0.4
		}
	}

	// 检查短长度响应
	if payloadLength < 3 {
		if strings.Contains(responseLower, "short") ||
			strings.Contains(responseLower, "minimum") ||
			strings.Contains(responseLower, "min") ||
			strings.Contains(responseLower, "太短") ||
			strings.Contains(responseLower, "最小") ||
			strings.Contains(responseLower, "最少") {
			confidence += 0.3
		}
	}

	// 检查特定长度边界
	if payloadLength == 255 || payloadLength == 256 || payloadLength == 1023 || payloadLength == 1024 {
		if strings.Contains(responseLower, "boundary") ||
			strings.Contains(responseLower, "limit") ||
			strings.Contains(responseLower, "maximum") ||
			strings.Contains(responseLower, "边界") ||
			strings.Contains(responseLower, "限制") ||
			strings.Contains(responseLower, "最大") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// extractDataValidationEvidence 提取数据验证证据
func (d *DataValidationDetector) extractDataValidationEvidence(response, payload, vulnType string) string {
	// 限制证据长度
	maxLength := 1000
	evidence := fmt.Sprintf("数据验证证据 (%s):\n", vulnType)

	// 添加载荷信息
	evidence += fmt.Sprintf("载荷: %s\n", payload)
	evidence += fmt.Sprintf("漏洞类型: %s\n", vulnType)

	// 检查响应中的数据验证相关模式
	evidence += "\n数据验证模式匹配:\n"
	for _, pattern := range d.validationPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现数据验证模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的错误模式
	evidence += "\n错误模式匹配:\n"
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现错误模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的成功模式
	evidence += "\n成功模式匹配:\n"
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现成功模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的绕过模式
	evidence += "\n绕过模式匹配:\n"
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现绕过模式: %s\n", pattern.String())
		}
	}

	// 添加响应摘要
	if len(response) > 200 {
		evidence += fmt.Sprintf("\n响应摘要: %s...\n", response[:200])
	} else {
		evidence += fmt.Sprintf("\n响应内容: %s\n", response)
	}

	// 分析载荷类型
	evidence += d.analyzeDataValidationPayloadType(payload, vulnType)

	// 限制证据长度
	if len(evidence) > maxLength {
		evidence = evidence[:maxLength] + "..."
	}

	return evidence
}

// analyzeDataValidationPayloadType 分析数据验证载荷类型
func (d *DataValidationDetector) analyzeDataValidationPayloadType(payload, vulnType string) string {
	analysis := "\n载荷分析:\n"
	payloadLower := strings.ToLower(payload)

	// 边界值载荷分析
	if vulnType == "boundary-value" {
		analysis += "- 载荷类型: 边界值测试\n"

		if payload == "" {
			analysis += "- 测试类型: 空值边界\n"
		} else if len(payload) > 1000 {
			analysis += "- 测试类型: 长度边界\n"
		} else if strings.Contains(payloadLower, "2147483647") || strings.Contains(payloadLower, "-2147483648") {
			analysis += "- 测试类型: 整数边界\n"
		} else if strings.Contains(payloadLower, "infinity") || strings.Contains(payloadLower, "nan") {
			analysis += "- 测试类型: 浮点数边界\n"
		} else if strings.Contains(payloadLower, "\x00") || strings.Contains(payloadLower, "\xff") {
			analysis += "- 测试类型: 字符边界\n"
		} else if strings.Contains(payloadLower, "1970") || strings.Contains(payloadLower, "2038") {
			analysis += "- 测试类型: 时间边界\n"
		}
	}

	// 类型混淆载荷分析
	if vulnType == "type-confusion" {
		analysis += "- 载荷类型: 类型混淆测试\n"

		if strings.Contains(payloadLower, "true") || strings.Contains(payloadLower, "false") {
			analysis += "- 混淆类型: 布尔值混淆\n"
		} else if strings.Contains(payloadLower, "null") || strings.Contains(payloadLower, "undefined") {
			analysis += "- 混淆类型: 空值混淆\n"
		} else if strings.Contains(payloadLower, "123") {
			analysis += "- 混淆类型: 数字字符串混淆\n"
		} else if strings.Contains(payloadLower, "[]") || strings.Contains(payloadLower, "array") {
			analysis += "- 混淆类型: 数组混淆\n"
		} else if strings.Contains(payloadLower, "{}") || strings.Contains(payloadLower, "object") {
			analysis += "- 混淆类型: 对象混淆\n"
		} else if strings.Contains(payloadLower, "function") {
			analysis += "- 混淆类型: 函数混淆\n"
		}
	}

	// 格式验证绕过载荷分析
	if vulnType == "format-bypass" {
		analysis += "- 载荷类型: 格式验证绕过\n"

		if strings.Contains(payloadLower, "@") {
			analysis += "- 绕过目标: 邮箱格式验证\n"
		} else if strings.Contains(payloadLower, "http") {
			analysis += "- 绕过目标: URL格式验证\n"
		} else if strings.Contains(payloadLower, "123") && strings.Contains(payloadLower, "-") {
			analysis += "- 绕过目标: 电话格式验证\n"
		} else if strings.Contains(payloadLower, "2023") {
			analysis += "- 绕过目标: 日期格式验证\n"
		} else if strings.Contains(payloadLower, "192.168") {
			analysis += "- 绕过目标: IP地址格式验证\n"
		} else if len(payload) == 16 && strings.Contains(payloadLower, "1234") {
			analysis += "- 绕过目标: 信用卡格式验证\n"
		} else if len(payload) == 18 {
			analysis += "- 绕过目标: 身份证格式验证\n"
		}
	}

	// 长度验证绕过载荷分析
	if vulnType == "length-bypass" {
		analysis += "- 载荷类型: 长度验证绕过\n"

		payloadLength := len(payload)
		if payloadLength == 0 {
			analysis += "- 绕过方法: 空长度测试\n"
		} else if payloadLength < 8 {
			analysis += "- 绕过方法: 超短长度测试\n"
		} else if payloadLength >= 255 && payloadLength <= 256 {
			analysis += "- 绕过方法: 标准边界长度测试\n"
		} else if payloadLength >= 1023 && payloadLength <= 1024 {
			analysis += "- 绕过方法: KB边界长度测试\n"
		} else if payloadLength > 10000 {
			analysis += "- 绕过方法: 超长长度测试\n"
		} else {
			analysis += fmt.Sprintf("- 绕过方法: 自定义长度测试 (%d字符)\n", payloadLength)
		}
	}

	// 检查载荷特征
	if strings.Contains(payload, "\x00") {
		analysis += "- 载荷特征: 包含空字节\n"
	} else if strings.Contains(payload, "\xff") {
		analysis += "- 载荷特征: 包含高位字节\n"
	} else if len(payload) > 1000 {
		analysis += "- 载荷特征: 超长字符串\n"
	} else if payload == "" {
		analysis += "- 载荷特征: 空字符串\n"
	} else if strings.Contains(payload, "中") {
		analysis += "- 载荷特征: 包含中文字符\n"
	}

	return analysis
}
