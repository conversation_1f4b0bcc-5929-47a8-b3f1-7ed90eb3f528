package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// InputValidationDetector 输入验证检测器
// 支持输入过滤绕过、数据类型验证、长度限制、特殊字符处理、输入格式验证等多种输入验证检测
type InputValidationDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	inputTypes         []string         // 输入类型
	validationPatterns []string         // 验证模式
	bypassPayloads     []string         // 绕过载荷
	lengthTestSizes    []int            // 长度测试大小
	specialCharacters  []string         // 特殊字符
	dataTypes          []string         // 数据类型
	formatPatterns     []*regexp.Regexp // 格式模式
	errorPatterns      []*regexp.Regexp // 错误模式
	bypassPatterns     []*regexp.Regexp // 绕过模式
	httpClient         *http.Client
}

// NewInputValidationDetector 创建输入验证检测器
func NewInputValidationDetector() *InputValidationDetector {
	detector := &InputValidationDetector{
		id:          "input-validation-comprehensive",
		name:        "输入验证漏洞综合检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{}, // 输入验证是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-20", "CWE-79", "CWE-89", "CWE-94", "CWE-190"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测输入验证漏洞，包括输入过滤绕过、数据类型验证、长度限制、特殊字符处理、输入格式验证等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         10 * time.Second, // 输入验证检测时间较短
		MaxRetries:      2,
		Concurrency:     4,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 256 * 1024, // 256KB，输入验证响应通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeInputTypes()
	detector.initializeValidationPatterns()
	detector.initializeBypassPayloads()
	detector.initializeLengthTests()
	detector.initializeSpecialCharacters()
	detector.initializeDataTypes()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *InputValidationDetector) GetID() string                     { return d.id }
func (d *InputValidationDetector) GetName() string                   { return d.name }
func (d *InputValidationDetector) GetCategory() string               { return d.category }
func (d *InputValidationDetector) GetSeverity() string               { return d.severity }
func (d *InputValidationDetector) GetCVE() []string                  { return d.cve }
func (d *InputValidationDetector) GetCWE() []string                  { return d.cwe }
func (d *InputValidationDetector) GetVersion() string                { return d.version }
func (d *InputValidationDetector) GetAuthor() string                 { return d.author }
func (d *InputValidationDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *InputValidationDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *InputValidationDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *InputValidationDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *InputValidationDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *InputValidationDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *InputValidationDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *InputValidationDetector) GetDependencies() []string         { return []string{} }
func (d *InputValidationDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *InputValidationDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *InputValidationDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *InputValidationDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *InputValidationDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 10 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 4
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *InputValidationDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *InputValidationDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.inputTypes) == 0 {
		return fmt.Errorf("输入类型列表不能为空")
	}
	if len(d.bypassPayloads) == 0 {
		return fmt.Errorf("绕过载荷列表不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *InputValidationDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 输入验证检测适用于有输入功能的Web应用
	// 检查是否有表单、参数或输入相关的功能
	if d.hasInputFeatures(target) {
		return true
	}

	// 对于API、表单、搜索、用户相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	inputKeywords := []string{
		"api", "form", "search", "query", "input", "submit",
		"register", "signup", "login", "contact", "feedback",
		"comment", "post", "create", "update", "edit",
		"表单", "搜索", "查询", "输入", "提交", "注册",
		"登录", "联系", "反馈", "评论", "发布", "创建",
	}

	for _, keyword := range inputKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return false
}

// hasInputFeatures 检查是否有输入功能
func (d *InputValidationDetector) hasInputFeatures(target *plugins.ScanTarget) bool {
	// 检查表单中是否有输入字段
	for _, form := range target.Forms {
		if len(form.Fields) > 0 {
			return true
		}
	}

	// 检查URL中是否有查询参数
	if strings.Contains(target.URL, "?") {
		return true
	}

	// 检查是否有POST方法的表单
	for _, form := range target.Forms {
		if strings.ToUpper(form.Method) == "POST" {
			return true
		}
	}

	// 检查头部中是否有内容类型指示输入
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "content-type" &&
			(strings.Contains(valueLower, "application/json") ||
				strings.Contains(valueLower, "application/x-www-form-urlencoded") ||
				strings.Contains(valueLower, "multipart/form-data")) {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *InputValidationDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种输入验证检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 长度限制绕过检测
	lengthEvidence, lengthConfidence, lengthPayload, lengthRequest, lengthResponse := d.detectLengthBypass(ctx, target)
	if lengthConfidence > maxConfidence {
		maxConfidence = lengthConfidence
		vulnerablePayload = lengthPayload
		vulnerableRequest = lengthRequest
		vulnerableResponse = lengthResponse
	}
	evidence = append(evidence, lengthEvidence...)

	// 2. 特殊字符绕过检测
	charEvidence, charConfidence, charPayload, charRequest, charResponse := d.detectSpecialCharacterBypass(ctx, target)
	if charConfidence > maxConfidence {
		maxConfidence = charConfidence
		vulnerablePayload = charPayload
		vulnerableRequest = charRequest
		vulnerableResponse = charResponse
	}
	evidence = append(evidence, charEvidence...)

	// 3. 数据类型验证绕过检测
	typeEvidence, typeConfidence, typePayload, typeRequest, typeResponse := d.detectDataTypeBypass(ctx, target)
	if typeConfidence > maxConfidence {
		maxConfidence = typeConfidence
		vulnerablePayload = typePayload
		vulnerableRequest = typeRequest
		vulnerableResponse = typeResponse
	}
	evidence = append(evidence, typeEvidence...)

	// 4. 格式验证绕过检测
	formatEvidence, formatConfidence, formatPayload, formatRequest, formatResponse := d.detectFormatBypass(ctx, target)
	if formatConfidence > maxConfidence {
		maxConfidence = formatConfidence
		vulnerablePayload = formatPayload
		vulnerableRequest = formatRequest
		vulnerableResponse = formatResponse
	}
	evidence = append(evidence, formatEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.5

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "输入验证漏洞",
		Description:       "检测到输入验证漏洞，可能导致输入过滤绕过、数据类型验证错误或其他输入处理问题",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "实施严格的输入验证，包括服务器端验证、数据类型检查、长度限制、特殊字符过滤和格式验证",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Input_Validation", "https://cwe.mitre.org/data/definitions/20.html"},
		Tags:              []string{"input-validation", "input-bypass", "data-validation", "web", "medium"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *InputValidationDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationPayloads := []string{
		"test_input_validation",
		"<script>alert('test')</script>",
		"' OR '1'='1",
		strings.Repeat("A", 1000),
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, payload := range verificationPayloads {
		// 构造验证请求
		verifyResp, err := d.sendInputValidationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查输入验证响应特征
		responseConfidence := d.checkInputValidationResponse(verifyResp, payload)
		if responseConfidence > 0.3 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证载荷确认了输入验证问题"),
				Content:     verifyResp,
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.4

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "input-validation-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用输入验证验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *InputValidationDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("input_validation_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *InputValidationDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (输入验证通常是中等风险)
	baseScore := 5.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializeInputTypes 初始化输入类型列表
func (d *InputValidationDetector) initializeInputTypes() {
	d.inputTypes = []string{
		// 基础输入类型
		"text",
		"password",
		"email",
		"number",
		"tel",
		"url",
		"search",
		"date",
		"time",
		"datetime-local",
		"month",
		"week",
		"color",
		"range",
		"file",
		"hidden",
		"checkbox",
		"radio",
		"submit",
		"button",
		"reset",

		// HTML5输入类型
		"textarea",
		"select",
		"option",

		// 自定义输入类型
		"json",
		"xml",
		"csv",
		"base64",
		"binary",

		// 中文输入类型
		"文本",
		"密码",
		"邮箱",
		"数字",
		"电话",
		"网址",
	}
}

// initializeValidationPatterns 初始化验证模式列表
func (d *InputValidationDetector) initializeValidationPatterns() {
	d.validationPatterns = []string{
		// 邮箱验证模式
		`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`,
		`^[\\w\\.-]+@[\\w\\.-]+\\.[a-zA-Z]{2,}$`,

		// 电话验证模式
		`^\\d{10,11}$`,
		`^\\+?[1-9]\\d{1,14}$`,
		`^1[3-9]\\d{9}$`,

		// 密码验证模式
		`^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$`,
		`^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$`,

		// URL验证模式
		`^https?://[\\w\\.-]+\\.[a-zA-Z]{2,}(/.*)?$`,
		`^(http|https)://[a-zA-Z0-9\\.-]+\\.[a-zA-Z]{2,}$`,

		// 数字验证模式
		`^\\d+$`,
		`^[0-9]+$`,
		`^\\d{1,10}$`,

		// 字母验证模式
		`^[a-zA-Z]+$`,
		`^[a-zA-Z\\s]+$`,

		// 字母数字验证模式
		`^[a-zA-Z0-9]+$`,
		`^[a-zA-Z0-9\\s]+$`,

		// 中文验证模式
		`^[\\u4e00-\\u9fa5]+$`,
		`^[\\u4e00-\\u9fa5a-zA-Z0-9]+$`,

		// 特殊字符限制模式
		`^[^<>\"'&]+$`,
		`^[^<script>]+$`,
		`^[^\\x00-\\x1f\\x7f]+$`,
	}
}

// initializeBypassPayloads 初始化绕过载荷列表
func (d *InputValidationDetector) initializeBypassPayloads() {
	d.bypassPayloads = []string{
		// XSS绕过载荷
		"<script>alert('xss')</script>",
		"<img src=x onerror=alert('xss')>",
		"javascript:alert('xss')",
		"<svg onload=alert('xss')>",
		"<iframe src=javascript:alert('xss')>",

		// SQL注入绕过载荷
		"' OR '1'='1",
		"' UNION SELECT NULL--",
		"'; DROP TABLE users--",
		"1' OR '1'='1' --",
		"admin'--",

		// 命令注入绕过载荷
		"; ls -la",
		"| whoami",
		"& dir",
		"`id`",
		"$(whoami)",

		// 路径遍历绕过载荷
		"../../../etc/passwd",
		"..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
		"%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",

		// 特殊字符绕过载荷
		"<>\"'&",
		"\\x00\\x01\\x02",
		"\\r\\n\\t",
		"\\u0000\\u0001",

		// 长度绕过载荷
		strings.Repeat("A", 100),
		strings.Repeat("A", 1000),
		strings.Repeat("A", 10000),

		// 编码绕过载荷
		"%3Cscript%3Ealert%28%27xss%27%29%3C%2Fscript%3E",
		"&lt;script&gt;alert(&#39;xss&#39;)&lt;/script&gt;",
		"\\u003cscript\\u003ealert(\\u0027xss\\u0027)\\u003c/script\\u003e",

		// 中文绕过载荷
		"测试输入验证绕过",
		"<脚本>警告('测试')</脚本>",
		"' 或 '1'='1",
	}
}

// initializeLengthTests 初始化长度测试大小列表
func (d *InputValidationDetector) initializeLengthTests() {
	d.lengthTestSizes = []int{
		// 小长度测试
		10,
		50,
		100,

		// 中等长度测试
		255,
		256,
		512,
		1000,
		1024,

		// 大长度测试
		2048,
		4096,
		8192,
		10000,

		// 极大长度测试
		16384,
		32768,
		65536,
		100000,

		// 特殊长度测试
		127,   // 有符号字节最大值
		128,   // 有符号字节溢出
		32767, // 有符号短整型最大值
		32768, // 有符号短整型溢出
		65535, // 无符号短整型最大值
		65536, // 无符号短整型溢出
	}
}

// initializeSpecialCharacters 初始化特殊字符列表
func (d *InputValidationDetector) initializeSpecialCharacters() {
	d.specialCharacters = []string{
		// HTML特殊字符
		"<",
		">",
		"\"",
		"'",
		"&",

		// SQL特殊字符
		"'",
		"\"",
		";",
		"--",
		"/*",
		"*/",

		// 命令注入特殊字符
		"|",
		"&",
		";",
		"`",
		"$",
		"(",
		")",

		// 路径遍历特殊字符
		".",
		"/",
		"\\",
		"..",

		// 控制字符
		"\\x00", // NULL
		"\\x01", // SOH
		"\\x02", // STX
		"\\x03", // ETX
		"\\x04", // EOT
		"\\x08", // BS
		"\\x09", // TAB
		"\\x0A", // LF
		"\\x0D", // CR
		"\\x1A", // SUB
		"\\x1B", // ESC
		"\\x7F", // DEL

		// Unicode特殊字符
		"\\u0000", // NULL
		"\\u0001", // SOH
		"\\u0008", // BS
		"\\u000A", // LF
		"\\u000D", // CR
		"\\u001A", // SUB
		"\\u001B", // ESC
		"\\u007F", // DEL
		"\\u2028", // Line Separator
		"\\u2029", // Paragraph Separator
		"\\uFEFF", // BOM

		// 编码字符
		"%00", // NULL
		"%0A", // LF
		"%0D", // CR
		"%20", // Space
		"%22", // "
		"%27", // '
		"%3C", // <
		"%3E", // >
		"%3D", // =

		// 中文特殊字符
		"，",
		"。",
		"；",
		"：",
		"？",
		"！",
		"\u201c", // 左双引号
		"\u201d", // 右双引号
		"\u2018", // 左单引号
		"\u2019", // 右单引号
	}
}

// initializeDataTypes 初始化数据类型列表
func (d *InputValidationDetector) initializeDataTypes() {
	d.dataTypes = []string{
		// 基础数据类型
		"string",
		"integer",
		"float",
		"double",
		"boolean",
		"date",
		"time",
		"datetime",
		"timestamp",

		// 复合数据类型
		"array",
		"object",
		"json",
		"xml",
		"csv",

		// 二进制数据类型
		"binary",
		"base64",
		"hex",
		"blob",

		// 网络数据类型
		"url",
		"uri",
		"email",
		"ip",
		"ipv4",
		"ipv6",
		"mac",
		"domain",

		// 文件数据类型
		"file",
		"image",
		"video",
		"audio",
		"document",

		// 中文数据类型
		"字符串",
		"整数",
		"浮点数",
		"布尔值",
		"日期",
		"时间",
		"数组",
		"对象",
	}
}

// initializePatterns 初始化检测模式
func (d *InputValidationDetector) initializePatterns() {
	// 格式模式 - 检测输入格式相关的响应内容
	formatPatternStrings := []string{
		// 输入验证
		`(?i)(input\s*validation|validation\s*error|invalid\s*input)`,
		`(?i)(format\s*error|format\s*invalid|invalid\s*format)`,
		`(?i)(pattern\s*mismatch|pattern\s*error|regex\s*error)`,
		`(?i)(type\s*error|type\s*mismatch|invalid\s*type)`,

		// 长度验证
		`(?i)(length\s*error|length\s*invalid|too\s*long|too\s*short)`,
		`(?i)(maxlength|minlength|length\s*limit|size\s*limit)`,
		`(?i)(string\s*too\s*long|input\s*too\s*long|value\s*too\s*long)`,

		// 字符验证
		`(?i)(invalid\s*character|illegal\s*character|forbidden\s*character)`,
		`(?i)(special\s*character|character\s*not\s*allowed|character\s*error)`,
		`(?i)(encoding\s*error|charset\s*error|unicode\s*error)`,

		// 数据类型验证
		`(?i)(number\s*expected|integer\s*expected|string\s*expected)`,
		`(?i)(boolean\s*expected|date\s*expected|email\s*expected)`,
		`(?i)(url\s*expected|json\s*expected|xml\s*expected)`,

		// 中文模式
		`(?i)(输入验证|验证错误|无效输入|格式错误)`,
		`(?i)(长度错误|字符错误|类型错误|数据错误)`,
	}

	d.formatPatterns = make([]*regexp.Regexp, 0, len(formatPatternStrings))
	for _, pattern := range formatPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.formatPatterns = append(d.formatPatterns, compiled)
		}
	}

	// 错误模式 - 检测输入验证错误的响应内容
	errorPatternStrings := []string{
		// 验证错误
		`(?i)(validation\s*failed|validation\s*error|input\s*error)`,
		`(?i)(invalid\s*data|invalid\s*value|invalid\s*parameter)`,
		`(?i)(bad\s*request|malformed\s*request|syntax\s*error)`,

		// 过滤错误
		`(?i)(filter\s*error|sanitization\s*error|escape\s*error)`,
		`(?i)(blocked\s*input|filtered\s*input|rejected\s*input)`,

		// 安全错误
		`(?i)(security\s*error|attack\s*detected|malicious\s*input)`,
		`(?i)(xss\s*detected|sql\s*injection|script\s*blocked)`,

		// 系统错误
		`(?i)(internal\s*error|server\s*error|application\s*error)`,
		`(?i)(exception|stack\s*trace|error\s*500)`,

		// 中文错误模式
		`(?i)(验证失败|输入错误|数据无效|参数错误)`,
		`(?i)(过滤错误|安全错误|系统错误|内部错误)`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 绕过模式 - 检测输入验证绕过的响应内容
	bypassPatternStrings := []string{
		// 绕过成功
		`(?i)(bypass\s*successful|validation\s*bypassed|filter\s*bypassed)`,
		`(?i)(input\s*accepted|payload\s*executed|script\s*executed)`,
		`(?i)(injection\s*successful|command\s*executed|query\s*executed)`,

		// XSS绕过
		`(?i)(alert\s*executed|script\s*tag|javascript\s*executed)`,
		`(?i)(<script|<img|<svg|<iframe|javascript:)`,

		// SQL注入绕过
		`(?i)(union\s*select|or\s*1=1|drop\s*table|insert\s*into)`,
		`(?i)(sql\s*syntax|mysql\s*error|oracle\s*error|postgresql\s*error)`,

		// 命令注入绕过
		`(?i)(command\s*output|shell\s*output|system\s*output)`,
		`(?i)(root:|bin/|etc/|usr/|var/|home/)`,
		`(?i)(volume|directory|<dir>|total\s*\d+)`,

		// 路径遍历绕过
		`(?i)(passwd|shadow|hosts|boot\.ini|win\.ini)`,
		`(?i)(windows|system32|program\s*files|users)`,

		// 中文绕过模式
		`(?i)(绕过成功|验证绕过|过滤绕过|注入成功)`,
		`(?i)(脚本执行|命令执行|查询执行|载荷执行)`,
	}

	d.bypassPatterns = make([]*regexp.Regexp, 0, len(bypassPatternStrings))
	for _, pattern := range bypassPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.bypassPatterns = append(d.bypassPatterns, compiled)
		}
	}
}
