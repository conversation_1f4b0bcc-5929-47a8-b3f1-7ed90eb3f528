package engines

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	"scanner/internal/scanner/logging"
)

// ScanPathOptimizer 扫描路径优化器 - 第四步智能化功能
type ScanPathOptimizer struct {
	targetProfile   *TargetProfile
	sessionManager  *SessionManager
	dependencyGraph *DependencyGraph
	apiDiscoverer   *ScanAPIDiscoverer
	pathPlanner     *PathPlanner
	scanLogger      *logging.ScanLogger
}

// SessionManager 智能会话管理器
type SessionManager struct {
	sessions     map[string]*ScanSession
	cookieJar    *http.CookieJar
	authTokens   map[string]string
	sessionState map[string]interface{}
	mutex        sync.RWMutex
}

// ScanSession 扫描会话
type ScanSession struct {
	ID           string
	URL          string
	Cookies      []*http.Cookie
	Headers      map[string]string
	AuthToken    string
	CSRFToken    string
	SessionData  map[string]interface{}
	LastActivity time.Time
	IsActive     bool
}

// DependencyGraph 依赖关系图
type DependencyGraph struct {
	nodes map[string]*DependencyNode
	edges map[string][]*DependencyEdge
	mutex sync.RWMutex
}

// DependencyNode 依赖节点
type DependencyNode struct {
	ID           string
	Type         string // page, api, resource, form等
	URL          string
	Method       string
	Priority     int
	Depth        int
	Dependencies []string
	Metadata     map[string]interface{}
}

// DependencyEdge 依赖边
type DependencyEdge struct {
	From      string
	To        string
	Type      string // redirect, form_submit, ajax_call, include等
	Weight    float64
	Condition string
	Metadata  map[string]interface{}
}

// ScanAPIDiscoverer 扫描API端点发现器（重命名避免冲突）
type ScanAPIDiscoverer struct {
	endpoints      map[string]*ScanAPIEndpoint
	patterns       []*ScanAPIPattern
	discoveryRules []*ScanDiscoveryRule
	mutex          sync.RWMutex
}

// ScanAPIEndpoint 扫描API端点
type ScanAPIEndpoint struct {
	URL          string
	Method       string
	Path         string
	Parameters   []ScanAPIParameter
	Headers      map[string]string
	ContentType  string
	ResponseType string
	AuthRequired bool
	Discovered   time.Time
	Tested       bool
	Vulnerable   bool
	Metadata     map[string]interface{}
}

// ScanAPIParameter 扫描API参数
type ScanAPIParameter struct {
	Name        string
	Type        string // query, body, header, path
	DataType    string // string, int, bool, object等
	Required    bool
	Default     interface{}
	Validation  string
	Description string
}

// ScanAPIPattern 扫描API模式
type ScanAPIPattern struct {
	Name        string
	Pattern     *regexp.Regexp
	Method      string
	ContentType string
	Confidence  float64
	Examples    []string
}

// ScanDiscoveryRule 扫描发现规则
type ScanDiscoveryRule struct {
	Name     string
	Type     string // js_analysis, link_extraction, directory_bruteforce等
	Pattern  string
	Enabled  bool
	Priority int
	Metadata map[string]interface{}
}

// PathPlanner 路径规划器
type PathPlanner struct {
	scanPaths   []*ScanPath
	priorities  map[string]int
	constraints map[string]interface{}
	optimizer   *PathOptimizer
}

// ScanPath 扫描路径
type ScanPath struct {
	ID            string
	Name          string
	Steps         []*ScanStep
	Priority      int
	EstimatedTime time.Duration
	Dependencies  []string
	Parallel      bool
	Metadata      map[string]interface{}
}

// ScanStep 扫描步骤
type ScanStep struct {
	ID         string
	Type       string // request, analysis, validation等
	URL        string
	Method     string
	Parameters map[string]interface{}
	Headers    map[string]string
	Payload    string
	Expected   map[string]interface{}
	Timeout    time.Duration
	Retries    int
	Metadata   map[string]interface{}
}

// PathOptimizer 路径优化器
type PathOptimizer struct {
	algorithms  map[string]OptimizationAlgorithm
	metrics     *OptimizationMetrics
	constraints *OptimizationConstraints
}

// OptimizationAlgorithm 优化算法接口
type OptimizationAlgorithm interface {
	Optimize(paths []*ScanPath, constraints *OptimizationConstraints) ([]*ScanPath, error)
	GetName() string
	GetDescription() string
}

// OptimizationMetrics 优化指标
type OptimizationMetrics struct {
	TotalTime       time.Duration
	ParallelPaths   int
	CoverageScore   float64
	EfficiencyScore float64
	RiskScore       float64
}

// OptimizationConstraints 优化约束
type OptimizationConstraints struct {
	MaxTime        time.Duration
	MaxParallel    int
	MinCoverage    float64
	PriorityWeight float64
	RiskWeight     float64
}

// NewScanPathOptimizer 创建扫描路径优化器
func NewScanPathOptimizer(profile *TargetProfile, scanLogger *logging.ScanLogger) *ScanPathOptimizer {
	optimizer := &ScanPathOptimizer{
		targetProfile:   profile,
		sessionManager:  NewSessionManager(),
		dependencyGraph: NewDependencyGraph(),
		apiDiscoverer:   NewScanAPIDiscoverer(),
		pathPlanner:     NewPathPlanner(),
		scanLogger:      scanLogger,
	}

	// 初始化优化器组件
	optimizer.initializeComponents()

	return optimizer
}

// NewSessionManager 创建会话管理器
func NewSessionManager() *SessionManager {
	return &SessionManager{
		sessions:     make(map[string]*ScanSession),
		authTokens:   make(map[string]string),
		sessionState: make(map[string]interface{}),
		mutex:        sync.RWMutex{},
	}
}

// NewDependencyGraph 创建依赖关系图
func NewDependencyGraph() *DependencyGraph {
	return &DependencyGraph{
		nodes: make(map[string]*DependencyNode),
		edges: make(map[string][]*DependencyEdge),
		mutex: sync.RWMutex{},
	}
}

// NewScanAPIDiscoverer 创建扫描API发现器
func NewScanAPIDiscoverer() *ScanAPIDiscoverer {
	discoverer := &ScanAPIDiscoverer{
		endpoints:      make(map[string]*ScanAPIEndpoint),
		patterns:       make([]*ScanAPIPattern, 0),
		discoveryRules: make([]*ScanDiscoveryRule, 0),
		mutex:          sync.RWMutex{},
	}

	// 初始化API模式
	discoverer.initializeAPIPatterns()

	return discoverer
}

// NewPathPlanner 创建路径规划器
func NewPathPlanner() *PathPlanner {
	return &PathPlanner{
		scanPaths:   make([]*ScanPath, 0),
		priorities:  make(map[string]int),
		constraints: make(map[string]interface{}),
		optimizer:   NewPathOptimizer(),
	}
}

// NewPathOptimizer 创建路径优化器
func NewPathOptimizer() *PathOptimizer {
	optimizer := &PathOptimizer{
		algorithms:  make(map[string]OptimizationAlgorithm),
		metrics:     &OptimizationMetrics{},
		constraints: &OptimizationConstraints{},
	}

	// 注册优化算法
	optimizer.registerAlgorithms()

	return optimizer
}

// initializeComponents 初始化优化器组件
func (spo *ScanPathOptimizer) initializeComponents() {
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "路径优化器", "初始化扫描路径优化器组件")

	// 初始化会话管理器
	spo.initializeSessionManager()

	// 初始化依赖关系图
	spo.initializeDependencyGraph()

	// 初始化API发现器
	spo.initializeAPIDiscoverer()

	// 初始化路径规划器
	spo.initializePathPlanner()

	spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "路径优化器", "扫描路径优化器组件初始化完成")
}

// initializeSessionManager 初始化会话管理器
func (spo *ScanPathOptimizer) initializeSessionManager() {
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "会话管理", "初始化智能会话管理器")

	// 创建主会话
	mainSession := &ScanSession{
		ID:           "main",
		URL:          spo.targetProfile.URL,
		Headers:      make(map[string]string),
		SessionData:  make(map[string]interface{}),
		LastActivity: time.Now(),
		IsActive:     true,
	}

	spo.sessionManager.sessions["main"] = mainSession

	spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "会话管理", "智能会话管理器初始化完成")
}

// initializeDependencyGraph 初始化依赖关系图
func (spo *ScanPathOptimizer) initializeDependencyGraph() {
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "依赖分析", "初始化依赖关系图")

	// 添加根节点
	rootNode := &DependencyNode{
		ID:           "root",
		Type:         "page",
		URL:          spo.targetProfile.URL,
		Method:       "GET",
		Priority:     10,
		Depth:        0,
		Dependencies: make([]string, 0),
		Metadata:     make(map[string]interface{}),
	}

	spo.dependencyGraph.nodes["root"] = rootNode

	spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "依赖分析", "依赖关系图初始化完成")
}

// initializeAPIDiscoverer 初始化API发现器
func (spo *ScanPathOptimizer) initializeAPIDiscoverer() {
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "API发现", "初始化API端点发现器")

	// 初始化发现规则
	spo.apiDiscoverer.initializeDiscoveryRules()

	spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "API发现", "API端点发现器初始化完成")
}

// initializePathPlanner 初始化路径规划器
func (spo *ScanPathOptimizer) initializePathPlanner() {
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "路径规划", "初始化智能路径规划器")

	// 设置默认约束
	spo.pathPlanner.constraints["max_depth"] = 5
	spo.pathPlanner.constraints["max_parallel"] = 3
	spo.pathPlanner.constraints["timeout"] = 30 * time.Second

	spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "路径规划", "智能路径规划器初始化完成")
}

// ===== 第四步智能化功能：扫描路径优化 =====

// OptimizeScanPaths 优化扫描路径
func (spo *ScanPathOptimizer) OptimizeScanPaths(ctx context.Context, baseURL string) ([]*ScanPath, error) {
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "路径优化", "开始扫描路径优化")

	// 1. 依赖关系分析和路径规划
	err := spo.analyzeDependencies(ctx, baseURL)
	if err != nil {
		spo.scanLogger.LogNodeError(logging.PhaseVulnScan, "依赖分析", "依赖关系分析失败", err)
		return nil, fmt.Errorf("依赖关系分析失败: %v", err)
	}

	// 2. API端点发现和测试
	endpoints, err := spo.discoverAPIEndpoints(ctx, baseURL)
	if err != nil {
		spo.scanLogger.LogNodeError(logging.PhaseVulnScan, "API发现", "API端点发现失败", err)
		return nil, fmt.Errorf("API端点发现失败: %v", err)
	}

	// 3. 智能会话管理和状态维护
	err = spo.manageSessionState(ctx, baseURL)
	if err != nil {
		spo.scanLogger.LogNodeError(logging.PhaseVulnScan, "会话管理", "会话状态管理失败", err)
		return nil, fmt.Errorf("会话状态管理失败: %v", err)
	}

	// 4. 生成优化的扫描路径
	optimizedPaths, err := spo.generateOptimizedPaths(endpoints)
	if err != nil {
		spo.scanLogger.LogNodeError(logging.PhaseVulnScan, "路径生成", "优化路径生成失败", err)
		return nil, fmt.Errorf("优化路径生成失败: %v", err)
	}

	spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "路径优化",
		fmt.Sprintf("扫描路径优化完成，生成%d条优化路径", len(optimizedPaths)))

	return optimizedPaths, nil
}

// analyzeDependencies 分析依赖关系
func (spo *ScanPathOptimizer) analyzeDependencies(ctx context.Context, baseURL string) error {
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "依赖分析", "开始分析页面依赖关系")

	// 解析基础URL
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return fmt.Errorf("URL解析失败: %v", err)
	}

	// 发现页面链接和资源
	links, err := spo.discoverPageLinks(ctx, baseURL)
	if err != nil {
		return fmt.Errorf("页面链接发现失败: %v", err)
	}

	// 构建依赖关系图
	for _, link := range links {
		err = spo.addDependencyNode(link, parsedURL)
		if err != nil {
			spo.scanLogger.LogNodeWarning(logging.PhaseVulnScan, "依赖分析",
				fmt.Sprintf("添加依赖节点失败: %v", err))
			continue
		}
	}

	// 分析依赖关系
	err = spo.analyzeDependencyRelations()
	if err != nil {
		return fmt.Errorf("依赖关系分析失败: %v", err)
	}

	spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "依赖分析",
		fmt.Sprintf("依赖关系分析完成，发现%d个节点", len(spo.dependencyGraph.nodes)))

	return nil
}

// discoverAPIEndpoints 发现API端点
func (spo *ScanPathOptimizer) discoverAPIEndpoints(ctx context.Context, baseURL string) ([]*ScanAPIEndpoint, error) {
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "API发现", "开始发现API端点")

	var allEndpoints []*ScanAPIEndpoint

	// 1. JavaScript分析发现API
	jsEndpoints, err := spo.discoverAPIFromJavaScript(ctx, baseURL)
	if err != nil {
		spo.scanLogger.LogNodeWarning(logging.PhaseVulnScan, "API发现",
			fmt.Sprintf("JavaScript API发现失败: %v", err))
	} else {
		allEndpoints = append(allEndpoints, jsEndpoints...)
	}

	// 2. 链接提取发现API
	linkEndpoints, err := spo.discoverAPIFromLinks(ctx, baseURL)
	if err != nil {
		spo.scanLogger.LogNodeWarning(logging.PhaseVulnScan, "API发现",
			fmt.Sprintf("链接API发现失败: %v", err))
	} else {
		allEndpoints = append(allEndpoints, linkEndpoints...)
	}

	// 3. 目录暴力破解发现API
	bruteEndpoints, err := spo.discoverAPIFromBruteforce(ctx, baseURL)
	if err != nil {
		spo.scanLogger.LogNodeWarning(logging.PhaseVulnScan, "API发现",
			fmt.Sprintf("暴力破解API发现失败: %v", err))
	} else {
		allEndpoints = append(allEndpoints, bruteEndpoints...)
	}

	// 4. 模式匹配发现API
	patternEndpoints, err := spo.discoverAPIFromPatterns(ctx, baseURL)
	if err != nil {
		spo.scanLogger.LogNodeWarning(logging.PhaseVulnScan, "API发现",
			fmt.Sprintf("模式匹配API发现失败: %v", err))
	} else {
		allEndpoints = append(allEndpoints, patternEndpoints...)
	}

	// 去重和验证
	uniqueEndpoints := spo.deduplicateEndpoints(allEndpoints)
	validatedEndpoints := spo.validateEndpoints(ctx, uniqueEndpoints)

	spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "API发现",
		fmt.Sprintf("API端点发现完成，发现%d个有效端点", len(validatedEndpoints)))

	return validatedEndpoints, nil
}

// manageSessionState 管理会话状态
func (spo *ScanPathOptimizer) manageSessionState(ctx context.Context, baseURL string) error {
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "会话管理", "开始智能会话状态管理")

	// 1. 检测认证需求
	authRequired, err := spo.detectAuthenticationRequirement(ctx, baseURL)
	if err != nil {
		return fmt.Errorf("认证需求检测失败: %v", err)
	}

	if authRequired {
		// 2. 尝试自动登录
		err = spo.attemptAutoLogin(ctx, baseURL)
		if err != nil {
			spo.scanLogger.LogNodeWarning(logging.PhaseVulnScan, "会话管理",
				fmt.Sprintf("自动登录失败: %v", err))
		}
	}

	// 3. 维护会话状态
	err = spo.maintainSessionState(ctx)
	if err != nil {
		return fmt.Errorf("会话状态维护失败: %v", err)
	}

	// 4. 检测CSRF保护
	csrfToken, err := spo.detectCSRFProtection(ctx, baseURL)
	if err != nil {
		spo.scanLogger.LogNodeWarning(logging.PhaseVulnScan, "会话管理",
			fmt.Sprintf("CSRF检测失败: %v", err))
	} else if csrfToken != "" {
		// 更新会话中的CSRF令牌
		spo.sessionManager.sessions["main"].CSRFToken = csrfToken
	}

	spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "会话管理", "智能会话状态管理完成")

	return nil
}

// generateOptimizedPaths 生成优化的扫描路径
func (spo *ScanPathOptimizer) generateOptimizedPaths(endpoints []*ScanAPIEndpoint) ([]*ScanPath, error) {
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "路径生成", "开始生成优化扫描路径")

	var scanPaths []*ScanPath

	// 1. 基于依赖关系生成路径
	dependencyPaths := spo.generateDependencyBasedPaths()
	scanPaths = append(scanPaths, dependencyPaths...)

	// 2. 基于API端点生成路径
	apiPaths := spo.generateAPIBasedPaths(endpoints)
	scanPaths = append(scanPaths, apiPaths...)

	// 3. 基于漏洞类型生成路径
	vulnPaths := spo.generateVulnerabilityBasedPaths()
	scanPaths = append(scanPaths, vulnPaths...)

	// 4. 应用路径优化算法
	optimizedPaths, err := spo.pathPlanner.optimizer.optimizePaths(scanPaths)
	if err != nil {
		return nil, fmt.Errorf("路径优化失败: %v", err)
	}

	spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "路径生成",
		fmt.Sprintf("优化扫描路径生成完成，共%d条路径", len(optimizedPaths)))

	return optimizedPaths, nil
}

// ===== 辅助方法实现 =====

// discoverPageLinks 发现页面链接
func (spo *ScanPathOptimizer) discoverPageLinks(ctx context.Context, baseURL string) ([]string, error) {
	// 模拟链接发现逻辑
	links := []string{
		baseURL + "/api/v1/users",
		baseURL + "/api/v1/login",
		baseURL + "/admin/dashboard",
		baseURL + "/api/v1/data",
		baseURL + "/upload",
	}

	return links, nil
}

// addDependencyNode 添加依赖节点
func (spo *ScanPathOptimizer) addDependencyNode(link string, baseURL *url.URL) error {
	parsedLink, err := url.Parse(link)
	if err != nil {
		return err
	}

	// 创建依赖节点
	nodeID := fmt.Sprintf("node_%s", strings.ReplaceAll(parsedLink.Path, "/", "_"))
	node := &DependencyNode{
		ID:           nodeID,
		Type:         spo.determineNodeType(parsedLink.Path),
		URL:          link,
		Method:       "GET",
		Priority:     spo.calculateNodePriority(parsedLink.Path),
		Depth:        strings.Count(parsedLink.Path, "/"),
		Dependencies: make([]string, 0),
		Metadata:     make(map[string]interface{}),
	}

	spo.dependencyGraph.mutex.Lock()
	spo.dependencyGraph.nodes[nodeID] = node
	spo.dependencyGraph.mutex.Unlock()

	return nil
}

// determineNodeType 确定节点类型
func (spo *ScanPathOptimizer) determineNodeType(path string) string {
	if strings.Contains(path, "/api/") {
		return "api"
	} else if strings.Contains(path, "/admin/") {
		return "admin"
	} else if strings.Contains(path, "/upload") {
		return "upload"
	} else if strings.Contains(path, "/login") {
		return "auth"
	}
	return "page"
}

// calculateNodePriority 计算节点优先级
func (spo *ScanPathOptimizer) calculateNodePriority(path string) int {
	priority := 5 // 默认优先级

	// 根据路径特征调整优先级
	if strings.Contains(path, "/admin/") {
		priority += 3
	}
	if strings.Contains(path, "/api/") {
		priority += 2
	}
	if strings.Contains(path, "/upload") {
		priority += 2
	}
	if strings.Contains(path, "/login") {
		priority += 1
	}

	return priority
}

// analyzeDependencyRelations 分析依赖关系
func (spo *ScanPathOptimizer) analyzeDependencyRelations() error {
	spo.dependencyGraph.mutex.Lock()
	defer spo.dependencyGraph.mutex.Unlock()

	// 分析节点间的依赖关系
	for nodeID, node := range spo.dependencyGraph.nodes {
		// 根据节点类型建立依赖关系
		switch node.Type {
		case "admin":
			// 管理页面依赖于认证
			spo.addDependencyEdge(nodeID, "auth", "requires_auth", 1.0)
		case "api":
			// API可能依赖于认证
			if strings.Contains(node.URL, "/user") || strings.Contains(node.URL, "/admin") {
				spo.addDependencyEdge(nodeID, "auth", "requires_auth", 0.8)
			}
		case "upload":
			// 上传功能通常需要认证
			spo.addDependencyEdge(nodeID, "auth", "requires_auth", 0.9)
		}
	}

	return nil
}

// addDependencyEdge 添加依赖边
func (spo *ScanPathOptimizer) addDependencyEdge(from, to, edgeType string, weight float64) {
	edge := &DependencyEdge{
		From:     from,
		To:       to,
		Type:     edgeType,
		Weight:   weight,
		Metadata: make(map[string]interface{}),
	}

	if spo.dependencyGraph.edges[from] == nil {
		spo.dependencyGraph.edges[from] = make([]*DependencyEdge, 0)
	}
	spo.dependencyGraph.edges[from] = append(spo.dependencyGraph.edges[from], edge)
}

// discoverAPIFromJavaScript 从JavaScript发现API
func (spo *ScanPathOptimizer) discoverAPIFromJavaScript(ctx context.Context, baseURL string) ([]*ScanAPIEndpoint, error) {
	// 模拟JavaScript API发现
	endpoints := []*ScanAPIEndpoint{
		{
			URL:          baseURL + "/api/v1/users",
			Method:       "GET",
			Path:         "/api/v1/users",
			ContentType:  "application/json",
			ResponseType: "application/json",
			AuthRequired: true,
			Discovered:   time.Now(),
			Metadata:     make(map[string]interface{}),
		},
		{
			URL:          baseURL + "/api/v1/login",
			Method:       "POST",
			Path:         "/api/v1/login",
			ContentType:  "application/json",
			ResponseType: "application/json",
			AuthRequired: false,
			Discovered:   time.Now(),
			Metadata:     make(map[string]interface{}),
		},
	}

	return endpoints, nil
}

// discoverAPIFromLinks 从链接发现API
func (spo *ScanPathOptimizer) discoverAPIFromLinks(ctx context.Context, baseURL string) ([]*ScanAPIEndpoint, error) {
	// 模拟链接API发现
	endpoints := []*ScanAPIEndpoint{
		{
			URL:          baseURL + "/api/v1/data",
			Method:       "GET",
			Path:         "/api/v1/data",
			ContentType:  "application/json",
			ResponseType: "application/json",
			AuthRequired: false,
			Discovered:   time.Now(),
			Metadata:     make(map[string]interface{}),
		},
	}

	return endpoints, nil
}

// discoverAPIFromBruteforce 从暴力破解发现API
func (spo *ScanPathOptimizer) discoverAPIFromBruteforce(ctx context.Context, baseURL string) ([]*ScanAPIEndpoint, error) {
	// 模拟暴力破解API发现
	commonPaths := []string{
		"/api/v1/status",
		"/api/v1/health",
		"/api/v1/config",
		"/api/admin/users",
		"/api/admin/settings",
	}

	var endpoints []*ScanAPIEndpoint
	for _, path := range commonPaths {
		endpoint := &ScanAPIEndpoint{
			URL:          baseURL + path,
			Method:       "GET",
			Path:         path,
			ContentType:  "application/json",
			ResponseType: "application/json",
			AuthRequired: strings.Contains(path, "admin"),
			Discovered:   time.Now(),
			Metadata:     make(map[string]interface{}),
		}
		endpoints = append(endpoints, endpoint)
	}

	return endpoints, nil
}

// discoverAPIFromPatterns 从模式匹配发现API
func (spo *ScanPathOptimizer) discoverAPIFromPatterns(ctx context.Context, baseURL string) ([]*ScanAPIEndpoint, error) {
	// 模拟模式匹配API发现
	endpoints := []*ScanAPIEndpoint{
		{
			URL:          baseURL + "/api/v1/search",
			Method:       "GET",
			Path:         "/api/v1/search",
			ContentType:  "application/json",
			ResponseType: "application/json",
			AuthRequired: false,
			Discovered:   time.Now(),
			Metadata:     make(map[string]interface{}),
		},
	}

	return endpoints, nil
}

// deduplicateEndpoints 去重端点
func (spo *ScanPathOptimizer) deduplicateEndpoints(endpoints []*ScanAPIEndpoint) []*ScanAPIEndpoint {
	seen := make(map[string]bool)
	var unique []*ScanAPIEndpoint

	for _, endpoint := range endpoints {
		key := fmt.Sprintf("%s:%s", endpoint.Method, endpoint.URL)
		if !seen[key] {
			seen[key] = true
			unique = append(unique, endpoint)
		}
	}

	return unique
}

// validateEndpoints 验证端点
func (spo *ScanPathOptimizer) validateEndpoints(ctx context.Context, endpoints []*ScanAPIEndpoint) []*ScanAPIEndpoint {
	var validated []*ScanAPIEndpoint

	for _, endpoint := range endpoints {
		// 简单验证逻辑
		if endpoint.URL != "" && endpoint.Method != "" {
			endpoint.Tested = true
			validated = append(validated, endpoint)
		}
	}

	return validated
}

// detectAuthenticationRequirement 检测认证需求
func (spo *ScanPathOptimizer) detectAuthenticationRequirement(ctx context.Context, baseURL string) (bool, error) {
	// 模拟认证需求检测
	// 检查是否有登录页面或认证相关的端点
	authIndicators := []string{"/login", "/auth", "/signin", "/api/login"}

	for _, indicator := range authIndicators {
		if strings.Contains(baseURL, indicator) {
			return true, nil
		}
	}

	return false, nil
}

// attemptAutoLogin 尝试自动登录
func (spo *ScanPathOptimizer) attemptAutoLogin(ctx context.Context, baseURL string) error {
	// 模拟自动登录逻辑
	spo.scanLogger.LogNodeStart(logging.PhaseVulnScan, "自动登录", "尝试自动登录")

	// 常见的默认凭据
	credentials := []map[string]string{
		{"username": "admin", "password": "admin"},
		{"username": "admin", "password": "password"},
		{"username": "admin", "password": "123456"},
		{"username": "test", "password": "test"},
	}

	for _, cred := range credentials {
		// 模拟登录尝试
		spo.scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "自动登录",
			fmt.Sprintf("尝试凭据: %s/%s", cred["username"], cred["password"]))

		// 这里应该实际发送登录请求
		// 如果成功，保存会话信息
	}

	return nil
}

// maintainSessionState 维护会话状态
func (spo *ScanPathOptimizer) maintainSessionState(ctx context.Context) error {
	spo.sessionManager.mutex.Lock()
	defer spo.sessionManager.mutex.Unlock()

	// 检查会话是否过期
	for sessionID, session := range spo.sessionManager.sessions {
		if time.Since(session.LastActivity) > 30*time.Minute {
			// 会话过期，尝试刷新
			spo.scanLogger.LogNodeWarning(logging.PhaseVulnScan, "会话管理",
				fmt.Sprintf("会话 %s 即将过期，尝试刷新", sessionID))

			// 这里应该实际刷新会话
			session.LastActivity = time.Now()
		}
	}

	return nil
}

// detectCSRFProtection 检测CSRF保护
func (spo *ScanPathOptimizer) detectCSRFProtection(ctx context.Context, baseURL string) (string, error) {
	// 模拟CSRF令牌检测
	// 在实际实现中，这里会分析页面内容查找CSRF令牌

	// 常见的CSRF令牌模式
	csrfPatterns := []string{
		"csrf_token",
		"_token",
		"authenticity_token",
		"__RequestVerificationToken",
	}

	// 模拟找到CSRF令牌
	for _, pattern := range csrfPatterns {
		// 这里应该实际分析页面内容
		if pattern == "csrf_token" {
			return "mock_csrf_token_12345", nil
		}
	}

	return "", nil
}

// generateDependencyBasedPaths 基于依赖关系生成路径
func (spo *ScanPathOptimizer) generateDependencyBasedPaths() []*ScanPath {
	var paths []*ScanPath

	// 根据依赖关系图生成扫描路径
	spo.dependencyGraph.mutex.RLock()
	defer spo.dependencyGraph.mutex.RUnlock()

	// 按优先级排序节点
	var sortedNodes []*DependencyNode
	for _, node := range spo.dependencyGraph.nodes {
		sortedNodes = append(sortedNodes, node)
	}

	sort.Slice(sortedNodes, func(i, j int) bool {
		return sortedNodes[i].Priority > sortedNodes[j].Priority
	})

	// 为每个高优先级节点生成扫描路径
	for _, node := range sortedNodes {
		if node.Priority >= 7 { // 只为高优先级节点生成路径
			path := &ScanPath{
				ID:            fmt.Sprintf("dep_path_%s", node.ID),
				Name:          fmt.Sprintf("依赖路径: %s", node.Type),
				Priority:      node.Priority,
				EstimatedTime: 5 * time.Minute,
				Dependencies:  node.Dependencies,
				Parallel:      false,
				Metadata:      make(map[string]interface{}),
			}

			// 添加扫描步骤
			step := &ScanStep{
				ID:         fmt.Sprintf("step_%s", node.ID),
				Type:       "request",
				URL:        node.URL,
				Method:     node.Method,
				Parameters: make(map[string]interface{}),
				Headers:    make(map[string]string),
				Timeout:    30 * time.Second,
				Retries:    3,
				Metadata:   make(map[string]interface{}),
			}

			path.Steps = []*ScanStep{step}
			paths = append(paths, path)
		}
	}

	return paths
}

// generateAPIBasedPaths 基于API端点生成路径
func (spo *ScanPathOptimizer) generateAPIBasedPaths(endpoints []*ScanAPIEndpoint) []*ScanPath {
	var paths []*ScanPath

	for i, endpoint := range endpoints {
		path := &ScanPath{
			ID:            fmt.Sprintf("api_path_%d", i),
			Name:          fmt.Sprintf("API路径: %s", endpoint.Path),
			Priority:      spo.calculateAPIPathPriority(endpoint),
			EstimatedTime: 3 * time.Minute,
			Dependencies:  make([]string, 0),
			Parallel:      true,
			Metadata:      make(map[string]interface{}),
		}

		// 添加API测试步骤
		step := &ScanStep{
			ID:         fmt.Sprintf("api_step_%d", i),
			Type:       "api_test",
			URL:        endpoint.URL,
			Method:     endpoint.Method,
			Parameters: make(map[string]interface{}),
			Headers:    map[string]string{"Content-Type": endpoint.ContentType},
			Timeout:    20 * time.Second,
			Retries:    2,
			Metadata:   make(map[string]interface{}),
		}

		path.Steps = []*ScanStep{step}
		paths = append(paths, path)
	}

	return paths
}

// calculateAPIPathPriority 计算API路径优先级
func (spo *ScanPathOptimizer) calculateAPIPathPriority(endpoint *ScanAPIEndpoint) int {
	priority := 5

	// 根据API特征调整优先级
	if strings.Contains(endpoint.Path, "/admin/") {
		priority += 3
	}
	if strings.Contains(endpoint.Path, "/user") {
		priority += 2
	}
	if endpoint.AuthRequired {
		priority += 1
	}
	if endpoint.Method == "POST" || endpoint.Method == "PUT" {
		priority += 1
	}

	return priority
}

// generateVulnerabilityBasedPaths 基于漏洞类型生成路径
func (spo *ScanPathOptimizer) generateVulnerabilityBasedPaths() []*ScanPath {
	var paths []*ScanPath

	// 常见漏洞类型的扫描路径
	vulnTypes := []string{"sql_injection", "xss", "file_inclusion", "command_injection"}

	for i, vulnType := range vulnTypes {
		path := &ScanPath{
			ID:            fmt.Sprintf("vuln_path_%s", vulnType),
			Name:          fmt.Sprintf("漏洞扫描: %s", vulnType),
			Priority:      8,
			EstimatedTime: 10 * time.Minute,
			Dependencies:  make([]string, 0),
			Parallel:      true,
			Metadata:      map[string]interface{}{"vuln_type": vulnType},
		}

		// 添加漏洞扫描步骤
		step := &ScanStep{
			ID:         fmt.Sprintf("vuln_step_%d", i),
			Type:       "vulnerability_scan",
			Parameters: map[string]interface{}{"vuln_type": vulnType},
			Headers:    make(map[string]string),
			Timeout:    60 * time.Second,
			Retries:    1,
			Metadata:   make(map[string]interface{}),
		}

		path.Steps = []*ScanStep{step}
		paths = append(paths, path)
	}

	return paths
}

// ===== 路径优化算法实现 =====

// optimizePaths 优化扫描路径
func (po *PathOptimizer) optimizePaths(paths []*ScanPath) ([]*ScanPath, error) {
	// 应用多种优化算法
	optimizedPaths := paths

	// 1. 优先级排序优化
	optimizedPaths = po.applyPriorityOptimization(optimizedPaths)

	// 2. 并行化优化
	optimizedPaths = po.applyParallelOptimization(optimizedPaths)

	// 3. 依赖关系优化
	optimizedPaths = po.applyDependencyOptimization(optimizedPaths)

	// 4. 时间约束优化
	optimizedPaths = po.applyTimeConstraintOptimization(optimizedPaths)

	return optimizedPaths, nil
}

// applyPriorityOptimization 应用优先级优化
func (po *PathOptimizer) applyPriorityOptimization(paths []*ScanPath) []*ScanPath {
	// 按优先级排序
	sort.Slice(paths, func(i, j int) bool {
		return paths[i].Priority > paths[j].Priority
	})

	return paths
}

// applyParallelOptimization 应用并行化优化
func (po *PathOptimizer) applyParallelOptimization(paths []*ScanPath) []*ScanPath {
	// 标记可以并行执行的路径
	for _, path := range paths {
		// 如果路径没有依赖关系，可以并行执行
		if len(path.Dependencies) == 0 {
			path.Parallel = true
		}
	}

	return paths
}

// applyDependencyOptimization 应用依赖关系优化
func (po *PathOptimizer) applyDependencyOptimization(paths []*ScanPath) []*ScanPath {
	// 根据依赖关系重新排序路径
	var optimized []*ScanPath
	processed := make(map[string]bool)

	// 首先处理没有依赖的路径
	for _, path := range paths {
		if len(path.Dependencies) == 0 && !processed[path.ID] {
			optimized = append(optimized, path)
			processed[path.ID] = true
		}
	}

	// 然后处理有依赖的路径
	for len(optimized) < len(paths) {
		for _, path := range paths {
			if processed[path.ID] {
				continue
			}

			// 检查依赖是否已满足
			dependenciesMet := true
			for _, dep := range path.Dependencies {
				if !processed[dep] {
					dependenciesMet = false
					break
				}
			}

			if dependenciesMet {
				optimized = append(optimized, path)
				processed[path.ID] = true
			}
		}
	}

	return optimized
}

// applyTimeConstraintOptimization 应用时间约束优化
func (po *PathOptimizer) applyTimeConstraintOptimization(paths []*ScanPath) []*ScanPath {
	// 根据时间约束调整路径
	var optimized []*ScanPath
	totalTime := time.Duration(0)
	maxTime := 60 * time.Minute // 最大扫描时间

	for _, path := range paths {
		if totalTime+path.EstimatedTime <= maxTime {
			optimized = append(optimized, path)
			totalTime += path.EstimatedTime
		} else {
			// 如果时间超限，跳过低优先级路径
			if path.Priority >= 8 {
				optimized = append(optimized, path)
				totalTime += path.EstimatedTime
			}
		}
	}

	return optimized
}

// registerAlgorithms 注册优化算法
func (po *PathOptimizer) registerAlgorithms() {
	// 注册不同的优化算法
	po.algorithms["priority"] = &PriorityOptimizationAlgorithm{}
	po.algorithms["parallel"] = &ParallelOptimizationAlgorithm{}
	po.algorithms["dependency"] = &DependencyOptimizationAlgorithm{}
	po.algorithms["time"] = &TimeOptimizationAlgorithm{}
}

// ===== 优化算法实现 =====

// PriorityOptimizationAlgorithm 优先级优化算法
type PriorityOptimizationAlgorithm struct{}

func (p *PriorityOptimizationAlgorithm) Optimize(paths []*ScanPath, constraints *OptimizationConstraints) ([]*ScanPath, error) {
	sort.Slice(paths, func(i, j int) bool {
		return paths[i].Priority > paths[j].Priority
	})
	return paths, nil
}

func (p *PriorityOptimizationAlgorithm) GetName() string {
	return "优先级优化算法"
}

func (p *PriorityOptimizationAlgorithm) GetDescription() string {
	return "根据路径优先级进行排序优化"
}

// ParallelOptimizationAlgorithm 并行优化算法
type ParallelOptimizationAlgorithm struct{}

func (p *ParallelOptimizationAlgorithm) Optimize(paths []*ScanPath, constraints *OptimizationConstraints) ([]*ScanPath, error) {
	parallelCount := 0
	for _, path := range paths {
		if len(path.Dependencies) == 0 && parallelCount < constraints.MaxParallel {
			path.Parallel = true
			parallelCount++
		}
	}
	return paths, nil
}

func (p *ParallelOptimizationAlgorithm) GetName() string {
	return "并行优化算法"
}

func (p *ParallelOptimizationAlgorithm) GetDescription() string {
	return "识别可并行执行的路径并进行优化"
}

// DependencyOptimizationAlgorithm 依赖优化算法
type DependencyOptimizationAlgorithm struct{}

func (d *DependencyOptimizationAlgorithm) Optimize(paths []*ScanPath, constraints *OptimizationConstraints) ([]*ScanPath, error) {
	// 拓扑排序实现依赖优化
	var result []*ScanPath
	processed := make(map[string]bool)

	for len(result) < len(paths) {
		progress := false
		for _, path := range paths {
			if processed[path.ID] {
				continue
			}

			dependenciesMet := true
			for _, dep := range path.Dependencies {
				if !processed[dep] {
					dependenciesMet = false
					break
				}
			}

			if dependenciesMet {
				result = append(result, path)
				processed[path.ID] = true
				progress = true
			}
		}

		if !progress {
			// 检测到循环依赖
			return nil, fmt.Errorf("检测到循环依赖")
		}
	}

	return result, nil
}

func (d *DependencyOptimizationAlgorithm) GetName() string {
	return "依赖优化算法"
}

func (d *DependencyOptimizationAlgorithm) GetDescription() string {
	return "根据依赖关系进行拓扑排序优化"
}

// TimeOptimizationAlgorithm 时间优化算法
type TimeOptimizationAlgorithm struct{}

func (t *TimeOptimizationAlgorithm) Optimize(paths []*ScanPath, constraints *OptimizationConstraints) ([]*ScanPath, error) {
	var result []*ScanPath
	totalTime := time.Duration(0)

	// 按优先级和时间效率排序
	sort.Slice(paths, func(i, j int) bool {
		efficiencyI := float64(paths[i].Priority) / float64(paths[i].EstimatedTime.Minutes())
		efficiencyJ := float64(paths[j].Priority) / float64(paths[j].EstimatedTime.Minutes())
		return efficiencyI > efficiencyJ
	})

	for _, path := range paths {
		if totalTime+path.EstimatedTime <= constraints.MaxTime {
			result = append(result, path)
			totalTime += path.EstimatedTime
		}
	}

	return result, nil
}

func (t *TimeOptimizationAlgorithm) GetName() string {
	return "时间优化算法"
}

func (t *TimeOptimizationAlgorithm) GetDescription() string {
	return "在时间约束下优化扫描路径"
}

// ===== API模式初始化 =====

// initializeAPIPatterns 初始化API模式
func (ad *ScanAPIDiscoverer) initializeAPIPatterns() {
	// REST API模式
	ad.patterns = append(ad.patterns, &ScanAPIPattern{
		Name:        "REST API",
		Pattern:     regexp.MustCompile(`/api/v\d+/\w+`),
		Method:      "GET",
		ContentType: "application/json",
		Confidence:  0.9,
		Examples:    []string{"/api/v1/users", "/api/v2/data"},
	})

	// GraphQL API模式
	ad.patterns = append(ad.patterns, &ScanAPIPattern{
		Name:        "GraphQL API",
		Pattern:     regexp.MustCompile(`/graphql`),
		Method:      "POST",
		ContentType: "application/json",
		Confidence:  0.95,
		Examples:    []string{"/graphql", "/api/graphql"},
	})

	// 管理API模式
	ad.patterns = append(ad.patterns, &ScanAPIPattern{
		Name:        "Admin API",
		Pattern:     regexp.MustCompile(`/admin/api/\w+`),
		Method:      "GET",
		ContentType: "application/json",
		Confidence:  0.8,
		Examples:    []string{"/admin/api/users", "/admin/api/settings"},
	})
}

// initializeDiscoveryRules 初始化发现规则
func (ad *ScanAPIDiscoverer) initializeDiscoveryRules() {
	// JavaScript分析规则
	ad.discoveryRules = append(ad.discoveryRules, &ScanDiscoveryRule{
		Name:     "JavaScript API分析",
		Type:     "js_analysis",
		Pattern:  `fetch\(['"]([^'"]+)['"]`,
		Enabled:  true,
		Priority: 8,
		Metadata: map[string]interface{}{"confidence": 0.8},
	})

	// 链接提取规则
	ad.discoveryRules = append(ad.discoveryRules, &ScanDiscoveryRule{
		Name:     "链接提取",
		Type:     "link_extraction",
		Pattern:  `href=['"]([^'"]+)['"]`,
		Enabled:  true,
		Priority: 6,
		Metadata: map[string]interface{}{"confidence": 0.6},
	})

	// 目录暴力破解规则
	ad.discoveryRules = append(ad.discoveryRules, &ScanDiscoveryRule{
		Name:     "目录暴力破解",
		Type:     "directory_bruteforce",
		Pattern:  "",
		Enabled:  true,
		Priority: 4,
		Metadata: map[string]interface{}{"confidence": 0.4},
	})
}
