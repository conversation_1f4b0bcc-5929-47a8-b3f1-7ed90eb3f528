import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    port: 3000,
    host: '0.0.0.0', // 支持IPv4和IPv6
    proxy: {
      '/api': {
        target: process.env.API_TARGET || 'http://127.0.0.1:8082',
        changeOrigin: true,
        secure: false,
        timeout: 10000,
        // 支持IPv4和IPv6的后备配置
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('代理错误:', err.message);
            // 如果IPv4连接失败，尝试IPv6
            if ((err as any).code === 'ECONNREFUSED' && typeof options.target === 'string' && options.target.includes('127.0.0.1')) {
              console.log('尝试IPv6连接...');
              options.target = 'http://[::1]:8080';
            }
          });
        },
      },
    },
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          element: ['element-plus'],
          charts: ['echarts'],
        },
      },
    },
  },
})
