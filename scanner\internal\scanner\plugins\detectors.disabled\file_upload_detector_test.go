package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestFileUploadDetectorBasicFunctionality 测试文件上传检测器基础功能
func TestFileUploadDetectorBasicFunctionality(t *testing.T) {
	detector := NewFileUploadDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "file-upload-comprehensive", detector.GetID())
	assert.Equal(t, "文件上传漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-434")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestFileUploadDetectorApplicability 测试文件上传检测器适用性
func TestFileUploadDetectorApplicability(t *testing.T) {
	detector := NewFileUploadDetector()

	// 测试有文件上传表单的目标
	fileFormTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/upload",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/upload",
				Method: "POST",
				Fields: map[string]string{
					"file":        "file",
					"description": "text",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(fileFormTarget))

	// 测试有上传字段的表单
	uploadFormTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/profile",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/profile/update",
				Method: "POST",
				Fields: map[string]string{
					"avatar_upload": "text",
					"name":          "text",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(uploadFormTarget))

	// 测试上传URL路径
	uploadURLTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/admin/file_upload",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(uploadURLTarget))

	// 测试有上传链接的目标
	uploadLinkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{
				URL:  "http://example.com/uploads/manager",
				Text: "File Manager",
			},
		},
	}
	assert.True(t, detector.IsApplicable(uploadLinkTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（无上传功能）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about",
		Protocol: "http",
		Port:     80,
	}
	assert.False(t, detector.IsApplicable(simpleTarget))
}

// TestFileUploadDetectorConfiguration 测试文件上传检测器配置
func TestFileUploadDetectorConfiguration(t *testing.T) {
	detector := NewFileUploadDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 15*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.True(t, config.FollowRedirects) // 文件上传检测跟随重定向

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       8,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestFileUploadDetectorData 测试文件上传检测器数据
func TestFileUploadDetectorData(t *testing.T) {
	detector := NewFileUploadDetector()

	// 检查上传路径
	assert.NotEmpty(t, detector.uploadPaths)
	assert.Greater(t, len(detector.uploadPaths), 30)

	// 检查危险扩展名
	assert.NotEmpty(t, detector.dangerousExtensions)
	assert.Greater(t, len(detector.dangerousExtensions), 50)

	// 检查安全问题模式
	assert.NotEmpty(t, detector.securityIssues)
	assert.Greater(t, len(detector.securityIssues), 10)

	// 检查上传模式
	assert.NotEmpty(t, detector.uploadPatterns)
	assert.Greater(t, len(detector.uploadPatterns), 5)

	// 检查文件类型模式
	assert.NotEmpty(t, detector.fileTypePatterns)
	assert.Greater(t, len(detector.fileTypePatterns), 5)

	// 检查特定的危险扩展名
	assert.Contains(t, detector.dangerousExtensions, "php")
	assert.Contains(t, detector.dangerousExtensions, "jsp")
	assert.Contains(t, detector.dangerousExtensions, "asp")
	assert.Contains(t, detector.dangerousExtensions, "exe")
}

// TestFileUploadDetectorUploadPageDetection 测试上传页面检测
func TestFileUploadDetectorUploadPageDetection(t *testing.T) {
	detector := NewFileUploadDetector()

	// 测试包含文件上传的页面
	uploadPageResponse := `
	<html>
	<body>
		<form action="/upload" method="post" enctype="multipart/form-data">
			<input type="file" name="file" accept="*/*">
			<input type="text" name="description">
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`
	assert.True(t, detector.isUploadPage(uploadPageResponse))

	// 测试包含中文上传页面
	chineseUploadPage := `
	<html>
	<body>
		<h1>文件上传</h1>
		<form action="/upload" method="post">
			<label>选择文件:</label>
			<input type="file" name="upload_file">
			<input type="submit" value="上传文件">
		</form>
	</body>
	</html>
	`
	assert.True(t, detector.isUploadPage(chineseUploadPage))

	// 测试不包含上传功能的页面
	normalPage := `
	<html>
	<body>
		<h1>Welcome</h1>
		<p>This is a normal page without upload functionality.</p>
	</body>
	</html>
	`
	assert.False(t, detector.isUploadPage(normalPage))
}

// TestFileUploadDetectorSecurityIssues 测试安全问题检测
func TestFileUploadDetectorSecurityIssues(t *testing.T) {
	detector := NewFileUploadDetector()

	// 测试包含安全问题的页面
	insecureUploadPage := `
	<html>
	<body>
		<form action="/upload" method="post" enctype="multipart/form-data">
			<input type="file" name="file" accept="*/*">
			<p>支持所有文件类型，无大小限制</p>
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`
	assert.True(t, detector.hasUploadSecurityIssues(insecureUploadPage))

	// 测试包含路径遍历风险的页面
	pathTraversalPage := `
	<html>
	<body>
		<form action="/upload" method="post">
			<input type="file" name="file">
			<input type="text" name="path" value="../uploads/">
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`
	assert.True(t, detector.hasPathTraversalRisk(pathTraversalPage))

	// 测试安全的上传页面
	secureUploadPage := `
	<html>
	<body>
		<form action="/upload" method="post" enctype="multipart/form-data">
			<input type="file" name="file" accept=".jpg,.png,.gif">
			<p>只支持图片文件，最大2MB</p>
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`
	assert.False(t, detector.hasUploadSecurityIssues(secureUploadPage))
}

// TestFileUploadDetectorFileTypeRestrictions 测试文件类型限制检测
func TestFileUploadDetectorFileTypeRestrictions(t *testing.T) {
	detector := NewFileUploadDetector()

	// 测试有文件类型限制的页面
	restrictedUploadPage := `
	<html>
	<body>
		<form action="/upload" method="post" enctype="multipart/form-data">
			<input type="file" name="file" accept=".jpg,.png,.gif,.pdf">
			<p>支持的文件类型: JPG, PNG, GIF, PDF</p>
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`
	assert.True(t, detector.hasFileTypeRestrictions(restrictedUploadPage))

	// 测试无明显文件类型限制的页面
	unrestrictedUploadPage := `
	<html>
	<body>
		<form action="/upload" method="post" enctype="multipart/form-data">
			<input type="file" name="file">
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`
	assert.False(t, detector.hasFileTypeRestrictions(unrestrictedUploadPage))
}

// TestFileUploadDetectorDangerousFileTypes 测试危险文件类型检测
func TestFileUploadDetectorDangerousFileTypes(t *testing.T) {
	detector := NewFileUploadDetector()

	// 测试允许所有文件类型的页面
	allFilesPage := `
	<html>
	<body>
		<form action="/upload" method="post" enctype="multipart/form-data">
			<input type="file" name="file" accept="*/*">
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`
	assert.True(t, detector.allowsDangerousFileTypes(allFilesPage))

	// 测试包含危险扩展名的页面
	dangerousExtPage := `
	<html>
	<body>
		<form action="/upload" method="post" enctype="multipart/form-data">
			<input type="file" name="file" accept=".php,.jsp,.asp">
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`
	assert.True(t, detector.allowsDangerousFileTypes(dangerousExtPage))

	// 测试只允许安全文件类型的页面
	safeFilesPage := `
	<html>
	<body>
		<form action="/upload" method="post" enctype="multipart/form-data">
			<input type="file" name="file" accept=".jpg,.png,.gif">
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`
	assert.False(t, detector.allowsDangerousFileTypes(safeFilesPage))
}

// TestFileUploadDetectorFormFileField 测试表单文件字段检查
func TestFileUploadDetectorFormFileField(t *testing.T) {
	detector := NewFileUploadDetector()

	// 测试有文件字段的表单
	fileForm := plugins.FormInfo{
		Action: "/upload",
		Method: "POST",
		Fields: map[string]string{
			"file":        "file",
			"description": "text",
		},
	}
	assert.True(t, detector.checkFormFileField(fileForm))

	// 测试有上传字段的表单
	uploadForm := plugins.FormInfo{
		Action: "/profile",
		Method: "POST",
		Fields: map[string]string{
			"avatar_upload": "text",
			"name":          "text",
		},
	}
	assert.True(t, detector.checkFormFileField(uploadForm))

	// 测试没有文件字段的表单
	normalForm := plugins.FormInfo{
		Action: "/contact",
		Method: "POST",
		Fields: map[string]string{
			"name":    "text",
			"email":   "email",
			"message": "textarea",
		},
	}
	assert.False(t, detector.checkFormFileField(normalForm))
}

// TestFileUploadDetectorRiskScore 测试风险评分计算
func TestFileUploadDetectorRiskScore(t *testing.T) {
	detector := NewFileUploadDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 4.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestFileUploadDetectorLifecycle 测试检测器生命周期
func TestFileUploadDetectorLifecycle(t *testing.T) {
	detector := NewFileUploadDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkFileUploadDetectorUploadPageCheck 基准测试上传页面检查性能
func BenchmarkFileUploadDetectorUploadPageCheck(b *testing.B) {
	detector := NewFileUploadDetector()
	response := `
	<html>
	<body>
		<form action="/upload" method="post" enctype="multipart/form-data">
			<input type="file" name="file" accept="*/*">
			<input type="text" name="description">
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.isUploadPage(response)
	}
}

// BenchmarkFileUploadDetectorSecurityCheck 基准测试安全检查性能
func BenchmarkFileUploadDetectorSecurityCheck(b *testing.B) {
	detector := NewFileUploadDetector()
	response := `
	<html>
	<body>
		<form action="/upload" method="post" enctype="multipart/form-data">
			<input type="file" name="file" accept="*/*">
			<p>支持所有文件类型，无大小限制</p>
			<input type="submit" value="Upload">
		</form>
	</body>
	</html>
	`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.hasUploadSecurityIssues(response)
	}
}
