package detectors

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// APIRateLimitingDetector API速率限制检测器
// 检测API端点是否缺乏适当的速率限制保护，可能导致DoS攻击或资源滥用
type APIRateLimitingDetector struct {
	id          string
	name        string
	category    string
	severity    string
	description string
	enabled     bool
	config      *plugins.DetectorConfig
	
	// 检测配置
	requestCount    int           // 测试请求数量
	requestInterval time.Duration // 请求间隔
	httpClient      *http.Client
}

// NewAPIRateLimitingDetector 创建API速率限制检测器实例
func NewAPIRateLimitingDetector() *APIRateLimitingDetector {
	return &APIRateLimitingDetector{
		id:          "api-rate-limiting-detector",
		name:        "API速率限制检测器",
		category:    "api_security",
		severity:    "medium",
		description: "检测API端点是否缺乏速率限制保护，防止DoS攻击和资源滥用",
		enabled:     true,
		
		// 默认检测配置
		requestCount:    20,                // 发送20个请求
		requestInterval: 100 * time.Millisecond, // 每100ms一个请求
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// 实现VulnerabilityDetector接口的基础方法
func (d *APIRateLimitingDetector) GetID() string                    { return d.id }
func (d *APIRateLimitingDetector) GetName() string                  { return d.name }
func (d *APIRateLimitingDetector) GetCategory() string              { return d.category }
func (d *APIRateLimitingDetector) GetSeverity() string              { return d.severity }
func (d *APIRateLimitingDetector) GetDescription() string           { return d.description }
func (d *APIRateLimitingDetector) IsEnabled() bool                  { return d.enabled }
func (d *APIRateLimitingDetector) SetEnabled(enabled bool)          { d.enabled = enabled }
func (d *APIRateLimitingDetector) GetConfiguration() *plugins.DetectorConfig { return d.config }
func (d *APIRateLimitingDetector) GetVersion() string               { return "1.0.0" }
func (d *APIRateLimitingDetector) GetAuthor() string                { return "Security Scanner Team" }
func (d *APIRateLimitingDetector) GetCreatedAt() time.Time          { return time.Now() }
func (d *APIRateLimitingDetector) GetUpdatedAt() time.Time          { return time.Now() }

func (d *APIRateLimitingDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	d.config = config
	
	// 从配置中读取参数
	if config != nil && config.Parameters != nil {
		if count, ok := config.Parameters["request_count"].(int); ok {
			d.requestCount = count
		}
		if interval, ok := config.Parameters["request_interval"].(string); ok {
			if duration, err := time.ParseDuration(interval); err == nil {
				d.requestInterval = duration
			}
		}
	}
	
	return nil
}

func (d *APIRateLimitingDetector) GetDependencies() []string {
	return []string{} // 无依赖
}

func (d *APIRateLimitingDetector) Initialize() error {
	logger.Infof("初始化API速率限制检测器: %s", d.id)
	return nil
}

func (d *APIRateLimitingDetector) Cleanup() error {
	logger.Infof("清理API速率限制检测器: %s", d.id)
	return nil
}

func (d *APIRateLimitingDetector) Validate() error {
	if d.requestCount <= 0 {
		return fmt.Errorf("请求数量必须大于0")
	}
	if d.requestInterval <= 0 {
		return fmt.Errorf("请求间隔必须大于0")
	}
	return nil
}

// 目标适用性检查
func (d *APIRateLimitingDetector) GetTargetTypes() []string {
	return []string{"url", "api"}
}

func (d *APIRateLimitingDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 5000, 8000, 9000}
}

func (d *APIRateLimitingDetector) GetRequiredServices() []string {
	return []string{"http", "https", "api"}
}

func (d *APIRateLimitingDetector) GetRequiredHeaders() []string {
	return []string{}
}

func (d *APIRateLimitingDetector) GetRequiredTechnologies() []string {
	return []string{"api", "rest", "graphql", "json", "xml"}
}

func (d *APIRateLimitingDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" {
		return false
	}
	
	// 检查协议
	if target.Protocol != "http" && target.Protocol != "https" {
		return false
	}
	
	// 检查是否为API端点
	url := strings.ToLower(target.URL)
	apiIndicators := []string{
		"/api/", "/rest/", "/graphql", "/v1/", "/v2/", "/v3/",
		".json", ".xml", "/service/", "/ws/", "/webservice/",
	}
	
	for _, indicator := range apiIndicators {
		if strings.Contains(url, indicator) {
			return true
		}
	}
	
	// 检查技术栈
	for _, tech := range target.Technologies {
		techLower := strings.ToLower(tech.Name)
		if strings.Contains(techLower, "api") ||
		   strings.Contains(techLower, "rest") ||
		   strings.Contains(techLower, "graphql") {
			return true
		}
	}
	
	return false
}

// 核心检测逻辑
func (d *APIRateLimitingDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	result := &plugins.DetectionResult{
		VulnerabilityID: d.generateVulnID(target),
		DetectorID:      d.id,
		IsVulnerable:    false,
		Confidence:      0.0,
		Severity:        d.severity,
		Title:           "API速率限制缺失",
		Category:        d.category,
		DetectedAt:      time.Now(),
		Metadata: map[string]interface{}{
			"url":            target.URL,
			"method":         "GET",
			"detector_type":  "rate_limiting",
		},
	}

	logger.Infof("开始检测API速率限制: %s", target.URL)

	// 执行速率限制检测
	rateLimitResult, err := d.performRateLimitTest(ctx, target)
	if err != nil {
		logger.Errorf("API速率限制检测失败: %v", err)
		return result, err
	}

	// 分析检测结果
	if rateLimitResult.IsVulnerable {
		result.IsVulnerable = true
		result.Confidence = rateLimitResult.Confidence
		result.Description = rateLimitResult.Description
		result.Evidence = rateLimitResult.Evidence
		result.Payload = rateLimitResult.TestDetails
		result.Remediation = "实施API速率限制机制，限制每个客户端的请求频率"
		result.References = []string{
			"https://owasp.org/www-community/controls/Rate_Limiting",
			"https://tools.ietf.org/html/rfc6585#section-4",
		}
		result.Tags = []string{"api", "rate-limiting", "dos", "resource-abuse"}
		result.RiskScore = d.calculateRiskScore(rateLimitResult)
		
		logger.Infof("检测到API速率限制缺失: %s (置信度: %.2f)", target.URL, result.Confidence)
	} else {
		result.Description = "API端点具有适当的速率限制保护"
		logger.Infof("API速率限制检测通过: %s", target.URL)
	}

	return result, nil
}

// RateLimitTestResult 速率限制测试结果
type RateLimitTestResult struct {
	IsVulnerable    bool                   `json:"is_vulnerable"`
	Confidence      float64                `json:"confidence"`
	Description     string                 `json:"description"`
	Evidence        []plugins.Evidence     `json:"evidence"`
	TestDetails     string                 `json:"test_details"`
	RequestsSent    int                    `json:"requests_sent"`
	SuccessfulReqs  int                    `json:"successful_requests"`
	BlockedReqs     int                    `json:"blocked_requests"`
	AverageRespTime time.Duration          `json:"average_response_time"`
	RateLimitHeaders map[string]string     `json:"rate_limit_headers"`
}

// performRateLimitTest 执行速率限制测试
func (d *APIRateLimitingDetector) performRateLimitTest(ctx context.Context, target *plugins.ScanTarget) (*RateLimitTestResult, error) {
	result := &RateLimitTestResult{
		IsVulnerable:     false,
		Confidence:       0.0,
		Evidence:         make([]plugins.Evidence, 0),
		RateLimitHeaders: make(map[string]string),
	}

	var responseTimes []time.Duration
	var statusCodes []int
	var rateLimitHeaders []map[string]string

	// 发送连续请求测试速率限制
	for i := 0; i < d.requestCount; i++ {
		select {
		case <-ctx.Done():
			return result, ctx.Err()
		default:
		}

		startTime := time.Now()
		
		// 发送HTTP请求
		req, err := http.NewRequestWithContext(ctx, "GET", target.URL, nil)
		if err != nil {
			continue
		}
		
		// 设置User-Agent
		req.Header.Set("User-Agent", "SecurityScanner/1.0")
		
		resp, err := d.httpClient.Do(req)
		if err != nil {
			continue
		}
		
		responseTime := time.Since(startTime)
		responseTimes = append(responseTimes, responseTime)
		statusCodes = append(statusCodes, resp.StatusCode)
		
		// 检查速率限制相关的HTTP头
		headers := make(map[string]string)
		rateLimitHeaderNames := []string{
			"X-RateLimit-Limit",
			"X-RateLimit-Remaining", 
			"X-RateLimit-Reset",
			"X-Rate-Limit-Limit",
			"X-Rate-Limit-Remaining",
			"X-Rate-Limit-Reset",
			"Retry-After",
			"X-Throttle-Limit",
			"X-Throttle-Remaining",
		}
		
		for _, headerName := range rateLimitHeaderNames {
			if value := resp.Header.Get(headerName); value != "" {
				headers[headerName] = value
				result.RateLimitHeaders[headerName] = value
			}
		}
		rateLimitHeaders = append(rateLimitHeaders, headers)
		
		resp.Body.Close()
		
		// 统计成功和被阻止的请求
		if resp.StatusCode == 200 {
			result.SuccessfulReqs++
		} else if resp.StatusCode == 429 || resp.StatusCode == 503 {
			result.BlockedReqs++
		}
		
		result.RequestsSent++
		
		// 请求间隔
		if i < d.requestCount-1 {
			time.Sleep(d.requestInterval)
		}
	}

	// 计算平均响应时间
	if len(responseTimes) > 0 {
		var total time.Duration
		for _, rt := range responseTimes {
			total += rt
		}
		result.AverageRespTime = total / time.Duration(len(responseTimes))
	}

	// 分析结果
	d.analyzeRateLimitResults(result, statusCodes, rateLimitHeaders)

	return result, nil
}

// analyzeRateLimitResults 分析速率限制测试结果
func (d *APIRateLimitingDetector) analyzeRateLimitResults(result *RateLimitTestResult, statusCodes []int, rateLimitHeaders []map[string]string) {
	// 检查是否有速率限制响应码 (429, 503)
	hasRateLimitResponse := false
	for _, code := range statusCodes {
		if code == 429 || code == 503 {
			hasRateLimitResponse = true
			break
		}
	}

	// 检查是否有速率限制HTTP头
	hasRateLimitHeaders := len(result.RateLimitHeaders) > 0

	// 计算成功请求比例
	successRate := float64(result.SuccessfulReqs) / float64(result.RequestsSent)

	// 判断是否存在速率限制
	if hasRateLimitResponse || hasRateLimitHeaders {
		// 有速率限制机制
		result.IsVulnerable = false
		result.Confidence = 0.9
		result.Description = "API端点具有速率限制保护机制"
		
		evidence := plugins.Evidence{
			Type:        "response",
			Description: "检测到速率限制机制",
			Timestamp:   time.Now(),
		}
		
		if hasRateLimitResponse {
			evidence.Content = fmt.Sprintf("收到速率限制响应码，被阻止请求: %d/%d", result.BlockedReqs, result.RequestsSent)
		} else {
			evidence.Content = fmt.Sprintf("检测到速率限制HTTP头: %v", result.RateLimitHeaders)
		}
		
		result.Evidence = append(result.Evidence, evidence)
		
	} else if successRate > 0.9 {
		// 没有速率限制，大部分请求成功
		result.IsVulnerable = true
		result.Confidence = 0.8
		result.Description = "API端点缺乏速率限制保护，可能遭受DoS攻击或资源滥用"
		
		result.Evidence = append(result.Evidence, plugins.Evidence{
			Type:        "response",
			Description: "连续请求未被限制",
			Content:     fmt.Sprintf("发送 %d 个请求，成功 %d 个 (%.1f%%)，未检测到速率限制机制", result.RequestsSent, result.SuccessfulReqs, successRate*100),
			Timestamp:   time.Now(),
		})
		
		result.TestDetails = fmt.Sprintf("请求统计: 发送=%d, 成功=%d, 被阻止=%d, 成功率=%.1f%%, 平均响应时间=%v", 
			result.RequestsSent, result.SuccessfulReqs, result.BlockedReqs, successRate*100, result.AverageRespTime)
			
	} else {
		// 部分请求失败，但不确定是否为速率限制
		result.IsVulnerable = false
		result.Confidence = 0.3
		result.Description = "无法确定API端点的速率限制状态"
		
		result.Evidence = append(result.Evidence, plugins.Evidence{
			Type:        "response", 
			Description: "请求结果不确定",
			Content:     fmt.Sprintf("发送 %d 个请求，成功 %d 个，可能存在其他限制机制", result.RequestsSent, result.SuccessfulReqs),
			Timestamp:   time.Now(),
		})
	}
}

// calculateRiskScore 计算风险评分
func (d *APIRateLimitingDetector) calculateRiskScore(result *RateLimitTestResult) float64 {
	if !result.IsVulnerable {
		return 0.0
	}
	
	baseScore := 5.0 // 基础分数
	
	// 根据成功率调整分数
	successRate := float64(result.SuccessfulReqs) / float64(result.RequestsSent)
	if successRate > 0.95 {
		baseScore += 2.0 // 几乎所有请求都成功，风险较高
	} else if successRate > 0.8 {
		baseScore += 1.0
	}
	
	// 根据响应时间调整分数
	if result.AverageRespTime < 500*time.Millisecond {
		baseScore += 1.0 // 响应很快，可能更容易被滥用
	}
	
	// 确保分数在合理范围内
	if baseScore > 10.0 {
		baseScore = 10.0
	}
	
	return baseScore
}

// 验证检测结果
func (d *APIRateLimitingDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	verifyResult := &plugins.VerificationResult{
		IsVerified: false,
		Confidence: 0.0,
		Method:     "rate-limit-retest",
		Notes:      "重新测试API速率限制",
		VerifiedAt: time.Now(),
	}

	if !result.IsVulnerable {
		verifyResult.IsVerified = true
		verifyResult.Confidence = 1.0
		verifyResult.Notes = "检测结果为安全，无需验证"
		return verifyResult, nil
	}

	// 重新执行速率限制测试进行验证
	rateLimitResult, err := d.performRateLimitTest(ctx, target)
	if err != nil {
		return verifyResult, err
	}

	if rateLimitResult.IsVulnerable {
		verifyResult.IsVerified = true
		verifyResult.Confidence = rateLimitResult.Confidence
		verifyResult.Notes = "验证确认API缺乏速率限制保护"
	} else {
		verifyResult.IsVerified = false
		verifyResult.Confidence = 0.0
		verifyResult.Notes = "验证时发现API已有速率限制保护，可能为误报"
	}

	return verifyResult, nil
}

// 生成漏洞ID
func (d *APIRateLimitingDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("%s_%s_%d", d.id, target.Domain, time.Now().Unix())
}

// GetCVE 返回相关CVE（此检测器不针对特定CVE）
func (d *APIRateLimitingDetector) GetCVE() []string {
	return []string{}
}

// GetCWE 返回相关CWE
func (d *APIRateLimitingDetector) GetCWE() []string {
	return []string{"CWE-770", "CWE-400"} // 资源分配不当、不受控制的资源消耗
}
