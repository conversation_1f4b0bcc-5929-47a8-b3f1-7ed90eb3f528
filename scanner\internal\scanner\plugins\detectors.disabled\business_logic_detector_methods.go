package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectPaymentLogicFlaws 检测支付逻辑漏洞
func (d *BusinessLogicDetector) detectPaymentLogicFlaws(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试价格篡改
	priceTestCases := []struct {
		name  string
		value string
	}{
		{"负价格", "-100"},
		{"零价格", "0"},
		{"极小价格", "0.01"},
		{"极大价格", "999999999"},
		{"非数字价格", "abc"},
		{"SQL注入价格", "1' OR '1'='1"},
	}

	for _, param := range d.priceParameters {
		for _, testCase := range priceTestCases {
			select {
			case <-ctx.Done():
				return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
			default:
			}

			// 构造测试参数
			testParams := map[string]string{param: testCase.value}

			// 发送支付请求
			resp, err := d.sendBusinessRequest(ctx, target.URL, testParams)
			if err != nil {
				continue
			}

			// 检查支付逻辑响应
			confidence := d.checkPaymentLogicResponse(resp, param, testCase.value)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("%s=%s", param, testCase.value)
				vulnerableRequest = target.URL
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "payment-logic-flaw",
					Description: fmt.Sprintf("发现支付逻辑漏洞: %s=%s (置信度: %.2f)", param, testCase.value, confidence),
					Content:     d.extractBusinessEvidence(resp, param),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Second * 1)
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectBusinessProcessBypass 检测业务流程绕过
func (d *BusinessLogicDetector) detectBusinessProcessBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试步骤跳过
	stepTestCases := []struct {
		name  string
		value string
	}{
		{"跳过步骤", "999"},
		{"负步骤", "-1"},
		{"零步骤", "0"},
		{"非数字步骤", "final"},
		{"SQL注入步骤", "1' OR '1'='1"},
	}

	stepParameters := []string{"step", "stage", "phase", "level", "page", "阶段", "步骤", "页面"}

	for _, param := range stepParameters {
		for _, testCase := range stepTestCases {
			select {
			case <-ctx.Done():
				return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
			default:
			}

			// 构造测试参数
			testParams := map[string]string{param: testCase.value}

			// 发送业务流程请求
			resp, err := d.sendBusinessRequest(ctx, target.URL, testParams)
			if err != nil {
				continue
			}

			// 检查业务流程响应
			confidence := d.checkBusinessProcessResponse(resp, param, testCase.value)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("%s=%s", param, testCase.value)
				vulnerableRequest = target.URL
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "business-process-bypass",
					Description: fmt.Sprintf("发现业务流程绕过: %s=%s (置信度: %.2f)", param, testCase.value, confidence),
					Content:     d.extractBusinessEvidence(resp, param),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 800)
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDataValidationFlaws 检测数据验证问题
func (d *BusinessLogicDetector) detectDataValidationFlaws(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试数量篡改
	quantityTestCases := []struct {
		name  string
		value string
	}{
		{"负数量", "-1"},
		{"零数量", "0"},
		{"极大数量", "999999999"},
		{"小数数量", "0.5"},
		{"非数字数量", "abc"},
		{"SQL注入数量", "1' OR '1'='1"},
	}

	for _, param := range d.quantityParameters {
		for _, testCase := range quantityTestCases {
			select {
			case <-ctx.Done():
				return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
			default:
			}

			// 构造测试参数
			testParams := map[string]string{param: testCase.value}

			// 发送数据验证请求
			resp, err := d.sendBusinessRequest(ctx, target.URL, testParams)
			if err != nil {
				continue
			}

			// 检查数据验证响应
			confidence := d.checkDataValidationResponse(resp, param, testCase.value)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("%s=%s", param, testCase.value)
				vulnerableRequest = target.URL
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "data-validation-flaw",
					Description: fmt.Sprintf("发现数据验证问题: %s=%s (置信度: %.2f)", param, testCase.value, confidence),
					Content:     d.extractBusinessEvidence(resp, param),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 600)
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectLogicFlaws 检测逻辑缺陷
func (d *BusinessLogicDetector) detectLogicFlaws(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试重定向篡改
	for _, param := range d.redirectParameters {
		redirectTestCases := []struct {
			name  string
			value string
		}{
			{"外部重定向", "http://evil.com"},
			{"内部重定向", "//evil.com"},
			{"协议重定向", "javascript:alert(1)"},
			{"文件重定向", "file:///etc/passwd"},
			{"数据重定向", "data:text/html,<script>alert(1)</script>"},
		}

		for _, testCase := range redirectTestCases {
			select {
			case <-ctx.Done():
				return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
			default:
			}

			// 构造测试参数
			testParams := map[string]string{param: testCase.value}

			// 发送逻辑缺陷请求
			resp, err := d.sendBusinessRequest(ctx, target.URL, testParams)
			if err != nil {
				continue
			}

			// 检查逻辑缺陷响应
			confidence := d.checkLogicFlawResponse(resp, param, testCase.value)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("%s=%s", param, testCase.value)
				vulnerableRequest = target.URL
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "logic-flaw",
					Description: fmt.Sprintf("发现逻辑缺陷: %s=%s (置信度: %.2f)", param, testCase.value, confidence),
					Content:     d.extractBusinessEvidence(resp, param),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Second * 1)
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendBusinessRequest 发送业务请求
func (d *BusinessLogicDetector) sendBusinessRequest(ctx context.Context, targetURL string, params map[string]string) (string, error) {
	// 构造POST数据
	data := url.Values{}
	for key, value := range params {
		data.Set(key, value)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkPaymentLogicResponse 检查支付逻辑响应
func (d *BusinessLogicDetector) checkPaymentLogicResponse(response, param, value string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	} else if strings.Contains(response, "status: 302") || strings.Contains(response, "status: 301") {
		confidence += 0.4 // 重定向可能表示支付成功
	}

	// 检查成功模式
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查逻辑模式
	for _, pattern := range d.logicPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查特定的支付成功指示器
	if strings.Contains(response, "payment successful") ||
		strings.Contains(response, "transaction completed") ||
		strings.Contains(response, "order confirmed") ||
		strings.Contains(response, "支付成功") ||
		strings.Contains(response, "交易完成") {
		confidence += 0.5
	}

	// 检查价格相关的异常值处理
	if param == "price" || param == "amount" || param == "total" {
		if value == "0" || value == "-100" || value == "0.01" {
			// 如果异常价格被接受，增加置信度
			if !strings.Contains(response, "error") && !strings.Contains(response, "invalid") {
				confidence += 0.3
			}
		}
	}

	// 检查错误模式（如果有错误，降低置信度）
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence -= 0.2
			break
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}
	if confidence < 0.0 {
		confidence = 0.0
	}

	return confidence
}

// checkBusinessProcessResponse 检查业务流程响应
func (d *BusinessLogicDetector) checkBusinessProcessResponse(response, param, value string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.4
	}

	// 检查成功模式
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查步骤跳过的特定指示器
	if param == "step" || param == "stage" || param == "phase" {
		if value == "999" || value == "-1" || value == "final" {
			// 如果异常步骤被接受，增加置信度
			if strings.Contains(response, "step") || strings.Contains(response, "stage") ||
				strings.Contains(response, "complete") || strings.Contains(response, "final") {
				confidence += 0.4
			}
		}
	}

	// 检查业务流程相关的内容
	if strings.Contains(response, "process") || strings.Contains(response, "workflow") ||
		strings.Contains(response, "流程") || strings.Contains(response, "步骤") {
		confidence += 0.2
	}

	// 检查错误模式
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence -= 0.3
			break
		}
	}

	return confidence
}

// checkDataValidationResponse 检查数据验证响应
func (d *BusinessLogicDetector) checkDataValidationResponse(response, param, value string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查数量相关的异常值处理
	if strings.Contains(param, "quantity") || strings.Contains(param, "qty") || strings.Contains(param, "count") {
		if value == "-1" || value == "0" || value == "999999999" {
			// 如果异常数量被接受，增加置信度
			if !strings.Contains(response, "error") && !strings.Contains(response, "invalid") {
				confidence += 0.5
			}
		}
	}

	// 检查成功模式
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查验证相关的内容
	if strings.Contains(response, "validation") || strings.Contains(response, "verify") ||
		strings.Contains(response, "验证") || strings.Contains(response, "校验") {
		confidence += 0.2
	}

	// 检查错误模式
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence -= 0.4
			break
		}
	}

	return confidence
}

// checkLogicFlawResponse 检查逻辑缺陷响应
func (d *BusinessLogicDetector) checkLogicFlawResponse(response, param, value string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查重定向相关的逻辑缺陷
	if strings.Contains(param, "redirect") || strings.Contains(param, "return") || strings.Contains(param, "callback") {
		// 检查是否有重定向响应
		if strings.Contains(response, "status: 302") || strings.Contains(response, "status: 301") {
			confidence += 0.4
		}

		// 检查Location头是否包含恶意URL
		if strings.Contains(response, "location:") {
			if strings.Contains(value, "evil.com") || strings.Contains(value, "javascript:") {
				confidence += 0.6
			}
		}
	}

	// 检查成功模式
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查错误模式
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence -= 0.3
			break
		}
	}

	return confidence
}

// checkBusinessLogicResponse 检查业务逻辑响应（通用方法）
func (d *BusinessLogicDetector) checkBusinessLogicResponse(response string, params map[string]string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查成功模式
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查逻辑模式
	for _, pattern := range d.logicPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	return confidence
}

// extractBusinessEvidence 提取业务证据
func (d *BusinessLogicDetector) extractBusinessEvidence(response, param string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 查找包含业务相关信息的行
	var businessLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)
		if strings.Contains(lineLower, "payment") ||
			strings.Contains(lineLower, "order") ||
			strings.Contains(lineLower, "total") ||
			strings.Contains(lineLower, "amount") ||
			strings.Contains(lineLower, "price") ||
			strings.Contains(lineLower, "success") ||
			strings.Contains(lineLower, "error") ||
			strings.Contains(lineLower, "支付") ||
			strings.Contains(lineLower, "订单") ||
			strings.Contains(lineLower, "金额") ||
			strings.Contains(lineLower, "成功") ||
			strings.Contains(lineLower, "错误") {
			businessLines = append(businessLines, line)
			if len(businessLines) >= 5 { // 只取前5行
				break
			}
		}
	}

	if len(businessLines) > 0 {
		return strings.Join(businessLines, "\n")
	}

	// 如果没有找到特定的业务信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
