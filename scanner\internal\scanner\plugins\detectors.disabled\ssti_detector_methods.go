package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectMathExpressions 检测数学表达式
func (d *SSTIDetector) detectMathExpressions(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试数学表达式载荷
	for _, payload := range d.mathPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送数学表达式请求
		resp, err := d.sendSSTIRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查数学表达式响应
		confidence := d.checkMathExpressionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("SSTI数学表达式: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.7 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ssti-math",
				Description: fmt.Sprintf("发现SSTI数学表达式注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractSSTIEvidence(resp, "ssti-math"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectConfigLeakage 检测配置泄露
func (d *SSTIDetector) detectConfigLeakage(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试配置泄露载荷
	for _, payload := range d.configPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送配置泄露请求
		resp, err := d.sendSSTIRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查配置泄露响应
		confidence := d.checkConfigLeakageResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("SSTI配置泄露: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ssti-config",
				Description: fmt.Sprintf("发现SSTI配置泄露: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractSSTIEvidence(resp, "ssti-config"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 400)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCodeExecution 检测代码执行
func (d *SSTIDetector) detectCodeExecution(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试代码执行载荷
	for _, payload := range d.codePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送代码执行请求
		resp, err := d.sendSSTIRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查代码执行响应
		confidence := d.checkCodeExecutionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("SSTI代码执行: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.8 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ssti-code",
				Description: fmt.Sprintf("发现SSTI代码执行: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractSSTIEvidence(resp, "ssti-code"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 500)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectErrorTriggers 检测错误触发
func (d *SSTIDetector) detectErrorTriggers(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试错误触发载荷
	for _, payload := range d.errorPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送错误触发请求
		resp, err := d.sendSSTIRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查错误触发响应
		confidence := d.checkErrorTriggerResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("SSTI错误触发: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ssti-error",
				Description: fmt.Sprintf("发现SSTI错误触发: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractSSTIEvidence(resp, "ssti-error"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendSSTIRequest 发送SSTI请求
func (d *SSTIDetector) sendSSTIRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendSSTIGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST参数注入
	postResp, err := d.sendSSTIPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 返回GET响应（即使有错误）
	if getResp != "" {
		return getResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendSSTIGETRequest 发送SSTI GET请求
func (d *SSTIDetector) sendSSTIGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()
	testParams := []string{"q", "search", "query", "input", "data", "content", "text", "value", "param", "test"}

	for _, param := range testParams {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendSSTIPOSTRequest 发送SSTI POST请求
func (d *SSTIDetector) sendSSTIPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造POST数据
	testParams := []string{"q", "search", "query", "input", "data", "content", "text", "value", "param", "test"}
	postData := url.Values{}

	for _, param := range testParams {
		postData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkMathExpressionResponse 检查数学表达式响应
func (d *SSTIDetector) checkMathExpressionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2
	}

	// 检查数学表达式计算结果
	if strings.Contains(payload, "7*7") && strings.Contains(response, "49") {
		confidence += 0.8 // 数学表达式被正确计算
	} else if strings.Contains(payload, "7+7") && strings.Contains(response, "14") {
		confidence += 0.8
	} else if strings.Contains(payload, "7-7") && strings.Contains(response, "0") {
		confidence += 0.8
	} else if strings.Contains(payload, "7**2") && strings.Contains(response, "49") {
		confidence += 0.8
	}

	// 检查模板特征模式匹配
	for _, pattern := range d.templatePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	// 检查模板引擎特定响应
	if d.checkTemplateEngineResponse(response, payload) {
		confidence += 0.4
	}

	return confidence
}

// checkConfigLeakageResponse 检查配置泄露响应
func (d *SSTIDetector) checkConfigLeakageResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2
	}

	// 检查配置信息泄露
	configIndicators := []string{
		"secret_key", "database_url", "api_key", "password", "token",
		"config", "settings", "debug", "development", "production",
		"flask", "django", "jinja2", "twig", "smarty", "freemarker",
		"<class", "dict_items", "dict_keys", "dict_values",
		"配置", "密钥", "密码", "令牌", "设置", "调试",
	}

	for _, indicator := range configIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
			break
		}
	}

	// 检查模板特征模式匹配
	for _, pattern := range d.templatePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.1
	}

	return confidence
}

// checkCodeExecutionResponse 检查代码执行响应
func (d *SSTIDetector) checkCodeExecutionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2
	}

	// 检查命令执行结果
	codeExecutionIndicators := []string{
		"uid=", "gid=", "root:", "bin/bash", "bin/sh", "cmd.exe",
		"windows", "system32", "program files", "etc/passwd", "etc/shadow",
		"用户", "系统", "管理员", "根目录",
	}

	for _, indicator := range codeExecutionIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.7 // 代码执行成功的强指示器
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查错误模式匹配（可能表示尝试执行）
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查模板特征模式匹配
	for _, pattern := range d.templatePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	return confidence
}

// checkErrorTriggerResponse 检查错误触发响应
func (d *SSTIDetector) checkErrorTriggerResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码（错误状态码也可能表示SSTI）
	if strings.Contains(response, "status: 500") {
		confidence += 0.3 // 内部服务器错误可能表示模板错误
	} else if strings.Contains(response, "status: 400") {
		confidence += 0.2 // 请求错误可能表示语法错误
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查模板特征模式匹配
	for _, pattern := range d.templatePatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查模板引擎特定错误
	templateErrors := []string{
		"templatenotfound", "templatesyntaxerror", "templateassertionerror",
		"undefinederror", "twig_error", "smarty_compiler_exception",
		"freemarker.template.templateexception", "velocityexception",
		"jasperexception", "templateprocessingexception",
		"模板错误", "语法错误", "编译错误", "解析错误",
	}

	for _, error := range templateErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // 模板引擎特定错误的强指示器
			break
		}
	}

	return confidence
}

// checkTemplateEngineResponse 检查模板引擎特定响应
func (d *SSTIDetector) checkTemplateEngineResponse(response, payload string) bool {
	response = strings.ToLower(response)
	payload = strings.ToLower(payload)

	// Jinja2特征
	if strings.Contains(payload, "{{") && strings.Contains(payload, "}}") {
		jinja2Indicators := []string{
			"jinja2", "flask", "werkzeug", "templatenotfound",
			"templatesyntaxerror", "undefinederror",
		}
		for _, indicator := range jinja2Indicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}
	}

	// Twig特征
	if strings.Contains(payload, "{{") && strings.Contains(payload, "}}") {
		twigIndicators := []string{
			"twig", "symfony", "twig_error", "unknown filter",
			"unknown function", "unknown variable",
		}
		for _, indicator := range twigIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}
	}

	// Smarty特征
	if strings.Contains(payload, "{") && strings.Contains(payload, "}") {
		smartyIndicators := []string{
			"smarty", "smarty_compiler_exception", "unrecognized tag",
			"unknown modifier", "syntax error in template",
		}
		for _, indicator := range smartyIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}
	}

	// Freemarker特征
	if strings.Contains(payload, "${") && strings.Contains(payload, "}") {
		freemarkerIndicators := []string{
			"freemarker", "templateexception", "invalidreferenceexception",
			"expression.*is undefined", "evaluated to null or missing",
		}
		for _, indicator := range freemarkerIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}
	}

	// Velocity特征
	if strings.Contains(payload, "$") || strings.Contains(payload, "#") {
		velocityIndicators := []string{
			"velocity", "apache", "velocityexception", "parseexception",
			"methodinvocationexception", "unable to find resource",
		}
		for _, indicator := range velocityIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}
	}

	return false
}

// extractSSTIEvidence 提取SSTI证据
func (d *SSTIDetector) extractSSTIEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var sstiLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "ssti-math":
			if strings.Contains(lineLower, "49") ||
				strings.Contains(lineLower, "14") ||
				strings.Contains(lineLower, "0") ||
				strings.Contains(lineLower, "7*7") ||
				strings.Contains(lineLower, "7+7") ||
				strings.Contains(lineLower, "7-7") ||
				strings.Contains(lineLower, "math") ||
				strings.Contains(lineLower, "expression") ||
				strings.Contains(lineLower, "calculate") ||
				strings.Contains(lineLower, "数学") ||
				strings.Contains(lineLower, "表达式") ||
				strings.Contains(lineLower, "计算") {
				sstiLines = append(sstiLines, line)
			}
		case "ssti-config":
			if strings.Contains(lineLower, "config") ||
				strings.Contains(lineLower, "settings") ||
				strings.Contains(lineLower, "secret") ||
				strings.Contains(lineLower, "key") ||
				strings.Contains(lineLower, "password") ||
				strings.Contains(lineLower, "token") ||
				strings.Contains(lineLower, "debug") ||
				strings.Contains(lineLower, "flask") ||
				strings.Contains(lineLower, "django") ||
				strings.Contains(lineLower, "class") ||
				strings.Contains(lineLower, "dict") ||
				strings.Contains(lineLower, "配置") ||
				strings.Contains(lineLower, "设置") ||
				strings.Contains(lineLower, "密钥") ||
				strings.Contains(lineLower, "密码") ||
				strings.Contains(lineLower, "令牌") {
				sstiLines = append(sstiLines, line)
			}
		case "ssti-code":
			if strings.Contains(lineLower, "uid=") ||
				strings.Contains(lineLower, "gid=") ||
				strings.Contains(lineLower, "root:") ||
				strings.Contains(lineLower, "bash") ||
				strings.Contains(lineLower, "sh") ||
				strings.Contains(lineLower, "cmd") ||
				strings.Contains(lineLower, "windows") ||
				strings.Contains(lineLower, "system") ||
				strings.Contains(lineLower, "etc/") ||
				strings.Contains(lineLower, "execute") ||
				strings.Contains(lineLower, "exec") ||
				strings.Contains(lineLower, "command") ||
				strings.Contains(lineLower, "用户") ||
				strings.Contains(lineLower, "系统") ||
				strings.Contains(lineLower, "执行") ||
				strings.Contains(lineLower, "命令") {
				sstiLines = append(sstiLines, line)
			}
		case "ssti-error":
			if strings.Contains(lineLower, "error") ||
				strings.Contains(lineLower, "exception") ||
				strings.Contains(lineLower, "traceback") ||
				strings.Contains(lineLower, "syntax") ||
				strings.Contains(lineLower, "parse") ||
				strings.Contains(lineLower, "template") ||
				strings.Contains(lineLower, "undefined") ||
				strings.Contains(lineLower, "not found") ||
				strings.Contains(lineLower, "invalid") ||
				strings.Contains(lineLower, "jinja2") ||
				strings.Contains(lineLower, "twig") ||
				strings.Contains(lineLower, "smarty") ||
				strings.Contains(lineLower, "freemarker") ||
				strings.Contains(lineLower, "velocity") ||
				strings.Contains(lineLower, "错误") ||
				strings.Contains(lineLower, "异常") ||
				strings.Contains(lineLower, "语法") ||
				strings.Contains(lineLower, "模板") ||
				strings.Contains(lineLower, "未定义") ||
				strings.Contains(lineLower, "未找到") {
				sstiLines = append(sstiLines, line)
			}
		default:
			if strings.Contains(lineLower, "template") ||
				strings.Contains(lineLower, "jinja") ||
				strings.Contains(lineLower, "twig") ||
				strings.Contains(lineLower, "smarty") ||
				strings.Contains(lineLower, "freemarker") ||
				strings.Contains(lineLower, "velocity") ||
				strings.Contains(lineLower, "erb") ||
				strings.Contains(lineLower, "jsp") ||
				strings.Contains(lineLower, "thymeleaf") ||
				strings.Contains(lineLower, "error") ||
				strings.Contains(lineLower, "exception") ||
				strings.Contains(lineLower, "模板") ||
				strings.Contains(lineLower, "引擎") ||
				strings.Contains(lineLower, "错误") ||
				strings.Contains(lineLower, "异常") {
				sstiLines = append(sstiLines, line)
			}
		}

		if len(sstiLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(sstiLines) > 0 {
		return strings.Join(sstiLines, "\n")
	}

	// 如果没有找到特定的SSTI信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
