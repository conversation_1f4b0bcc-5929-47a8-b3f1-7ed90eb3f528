package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectJSONStructureInjection 检测JSON结构注入
func (d *JSONInjectionDetector) detectJSONStructureInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试JSON结构注入载荷
	for _, payload := range d.structurePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送JSON结构注入请求
		resp, err := d.sendJSONRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查JSON结构注入响应
		confidence := d.checkJSONStructureResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("JSON结构注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "json-structure-injection",
				Description: fmt.Sprintf("发现JSON结构注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractJSONEvidence(resp, "json-structure-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectTypeConfusion 检测数据类型混淆
func (d *JSONInjectionDetector) detectTypeConfusion(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试类型混淆载荷
	for _, payload := range d.typeConfusionPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送类型混淆请求
		resp, err := d.sendJSONRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查类型混淆响应
		confidence := d.checkTypeConfusionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("类型混淆: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "type-confusion",
				Description: fmt.Sprintf("发现类型混淆: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractJSONEvidence(resp, "type-confusion"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectLogicBypass 检测逻辑绕过
func (d *JSONInjectionDetector) detectLogicBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试逻辑绕过载荷
	for _, payload := range d.logicBypassPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送逻辑绕过请求
		resp, err := d.sendJSONRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查逻辑绕过响应
		confidence := d.checkLogicBypassResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("逻辑绕过: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "logic-bypass",
				Description: fmt.Sprintf("发现逻辑绕过: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractJSONEvidence(resp, "logic-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCodeExecution 检测代码执行
func (d *JSONInjectionDetector) detectCodeExecution(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试代码执行载荷
	for _, payload := range d.codeExecutionPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送代码执行请求
		resp, err := d.sendJSONRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查代码执行响应
		confidence := d.checkCodeExecutionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("代码执行: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "code-execution",
				Description: fmt.Sprintf("发现代码执行: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractJSONEvidence(resp, "code-execution"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendJSONRequest 发送JSON注入请求
func (d *JSONInjectionDetector) sendJSONRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendJSONGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST JSON注入
	postResp, err := d.sendJSONPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 尝试PUT JSON注入
	putResp, err := d.sendJSONPUTRequest(ctx, targetURL, payload)
	if err == nil && putResp != "" {
		return putResp, nil
	}

	// 返回POST响应（即使有错误）
	if postResp != "" {
		return postResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendJSONGETRequest 发送JSON注入GET请求
func (d *JSONInjectionDetector) sendJSONGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json, text/json, */*")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendJSONPOSTRequest 发送JSON注入POST请求
func (d *JSONInjectionDetector) sendJSONPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(payload))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json, text/json, */*")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendJSONPUTRequest 发送JSON注入PUT请求
func (d *JSONInjectionDetector) sendJSONPUTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "PUT", targetURL, strings.NewReader(payload))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json, text/json, */*")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkJSONStructureResponse 检查JSON结构注入响应
func (d *JSONInjectionDetector) checkJSONStructureResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 400") {
		confidence += 0.3 // 错误响应可能表示JSON解析错误
	}

	// 检查JSON结构模式匹配
	for _, pattern := range d.structurePatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查JSON结构特定指示器
	structureIndicators := []string{
		"\"admin\":", "\"role\":", "\"isadmin\":", "\"permissions\":",
		"\"user_role\":", "\"access_level\":", "\"privilege\":",
		"application/json", "text/json", "json", "结构注入", "管理员",
	}

	for _, indicator := range structureIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4 // JSON结构特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkTypeConfusionResponse 检查类型混淆响应
func (d *JSONInjectionDetector) checkTypeConfusionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 400") {
		confidence += 0.3 // 错误响应可能表示类型错误
	}

	// 检查类型模式匹配
	for _, pattern := range d.typePatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查类型混淆特定指示器
	typeIndicators := []string{
		"\"admin\": \"true\"", "\"admin\": \"false\"", "\"admin\": \"1\"", "\"admin\": \"0\"",
		"\"admin\": true", "\"admin\": false", "\"admin\": 1", "\"admin\": 0",
		"\"admin\": null", "\"admin\": []", "\"admin\": {}",
		"type error", "type mismatch", "类型错误", "类型混淆",
	}

	for _, indicator := range typeIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4 // 类型混淆特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkLogicBypassResponse 检查逻辑绕过响应
func (d *JSONInjectionDetector) checkLogicBypassResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 401") {
		confidence += 0.3 // 认证错误可能表示绕过尝试
	} else if strings.Contains(response, "status: 403") {
		confidence += 0.3 // 权限错误可能表示绕过尝试
	}

	// 检查逻辑模式匹配
	for _, pattern := range d.logicPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查逻辑绕过特定指示器
	logicIndicators := []string{
		"\"username\": \"admin\"", "\"password\": \"\"", "\"password\": null",
		"\"admin\": true", "\"role\": \"admin\"", "\"permissions\":",
		"\"price\": 0", "\"price\": -1", "\"quantity\": -1", "\"discount\": 100",
		"\"status\": \"active\"", "\"deleted\": false", "\"enabled\": true",
		"authentication", "authorization", "privilege", "逻辑绕过", "权限提升",
	}

	for _, indicator := range logicIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4 // 逻辑绕过特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkCodeExecutionResponse 检查代码执行响应
func (d *JSONInjectionDetector) checkCodeExecutionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示代码执行错误
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查代码执行特定指示器
	codeIndicators := []string{
		"\"eval\":", "\"script\":", "\"code\":", "\"function\":",
		"\"require\":", "\"process\":", "\"global\":", "\"module\":",
		"{{7*7}}", "${7*7}", "#{7*7}", "%{7*7}",
		"\"__proto__\":", "\"constructor\":", "prototype",
		"alert('xss')", "console.log", "function()", "代码执行", "脚本注入",
	}

	for _, indicator := range codeIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.6 // 代码执行特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// extractJSONEvidence 提取JSON注入证据
func (d *JSONInjectionDetector) extractJSONEvidence(response, injectionType string) string {
	// 限制证据长度
	maxLength := 500
	if len(response) > maxLength {
		response = response[:maxLength] + "..."
	}

	// 根据注入类型提取相关证据
	switch injectionType {
	case "json-structure-injection":
		return d.extractJSONStructureEvidence(response)
	case "type-confusion":
		return d.extractTypeConfusionEvidence(response)
	case "logic-bypass":
		return d.extractLogicBypassEvidence(response)
	case "code-execution":
		return d.extractCodeExecutionEvidence(response)
	default:
		return response
	}
}

// extractJSONStructureEvidence 提取JSON结构证据
func (d *JSONInjectionDetector) extractJSONStructureEvidence(response string) string {
	evidence := "JSON结构注入证据:\n"

	// 查找JSON结构特定内容
	structureIndicators := []string{
		"\"admin\":", "\"role\":", "\"isadmin\":", "\"permissions\":",
		"\"user_role\":", "\"access_level\":", "\"privilege\":",
		"application/json", "text/json",
	}

	for _, indicator := range structureIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现JSON结构指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractTypeConfusionEvidence 提取类型混淆证据
func (d *JSONInjectionDetector) extractTypeConfusionEvidence(response string) string {
	evidence := "类型混淆证据:\n"

	// 查找类型混淆特定内容
	typeIndicators := []string{
		"\"admin\": \"true\"", "\"admin\": \"false\"", "\"admin\": \"1\"", "\"admin\": \"0\"",
		"\"admin\": true", "\"admin\": false", "\"admin\": 1", "\"admin\": 0",
		"\"admin\": null", "\"admin\": []", "\"admin\": {}",
		"type error", "type mismatch",
	}

	for _, indicator := range typeIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现类型混淆指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractLogicBypassEvidence 提取逻辑绕过证据
func (d *JSONInjectionDetector) extractLogicBypassEvidence(response string) string {
	evidence := "逻辑绕过证据:\n"

	// 查找逻辑绕过特定内容
	logicIndicators := []string{
		"\"username\": \"admin\"", "\"password\": \"\"", "\"password\": null",
		"\"admin\": true", "\"role\": \"admin\"", "\"permissions\":",
		"\"price\": 0", "\"price\": -1", "\"quantity\": -1", "\"discount\": 100",
		"\"status\": \"active\"", "\"deleted\": false", "\"enabled\": true",
		"authentication", "authorization", "privilege",
	}

	for _, indicator := range logicIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现逻辑绕过指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractCodeExecutionEvidence 提取代码执行证据
func (d *JSONInjectionDetector) extractCodeExecutionEvidence(response string) string {
	evidence := "代码执行证据:\n"

	// 查找代码执行特定内容
	codeIndicators := []string{
		"\"eval\":", "\"script\":", "\"code\":", "\"function\":",
		"\"require\":", "\"process\":", "\"global\":", "\"module\":",
		"{{7*7}}", "${7*7}", "#{7*7}", "%{7*7}",
		"\"__proto__\":", "\"constructor\":", "prototype",
		"alert('xss')", "console.log", "function()",
	}

	for _, indicator := range codeIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现代码执行指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}
