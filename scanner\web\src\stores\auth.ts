// 认证状态管理

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { AuthApi } from '@/api/auth'
import type { User, LoginRequest } from '@/types/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const user = ref<User | null>(null)
  const permissions = ref<string[]>([])
  const isLoading = ref(false)

  // Token刷新定时器
  let refreshTimer: NodeJS.Timeout | null = null

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const userRole = computed(() => user.value?.role || '')
  const userName = computed(() => user.value?.username || '')
  const userEmail = computed(() => user.value?.email || '')

  // 初始化认证状态
  const initAuth = () => {
    const savedToken = localStorage.getItem('auth_token')
    const savedRefreshToken = localStorage.getItem('refresh_token')
    const savedUser = localStorage.getItem('user_info')
    const savedPermissions = localStorage.getItem('user_permissions')

    if (savedToken) {
      token.value = savedToken
    }
    if (savedRefreshToken) {
      refreshToken.value = savedRefreshToken
    }
    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        clearAuth()
      }
    }
    if (savedPermissions) {
      try {
        permissions.value = JSON.parse(savedPermissions)
      } catch (error) {
        console.error('解析权限信息失败:', error)
        permissions.value = []
      }
    }

    // 启动Token刷新定时器
    startTokenRefreshTimer()
  }

  // 用户登录
  const login = async (loginData: LoginRequest) => {
    try {
      isLoading.value = true
      const response = await AuthApi.login(loginData)
      
      // 保存认证信息
      token.value = response.token
      refreshToken.value = response.refreshToken
      user.value = response.user
      
      // 持久化存储
      localStorage.setItem('auth_token', response.token)
      localStorage.setItem('refresh_token', response.refreshToken)
      localStorage.setItem('user_info', JSON.stringify(response.user))
      
      // 获取用户权限
      await loadUserPermissions()

      // 启动Token刷新定时器
      startTokenRefreshTimer()

      ElMessage.success('登录成功')

      return response
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 用户登出
  const logout = async (showMessage = true) => {
    try {
      // 调用登出API
      if (token.value) {
        await AuthApi.logout()
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      // 清除本地状态
      clearAuth()
      
      if (showMessage) {
        ElMessage.success('已退出登录')
      }
    }
  }



  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      const userData = await AuthApi.getCurrentUser()
      user.value = userData
      localStorage.setItem('user_info', JSON.stringify(userData))
      return userData
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 加载用户权限
  const loadUserPermissions = async () => {
    try {
      const userPermissions = await AuthApi.getUserPermissions()
      permissions.value = userPermissions
      localStorage.setItem('user_permissions', JSON.stringify(userPermissions))
    } catch (error) {
      console.error('获取用户权限失败:', error)
      permissions.value = []
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (isAdmin.value) {
      return true // 管理员拥有所有权限
    }
    return permissions.value.includes(permission)
  }

  // 检查多个权限（需要全部拥有）
  const hasAllPermissions = (requiredPermissions: string[]): boolean => {
    if (isAdmin.value) {
      return true
    }
    return requiredPermissions.every(permission => 
      permissions.value.includes(permission)
    )
  }

  // 检查多个权限（拥有其中一个即可）
  const hasAnyPermission = (requiredPermissions: string[]): boolean => {
    if (isAdmin.value) {
      return true
    }
    return requiredPermissions.some(permission => 
      permissions.value.includes(permission)
    )
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      isLoading.value = true
      await AuthApi.changePassword({ oldPassword, newPassword })
      ElMessage.success('密码修改成功')
    } catch (error: any) {
      ElMessage.error(error.message || '密码修改失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 验证Token有效性
  const validateToken = async (): Promise<boolean> => {
    try {
      if (!token.value) {
        return false
      }

      // 检查Token是否即将过期（提前5分钟刷新）
      if (isTokenExpiringSoon()) {
        console.log('Token即将过期，尝试自动刷新')
        const refreshed = await refreshTokenFunc()
        if (!refreshed) {
          console.log('Token刷新失败，需要重新登录')
          return false
        }
      }

      return await AuthApi.validateToken()
    } catch (error) {
      console.error('Token验证失败:', error)
      // 如果验证失败，尝试刷新Token
      const refreshed = await refreshTokenFunc()
      if (refreshed) {
        // 刷新成功后重新验证
        try {
          return await AuthApi.validateToken()
        } catch (retryError) {
          console.error('Token刷新后验证仍失败:', retryError)
          return false
        }
      }
      return false
    }
  }

  // 检查Token是否即将过期
  const isTokenExpiringSoon = (): boolean => {
    if (!token.value) return false

    try {
      // 解析JWT Token获取过期时间
      const payload = JSON.parse(atob(token.value.split('.')[1]))
      const exp = payload.exp * 1000 // 转换为毫秒
      const now = Date.now()
      const fiveMinutes = 5 * 60 * 1000 // 5分钟

      return (exp - now) < fiveMinutes
    } catch (error) {
      console.error('解析Token失败:', error)
      return true // 解析失败时认为需要刷新
    }
  }

  // 刷新Token
  const refreshTokenFunc = async (): Promise<boolean> => {
    try {
      if (!refreshToken.value) {
        console.log('没有refresh token，无法刷新')
        return false
      }

      const response = await AuthApi.refreshToken({ refreshToken: refreshToken.value })

      // 更新Token
      token.value = response.access_token
      refreshToken.value = response.refresh_token

      // 保存到localStorage
      localStorage.setItem('auth_token', response.access_token)
      localStorage.setItem('refresh_token', response.refresh_token)

      console.log('Token刷新成功')
      return true
    } catch (error) {
      console.error('Token刷新失败:', error)
      // 刷新失败，清除认证信息
      clearAuth()
      return false
    }
  }

  // 启动Token刷新定时器
  const startTokenRefreshTimer = () => {
    // 清除现有定时器
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }

    // 每5分钟检查一次Token是否需要刷新
    refreshTimer = setInterval(async () => {
      if (token.value && isTokenExpiringSoon()) {
        console.log('定时器检测到Token即将过期，自动刷新')
        await refreshTokenFunc()
      }
    }, 5 * 60 * 1000) // 5分钟
  }

  // 停止Token刷新定时器
  const stopTokenRefreshTimer = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  // 清除认证状态
  const clearAuth = () => {
    // 停止Token刷新定时器
    stopTokenRefreshTimer()

    token.value = ''
    refreshToken.value = ''
    user.value = null
    permissions.value = []

    // 清除本地存储
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_info')
    localStorage.removeItem('user_permissions')
  }

  // 更新用户信息
  const updateUserInfo = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('user_info', JSON.stringify(user.value))
    }
  }

  // 临时认证方法（仅用于开发测试）
  const setTempAuth = () => {
    console.log('设置临时认证状态')
    token.value = 'temp_token_for_development'
    user.value = {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    permissions.value = ['*'] // 所有权限

    // 保存到localStorage
    localStorage.setItem('auth_token', token.value)
    localStorage.setItem('user_info', JSON.stringify(user.value))
    localStorage.setItem('user_permissions', JSON.stringify(permissions.value))
  }

  return {
    // 状态
    token,
    user,
    permissions,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    userRole,
    userName,
    userEmail,
    
    // 方法
    initAuth,
    login,
    logout,
    getCurrentUser,
    loadUserPermissions,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    changePassword,
    validateToken,
    refreshToken: refreshTokenFunc,
    isTokenExpiringSoon,
    startTokenRefreshTimer,
    stopTokenRefreshTimer,
    clearAuth,
    updateUserInfo,
    setTempAuth
  }
})
