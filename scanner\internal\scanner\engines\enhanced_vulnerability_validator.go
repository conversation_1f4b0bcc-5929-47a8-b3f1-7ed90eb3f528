package engines

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/types"
	"scanner/pkg/logger"
)

// EnhancedVulnerabilityValidator 增强漏洞验证器
// 集成参数影响验证，专门解决误报问题
type EnhancedVulnerabilityValidator struct {
	paramValidator   *ParameterImpactValidator
	responseAnalyzer *VulnResponseAnalyzer
	contextValidator *ContextValidator
	config           *ValidationConfig
}

// ValidationConfig 验证配置
type ValidationConfig struct {
	EnableParameterValidation bool          `json:"enable_parameter_validation"`
	EnableResponseAnalysis    bool          `json:"enable_response_analysis"`
	EnableContextValidation   bool          `json:"enable_context_validation"`
	MaxValidationTime         time.Duration `json:"max_validation_time"`
	FalsePositiveThreshold    float64       `json:"false_positive_threshold"`
	ConfidenceThreshold       float64       `json:"confidence_threshold"`
}

// VulnResponseAnalyzer 漏洞响应分析器
type VulnResponseAnalyzer struct {
	patterns map[string][]string
}

// ContextValidator 上下文验证器
type ContextValidator struct {
	businessRules map[string][]VulnValidationRule
}

// VulnValidationRule 漏洞验证规则
type VulnValidationRule struct {
	Name        string                                          `json:"name"`
	Description string                                          `json:"description"`
	Validator   func(*types.Vulnerability, *http.Response) bool `json:"-"`
	Weight      float64                                         `json:"weight"`
}

// VulnerabilityValidationResult 漏洞验证结果
type VulnerabilityValidationResult struct {
	Vulnerability     *types.Vulnerability            `json:"vulnerability"`
	IsValid           bool                            `json:"is_valid"`
	Confidence        float64                         `json:"confidence"`
	FalsePositiveRisk float64                         `json:"false_positive_risk"`
	ValidationDetails *VulnerabilityValidationDetails `json:"validation_details"`
	FilteringReasons  []string                        `json:"filtering_reasons"`
	RecommendedAction string                          `json:"recommended_action"`
	ProcessingTime    time.Duration                   `json:"processing_time"`
}

// VulnerabilityValidationDetails 漏洞验证详情
type VulnerabilityValidationDetails struct {
	ParameterImpact    *ParameterImpactResult    `json:"parameter_impact,omitempty"`
	ResponseAnalysis   *ResponseAnalysisResult   `json:"response_analysis,omitempty"`
	ContextValidation  *ContextValidationResult  `json:"context_validation,omitempty"`
	TechnicalEvidence  []string                  `json:"technical_evidence"`
	BusinessLogicCheck *BusinessLogicCheckResult `json:"business_logic_check,omitempty"`
}

// ResponseAnalysisResult 响应分析结果
type ResponseAnalysisResult struct {
	IsGenericResponse  bool     `json:"is_generic_response"`
	IsErrorPage        bool     `json:"is_error_page"`
	IsDefaultPage      bool     `json:"is_default_page"`
	ContentRelevance   float64  `json:"content_relevance"`
	SuspiciousPatterns []string `json:"suspicious_patterns"`
	ResponseType       string   `json:"response_type"`
}

// ContextValidationResult 上下文验证结果
type ContextValidationResult struct {
	IsContextRelevant  bool     `json:"is_context_relevant"`
	BusinessLogicValid bool     `json:"business_logic_valid"`
	PermissionCheck    bool     `json:"permission_check"`
	DataConsistency    float64  `json:"data_consistency"`
	ValidationErrors   []string `json:"validation_errors"`
}

// BusinessLogicCheckResult 业务逻辑检查结果
type BusinessLogicCheckResult struct {
	LogicValid          bool     `json:"logic_valid"`
	ExpectedBehavior    bool     `json:"expected_behavior"`
	SideEffectsDetected bool     `json:"side_effects_detected"`
	DataIntegrity       bool     `json:"data_integrity"`
	CheckDetails        []string `json:"check_details"`
}

// NewEnhancedVulnerabilityValidator 创建增强漏洞验证器
func NewEnhancedVulnerabilityValidator(config *ValidationConfig) *EnhancedVulnerabilityValidator {
	if config == nil {
		config = &ValidationConfig{
			EnableParameterValidation: true,
			EnableResponseAnalysis:    true,
			EnableContextValidation:   true,
			MaxValidationTime:         60 * time.Second,
			FalsePositiveThreshold:    0.7,
			ConfidenceThreshold:       0.6,
		}
	}

	validator := &EnhancedVulnerabilityValidator{
		config: config,
	}

	// 初始化组件
	validator.initializeComponents()

	return validator
}

// initializeComponents 初始化组件
func (evv *EnhancedVulnerabilityValidator) initializeComponents() {
	// 初始化参数验证器
	if evv.config.EnableParameterValidation {
		paramConfig := &ParameterValidationConfig{
			MaxValidationRequests: 8,
			RequestTimeout:        30 * time.Second,
			RequestInterval:       150 * time.Millisecond,
			SimilarityThreshold:   0.95,
			ImpactThreshold:       0.3,
			EnableDeepValidation:  true,
			EnableTimingAnalysis:  true,
		}
		evv.paramValidator = NewParameterImpactValidator(paramConfig)
	}

	// 初始化响应分析器
	if evv.config.EnableResponseAnalysis {
		evv.responseAnalyzer = &VulnResponseAnalyzer{
			patterns: map[string][]string{
				"generic_error": {
					"error", "exception", "failed", "invalid",
					"not found", "access denied", "forbidden",
				},
				"default_page": {
					"welcome", "home", "index", "default",
					"main page", "dashboard",
				},
				"success_indicators": {
					"success", "completed", "done", "ok",
					"successful", "processed",
				},
			},
		}
	}

	// 初始化上下文验证器
	if evv.config.EnableContextValidation {
		evv.contextValidator = &ContextValidator{
			businessRules: make(map[string][]VulnValidationRule),
		}
		evv.initializeBusinessRules()
	}
}

// ValidateVulnerability 验证漏洞
func (evv *EnhancedVulnerabilityValidator) ValidateVulnerability(
	ctx context.Context,
	vuln *types.Vulnerability,
) (*VulnerabilityValidationResult, error) {

	startTime := time.Now()
	logger.Infof("开始验证漏洞: %s - %s", vuln.Type, vuln.URL)

	result := &VulnerabilityValidationResult{
		Vulnerability: vuln,
		ValidationDetails: &VulnerabilityValidationDetails{
			TechnicalEvidence: make([]string, 0),
		},
		FilteringReasons: make([]string, 0),
	}

	// 设置验证超时
	validationCtx, cancel := context.WithTimeout(ctx, evv.config.MaxValidationTime)
	defer cancel()

	// 1. 参数影响验证
	if evv.config.EnableParameterValidation && evv.paramValidator != nil {
		paramResult, err := evv.validateParameterImpact(validationCtx, vuln)
		if err != nil {
			logger.Warnf("参数影响验证失败: %v", err)
		} else {
			result.ValidationDetails.ParameterImpact = paramResult

			// 基于参数验证结果调整误报风险
			if paramResult.IsParameterIgnored {
				result.FalsePositiveRisk += 0.5
				result.FilteringReasons = append(result.FilteringReasons, "参数被应用忽略")
			}

			if paramResult.IsDefaultBehavior {
				result.FalsePositiveRisk += 0.3
				result.FilteringReasons = append(result.FilteringReasons, "触发默认行为")
			}

			if !paramResult.HasRealImpact {
				result.FalsePositiveRisk += 0.4
				result.FilteringReasons = append(result.FilteringReasons, "参数无实际影响")
			}
		}
	}

	// 2. 响应分析
	if evv.config.EnableResponseAnalysis && evv.responseAnalyzer != nil {
		responseResult := evv.analyzeResponse(vuln)
		result.ValidationDetails.ResponseAnalysis = responseResult

		if responseResult.IsGenericResponse {
			result.FalsePositiveRisk += 0.3
			result.FilteringReasons = append(result.FilteringReasons, "响应为通用页面")
		}

		if responseResult.IsDefaultPage {
			result.FalsePositiveRisk += 0.4
			result.FilteringReasons = append(result.FilteringReasons, "响应为默认页面")
		}
	}

	// 3. 上下文验证
	if evv.config.EnableContextValidation && evv.contextValidator != nil {
		contextResult := evv.validateContext(vuln)
		result.ValidationDetails.ContextValidation = contextResult

		if !contextResult.IsContextRelevant {
			result.FalsePositiveRisk += 0.2
			result.FilteringReasons = append(result.FilteringReasons, "上下文不相关")
		}

		if !contextResult.BusinessLogicValid {
			result.FalsePositiveRisk += 0.25
			result.FilteringReasons = append(result.FilteringReasons, "业务逻辑不符")
		}
	}

	// 4. 业务逻辑检查
	businessLogicResult := evv.checkBusinessLogic(vuln)
	result.ValidationDetails.BusinessLogicCheck = businessLogicResult

	if !businessLogicResult.LogicValid {
		result.FalsePositiveRisk += 0.2
		result.FilteringReasons = append(result.FilteringReasons, "业务逻辑检查失败")
	}

	// 5. 计算最终结果
	result.Confidence = evv.calculateValidationConfidence(result.ValidationDetails)
	result.IsValid = result.FalsePositiveRisk < evv.config.FalsePositiveThreshold &&
		result.Confidence > evv.config.ConfidenceThreshold
	result.RecommendedAction = evv.getValidationRecommendedAction(result)
	result.ProcessingTime = time.Since(startTime)

	logger.Infof("漏洞验证完成: 有效=%v, 置信度=%.2f, 误报风险=%.2f, 耗时=%v",
		result.IsValid, result.Confidence, result.FalsePositiveRisk, result.ProcessingTime)

	return result, nil
}

// validateParameterImpact 验证参数影响
func (evv *EnhancedVulnerabilityValidator) validateParameterImpact(
	ctx context.Context,
	vuln *types.Vulnerability,
) (*ParameterImpactResult, error) {

	// 解析URL和参数
	u, err := url.Parse(vuln.URL)
	if err != nil {
		return nil, fmt.Errorf("解析URL失败: %v", err)
	}

	// 提取参数信息
	paramName, normalValue, testValue := evv.extractParameterInfo(vuln)
	if paramName == "" {
		return nil, fmt.Errorf("无法提取参数信息")
	}

	// 构建基础URL（不包含查询参数）
	baseURL := fmt.Sprintf("%s://%s%s", u.Scheme, u.Host, u.Path)

	// 执行参数影响验证
	return evv.paramValidator.ValidateParameterImpact(
		ctx,
		baseURL,
		paramName,
		normalValue,
		testValue,
	)
}

// extractParameterInfo 提取参数信息
func (evv *EnhancedVulnerabilityValidator) extractParameterInfo(vuln *types.Vulnerability) (string, string, string) {
	// 从漏洞信息中提取参数名称和值
	// 这里需要根据实际的漏洞数据结构来实现

	// 示例实现：从URL中提取第一个参数
	u, err := url.Parse(vuln.URL)
	if err != nil {
		return "", "", ""
	}

	query := u.Query()
	for paramName, values := range query {
		if len(values) > 0 {
			// 假设测试值包含在漏洞描述或payload中
			testValue := values[0]
			normalValue := "test" // 默认正常值

			// 尝试从漏洞描述中提取正常值
			if strings.Contains(vuln.Description, "原始值:") {
				// 解析原始值
			}

			return paramName, normalValue, testValue
		}
	}

	return "", "", ""
}

// analyzeResponse 分析响应
func (evv *EnhancedVulnerabilityValidator) analyzeResponse(vuln *types.Vulnerability) *ResponseAnalysisResult {
	result := &ResponseAnalysisResult{
		SuspiciousPatterns: make([]string, 0),
	}

	// 分析响应内容（这里需要根据实际的响应数据来实现）
	responseContent := vuln.Response // 假设漏洞对象包含响应内容

	// 检查是否为通用错误页面
	for _, pattern := range evv.responseAnalyzer.patterns["generic_error"] {
		if strings.Contains(strings.ToLower(responseContent), pattern) {
			result.IsGenericResponse = true
			result.SuspiciousPatterns = append(result.SuspiciousPatterns, "通用错误模式: "+pattern)
			break
		}
	}

	// 检查是否为默认页面
	for _, pattern := range evv.responseAnalyzer.patterns["default_page"] {
		if strings.Contains(strings.ToLower(responseContent), pattern) {
			result.IsDefaultPage = true
			result.SuspiciousPatterns = append(result.SuspiciousPatterns, "默认页面模式: "+pattern)
			break
		}
	}

	// 检查是否为错误页面
	if strings.Contains(responseContent, "404") || strings.Contains(responseContent, "403") ||
		strings.Contains(responseContent, "500") {
		result.IsErrorPage = true
		result.SuspiciousPatterns = append(result.SuspiciousPatterns, "HTTP错误页面")
	}

	// 计算内容相关性
	result.ContentRelevance = evv.calculateContentRelevance(vuln, responseContent)

	// 确定响应类型
	if result.IsErrorPage {
		result.ResponseType = "error_page"
	} else if result.IsDefaultPage {
		result.ResponseType = "default_page"
	} else if result.IsGenericResponse {
		result.ResponseType = "generic_response"
	} else {
		result.ResponseType = "specific_response"
	}

	return result
}

// validateContext 验证上下文
func (evv *EnhancedVulnerabilityValidator) validateContext(vuln *types.Vulnerability) *ContextValidationResult {
	result := &ContextValidationResult{
		ValidationErrors: make([]string, 0),
	}

	// 检查漏洞类型的上下文相关性
	result.IsContextRelevant = evv.isVulnerabilityContextRelevant(vuln)

	// 验证业务逻辑
	result.BusinessLogicValid = evv.validateBusinessLogic(vuln)

	// 检查权限
	result.PermissionCheck = evv.checkPermissions(vuln)

	// 计算数据一致性
	result.DataConsistency = evv.calculateDataConsistency(vuln)

	return result
}

// checkBusinessLogic 检查业务逻辑
func (evv *EnhancedVulnerabilityValidator) checkBusinessLogic(vuln *types.Vulnerability) *BusinessLogicCheckResult {
	result := &BusinessLogicCheckResult{
		CheckDetails: make([]string, 0),
	}

	// 检查逻辑有效性
	result.LogicValid = evv.isLogicValid(vuln)

	// 检查预期行为
	result.ExpectedBehavior = evv.isExpectedBehavior(vuln)

	// 检查副作用
	result.SideEffectsDetected = evv.detectSideEffects(vuln)

	// 检查数据完整性
	result.DataIntegrity = evv.checkDataIntegrity(vuln)

	return result
}

// calculateValidationConfidence 计算验证置信度
func (evv *EnhancedVulnerabilityValidator) calculateValidationConfidence(details *VulnerabilityValidationDetails) float64 {
	confidence := 0.0

	// 参数影响置信度 (权重: 0.4)
	if details.ParameterImpact != nil {
		confidence += details.ParameterImpact.Confidence * 0.4
	}

	// 响应分析置信度 (权重: 0.3)
	if details.ResponseAnalysis != nil {
		responseConfidence := 1.0 - details.ResponseAnalysis.ContentRelevance
		if details.ResponseAnalysis.ResponseType == "specific_response" {
			responseConfidence += 0.3
		}
		confidence += responseConfidence * 0.3
	}

	// 上下文验证置信度 (权重: 0.2)
	if details.ContextValidation != nil {
		contextConfidence := 0.0
		if details.ContextValidation.IsContextRelevant {
			contextConfidence += 0.5
		}
		if details.ContextValidation.BusinessLogicValid {
			contextConfidence += 0.3
		}
		if details.ContextValidation.PermissionCheck {
			contextConfidence += 0.2
		}
		confidence += contextConfidence * 0.2
	}

	// 业务逻辑检查置信度 (权重: 0.1)
	if details.BusinessLogicCheck != nil {
		businessConfidence := 0.0
		if details.BusinessLogicCheck.LogicValid {
			businessConfidence += 0.4
		}
		if details.BusinessLogicCheck.ExpectedBehavior {
			businessConfidence += 0.3
		}
		if details.BusinessLogicCheck.SideEffectsDetected {
			businessConfidence += 0.2
		}
		if details.BusinessLogicCheck.DataIntegrity {
			businessConfidence += 0.1
		}
		confidence += businessConfidence * 0.1
	}

	return confidence
}

// getValidationRecommendedAction 获取验证推荐操作
func (evv *EnhancedVulnerabilityValidator) getValidationRecommendedAction(result *VulnerabilityValidationResult) string {
	if result.IsValid && result.Confidence > 0.8 {
		return "漏洞确认有效，建议立即修复"
	}

	if result.FalsePositiveRisk > 0.7 {
		return "高误报风险，建议人工复核"
	}

	if result.ValidationDetails.ParameterImpact != nil && result.ValidationDetails.ParameterImpact.IsParameterIgnored {
		return "参数被忽略，建议测试其他攻击向量"
	}

	if result.ValidationDetails.ResponseAnalysis != nil && result.ValidationDetails.ResponseAnalysis.IsDefaultPage {
		return "触发默认页面，建议验证是否存在绕过方法"
	}

	if result.Confidence < 0.5 {
		return "验证结果不确定，建议进一步分析"
	}

	return "建议进行深度验证"
}

// 辅助方法实现

// calculateContentRelevance 计算内容相关性
func (evv *EnhancedVulnerabilityValidator) calculateContentRelevance(vuln *types.Vulnerability, responseContent string) float64 {
	// 简化实现：基于响应长度和关键词匹配
	relevance := 0.5

	// 检查是否包含漏洞相关的关键词
	vulnKeywords := evv.getVulnerabilityKeywords(vuln.Type)
	for _, keyword := range vulnKeywords {
		if strings.Contains(strings.ToLower(responseContent), keyword) {
			relevance += 0.1
		}
	}

	// 检查响应长度合理性
	if len(responseContent) > 100 && len(responseContent) < 10000 {
		relevance += 0.2
	}

	if relevance > 1.0 {
		relevance = 1.0
	}

	return relevance
}

// getVulnerabilityKeywords 获取漏洞类型关键词
func (evv *EnhancedVulnerabilityValidator) getVulnerabilityKeywords(vulnType string) []string {
	keywords := map[string][]string{
		"SQL注入": {"sql", "database", "mysql", "oracle", "select", "union"},
		"XSS":   {"script", "alert", "javascript", "html", "dom"},
		"文件包含":  {"file", "include", "require", "path", "directory"},
		"命令注入":  {"command", "exec", "system", "shell", "cmd"},
	}

	if words, exists := keywords[vulnType]; exists {
		return words
	}

	return []string{}
}

// isVulnerabilityContextRelevant 检查漏洞上下文相关性
func (evv *EnhancedVulnerabilityValidator) isVulnerabilityContextRelevant(vuln *types.Vulnerability) bool {
	// 检查URL路径是否与漏洞类型相关
	urlPath := strings.ToLower(vuln.URL)

	contextMap := map[string][]string{
		"SQL注入": {"search", "query", "data", "user", "login", "admin"},
		"XSS":   {"comment", "message", "post", "input", "form"},
		"文件包含":  {"file", "download", "upload", "include", "view"},
		"命令注入":  {"exec", "run", "command", "system", "admin"},
	}

	if contexts, exists := contextMap[vuln.Type]; exists {
		for _, context := range contexts {
			if strings.Contains(urlPath, context) {
				return true
			}
		}
	}

	return false
}

// validateBusinessLogic 验证业务逻辑
func (evv *EnhancedVulnerabilityValidator) validateBusinessLogic(vuln *types.Vulnerability) bool {
	// 简化实现：检查漏洞是否符合业务逻辑
	return true // 默认认为符合业务逻辑
}

// checkPermissions 检查权限
func (evv *EnhancedVulnerabilityValidator) checkPermissions(vuln *types.Vulnerability) bool {
	// 简化实现：检查是否需要特殊权限
	return true // 默认认为权限检查通过
}

// calculateDataConsistency 计算数据一致性
func (evv *EnhancedVulnerabilityValidator) calculateDataConsistency(vuln *types.Vulnerability) float64 {
	// 简化实现：返回默认一致性分数
	return 0.8
}

// isLogicValid 检查逻辑有效性
func (evv *EnhancedVulnerabilityValidator) isLogicValid(vuln *types.Vulnerability) bool {
	// 检查漏洞逻辑是否合理
	return true
}

// isExpectedBehavior 检查预期行为
func (evv *EnhancedVulnerabilityValidator) isExpectedBehavior(vuln *types.Vulnerability) bool {
	// 检查是否为预期的漏洞行为
	return true
}

// detectSideEffects 检测副作用
func (evv *EnhancedVulnerabilityValidator) detectSideEffects(vuln *types.Vulnerability) bool {
	// 检测是否有副作用
	return false
}

// checkDataIntegrity 检查数据完整性
func (evv *EnhancedVulnerabilityValidator) checkDataIntegrity(vuln *types.Vulnerability) bool {
	// 检查数据完整性
	return true
}

// initializeBusinessRules 初始化业务规则
func (evv *EnhancedVulnerabilityValidator) initializeBusinessRules() {
	// 初始化各种业务验证规则
	sqlRules := []VulnValidationRule{
		{
			Name:        "SQL语法检查",
			Description: "检查SQL注入是否符合语法",
			Validator: func(vuln *types.Vulnerability, resp *http.Response) bool {
				return strings.Contains(vuln.Description, "SQL")
			},
			Weight: 0.3,
		},
		{
			Name:        "数据库错误检查",
			Description: "检查是否返回数据库错误信息",
			Validator: func(vuln *types.Vulnerability, resp *http.Response) bool {
				return strings.Contains(vuln.Response, "mysql") ||
					strings.Contains(vuln.Response, "oracle")
			},
			Weight: 0.4,
		},
	}

	xssRules := []VulnValidationRule{
		{
			Name:        "脚本执行检查",
			Description: "检查XSS是否能执行脚本",
			Validator: func(vuln *types.Vulnerability, resp *http.Response) bool {
				return strings.Contains(vuln.Response, "<script>")
			},
			Weight: 0.5,
		},
	}

	evv.contextValidator.businessRules["SQL注入"] = sqlRules
	evv.contextValidator.businessRules["XSS"] = xssRules
}
