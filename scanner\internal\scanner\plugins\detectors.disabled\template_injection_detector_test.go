package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestTemplateInjectionDetectorBasicFunctionality 测试模板注入检测器基础功能
func TestTemplateInjectionDetectorBasicFunctionality(t *testing.T) {
	detector := NewTemplateInjectionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "template-injection-comprehensive", detector.GetID())
	assert.Equal(t, "模板注入漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "critical", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-94")
	assert.Contains(t, detector.GetCVE(), "CVE-2021-44228")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestTemplateInjectionDetectorApplicability 测试模板注入检测器适用性
func TestTemplateInjectionDetectorApplicability(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 测试有模板引擎的目标
	templateTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/template/view",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Server":       "Flask/1.1.1",
			"X-Powered-By": "PHP/7.4.0",
		},
	}
	assert.True(t, detector.IsApplicable(templateTarget))

	// 测试有模板技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "Flask", Version: "1.1.1", Confidence: 0.9},
			{Name: "Jinja2", Version: "2.11.2", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有模板链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/home",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/template/render", Text: "Template Render"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有模板表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/form",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"template": "text", "content": "textarea"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/render?template=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试模板相关URL
	templateURLTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/jinja2/render",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(templateURLTarget))

	// 测试普通Web目标（模板注入是通用问题）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestTemplateInjectionDetectorConfiguration 测试模板注入检测器配置
func TestTemplateInjectionDetectorConfiguration(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 18*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 2, config.Concurrency)
	assert.True(t, config.FollowRedirects) // 模板注入跟随重定向
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 4 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 3, updatedConfig.Concurrency)
}

// TestTemplateInjectionDetectorJinja2Payloads 测试Jinja2载荷
func TestTemplateInjectionDetectorJinja2Payloads(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 检查Jinja2载荷列表
	assert.NotEmpty(t, detector.jinja2Payloads)
	assert.GreaterOrEqual(t, len(detector.jinja2Payloads), 25)

	// 检查基础数学表达式
	assert.Contains(t, detector.jinja2Payloads, "{{7*7}}")
	assert.Contains(t, detector.jinja2Payloads, "{{7*'7'}}")
	assert.Contains(t, detector.jinja2Payloads, "{{49}}")
	assert.Contains(t, detector.jinja2Payloads, "{{7+7+7+7+7+7+7}}")

	// 检查配置访问
	assert.Contains(t, detector.jinja2Payloads, "{{config}}")
	assert.Contains(t, detector.jinja2Payloads, "{{config.items()}}")
	assert.Contains(t, detector.jinja2Payloads, "{{config['SECRET_KEY']}}")
	assert.Contains(t, detector.jinja2Payloads, "{{config.get('SECRET_KEY')}}")

	// 检查全局对象访问
	assert.Contains(t, detector.jinja2Payloads, "{{self}}")
	assert.Contains(t, detector.jinja2Payloads, "{{self.__dict__}}")
	assert.Contains(t, detector.jinja2Payloads, "{{self.__class__}}")
	assert.Contains(t, detector.jinja2Payloads, "{{self.__class__.__bases__}}")

	// 检查内置函数访问
	assert.Contains(t, detector.jinja2Payloads, "{{''.__class__}}")
	assert.Contains(t, detector.jinja2Payloads, "{{''.__class__.__mro__}}")
	assert.Contains(t, detector.jinja2Payloads, "{{''.__class__.__mro__[2]}}")
	assert.Contains(t, detector.jinja2Payloads, "{{''.__class__.__mro__[2].__subclasses__()}}")

	// 检查代码执行
	assert.Contains(t, detector.jinja2Payloads, "{{''.__class__.__mro__[2].__subclasses__()[40]('/etc/passwd').read()}}")
	assert.Contains(t, detector.jinja2Payloads, "{{config.__class__.__init__.__globals__['os'].popen('id').read()}}")
	assert.Contains(t, detector.jinja2Payloads, "{{lipsum.__globals__['os'].popen('id').read()}}")

	// 检查错误触发
	assert.Contains(t, detector.jinja2Payloads, "{{undefined_variable}}")
	assert.Contains(t, detector.jinja2Payloads, "{{7/0}}")
	assert.Contains(t, detector.jinja2Payloads, "{{''[999]}}")
	assert.Contains(t, detector.jinja2Payloads, "{{None.invalid_method()}}")

	// 检查中文Jinja2载荷
	assert.Contains(t, detector.jinja2Payloads, "{{7*7}} 测试")
	assert.Contains(t, detector.jinja2Payloads, "{{配置}}")
	assert.Contains(t, detector.jinja2Payloads, "{{未定义变量}}")
	assert.Contains(t, detector.jinja2Payloads, "{{错误测试}}")
}

// TestTemplateInjectionDetectorTwigPayloads 测试Twig载荷
func TestTemplateInjectionDetectorTwigPayloads(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 检查Twig载荷列表
	assert.NotEmpty(t, detector.twigPayloads)
	assert.GreaterOrEqual(t, len(detector.twigPayloads), 25)

	// 检查基础数学表达式
	assert.Contains(t, detector.twigPayloads, "{{7*7}}")
	assert.Contains(t, detector.twigPayloads, "{{7*'7'}}")
	assert.Contains(t, detector.twigPayloads, "{{49}}")
	assert.Contains(t, detector.twigPayloads, "{{7+7+7+7+7+7+7}}")

	// 检查全局变量访问
	assert.Contains(t, detector.twigPayloads, "{{_self}}")
	assert.Contains(t, detector.twigPayloads, "{{_context}}")
	assert.Contains(t, detector.twigPayloads, "{{_charset}}")
	assert.Contains(t, detector.twigPayloads, "{{app}}")

	// 检查环境访问
	assert.Contains(t, detector.twigPayloads, "{{_self.env}}")
	assert.Contains(t, detector.twigPayloads, "{{_self.getTemplateName()}}")
	assert.Contains(t, detector.twigPayloads, "{{_self.getEnvironment()}}")
	assert.Contains(t, detector.twigPayloads, "{{dump()}}")

	// 检查代码执行
	assert.Contains(t, detector.twigPayloads, "{{_self.env.registerUndefinedFilterCallback('exec')}}{{_self.env.getFilter('id')}}")
	assert.Contains(t, detector.twigPayloads, "{{_self.env.setCache('/tmp')}}{{_self.env.enableDebug()}}")
	assert.Contains(t, detector.twigPayloads, "{{['id']|filter('system')}}")

	// 检查文件操作
	assert.Contains(t, detector.twigPayloads, "{{'/etc/passwd'|file_get_contents}}")
	assert.Contains(t, detector.twigPayloads, "{{source('/etc/passwd')}}")
	assert.Contains(t, detector.twigPayloads, "{{include('/etc/passwd')}}")

	// 检查错误触发
	assert.Contains(t, detector.twigPayloads, "{{undefined_variable}}")
	assert.Contains(t, detector.twigPayloads, "{{7/0}}")
	assert.Contains(t, detector.twigPayloads, "{{''[999]}}")
	assert.Contains(t, detector.twigPayloads, "{{null.invalid_method()}}")

	// 检查中文Twig载荷
	assert.Contains(t, detector.twigPayloads, "{{7*7}} 测试")
	assert.Contains(t, detector.twigPayloads, "{{应用}}")
	assert.Contains(t, detector.twigPayloads, "{{未定义变量}}")
	assert.Contains(t, detector.twigPayloads, "{{错误测试}}")
}

// TestTemplateInjectionDetectorFreemarkerPayloads 测试Freemarker载荷
func TestTemplateInjectionDetectorFreemarkerPayloads(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 检查Freemarker载荷列表
	assert.NotEmpty(t, detector.freemarkerPayloads)
	assert.GreaterOrEqual(t, len(detector.freemarkerPayloads), 25)

	// 检查基础数学表达式
	assert.Contains(t, detector.freemarkerPayloads, "${7*7}")
	assert.Contains(t, detector.freemarkerPayloads, "${7*'7'}")
	assert.Contains(t, detector.freemarkerPayloads, "${49}")
	assert.Contains(t, detector.freemarkerPayloads, "${7+7+7+7+7+7+7}")

	// 检查内置对象访问
	assert.Contains(t, detector.freemarkerPayloads, "${.version}")
	assert.Contains(t, detector.freemarkerPayloads, "${.data_model}")
	assert.Contains(t, detector.freemarkerPayloads, "${.globals}")
	assert.Contains(t, detector.freemarkerPayloads, "${.main}")

	// 检查类访问
	assert.Contains(t, detector.freemarkerPayloads, "${''.getClass()}")
	assert.Contains(t, detector.freemarkerPayloads, "${''.getClass().forName('java.lang.Runtime')}")
	assert.Contains(t, detector.freemarkerPayloads, "${''.getClass().forName('java.lang.System')}")
	assert.Contains(t, detector.freemarkerPayloads, "${''.class}")

	// 检查代码执行
	assert.Contains(t, detector.freemarkerPayloads, "${''.getClass().forName('java.lang.Runtime').getRuntime().exec('id')}")
	assert.Contains(t, detector.freemarkerPayloads, "<#assign ex='freemarker.template.utility.Execute'?new()>${ex('id')}")

	// 检查文件操作
	assert.Contains(t, detector.freemarkerPayloads, "${'/etc/passwd'?url}")
	assert.Contains(t, detector.freemarkerPayloads, "<#include '/etc/passwd'>")
	assert.Contains(t, detector.freemarkerPayloads, "<#import '/etc/passwd' as passwd>")

	// 检查错误触发
	assert.Contains(t, detector.freemarkerPayloads, "${undefined_variable}")
	assert.Contains(t, detector.freemarkerPayloads, "${7/0}")
	assert.Contains(t, detector.freemarkerPayloads, "${''[999]}")
	assert.Contains(t, detector.freemarkerPayloads, "${null.invalid_method()}")

	// 检查中文Freemarker载荷
	assert.Contains(t, detector.freemarkerPayloads, "${7*7} 测试")
	assert.Contains(t, detector.freemarkerPayloads, "${版本}")
	assert.Contains(t, detector.freemarkerPayloads, "${未定义变量}")
	assert.Contains(t, detector.freemarkerPayloads, "${错误测试}")
}

// TestTemplateInjectionDetectorVelocityPayloads 测试Velocity载荷
func TestTemplateInjectionDetectorVelocityPayloads(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 检查Velocity载荷列表
	assert.NotEmpty(t, detector.velocityPayloads)
	assert.GreaterOrEqual(t, len(detector.velocityPayloads), 20)

	// 检查基础数学表达式
	assert.Contains(t, detector.velocityPayloads, "#set($x=7*7)$x")
	assert.Contains(t, detector.velocityPayloads, "#set($x=7*'7')$x")
	assert.Contains(t, detector.velocityPayloads, "#set($x=49)$x")
	assert.Contains(t, detector.velocityPayloads, "#set($x=7+7+7+7+7+7+7)$x")

	// 检查类访问
	assert.Contains(t, detector.velocityPayloads, "$class.inspect('java.lang.Runtime')")
	assert.Contains(t, detector.velocityPayloads, "$class.type.name")
	assert.Contains(t, detector.velocityPayloads, "$class.getClass()")
	assert.Contains(t, detector.velocityPayloads, "#set($str='')$str.getClass()")

	// 检查代码执行
	assert.Contains(t, detector.velocityPayloads, "#set($ex=$class.inspect('java.lang.Runtime').type.getRuntime().exec('id'))$ex.waitFor()$ex.exitValue()")
	assert.Contains(t, detector.velocityPayloads, "$class.inspect('java.lang.System').type.getProperty('user.name')")

	// 检查反射访问
	assert.Contains(t, detector.velocityPayloads, "$class.inspect('java.lang.Class').type.forName('java.lang.Runtime')")
	assert.Contains(t, detector.velocityPayloads, "#set($rt=$class.inspect('java.lang.Runtime').type)$rt.getRuntime().exec('id')")

	// 检查错误触发
	assert.Contains(t, detector.velocityPayloads, "$undefined_variable")
	assert.Contains(t, detector.velocityPayloads, "#set($x=7/0)$x")
	assert.Contains(t, detector.velocityPayloads, "#set($x='')$x.get(999)")
	assert.Contains(t, detector.velocityPayloads, "$null.invalid_method()")

	// 检查中文Velocity载荷
	assert.Contains(t, detector.velocityPayloads, "#set($x=7*7)$x 测试")
	assert.Contains(t, detector.velocityPayloads, "$类型")
	assert.Contains(t, detector.velocityPayloads, "$未定义变量")
	assert.Contains(t, detector.velocityPayloads, "$错误测试")
}

// TestTemplateInjectionDetectorTestParameters 测试参数列表
func TestTemplateInjectionDetectorTestParameters(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.GreaterOrEqual(t, len(detector.testParameters), 100)

	// 检查模板相关参数
	assert.Contains(t, detector.testParameters, "template")
	assert.Contains(t, detector.testParameters, "view")
	assert.Contains(t, detector.testParameters, "render")
	assert.Contains(t, detector.testParameters, "page")
	assert.Contains(t, detector.testParameters, "content")
	assert.Contains(t, detector.testParameters, "display")

	// 检查内容相关参数
	assert.Contains(t, detector.testParameters, "message")
	assert.Contains(t, detector.testParameters, "text")
	assert.Contains(t, detector.testParameters, "body")
	assert.Contains(t, detector.testParameters, "data")
	assert.Contains(t, detector.testParameters, "input")
	assert.Contains(t, detector.testParameters, "value")

	// 检查用户相关参数
	assert.Contains(t, detector.testParameters, "user")
	assert.Contains(t, detector.testParameters, "username")
	assert.Contains(t, detector.testParameters, "name")
	assert.Contains(t, detector.testParameters, "email")
	assert.Contains(t, detector.testParameters, "profile")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "模板")
	assert.Contains(t, detector.testParameters, "视图")
	assert.Contains(t, detector.testParameters, "渲染")
	assert.Contains(t, detector.testParameters, "页面")
	assert.Contains(t, detector.testParameters, "内容")
	assert.Contains(t, detector.testParameters, "显示")
}

// TestTemplateInjectionDetectorTemplateEngines 测试模板引擎列表
func TestTemplateInjectionDetectorTemplateEngines(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 检查模板引擎列表
	assert.NotEmpty(t, detector.templateEngines)
	assert.GreaterOrEqual(t, len(detector.templateEngines), 30)

	// 检查Python模板引擎
	assert.Contains(t, detector.templateEngines, "jinja2")
	assert.Contains(t, detector.templateEngines, "jinja")
	assert.Contains(t, detector.templateEngines, "django")
	assert.Contains(t, detector.templateEngines, "mako")

	// 检查PHP模板引擎
	assert.Contains(t, detector.templateEngines, "twig")
	assert.Contains(t, detector.templateEngines, "smarty")
	assert.Contains(t, detector.templateEngines, "blade")
	assert.Contains(t, detector.templateEngines, "plates")

	// 检查Java模板引擎
	assert.Contains(t, detector.templateEngines, "freemarker")
	assert.Contains(t, detector.templateEngines, "velocity")
	assert.Contains(t, detector.templateEngines, "thymeleaf")
	assert.Contains(t, detector.templateEngines, "jsp")

	// 检查JavaScript模板引擎
	assert.Contains(t, detector.templateEngines, "handlebars")
	assert.Contains(t, detector.templateEngines, "mustache")
	assert.Contains(t, detector.templateEngines, "ejs")
	assert.Contains(t, detector.templateEngines, "pug")

	// 检查Ruby模板引擎
	assert.Contains(t, detector.templateEngines, "erb")
	assert.Contains(t, detector.templateEngines, "haml")
	assert.Contains(t, detector.templateEngines, "slim")
	assert.Contains(t, detector.templateEngines, "liquid")

	// 检查.NET模板引擎
	assert.Contains(t, detector.templateEngines, "razor")
	assert.Contains(t, detector.templateEngines, "nvelocity")
	assert.Contains(t, detector.templateEngines, "dotliquid")

	// 检查中文模板引擎
	assert.Contains(t, detector.templateEngines, "模板引擎")
	assert.Contains(t, detector.templateEngines, "渲染引擎")
	assert.Contains(t, detector.templateEngines, "视图引擎")
	assert.Contains(t, detector.templateEngines, "页面引擎")
}

// TestTemplateInjectionDetectorPatterns 测试模式
func TestTemplateInjectionDetectorPatterns(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 检查Jinja2模式
	assert.NotEmpty(t, detector.jinja2Patterns)
	assert.GreaterOrEqual(t, len(detector.jinja2Patterns), 15)

	// 检查Twig模式
	assert.NotEmpty(t, detector.twigPatterns)
	assert.GreaterOrEqual(t, len(detector.twigPatterns), 15)

	// 检查Freemarker模式
	assert.NotEmpty(t, detector.freemarkerPatterns)
	assert.GreaterOrEqual(t, len(detector.freemarkerPatterns), 15)

	// 检查Velocity模式
	assert.NotEmpty(t, detector.velocityPatterns)
	assert.GreaterOrEqual(t, len(detector.velocityPatterns), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.GreaterOrEqual(t, len(detector.errorPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.GreaterOrEqual(t, len(detector.responsePatterns), 15)
}

// TestTemplateInjectionDetectorTemplateFeatures 测试模板功能检查
func TestTemplateInjectionDetectorTemplateFeatures(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 测试有模板头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Server":       "Flask/1.1.1",
			"X-Powered-By": "PHP/7.4.0",
		},
	}
	assert.True(t, detector.hasTemplateFeatures(headerTarget))

	// 测试有模板技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "Flask", Version: "1.1.1", Confidence: 0.9},
			{Name: "Jinja2", Version: "2.11.2", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasTemplateFeatures(techTarget))

	// 测试有模板链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/template/render", Text: "Template Render"},
		},
	}
	assert.True(t, detector.hasTemplateFeatures(linkTarget))

	// 测试有模板表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"template": "text", "content": "textarea"}},
		},
	}
	assert.True(t, detector.hasTemplateFeatures(formTarget))

	// 测试无模板功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasTemplateFeatures(simpleTarget))
}

// TestTemplateInjectionDetectorRiskScore 测试风险评分计算
func TestTemplateInjectionDetectorRiskScore(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 8.5)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.6)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 2.8)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestTemplateInjectionDetectorLifecycle 测试检测器生命周期
func TestTemplateInjectionDetectorLifecycle(t *testing.T) {
	detector := NewTemplateInjectionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
