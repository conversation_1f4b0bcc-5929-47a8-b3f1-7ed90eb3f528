// API客户端配置

import axios, { type AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import type { ApiResponse } from '@/types/api'
import { getBestApiBaseUrl, clearEndpointCache, getNetworkStatus } from '@/utils/network'

// 动态基础URL
let currentBaseURL = '/api/v1'

// 创建axios实例
const client = axios.create({
  baseURL: currentBaseURL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 初始化最佳端点
let initPromise: Promise<void> | null = null

async function initializeBestEndpoint(): Promise<void> {
  if (initPromise) {
    return initPromise
  }

  initPromise = (async () => {
    try {
      console.log('正在检测最佳API端点...')

      // 在开发环境下使用网络检测
      if (import.meta.env.DEV) {
        const bestEndpoint = await getBestApiBaseUrl()
        currentBaseURL = `${bestEndpoint}/api/v1`
        client.defaults.baseURL = currentBaseURL
        console.log(`已设置API基础URL: ${currentBaseURL}`)
      } else {
        // 生产环境使用相对路径
        currentBaseURL = '/api/v1'
        client.defaults.baseURL = currentBaseURL
      }
    } catch (error) {
      console.warn('端点检测失败，使用默认配置:', error)
      currentBaseURL = '/api/v1'
      client.defaults.baseURL = currentBaseURL
    }
  })()

  return initPromise
}

// 请求拦截器
client.interceptors.request.use(
  (config: any) => {
    // 添加认证token
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    // 添加请求ID用于追踪
    config.headers = config.headers || {}
    config.headers['X-Request-ID'] = generateRequestId()
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 用于防止重复刷新Token的标志
let isRefreshing = false
let failedQueue: Array<{
  resolve: (value?: any) => void
  reject: (reason?: any) => void
}> = []

// 处理队列中的请求
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })

  failedQueue = []
}

// 响应拦截器
client.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response

    // 检查业务状态码
    if (data.code !== 200 && data.code !== 201) {
      // 处理业务错误
      handleBusinessError(data)
      return Promise.reject(new Error(data.message || '请求失败'))
    }

    return response
  },
  async (error) => {
    const originalRequest = error.config

    // 检查是否是401错误且不是登录请求
    if (error.response?.status === 401 && !originalRequest._retry && !originalRequest.url?.includes('/auth/login')) {
      if (isRefreshing) {
        // 如果正在刷新Token，将请求加入队列
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(() => {
          // 重新发送原始请求
          const authStore = useAuthStore()
          if (authStore.token) {
            originalRequest.headers.Authorization = `Bearer ${authStore.token}`
          }
          return client(originalRequest)
        }).catch(err => {
          return Promise.reject(err)
        })
      }

      originalRequest._retry = true
      isRefreshing = true

      try {
        const authStore = useAuthStore()
        console.log('检测到401错误，尝试刷新Token...')

        // 尝试刷新Token
        const refreshed = await authStore.refreshToken()

        if (refreshed) {
          console.log('Token刷新成功，重新发送请求')
          // 更新原始请求的Authorization头
          originalRequest.headers.Authorization = `Bearer ${authStore.token}`

          // 处理队列中的请求
          processQueue(null, authStore.token)

          // 重新发送原始请求
          return client(originalRequest)
        } else {
          throw new Error('Token刷新失败')
        }
      } catch (refreshError) {
        console.error('Token刷新失败:', refreshError)

        // 处理队列中的请求
        processQueue(refreshError, null)

        // 清除认证信息并跳转到登录页
        const authStore = useAuthStore()
        authStore.clearAuth()

        // 如果当前不在登录页，跳转到登录页
        if (window.location.pathname !== '/login') {
          window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}`
        }

        return Promise.reject(refreshError)
      } finally {
        isRefreshing = false
      }
    }

    // 处理其他HTTP错误
    handleHttpError(error)
    return Promise.reject(error)
  }
)

// 处理业务错误
function handleBusinessError(data: ApiResponse) {
  const { code, message } = data

  switch (code) {
    case 401:
      // 401错误在响应拦截器中处理，这里只显示消息
      ElMessage.error('认证失败，请重新登录')
      break
    case 403:
      ElMessage.error('权限不足')
      break
    case 404:
      ElMessage.error('请求的资源不存在')
      break
    case 422:
      ElMessage.error('请求参数验证失败')
      break
    case 500:
      ElMessage.error('服务器内部错误')
      break
    default:
      ElMessage.error(message || '请求失败')
  }
}

// 处理HTTP错误
async function handleHttpError(error: any) {
  const { response, message, code } = error

  // 检查是否是连接错误
  if (code === 'ECONNREFUSED' || message.includes('ECONNREFUSED') ||
      message.includes('Network Error') || message.includes('ERR_NETWORK')) {
    console.warn('检测到连接错误，尝试重新检测端点...')

    // 清除缓存并重新检测
    clearEndpointCache()

    try {
      const networkStatus = await getNetworkStatus()
      console.log('网络状态:', networkStatus)

      if (networkStatus.ipv4Available || networkStatus.ipv6Available) {
        // 更新基础URL
        currentBaseURL = `${networkStatus.bestEndpoint}/api/v1`
        client.defaults.baseURL = currentBaseURL
        console.log(`已切换到新端点: ${currentBaseURL}`)

        ElMessage.warning(`网络连接已切换到${networkStatus.bestProtocol.toUpperCase()}`)
        return // 不显示错误消息，让用户重试
      }
    } catch (retryError) {
      console.error('端点重新检测失败:', retryError)
    }

    ElMessage.error('网络连接失败，请检查后端服务是否正常运行')
    return
  }

  if (response) {
    const { status, statusText } = response

    switch (status) {
      case 400:
        ElMessage.error('请求参数错误')
        break
      case 401:
        // 401错误在响应拦截器中处理，这里只显示消息
        ElMessage.error('认证失败，请重新登录')
        break
      case 403:
        ElMessage.error('权限不足')
        break
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      case 408:
        ElMessage.error('请求超时')
        break
      case 500:
        ElMessage.error('服务器内部错误')
        break
      case 502:
        ElMessage.error('网关错误')
        break
      case 503:
        ElMessage.error('服务不可用')
        break
      case 504:
        ElMessage.error('网关超时')
        break
      default:
        ElMessage.error(`请求失败: ${status} ${statusText}`)
    }
  } else if (message.includes('timeout')) {
    ElMessage.error('请求超时，请检查网络连接')
  } else {
    ElMessage.error('请求失败，请稍后重试')
  }
}

// 生成请求ID
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 通用请求方法
export class ApiClient {
  // 初始化API客户端
  static async initialize(): Promise<void> {
    await initializeBestEndpoint()
  }

  // 获取当前基础URL
  static getCurrentBaseURL(): string {
    return currentBaseURL
  }

  // 手动设置基础URL
  static setBaseURL(baseURL: string): void {
    currentBaseURL = baseURL
    client.defaults.baseURL = baseURL
  }

  // 获取网络状态
  static async getNetworkStatus() {
    return await getNetworkStatus()
  }

  // GET请求
  static async get<T = any>(
    url: string,
    params?: any,
    config?: any
  ): Promise<T> {
    await initializeBestEndpoint() // 确保端点已初始化
    const response = await client.get<ApiResponse<T>>(url, {
      params,
      ...config,
    })
    return response.data.data as T
  }

  // POST请求
  static async post<T = any>(
    url: string,
    data?: any,
    config?: any
  ): Promise<T> {
    await initializeBestEndpoint() // 确保端点已初始化
    const response = await client.post<ApiResponse<T>>(url, data, config)
    return response.data.data as T
  }

  // PUT请求
  static async put<T = any>(
    url: string,
    data?: any,
    config?: any
  ): Promise<T> {
    await initializeBestEndpoint() // 确保端点已初始化
    const response = await client.put<ApiResponse<T>>(url, data, config)
    return response.data.data as T
  }

  // PATCH请求
  static async patch<T = any>(
    url: string,
    data?: any,
    config?: any
  ): Promise<T> {
    const response = await client.patch<ApiResponse<T>>(url, data, config)
    return response.data.data as T
  }

  // DELETE请求
  static async delete<T = any>(
    url: string,
    config?: any
  ): Promise<T> {
    const response = await client.delete<ApiResponse<T>>(url, config)
    return response.data.data as T
  }

  // 文件上传
  static async upload<T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void,
    config?: any
  ): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await client.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(progress)
        }
      },
      ...config,
    })
    return response.data.data as T
  }

  // 文件下载
  static async download(
    url: string,
    filename?: string,
    config?: any
  ): Promise<void> {
    try {
      // 创建一个新的axios实例，跳过响应拦截器的业务逻辑检查
      const downloadClient = axios.create({
        baseURL: client.defaults.baseURL,
        timeout: client.defaults.timeout,
        headers: client.defaults.headers,
      })

      // 只添加请求拦截器，不添加响应拦截器
      downloadClient.interceptors.request.use(
        (config) => {
          const token = localStorage.getItem('auth_token')
          if (token) {
            config.headers.Authorization = `Bearer ${token}`
          }
          return config
        },
        (error) => Promise.reject(error)
      )

      const response = await downloadClient.get(url, {
        responseType: 'blob',
        ...config,
      })

      // 从响应头获取文件名
      let downloadFilename = filename
      if (!downloadFilename) {
        const contentDisposition = response.headers['content-disposition']
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
          if (filenameMatch && filenameMatch[1]) {
            downloadFilename = filenameMatch[1].replace(/['"]/g, '')
          }
        }
      }
      downloadFilename = downloadFilename || 'download'

      // 创建下载链接
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = downloadFilename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    } catch (error: any) {
      console.error('下载失败:', error)
      throw new Error(error.response?.data?.message || error.message || '下载失败')
    }
  }

  // 批量请求
  static async batch<T = any>(
    requests: Array<() => Promise<any>>
  ): Promise<T[]> {
    try {
      const results = await Promise.allSettled(requests.map(req => req()))
      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value
        } else {
          console.error(`批量请求第${index + 1}个失败:`, result.reason)
          throw result.reason
        }
      })
    } catch (error) {
      console.error('批量请求失败:', error)
      throw error
    }
  }

  // 重试请求
  static async retry<T = any>(
    requestFn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: any
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error
        if (i < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
        }
      }
    }
    
    throw lastError
  }
}

export default client
