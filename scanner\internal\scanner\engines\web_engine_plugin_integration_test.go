package engines

import (
	"context"
	"net/url"
	"testing"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/internal/scanner/types"
	"scanner/pkg/logger"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestPluginizedVulnerabilityDetection 测试插件化漏洞检测集成
func TestPluginizedVulnerabilityDetection(t *testing.T) {
	// 初始化日志
	logger.InitLogger("debug", "console", "")

	// 创建Web引擎
	engine := NewWebEngine()
	require.NotNil(t, engine)
	require.NotNil(t, engine.detectorManager, "检测器管理器应该已初始化")

	// 验证检测器已注册
	stats := engine.detectorManager.GetStats()
	assert.Greater(t, stats.TotalDetectors, int64(0), "应该至少注册了一个检测器")

	// 创建测试目标URL
	targetURL, err := url.Parse("http://example.com/test")
	require.NoError(t, err)

	// 创建扫描结果对象
	result := &types.ScanResult{
		TaskID:          "test-task-123",
		StartTime:       time.Now(),
		Vulnerabilities: make([]*types.Vulnerability, 0),
		Statistics: &types.ScanStatistics{
			TotalTargets:   1,
			ScannedTargets: 0,
		},
	}

	// 创建进度通道
	progress := make(chan *types.ScanProgress, 10)
	defer close(progress)

	// 创建停止通道
	stopChan := make(chan bool, 1)
	defer close(stopChan)

	// 测试插件化漏洞检测
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err = engine.vulnerabilityDetection(ctx, targetURL, result, progress, "test-task-123", stopChan)
	assert.NoError(t, err, "插件化漏洞检测应该成功执行")

	// 验证进度消息
	progressCount := 0
	for {
		select {
		case prog := <-progress:
			if prog != nil {
				progressCount++
				t.Logf("进度更新: %s - %d%% - %s", prog.Stage, prog.Progress, prog.Message)
			}
		default:
			goto progressDone
		}
	}
progressDone:
	assert.Greater(t, progressCount, 0, "应该收到进度更新")

	// 验证统计信息更新
	assert.Equal(t, 1, result.Statistics.ScannedTargets, "扫描目标数应该更新")

	t.Logf("插件化漏洞检测完成，发现 %d 个漏洞", len(result.Vulnerabilities))
}

// TestConvertToPluginTarget 测试目标转换功能
func TestConvertToPluginTarget(t *testing.T) {
	engine := NewWebEngine()
	require.NotNil(t, engine)

	// 测试HTTP目标
	httpURL, _ := url.Parse("http://example.com:8080/path/to/resource")
	pluginTarget := engine.convertToPluginTarget(httpURL, "test-task")

	assert.Equal(t, "url", pluginTarget.Type)
	assert.Equal(t, "http://example.com:8080/path/to/resource", pluginTarget.URL)
	assert.Equal(t, "example.com", pluginTarget.Domain)
	assert.Equal(t, 8080, pluginTarget.Port)
	assert.Equal(t, "http", pluginTarget.Protocol)
	assert.NotEmpty(t, pluginTarget.ID)
	assert.Contains(t, pluginTarget.ID, "test-task")

	// 验证服务信息
	require.Len(t, pluginTarget.Services, 1)
	assert.Equal(t, "http", pluginTarget.Services[0].Name)
	assert.Equal(t, 8080, pluginTarget.Services[0].Port)

	// 验证技术栈信息
	assert.NotEmpty(t, pluginTarget.Technologies)
	assert.Equal(t, "HTTP", pluginTarget.Technologies[0].Name)

	// 测试HTTPS目标
	httpsURL, _ := url.Parse("https://secure.example.com/api")
	httpsTarget := engine.convertToPluginTarget(httpsURL, "test-task")

	assert.Equal(t, 443, httpsTarget.Port)
	assert.Equal(t, "https", httpsTarget.Protocol)
	assert.Len(t, httpsTarget.Technologies, 2) // HTTP + HTTPS
}

// TestConvertPluginResultToVulnerability 测试结果转换功能
func TestConvertPluginResultToVulnerability(t *testing.T) {
	engine := NewWebEngine()
	require.NotNil(t, engine)

	// 创建测试检测结果
	detectionResult := &plugins.DetectionResult{
		VulnerabilityID: "test-vuln-123",
		DetectorID:      "sql-injection-basic",
		IsVulnerable:    true,
		Confidence:      0.85,
		Severity:        "high",
		Title:           "SQL注入漏洞",
		Description:     "在参数中发现SQL注入漏洞",
		Evidence: []plugins.Evidence{
			{
				Type:        "response",
				Description: "数据库错误信息",
				Content:     "MySQL syntax error",
				Timestamp:   time.Now(),
			},
		},
		Payload:       "' OR 1=1 --",
		Remediation:   "使用参数化查询",
		References:    []string{"https://owasp.org/www-community/attacks/SQL_Injection"},
		Tags:          []string{"injection", "database"},
		DetectedAt:    time.Now(),
		RiskScore:     8.5,
		CVSS: &plugins.CVSSScore{
			Version: "3.1",
			Vector:  "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",
			Score:   9.8,
		},
		Metadata: map[string]interface{}{
			"url":       "http://example.com/login",
			"method":    "POST",
			"parameter": "username",
		},
	}

	// 转换为漏洞对象
	vuln := engine.convertPluginResultToVulnerability(detectionResult, "test-task")
	require.NotNil(t, vuln)

	// 验证基本信息
	assert.NotEmpty(t, vuln.ID)
	assert.Equal(t, "sql-injection-basic", vuln.Type)
	assert.Equal(t, "SQL注入漏洞", vuln.Name)
	assert.Equal(t, "在参数中发现SQL注入漏洞", vuln.Description)
	assert.Equal(t, "high", vuln.Severity)
	assert.Equal(t, "Web安全", vuln.Category)
	assert.Equal(t, "web_scanner", vuln.Scanner)

	// 验证目标信息
	assert.Equal(t, "http://example.com/login", vuln.URL)
	assert.Equal(t, "POST", vuln.Method)
	assert.Equal(t, "username", vuln.Parameter)
	assert.Equal(t, "' OR 1=1 --", vuln.Payload)

	// 验证证据和解决方案
	assert.Contains(t, vuln.Evidence, "数据库错误信息")
	assert.Equal(t, "使用参数化查询", vuln.Solution)
	assert.Contains(t, vuln.References, "https://owasp.org/www-community/attacks/SQL_Injection")

	// 验证CVSS信息
	assert.Equal(t, 9.8, vuln.CVSS)
	assert.Equal(t, "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H", vuln.Metadata["cvss_vector"])
	assert.Equal(t, "3.1", vuln.Metadata["cvss_version"])

	// 验证元数据
	assert.Equal(t, "sql-injection-basic", vuln.Metadata["detector_id"])
	assert.Equal(t, 0.85, vuln.Metadata["confidence"])
	assert.Equal(t, 8.5, vuln.Metadata["risk_score"])
}

// TestFallbackVulnerabilityDetection 测试回退检测功能
func TestFallbackVulnerabilityDetection(t *testing.T) {
	// 创建没有检测器管理器的引擎（模拟初始化失败）
	engine := &WebEngine{
		detectorManager: nil, // 故意设置为nil以触发回退
	}

	targetURL, _ := url.Parse("http://example.com")
	result := &types.ScanResult{
		TaskID:          "test-task",
		Vulnerabilities: make([]*types.Vulnerability, 0),
		Statistics:      &types.ScanStatistics{},
	}

	progress := make(chan *types.ScanProgress, 10)
	defer close(progress)

	stopChan := make(chan bool, 1)
	defer close(stopChan)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 执行检测（应该触发回退机制）
	err := engine.vulnerabilityDetection(ctx, targetURL, result, progress, "test-task", stopChan)
	
	// 由于没有实现传统检测方法，可能会有错误，但不应该panic
	t.Logf("回退检测结果: %v", err)
}

// BenchmarkPluginizedDetection 基准测试插件化检测性能
func BenchmarkPluginizedDetection(b *testing.B) {
	engine := NewWebEngine()
	if engine.detectorManager == nil {
		b.Skip("检测器管理器未初始化")
	}

	targetURL, _ := url.Parse("http://httpbin.org/get")
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result := &types.ScanResult{
			TaskID:          "bench-task",
			Vulnerabilities: make([]*types.Vulnerability, 0),
			Statistics:      &types.ScanStatistics{},
		}

		progress := make(chan *types.ScanProgress, 10)
		stopChan := make(chan bool, 1)

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		
		_ = engine.vulnerabilityDetection(ctx, targetURL, result, progress, "bench-task", stopChan)
		
		cancel()
		close(progress)
		close(stopChan)
	}
}
