package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectAccessControlBypass 检测访问控制绕过
func (d *PrivilegeBypassDetector) detectAccessControlBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试访问控制绕过载荷
	for _, payload := range d.accessControlPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送访问控制绕过测试请求
		response, err := d.sendPrivilegeBypassRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在访问控制绕过
		confidence := d.checkAccessControlBypass(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("访问控制绕过: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "access-control-bypass",
				Description: fmt.Sprintf("发现访问控制绕过: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractPrivilegeBypassEvidence(response, payload, "access-control"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 50)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectAuthBypass 检测权限验证绕过
func (d *PrivilegeBypassDetector) detectAuthBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试权限验证绕过载荷
	for _, payload := range d.authBypassPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送权限验证绕过测试请求
		response, err := d.sendAuthBypassRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在权限验证绕过
		confidence := d.checkAuthBypass(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("权限验证绕过: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "auth-bypass",
				Description: fmt.Sprintf("发现权限验证绕过: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractPrivilegeBypassEvidence(response, payload, "auth-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 60)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectRoleBypass 检测角色权限绕过
func (d *PrivilegeBypassDetector) detectRoleBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试角色权限绕过载荷
	for _, payload := range d.roleBypassPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送角色权限绕过测试请求
		response, err := d.sendPrivilegeBypassRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在角色权限绕过
		confidence := d.checkRoleBypass(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("角色权限绕过: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "role-bypass",
				Description: fmt.Sprintf("发现角色权限绕过: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractPrivilegeBypassEvidence(response, payload, "role-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 70)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPathTraversalBypass 检测路径遍历绕过
func (d *PrivilegeBypassDetector) detectPathTraversalBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试路径遍历绕过载荷
	for _, payload := range d.pathTraversalPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送路径遍历绕过测试请求
		response, err := d.sendPathTraversalRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查是否存在路径遍历绕过
		confidence := d.checkPathTraversalBypass(response, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("路径遍历绕过: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = response
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "path-traversal-bypass",
				Description: fmt.Sprintf("发现路径遍历绕过: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractPrivilegeBypassEvidence(response, payload, "path-traversal"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 80)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendPrivilegeBypassRequest 发送权限绕过测试请求
func (d *PrivilegeBypassDetector) sendPrivilegeBypassRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendPrivilegeBypassGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST表单注入
	postResp, err := d.sendPrivilegeBypassPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 如果有POST响应（即使有错误），也返回
	if postResp != "" {
		return postResp, nil
	}

	return "", fmt.Errorf("所有权限绕过请求都失败")
}

// sendAuthBypassRequest 发送权限验证绕过请求
func (d *PrivilegeBypassDetector) sendAuthBypassRequest(ctx context.Context, targetURL, payload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置基础请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	// 解析并设置权限验证绕过头部
	if strings.Contains(payload, ":") {
		parts := strings.SplitN(payload, ":", 2)
		if len(parts) == 2 {
			headerName := strings.TrimSpace(parts[0])
			headerValue := strings.TrimSpace(parts[1])
			req.Header.Set(headerName, headerValue)
		}
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// sendPathTraversalRequest 发送路径遍历请求
func (d *PrivilegeBypassDetector) sendPathTraversalRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建路径遍历URL
	var testURL string
	if strings.HasSuffix(targetURL, "/") {
		testURL = targetURL + payload
	} else {
		testURL = targetURL + "/" + payload
	}

	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// checkAccessControlBypass 检查访问控制绕过
func (d *PrivilegeBypassDetector) checkAccessControlBypass(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查权限绕过相关的响应模式
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查访问控制相关的响应模式
	for _, pattern := range d.accessPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查路径遍历成功的响应
	if strings.Contains(payloadLower, "../") || strings.Contains(payloadLower, "..\\") {
		if strings.Contains(responseLower, "admin") ||
			strings.Contains(responseLower, "administrator") ||
			strings.Contains(responseLower, "root") ||
			strings.Contains(responseLower, "superuser") ||
			strings.Contains(responseLower, "管理") ||
			strings.Contains(responseLower, "管理员") {
			confidence += 0.4
		}
	}

	// 检查编码绕过成功的响应
	if strings.Contains(payloadLower, "%2f") || strings.Contains(payloadLower, "%5c") {
		if strings.Contains(responseLower, "success") ||
			strings.Contains(responseLower, "access") ||
			strings.Contains(responseLower, "granted") ||
			strings.Contains(responseLower, "成功") ||
			strings.Contains(responseLower, "访问") ||
			strings.Contains(responseLower, "授权") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkAuthBypass 检查权限验证绕过
func (d *PrivilegeBypassDetector) checkAuthBypass(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查权限验证相关的响应模式
	for _, pattern := range d.authPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查认证绕过成功的响应
	if strings.Contains(payloadLower, "admin") || strings.Contains(payloadLower, "root") {
		if strings.Contains(responseLower, "welcome") ||
			strings.Contains(responseLower, "dashboard") ||
			strings.Contains(responseLower, "panel") ||
			strings.Contains(responseLower, "control") ||
			strings.Contains(responseLower, "欢迎") ||
			strings.Contains(responseLower, "仪表板") ||
			strings.Contains(responseLower, "面板") ||
			strings.Contains(responseLower, "控制") {
			confidence += 0.4
		}
	}

	// 检查头部绕过成功的响应
	if strings.Contains(payloadLower, "x-") || strings.Contains(payloadLower, "authorization") {
		if strings.Contains(responseLower, "authenticated") ||
			strings.Contains(responseLower, "authorized") ||
			strings.Contains(responseLower, "logged") ||
			strings.Contains(responseLower, "session") ||
			strings.Contains(responseLower, "认证") ||
			strings.Contains(responseLower, "授权") ||
			strings.Contains(responseLower, "登录") ||
			strings.Contains(responseLower, "会话") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkRoleBypass 检查角色权限绕过
func (d *PrivilegeBypassDetector) checkRoleBypass(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查角色权限相关的响应模式
	for _, pattern := range d.rolePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查角色绕过成功的响应
	if strings.Contains(payloadLower, "role") || strings.Contains(payloadLower, "permission") {
		if strings.Contains(responseLower, "admin") ||
			strings.Contains(responseLower, "administrator") ||
			strings.Contains(responseLower, "manager") ||
			strings.Contains(responseLower, "owner") ||
			strings.Contains(responseLower, "管理员") ||
			strings.Contains(responseLower, "管理者") {
			confidence += 0.4
		}
	}

	// 检查权限级别绕过成功的响应
	if strings.Contains(payloadLower, "level") || strings.Contains(payloadLower, "access") {
		if strings.Contains(responseLower, "elevated") ||
			strings.Contains(responseLower, "privileged") ||
			strings.Contains(responseLower, "high") ||
			strings.Contains(responseLower, "full") ||
			strings.Contains(responseLower, "提升") ||
			strings.Contains(responseLower, "特权") ||
			strings.Contains(responseLower, "完全") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkPathTraversalBypass 检查路径遍历绕过
func (d *PrivilegeBypassDetector) checkPathTraversalBypass(response, payload string) float64 {
	confidence := 0.0
	responseLower := strings.ToLower(response)
	payloadLower := strings.ToLower(payload)

	// 检查权限绕过相关的响应模式
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查路径遍历成功的响应
	if strings.Contains(payloadLower, "admin") || strings.Contains(payloadLower, "管理") {
		if strings.Contains(responseLower, "admin panel") ||
			strings.Contains(responseLower, "administration") ||
			strings.Contains(responseLower, "管理面板") ||
			strings.Contains(responseLower, "管理界面") {
			confidence += 0.4
		}
	}

	// 检查目录访问成功的响应
	if strings.Contains(payloadLower, "../") || strings.Contains(payloadLower, "..\\") {
		if strings.Contains(responseLower, "directory") ||
			strings.Contains(responseLower, "folder") ||
			strings.Contains(responseLower, "index") ||
			strings.Contains(responseLower, "listing") ||
			strings.Contains(responseLower, "目录") ||
			strings.Contains(responseLower, "文件夹") ||
			strings.Contains(responseLower, "列表") {
			confidence += 0.3
		}
	}

	// 检查编码绕过成功的响应
	if strings.Contains(payloadLower, "%") {
		if strings.Contains(responseLower, "decoded") ||
			strings.Contains(responseLower, "unescaped") ||
			strings.Contains(responseLower, "解码") ||
			strings.Contains(responseLower, "转义") {
			confidence += 0.3
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// extractPrivilegeBypassEvidence 提取权限绕过证据
func (d *PrivilegeBypassDetector) extractPrivilegeBypassEvidence(response, payload, vulnType string) string {
	// 限制证据长度
	maxLength := 1000
	evidence := fmt.Sprintf("权限绕过证据 (%s):\n", vulnType)

	// 添加载荷信息
	evidence += fmt.Sprintf("载荷: %s\n", payload)
	evidence += fmt.Sprintf("漏洞类型: %s\n", vulnType)

	// 检查响应中的权限绕过相关模式
	evidence += "\n权限绕过模式匹配:\n"
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现权限绕过模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的访问控制相关模式
	evidence += "\n访问控制模式匹配:\n"
	for _, pattern := range d.accessPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现访问控制模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的权限验证相关模式
	evidence += "\n权限验证模式匹配:\n"
	for _, pattern := range d.authPatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现权限验证模式: %s\n", pattern.String())
		}
	}

	// 检查响应中的角色权限相关模式
	evidence += "\n角色权限模式匹配:\n"
	for _, pattern := range d.rolePatterns {
		if pattern.MatchString(response) {
			evidence += fmt.Sprintf("- 发现角色权限模式: %s\n", pattern.String())
		}
	}

	// 添加响应摘要
	if len(response) > 200 {
		evidence += fmt.Sprintf("\n响应摘要: %s...\n", response[:200])
	} else {
		evidence += fmt.Sprintf("\n响应内容: %s\n", response)
	}

	// 分析载荷类型
	evidence += d.analyzePrivilegeBypassPayloadType(payload, vulnType)

	// 限制证据长度
	if len(evidence) > maxLength {
		evidence = evidence[:maxLength] + "..."
	}

	return evidence
}

// analyzePrivilegeBypassPayloadType 分析权限绕过载荷类型
func (d *PrivilegeBypassDetector) analyzePrivilegeBypassPayloadType(payload, vulnType string) string {
	analysis := "\n载荷分析:\n"
	payloadLower := strings.ToLower(payload)

	// 访问控制绕过载荷分析
	if vulnType == "access-control" {
		analysis += "- 载荷类型: 访问控制绕过\n"

		if strings.Contains(payloadLower, "../") || strings.Contains(payloadLower, "..\\") {
			analysis += "- 攻击方法: 路径遍历\n"
		} else if strings.Contains(payloadLower, "%2f") || strings.Contains(payloadLower, "%5c") {
			analysis += "- 攻击方法: URL编码绕过\n"
		} else if strings.Contains(payloadLower, "%u") {
			analysis += "- 攻击方法: Unicode编码绕过\n"
		} else if strings.Contains(payloadLower, "%25") {
			analysis += "- 攻击方法: 双重编码绕过\n"
		} else if strings.Contains(payloadLower, "%00") {
			analysis += "- 攻击方法: 空字节绕过\n"
		}
	}

	// 权限验证绕过载荷分析
	if vulnType == "auth-bypass" {
		analysis += "- 载荷类型: 权限验证绕过\n"

		if strings.Contains(payloadLower, "x-forwarded") || strings.Contains(payloadLower, "x-real-ip") {
			analysis += "- 攻击方法: HTTP头部绕过\n"
		} else if strings.Contains(payloadLower, "user-agent") {
			analysis += "- 攻击方法: 用户代理绕过\n"
		} else if strings.Contains(payloadLower, "authorization") {
			analysis += "- 攻击方法: 认证头部绕过\n"
		} else if strings.Contains(payloadLower, "cookie") {
			analysis += "- 攻击方法: 会话绕过\n"
		} else if strings.Contains(payloadLower, "x-admin") || strings.Contains(payloadLower, "x-user") {
			analysis += "- 攻击方法: 自定义头部绕过\n"
		}
	}

	// 角色权限绕过载荷分析
	if vulnType == "role-bypass" {
		analysis += "- 载荷类型: 角色权限绕过\n"

		if strings.Contains(payloadLower, "role") {
			analysis += "- 攻击目标: 角色参数\n"
		} else if strings.Contains(payloadLower, "permission") || strings.Contains(payloadLower, "access") {
			analysis += "- 攻击目标: 权限参数\n"
		} else if strings.Contains(payloadLower, "level") {
			analysis += "- 攻击目标: 级别参数\n"
		} else if strings.Contains(payloadLower, "group") {
			analysis += "- 攻击目标: 组参数\n"
		} else if strings.Contains(payloadLower, "type") {
			analysis += "- 攻击目标: 类型参数\n"
		} else if strings.Contains(payloadLower, "admin") || strings.Contains(payloadLower, "root") {
			analysis += "- 攻击目标: 管理员权限\n"
		}
	}

	// 路径遍历绕过载荷分析
	if vulnType == "path-traversal" {
		analysis += "- 载荷类型: 路径遍历绕过\n"

		if strings.Contains(payloadLower, "admin") {
			analysis += "- 攻击目标: 管理员目录\n"
		} else if strings.Contains(payloadLower, "root") {
			analysis += "- 攻击目标: 根目录\n"
		} else if strings.Contains(payloadLower, "panel") || strings.Contains(payloadLower, "dashboard") {
			analysis += "- 攻击目标: 管理面板\n"
		} else if strings.Contains(payloadLower, "control") || strings.Contains(payloadLower, "management") {
			analysis += "- 攻击目标: 控制界面\n"
		}
	}

	// 检查载荷特征
	if strings.Contains(payload, "../") || strings.Contains(payload, "..\\") {
		analysis += "- 载荷特征: 目录遍历\n"
	} else if strings.Contains(payload, "%") {
		analysis += "- 载荷特征: 编码绕过\n"
	} else if strings.Contains(payload, "admin") || strings.Contains(payload, "root") {
		analysis += "- 载荷特征: 权限提升\n"
	} else if strings.Contains(payload, "true") || strings.Contains(payload, "1") {
		analysis += "- 载荷特征: 布尔值绕过\n"
	} else if strings.Contains(payload, "=") {
		analysis += "- 载荷特征: 参数注入\n"
	}

	return analysis
}

// sendPrivilegeBypassGETRequest 发送权限绕过GET请求
func (d *PrivilegeBypassDetector) sendPrivilegeBypassGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// sendPrivilegeBypassPOSTRequest 发送权限绕过POST请求
func (d *PrivilegeBypassDetector) sendPrivilegeBypassPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}
