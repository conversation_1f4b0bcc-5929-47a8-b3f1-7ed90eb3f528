package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestXSSDetectorBasicFunctionality 测试XSS检测器基础功能
func TestXSSDetectorBasicFunctionality(t *testing.T) {
	detector := NewXSSDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "xss-comprehensive", detector.GetID())
	assert.Equal(t, "跨站脚本(XSS)综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "medium", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-79")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestXSSDetectorApplicability 测试XSS检测器适用性
func TestXSSDetectorApplicability(t *testing.T) {
	detector := NewXSSDetector()

	// 测试HTTP目标
	httpTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/test?id=1",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(httpTarget))

	// 测试HTTPS目标
	httpsTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "https://example.com/test",
		Protocol: "https",
		Port:     443,
	}
	assert.True(t, detector.IsApplicable(httpsTarget))

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/form",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/submit",
				Method: "POST",
				Fields: map[string]string{
					"username": "text",
					"comment":  "textarea",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestXSSDetectorConfiguration 测试XSS检测器配置
func TestXSSDetectorConfiguration(t *testing.T) {
	detector := NewXSSDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 30*time.Second, config.Timeout)
	assert.Equal(t, 3, config.MaxRetries)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         60 * time.Second,
		MaxRetries:      5,
		Concurrency:     10,
		RateLimit:       20,
		FollowRedirects: false,
		VerifySSL:       true,
		MaxResponseSize: 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 60*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 5, updatedConfig.MaxRetries)
	assert.Equal(t, 10, updatedConfig.Concurrency)
}

// TestXSSDetectorPayloads 测试XSS检测器载荷
func TestXSSDetectorPayloads(t *testing.T) {
	detector := NewXSSDetector()

	// 检查载荷是否已初始化
	assert.NotEmpty(t, detector.payloads)
	assert.Greater(t, len(detector.payloads), 50) // 应该有足够多的载荷

	// 检查上下文载荷
	assert.NotEmpty(t, detector.contextPayloads)
	assert.Contains(t, detector.contextPayloads, "attribute")
	assert.Contains(t, detector.contextPayloads, "javascript")
	assert.Contains(t, detector.contextPayloads, "css")

	// 检查反射模式
	assert.NotEmpty(t, detector.reflectionPatterns)
	assert.Greater(t, len(detector.reflectionPatterns), 10)

	// 检查执行模式
	assert.NotEmpty(t, detector.executionPatterns)
	assert.Greater(t, len(detector.executionPatterns), 10)
}

// TestXSSDetectorReflectionCheck 测试XSS反射检查
func TestXSSDetectorReflectionCheck(t *testing.T) {
	detector := NewXSSDetector()

	// 测试明显的XSS反射
	payload := "<script>alert('XSS')</script>"
	response := `<html><body>Hello <script>alert('XSS')</script> World</body></html>`
	confidence := detector.checkXSSReflection(response, payload)
	assert.Greater(t, confidence, 0.5)

	// 测试事件处理器反射
	payload2 := "<img src=x onerror=alert('XSS')>"
	response2 := `<html><body><img src=x onerror=alert('XSS')></body></html>`
	confidence2 := detector.checkXSSReflection(response2, payload2)
	assert.Greater(t, confidence2, 0.5)

	// 测试无反射情况
	payload3 := "<script>alert('XSS')</script>"
	response3 := `<html><body>Hello World</body></html>`
	confidence3 := detector.checkXSSReflection(response3, payload3)
	assert.Equal(t, 0.0, confidence3)

	// 测试编码后的反射（应该置信度较低）
	payload4 := "<script>alert('XSS')</script>"
	response4 := `<html><body>&lt;script&gt;alert('XSS')&lt;/script&gt;</body></html>`
	confidence4 := detector.checkXSSReflection(response4, payload4)
	assert.Less(t, confidence4, 0.5) // 编码后的反射置信度应该较低
}

// TestXSSDetectorDOMPatterns 测试DOM XSS模式检查
func TestXSSDetectorDOMPatterns(t *testing.T) {
	detector := NewXSSDetector()

	// 测试包含DOM操作的响应
	payload := "#<script>alert('DOM-XSS')</script>"
	response := `
	<script>
		var hash = location.hash;
		document.write(hash);
	</script>
	`
	confidence := detector.checkDOMXSSPatterns(response, payload)
	assert.Greater(t, confidence, 0.3)

	// 测试innerHTML操作
	response2 := `
	<script>
		element.innerHTML = userInput;
	</script>
	`
	confidence2 := detector.checkDOMXSSPatterns(response2, payload)
	assert.GreaterOrEqual(t, confidence2, 0.2)

	// 测试无DOM操作的响应
	response3 := `<html><body>Static content</body></html>`
	confidence3 := detector.checkDOMXSSPatterns(response3, payload)
	assert.Equal(t, 0.0, confidence3)
}

// TestXSSDetectorPayloadInjection 测试载荷注入
func TestXSSDetectorPayloadInjection(t *testing.T) {
	detector := NewXSSDetector()

	// 测试URL参数注入
	targetURL := "http://example.com/test?id=123"
	payload := "<script>alert('XSS')</script>"
	injectedURL := detector.injectPayload(targetURL, payload)
	// URL编码后的载荷应该在URL中
	assert.Contains(t, injectedURL, "%3Cscript%3E") // <script> 编码后

	// 测试无参数URL注入
	targetURL2 := "http://example.com/test"
	injectedURL2 := detector.injectPayload(targetURL2, payload)
	assert.Contains(t, injectedURL2, "test=")
	assert.Contains(t, injectedURL2, "%3Cscript%3E") // 编码后的载荷

	// 测试多参数URL注入
	targetURL3 := "http://example.com/test?param1=value1&param2=value2"
	injectedURL3 := detector.injectPayload(targetURL3, payload)
	assert.Contains(t, injectedURL3, "%3Cscript%3E") // 编码后的载荷
}

// TestXSSDetectorRiskScore 测试风险评分计算
func TestXSSDetectorRiskScore(t *testing.T) {
	detector := NewXSSDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 5.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 3.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestXSSDetectorLifecycle 测试检测器生命周期
func TestXSSDetectorLifecycle(t *testing.T) {
	detector := NewXSSDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkXSSDetectorReflectionCheck 基准测试反射检查性能
func BenchmarkXSSDetectorReflectionCheck(b *testing.B) {
	detector := NewXSSDetector()
	payload := "<script>alert('XSS')</script>"
	response := `<html><body>Hello <script>alert('XSS')</script> World</body></html>`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkXSSReflection(response, payload)
	}
}

// BenchmarkXSSDetectorPayloadInjection 基准测试载荷注入性能
func BenchmarkXSSDetectorPayloadInjection(b *testing.B) {
	detector := NewXSSDetector()
	targetURL := "http://example.com/test?id=123&name=test"
	payload := "<script>alert('XSS')</script>"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.injectPayload(targetURL, payload)
	}
}
