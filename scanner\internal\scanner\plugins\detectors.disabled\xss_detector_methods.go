package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// initializePatterns 初始化检测模式
func (d *XSSDetector) initializePatterns() {
	// 反射模式 - 检测载荷是否在响应中被反射
	reflectionPatterns := []string{
		`<script[^>]*>.*?alert\s*\(.*?\).*?</script>`,
		`<script[^>]*>.*?confirm\s*\(.*?\).*?</script>`,
		`<script[^>]*>.*?prompt\s*\(.*?\).*?</script>`,
		`on\w+\s*=\s*["\']?[^"\']*alert\s*\([^"\']*["\']?`,
		`on\w+\s*=\s*["\']?[^"\']*confirm\s*\([^"\']*["\']?`,
		`on\w+\s*=\s*["\']?[^"\']*prompt\s*\([^"\']*["\']?`,
		`javascript\s*:\s*alert\s*\(`,
		`javascript\s*:\s*confirm\s*\(`,
		`javascript\s*:\s*prompt\s*\(`,
		`<svg[^>]*onload\s*=\s*["\']?[^"\']*alert\s*\([^"\']*["\']?`,
		`<img[^>]*onerror\s*=\s*["\']?[^"\']*alert\s*\([^"\']*["\']?`,
		`<iframe[^>]*src\s*=\s*["\']?javascript:`,
		`<object[^>]*data\s*=\s*["\']?javascript:`,
		`<embed[^>]*src\s*=\s*["\']?javascript:`,
		`data:text/html[^>]*<script`,
		`expression\s*\(\s*alert\s*\(`,
		`@import\s*["\']?javascript:`,
		`url\s*\(\s*["\']?javascript:`,
	}

	d.reflectionPatterns = make([]*regexp.Regexp, 0, len(reflectionPatterns))
	for _, pattern := range reflectionPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.reflectionPatterns = append(d.reflectionPatterns, compiled)
		}
	}

	// 执行模式 - 检测JavaScript是否可能被执行
	executionPatterns := []string{
		`<script[^>]*>(?:(?!</script>).)*</script>`,
		`on\w+\s*=\s*["\']?[^"\']*["\']?`,
		`javascript\s*:`,
		`vbscript\s*:`,
		`data:text/html`,
		`expression\s*\(`,
		`@import\s*["\']?javascript:`,
		`url\s*\(\s*["\']?javascript:`,
		`<svg[^>]*onload`,
		`<img[^>]*onerror`,
		`<iframe[^>]*srcdoc`,
		`<object[^>]*data\s*=\s*["\']?javascript:`,
		`<embed[^>]*src\s*=\s*["\']?javascript:`,
		`<meta[^>]*http-equiv\s*=\s*["\']?refresh[^>]*url\s*=\s*["\']?javascript:`,
		`<form[^>]*action\s*=\s*["\']?javascript:`,
		`<button[^>]*formaction\s*=\s*["\']?javascript:`,
	}

	d.executionPatterns = make([]*regexp.Regexp, 0, len(executionPatterns))
	for _, pattern := range executionPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.executionPatterns = append(d.executionPatterns, compiled)
		}
	}
}

// detectReflectedXSS 检测反射型XSS
func (d *XSSDetector) detectReflectedXSS(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试基础载荷
	for i, payload := range d.payloads {
		if i >= 20 { // 限制载荷数量避免过多请求
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造测试URL
		testURL := d.injectPayload(target.URL, payload)

		// 发送请求
		resp, err := d.makeRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查反射
		confidence := d.checkXSSReflection(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = testURL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "reflection",
				Description: fmt.Sprintf("载荷 %s 在响应中被反射 (置信度: %.2f)", payload, confidence),
				Content:     d.extractReflectionEvidence(resp, payload),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDOMXSS 检测DOM XSS
func (d *XSSDetector) detectDOMXSS(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// DOM XSS特定载荷
	domPayloads := []string{
		"#<script>alert('DOM-XSS')</script>",
		"#<img src=x onerror=alert('DOM-XSS')>",
		"#<svg onload=alert('DOM-XSS')>",
		"#javascript:alert('DOM-XSS')",
		"#data:text/html,<script>alert('DOM-XSS')</script>",
	}

	for _, payload := range domPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造测试URL（DOM XSS通常在fragment中）
		testURL := target.URL + payload

		// 发送请求
		resp, err := d.makeRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查DOM操作模式
		confidence := d.checkDOMXSSPatterns(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = testURL
			vulnerableResponse = resp
		}

		if confidence > 0.4 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "dom-xss",
				Description: fmt.Sprintf("检测到可能的DOM XSS，载荷: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractDOMEvidence(resp, payload),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectStoredXSS 检测存储型XSS
func (d *XSSDetector) detectStoredXSS(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 存储型XSS载荷（更保守）
	storedPayloads := []string{
		"<script>alert('Stored-XSS')</script>",
		"<img src=x onerror=alert('Stored-XSS')>",
		"<svg onload=alert('Stored-XSS')>",
	}

	for _, form := range target.Forms {
		for _, payload := range storedPayloads {
			select {
			case <-ctx.Done():
				return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
			default:
			}

			// 提交表单数据
			resp, err := d.submitForm(ctx, form, payload)
			if err != nil {
				continue
			}

			// 检查载荷是否被存储并反射
			confidence := d.checkStoredXSSReflection(resp, payload)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = payload
				vulnerableRequest = form.Action
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "stored-xss",
					Description: fmt.Sprintf("检测到可能的存储型XSS，载荷: %s (置信度: %.2f)", payload, confidence),
					Content:     d.extractStoredEvidence(resp, payload),
					Location:    form.Action,
					Timestamp:   time.Now(),
				})
			}
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// injectPayload 将载荷注入到URL中
func (d *XSSDetector) injectPayload(targetURL, payload string) string {
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return targetURL
	}

	// 如果URL已有参数，在现有参数中注入
	if parsedURL.RawQuery != "" {
		values, err := url.ParseQuery(parsedURL.RawQuery)
		if err == nil {
			// 在第一个参数中注入载荷
			for key := range values {
				values.Set(key, payload)
				break
			}
			parsedURL.RawQuery = values.Encode()
			return parsedURL.String()
		}
	}

	// 如果没有参数，添加测试参数
	if parsedURL.RawQuery == "" {
		parsedURL.RawQuery = "test=" + url.QueryEscape(payload)
	}

	return parsedURL.String()
}

// makeRequest 发送HTTP请求
func (d *XSSDetector) makeRequest(ctx context.Context, targetURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置常见的浏览器头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// checkXSSReflection 检查XSS反射
func (d *XSSDetector) checkXSSReflection(response, payload string) float64 {
	confidence := 0.0

	// 检查载荷是否直接反射
	if strings.Contains(response, payload) {
		confidence += 0.4
	}

	// 检查反射模式
	for _, pattern := range d.reflectionPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查执行模式
	for _, pattern := range d.executionPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查特定的XSS指示器
	xssIndicators := []string{
		"alert('XSS')",
		"alert(1)",
		"confirm('XSS')",
		"prompt('XSS')",
		"javascript:alert",
		"onerror=alert",
		"onload=alert",
		"onmouseover=alert",
	}

	for _, indicator := range xssIndicators {
		if strings.Contains(strings.ToLower(response), strings.ToLower(indicator)) {
			confidence += 0.2
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkDOMXSSPatterns 检查DOM XSS模式
func (d *XSSDetector) checkDOMXSSPatterns(response, payload string) float64 {
	confidence := 0.0

	// 检查DOM操作相关的JavaScript代码
	domPatterns := []string{
		`document\.write\s*\(`,
		`document\.writeln\s*\(`,
		`innerHTML\s*=`,
		`outerHTML\s*=`,
		`document\.location`,
		`window\.location`,
		`location\.href`,
		`location\.hash`,
		`location\.search`,
		`eval\s*\(`,
		`setTimeout\s*\(`,
		`setInterval\s*\(`,
	}

	for _, pattern := range domPatterns {
		if matched, _ := regexp.MatchString(`(?i)`+pattern, response); matched {
			confidence += 0.2
		}
	}

	// 检查载荷是否在JavaScript上下文中
	if strings.Contains(response, payload) {
		confidence += 0.3
	}

	// 检查是否有可能的DOM XSS sink
	sinkPatterns := []string{
		`\.src\s*=`,
		`\.href\s*=`,
		`\.action\s*=`,
		`\.formAction\s*=`,
		`\.data\s*=`,
	}

	for _, pattern := range sinkPatterns {
		if matched, _ := regexp.MatchString(`(?i)`+pattern, response); matched {
			confidence += 0.1
		}
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkStoredXSSReflection 检查存储型XSS反射
func (d *XSSDetector) checkStoredXSSReflection(response, payload string) float64 {
	confidence := 0.0

	// 存储型XSS需要更高的置信度
	if strings.Contains(response, payload) {
		confidence += 0.6
	}

	// 检查是否在HTML上下文中未编码
	if strings.Contains(response, "<script>") && strings.Contains(response, payload) {
		confidence += 0.3
	}

	// 检查事件处理器
	eventHandlers := []string{"onload", "onerror", "onclick", "onmouseover", "onfocus"}
	for _, handler := range eventHandlers {
		if strings.Contains(response, handler) && strings.Contains(response, payload) {
			confidence += 0.2
		}
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// submitForm 提交表单数据
func (d *XSSDetector) submitForm(ctx context.Context, form plugins.FormInfo, payload string) (string, error) {
	// 构造表单数据
	formData := url.Values{}
	for fieldName, fieldType := range form.Fields {
		if fieldType == "text" || fieldType == "textarea" || fieldType == "email" || fieldType == "" {
			formData.Set(fieldName, payload)
		} else {
			formData.Set(fieldName, "test")
		}
	}

	// 创建POST请求
	req, err := http.NewRequestWithContext(ctx, form.Method, form.Action, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// extractReflectionEvidence 提取反射证据
func (d *XSSDetector) extractReflectionEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		if strings.Contains(line, payload) {
			return strings.TrimSpace(line)
		}
	}
	return "载荷在响应中被反射"
}

// extractDOMEvidence 提取DOM证据
func (d *XSSDetector) extractDOMEvidence(response, payload string) string {
	// 查找包含DOM操作的行
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		if strings.Contains(line, "document.") || strings.Contains(line, "window.") ||
			strings.Contains(line, "location.") || strings.Contains(line, "innerHTML") {
			return strings.TrimSpace(line)
		}
	}
	return "检测到可能的DOM XSS"
}

// extractStoredEvidence 提取存储型证据
func (d *XSSDetector) extractStoredEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		if strings.Contains(line, payload) &&
			(strings.Contains(line, "<script>") || strings.Contains(line, "onerror") ||
				strings.Contains(line, "onload") || strings.Contains(line, "onclick")) {
			return strings.TrimSpace(line)
		}
	}
	return "载荷可能被存储并在响应中执行"
}
