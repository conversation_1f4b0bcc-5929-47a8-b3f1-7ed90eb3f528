package engines

import (
	"context"
	"sync"
	"time"

	"scanner/internal/scanner/types"

	"github.com/sirupsen/logrus"
)

// IntelligentFalsePositiveFilter 智能误报过滤器
// 使用机器学习和启发式规则减少误报，提高检测准确性
type IntelligentFalsePositiveFilter struct {
	// 过滤引擎
	mlEngine           *MachineLearningEngine
	ruleEngine         *HeuristicRuleEngine
	contextEngine      *ContextAnalysisEngine
	verificationEngine *VerificationEngine

	// 学习系统
	feedbackSystem    *FeedbackLearningSystem
	patternRecognizer *PatternRecognizer

	// 配置和状态
	config *FilterConfig
	logger *logrus.Logger
	stats  *FilterStats
	mutex  sync.RWMutex
}

// MachineLearningEngine 机器学习引擎
// 基于历史数据训练模型，预测漏洞的真实性
type MachineLearningEngine struct {
	models           map[string]*MLModel
	featureExtractor *FeatureExtractor
	trainer          *ModelTrainer
	predictor        *ModelPredictor
}

// HeuristicRuleEngine 启发式规则引擎
// 基于专家知识和经验规则过滤明显的误报
type HeuristicRuleEngine struct {
	rules         []*HeuristicRule
	ruleIndex     map[string][]*HeuristicRule
	ruleEvaluator *RuleEvaluator
}

// ContextAnalysisEngine 上下文分析引擎
// 分析漏洞的上下文环境，判断其真实性
type ContextAnalysisEngine struct {
	environmentAnalyzer *EnvironmentAnalyzer
	serviceAnalyzer     *ServiceAnalyzer
	configAnalyzer      *ConfigurationAnalyzer
	behaviorAnalyzer    *BehaviorAnalyzer
}

// VerificationEngine 验证引擎
// 通过多种方式验证漏洞的真实性
type VerificationEngine struct {
	pocVerifier *PoCVerifier
	// 注意：这些验证器类型已在poc_manager.go中重新定义
	// responseVerifier    *ResponseVerifier
	// timeVerifier        *TimingVerifier
	// sideChannelVerifier *SideChannelVerifier
}

// FilterConfig 过滤器配置
type FilterConfig struct {
	// ML配置
	EnableMLFiltering     bool    `json:"enable_ml_filtering"`
	MLConfidenceThreshold float64 `json:"ml_confidence_threshold"`
	ModelUpdateInterval   int     `json:"model_update_interval_hours"`

	// 规则配置
	EnableRuleFiltering bool   `json:"enable_rule_filtering"`
	RuleStrictness      string `json:"rule_strictness"` // strict, moderate, loose
	CustomRulesEnabled  bool   `json:"custom_rules_enabled"`

	// 上下文配置
	EnableContextAnalysis bool `json:"enable_context_analysis"`
	ContextDepth          int  `json:"context_depth"`
	EnvironmentAware      bool `json:"environment_aware"`

	// 验证配置
	EnableDeepVerification  bool `json:"enable_deep_verification"`
	VerificationTimeout     int  `json:"verification_timeout_seconds"`
	MaxVerificationAttempts int  `json:"max_verification_attempts"`

	// 性能配置
	MaxConcurrency int `json:"max_concurrency"`
	FilterTimeout  int `json:"filter_timeout_seconds"`

	// 质量配置
	TargetFalsePositiveRate float64 `json:"target_false_positive_rate"`
	MinTruePositiveRate     float64 `json:"min_true_positive_rate"`
}

// FilterStats 过滤器统计
type FilterStats struct {
	// 处理统计
	TotalProcessed int64 `json:"total_processed"`
	FilteredOut    int64 `json:"filtered_out"`
	Verified       int64 `json:"verified"`

	// 准确性统计
	TruePositives  int64 `json:"true_positives"`
	FalsePositives int64 `json:"false_positives"`
	TrueNegatives  int64 `json:"true_negatives"`
	FalseNegatives int64 `json:"false_negatives"`

	// 性能统计
	AverageProcessingTime time.Duration `json:"average_processing_time"`
	MLAccuracy            float64       `json:"ml_accuracy"`
	RuleEffectiveness     float64       `json:"rule_effectiveness"`

	// 学习统计
	ModelVersion     int       `json:"model_version"`
	TrainingDataSize int64     `json:"training_data_size"`
	LastModelUpdate  time.Time `json:"last_model_update"`
}

// MLModel 机器学习模型
type MLModel struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"` // random_forest, neural_network, svm
	Version      int                    `json:"version"`
	Accuracy     float64                `json:"accuracy"`
	Features     []string               `json:"features"`
	Parameters   map[string]interface{} `json:"parameters"`
	TrainingData []*TrainingExample     `json:"training_data"`
	LastTrained  time.Time              `json:"last_trained"`
}

// TrainingExample 训练样本
type TrainingExample struct {
	Features   map[string]float64 `json:"features"`
	Label      bool               `json:"label"` // true = real vulnerability, false = false positive
	Confidence float64            `json:"confidence"`
	Source     string             `json:"source"`
	Timestamp  time.Time          `json:"timestamp"`
}

// HeuristicRule 启发式规则
type HeuristicRule struct {
	ID            string           `json:"id"`
	Name          string           `json:"name"`
	Description   string           `json:"description"`
	Category      string           `json:"category"`
	Conditions    []*RuleCondition `json:"conditions"`
	Action        string           `json:"action"` // filter, flag, verify
	Confidence    float64          `json:"confidence"`
	Effectiveness float64          `json:"effectiveness"`
	Enabled       bool             `json:"enabled"`
}

// RuleCondition 规则条件
type RuleCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"` // equals, contains, regex, range
	Value    interface{} `json:"value"`
	Weight   float64     `json:"weight"`
}

// FilterResult 过滤结果
type FilterResult struct {
	OriginalVulns []*types.Vulnerability `json:"original_vulns"`
	FilteredVulns []*types.Vulnerability `json:"filtered_vulns"`
	RemovedVulns  []*types.Vulnerability `json:"removed_vulns"`

	// 过滤详情
	MLPredictions   []*MLPrediction       `json:"ml_predictions"`
	RuleMatches     []*RuleMatch          `json:"rule_matches"`
	ContextAnalysis []*ContextAnalysis    `json:"context_analysis"`
	Verifications   []*VerificationResult `json:"verifications"`

	// 统计信息
	FilterEfficiency float64       `json:"filter_efficiency"`
	ProcessingTime   time.Duration `json:"processing_time"`
	Confidence       float64       `json:"confidence"`
}

// MLPrediction ML预测结果
type MLPrediction struct {
	VulnID     string             `json:"vuln_id"`
	Prediction bool               `json:"prediction"` // true = real, false = false positive
	Confidence float64            `json:"confidence"`
	ModelUsed  string             `json:"model_used"`
	Features   map[string]float64 `json:"features"`
}

// RuleMatch 规则匹配结果
type RuleMatch struct {
	VulnID     string  `json:"vuln_id"`
	RuleID     string  `json:"rule_id"`
	RuleName   string  `json:"rule_name"`
	Matched    bool    `json:"matched"`
	Confidence float64 `json:"confidence"`
	Action     string  `json:"action"`
}

// ContextAnalysis 上下文分析结果
type ContextAnalysis struct {
	VulnID        string                `json:"vuln_id"`
	Environment   *EnvironmentContext   `json:"environment"`
	Service       *ServiceContext       `json:"service"`
	Configuration *ConfigurationContext `json:"configuration"`
	Behavior      *BehaviorContext      `json:"behavior"`
	Likelihood    float64               `json:"likelihood"`
}

// NewIntelligentFalsePositiveFilter 创建智能误报过滤器
func NewIntelligentFalsePositiveFilter(config *FilterConfig) *IntelligentFalsePositiveFilter {
	if config == nil {
		config = DefaultFilterConfig()
	}

	filter := &IntelligentFalsePositiveFilter{
		config: config,
		logger: logrus.New(),
		stats:  &FilterStats{},
	}

	// 初始化组件
	filter.initializeComponents()

	// 加载预训练模型和规则
	filter.loadModelsAndRules()

	return filter
}

// DefaultFilterConfig 默认过滤器配置
func DefaultFilterConfig() *FilterConfig {
	return &FilterConfig{
		EnableMLFiltering:       true,
		MLConfidenceThreshold:   0.7,
		ModelUpdateInterval:     24,
		EnableRuleFiltering:     true,
		RuleStrictness:          "moderate",
		CustomRulesEnabled:      true,
		EnableContextAnalysis:   true,
		ContextDepth:            3,
		EnvironmentAware:        true,
		EnableDeepVerification:  false, // 默认关闭，影响性能
		VerificationTimeout:     30,
		MaxVerificationAttempts: 3,
		MaxConcurrency:          3,
		FilterTimeout:           60,
		TargetFalsePositiveRate: 0.02,
		MinTruePositiveRate:     0.95,
	}
}

// FilterVulnerabilities 过滤漏洞
func (f *IntelligentFalsePositiveFilter) FilterVulnerabilities(ctx context.Context, vulns []*types.Vulnerability) (*FilterResult, error) {
	if len(vulns) == 0 {
		return &FilterResult{
			OriginalVulns: vulns,
			FilteredVulns: vulns,
			RemovedVulns:  []*types.Vulnerability{},
		}, nil
	}

	f.logger.Infof("开始智能误报过滤，漏洞数量: %d", len(vulns))
	startTime := time.Now()

	// 创建参数影响验证器
	paramConfig := &ParameterValidationConfig{
		MaxValidationRequests: 10,
		RequestTimeout:        30 * time.Second,
		RequestInterval:       200 * time.Millisecond,
		SimilarityThreshold:   0.95,
		ImpactThreshold:       0.3,
		EnableDeepValidation:  true,
		EnableTimingAnalysis:  true,
	}
	_ = NewParameterImpactValidator(paramConfig) // paramValidator 暂时未使用

	result := &FilterResult{
		OriginalVulns:   vulns,
		FilteredVulns:   []*types.Vulnerability{},
		RemovedVulns:    []*types.Vulnerability{},
		MLPredictions:   []*MLPrediction{},
		RuleMatches:     []*RuleMatch{},
		ContextAnalysis: []*ContextAnalysis{},
		Verifications:   []*VerificationResult{},
	}

	// 创建过滤上下文
	filterCtx, cancel := context.WithTimeout(ctx, time.Duration(f.config.FilterTimeout)*time.Second)
	defer cancel()

	// 并发处理漏洞
	vulnChan := make(chan *types.Vulnerability, len(vulns))
	resultChan := make(chan *VulnFilterResult, len(vulns))

	// 启动工作协程
	for i := 0; i < f.config.MaxConcurrency; i++ {
		go f.filterWorker(filterCtx, vulnChan, resultChan)
	}

	// 发送漏洞到处理队列
	for _, vuln := range vulns {
		vulnChan <- vuln
	}
	close(vulnChan)

	// 收集结果
	for i := 0; i < len(vulns); i++ {
		select {
		case vulnResult := <-resultChan:
			if vulnResult.KeepVuln {
				result.FilteredVulns = append(result.FilteredVulns, vulnResult.Vuln)
			} else {
				result.RemovedVulns = append(result.RemovedVulns, vulnResult.Vuln)
			}

			// 收集分析结果
			if vulnResult.MLPrediction != nil {
				result.MLPredictions = append(result.MLPredictions, vulnResult.MLPrediction)
			}
			if vulnResult.RuleMatch != nil {
				result.RuleMatches = append(result.RuleMatches, vulnResult.RuleMatch)
			}
			if vulnResult.ContextAnalysis != nil {
				result.ContextAnalysis = append(result.ContextAnalysis, vulnResult.ContextAnalysis)
			}
			if vulnResult.Verification != nil {
				result.Verifications = append(result.Verifications, vulnResult.Verification)
			}

		case <-filterCtx.Done():
			f.logger.Warn("过滤超时")
			break
		}
	}

	// 计算统计信息
	result.ProcessingTime = time.Since(startTime)
	result.FilterEfficiency = float64(len(result.RemovedVulns)) / float64(len(vulns))
	result.Confidence = f.calculateOverallConfidence(result)

	// 更新统计
	f.updateStats(result)

	f.logger.Infof("误报过滤完成: 原始=%d, 保留=%d, 过滤=%d, 效率=%.2f%%",
		len(vulns), len(result.FilteredVulns), len(result.RemovedVulns), result.FilterEfficiency*100)

	return result, nil
}

// VulnFilterResult 单个漏洞过滤结果
type VulnFilterResult struct {
	Vuln            *types.Vulnerability
	KeepVuln        bool
	MLPrediction    *MLPrediction
	RuleMatch       *RuleMatch
	ContextAnalysis *ContextAnalysis
	Verification    *VerificationResult
	Confidence      float64
}

// filterWorker 过滤工作协程
func (f *IntelligentFalsePositiveFilter) filterWorker(ctx context.Context, vulnChan <-chan *types.Vulnerability, resultChan chan<- *VulnFilterResult) {
	for vuln := range vulnChan {
		select {
		case <-ctx.Done():
			return
		default:
			result := f.filterSingleVulnerability(ctx, vuln)
			resultChan <- result
		}
	}
}

// filterSingleVulnerability 过滤单个漏洞
func (f *IntelligentFalsePositiveFilter) filterSingleVulnerability(ctx context.Context, vuln *types.Vulnerability) *VulnFilterResult {
	result := &VulnFilterResult{
		Vuln:     vuln,
		KeepVuln: true, // 默认保留
	}

	var confidenceScores []float64

	// 1. 机器学习预测
	if f.config.EnableMLFiltering {
		mlPred := f.mlEngine.Predict(vuln)
		result.MLPrediction = mlPred

		if mlPred.Confidence >= f.config.MLConfidenceThreshold {
			if !mlPred.Prediction { // 预测为误报
				result.KeepVuln = false
			}
			confidenceScores = append(confidenceScores, mlPred.Confidence)
		}
	}

	// 2. 启发式规则检查
	if f.config.EnableRuleFiltering {
		ruleMatch := f.ruleEngine.EvaluateRules(vuln)
		result.RuleMatch = ruleMatch

		if ruleMatch.Matched && ruleMatch.Action == "filter" {
			result.KeepVuln = false
		}
		confidenceScores = append(confidenceScores, ruleMatch.Confidence)
	}

	// 3. 上下文分析
	if f.config.EnableContextAnalysis {
		contextAnalysis := f.contextEngine.AnalyzeContext(vuln)
		result.ContextAnalysis = contextAnalysis

		if contextAnalysis.Likelihood < 0.3 { // 低可能性
			result.KeepVuln = false
		}
		confidenceScores = append(confidenceScores, contextAnalysis.Likelihood)
	}

	// 4. 深度验证（可选）
	if f.config.EnableDeepVerification && result.KeepVuln {
		verification := f.verificationEngine.VerifyVulnerability(ctx, vuln)
		result.Verification = verification

		if !verification.IsVulnerable {
			result.KeepVuln = false
		}
		confidenceScores = append(confidenceScores, verification.Confidence)
	}

	// 计算综合置信度
	if len(confidenceScores) > 0 {
		var sum float64
		for _, score := range confidenceScores {
			sum += score
		}
		result.Confidence = sum / float64(len(confidenceScores))
	}

	return result
}

// initializeComponents 初始化组件
func (f *IntelligentFalsePositiveFilter) initializeComponents() {
	f.mlEngine = NewMachineLearningEngine()
	f.ruleEngine = NewHeuristicRuleEngine()
	f.contextEngine = NewContextAnalysisEngine()
	f.verificationEngine = NewVerificationEngine()
	f.feedbackSystem = NewFeedbackLearningSystem()
	f.patternRecognizer = NewPatternRecognizer()
}

// loadModelsAndRules 加载模型和规则
func (f *IntelligentFalsePositiveFilter) loadModelsAndRules() {
	// 加载预训练模型
	f.loadPretrainedModels()

	// 加载启发式规则
	f.loadHeuristicRules()

	// 初始化上下文分析器
	f.initializeContextAnalyzers()
}

// loadPretrainedModels 加载预训练模型
func (f *IntelligentFalsePositiveFilter) loadPretrainedModels() {
	// 创建基础模型
	models := map[string]*MLModel{
		"web_vuln_classifier": {
			ID:       "web_vuln_classifier",
			Type:     "random_forest",
			Version:  1,
			Accuracy: 0.85,
			Features: []string{
				"response_size", "status_code", "response_time",
				"payload_complexity", "error_indicators", "success_indicators",
				"context_relevance", "service_type", "framework_type",
			},
		},
		"injection_detector": {
			ID:       "injection_detector",
			Type:     "neural_network",
			Version:  1,
			Accuracy: 0.90,
			Features: []string{
				"payload_entropy", "special_chars_ratio", "sql_keywords",
				"script_tags", "command_patterns", "response_changes",
			},
		},
	}

	f.mlEngine.models = models
}

// loadHeuristicRules 加载启发式规则
func (f *IntelligentFalsePositiveFilter) loadHeuristicRules() {
	rules := []*HeuristicRule{
		{
			ID:          "generic_error_page",
			Name:        "通用错误页面误报",
			Description: "过滤通用错误页面导致的误报",
			Category:    "response_analysis",
			Conditions: []*RuleCondition{
				{
					Field:    "response_body",
					Operator: "contains",
					Value:    []string{"404 Not Found", "403 Forbidden", "500 Internal Server Error"},
					Weight:   0.8,
				},
				{
					Field:    "response_size",
					Operator: "range",
					Value:    []int{0, 1024}, // 小于1KB的响应
					Weight:   0.6,
				},
			},
			Action:        "filter",
			Confidence:    0.9,
			Effectiveness: 0.85,
			Enabled:       true,
		},
		{
			ID:          "cdn_protection",
			Name:        "CDN保护页面",
			Description: "过滤CDN保护页面的误报",
			Category:    "response_analysis",
			Conditions: []*RuleCondition{
				{
					Field:    "response_headers",
					Operator: "contains",
					Value:    []string{"cloudflare", "akamai", "fastly"},
					Weight:   0.9,
				},
				{
					Field:    "response_body",
					Operator: "contains",
					Value:    []string{"security check", "ddos protection", "rate limit"},
					Weight:   0.8,
				},
			},
			Action:        "filter",
			Confidence:    0.95,
			Effectiveness: 0.90,
			Enabled:       true,
		},
		{
			ID:          "honeypot_detection",
			Name:        "蜜罐检测",
			Description: "检测并过滤蜜罐系统的误报",
			Category:    "environment_analysis",
			Conditions: []*RuleCondition{
				{
					Field:    "service_banner",
					Operator: "regex",
					Value:    ".*honeypot.*|.*trap.*|.*decoy.*",
					Weight:   0.95,
				},
				{
					Field:    "response_pattern",
					Operator: "equals",
					Value:    "too_perfect", // 响应过于完美，可能是蜜罐
					Weight:   0.7,
				},
			},
			Action:        "flag", // 标记而不是直接过滤
			Confidence:    0.8,
			Effectiveness: 0.75,
			Enabled:       true,
		},
	}

	f.ruleEngine.rules = rules

	// 构建规则索引
	f.ruleEngine.buildRuleIndex()
}

// calculateOverallConfidence 计算总体置信度
func (f *IntelligentFalsePositiveFilter) calculateOverallConfidence(result *FilterResult) float64 {
	if len(result.MLPredictions) == 0 && len(result.RuleMatches) == 0 {
		return 0.5 // 默认置信度
	}

	var confidenceSum float64
	var count int

	// ML预测置信度
	for _, pred := range result.MLPredictions {
		confidenceSum += pred.Confidence
		count++
	}

	// 规则匹配置信度
	for _, match := range result.RuleMatches {
		confidenceSum += match.Confidence
		count++
	}

	// 上下文分析置信度
	for _, analysis := range result.ContextAnalysis {
		confidenceSum += analysis.Likelihood
		count++
	}

	if count == 0 {
		return 0.5
	}

	return confidenceSum / float64(count)
}

// updateStats 更新统计信息
func (f *IntelligentFalsePositiveFilter) updateStats(result *FilterResult) {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	f.stats.TotalProcessed += int64(len(result.OriginalVulns))
	f.stats.FilteredOut += int64(len(result.RemovedVulns))
	f.stats.Verified += int64(len(result.FilteredVulns))

	// 更新平均处理时间
	if f.stats.TotalProcessed > 0 {
		totalTime := time.Duration(f.stats.TotalProcessed-1)*f.stats.AverageProcessingTime + result.ProcessingTime
		f.stats.AverageProcessingTime = totalTime / time.Duration(f.stats.TotalProcessed)
	} else {
		f.stats.AverageProcessingTime = result.ProcessingTime
	}
}

// GetStats 获取过滤器统计信息
func (f *IntelligentFalsePositiveFilter) GetStats() *FilterStats {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	stats := *f.stats
	return &stats
}

// 占位符结构体和函数
type EnvironmentContext struct{}
type ServiceContext struct{}
type ConfigurationContext struct{}
type BehaviorContext struct{}

// 占位符函数，需要具体实现
func NewMachineLearningEngine() *MachineLearningEngine   { return &MachineLearningEngine{} }
func NewHeuristicRuleEngine() *HeuristicRuleEngine       { return &HeuristicRuleEngine{} }
func NewContextAnalysisEngine() *ContextAnalysisEngine   { return &ContextAnalysisEngine{} }
func NewVerificationEngine() *VerificationEngine         { return &VerificationEngine{} }
func NewFeedbackLearningSystem() *FeedbackLearningSystem { return &FeedbackLearningSystem{} }
func NewPatternRecognizer() *PatternRecognizer           { return &PatternRecognizer{} }

func (m *MachineLearningEngine) Predict(vuln *types.Vulnerability) *MLPrediction {
	return &MLPrediction{Prediction: true, Confidence: 0.8}
}
func (r *HeuristicRuleEngine) EvaluateRules(vuln *types.Vulnerability) *RuleMatch {
	return &RuleMatch{Matched: false, Confidence: 0.5}
}
func (c *ContextAnalysisEngine) AnalyzeContext(vuln *types.Vulnerability) *ContextAnalysis {
	return &ContextAnalysis{Likelihood: 0.7}
}
func (v *VerificationEngine) VerifyVulnerability(ctx context.Context, vuln *types.Vulnerability) *VerificationResult {
	return &VerificationResult{IsVulnerable: true, Confidence: 0.8}
}
func (r *HeuristicRuleEngine) buildRuleIndex() {}

type FeedbackLearningSystem struct{}
type PatternRecognizer struct{}

type ModelTrainer struct{}
type ModelPredictor struct{}
type EnvironmentAnalyzer struct{}
type ServiceAnalyzer struct{}
type ConfigurationAnalyzer struct{}

// 注意：PoCVerifier等类型已在poc_manager.go中定义
type RuleEvaluator struct{}

func (f *IntelligentFalsePositiveFilter) initializeContextAnalyzers() {}
