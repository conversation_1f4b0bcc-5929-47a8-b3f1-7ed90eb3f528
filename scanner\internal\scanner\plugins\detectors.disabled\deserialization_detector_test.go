package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestDeserializationDetectorBasicFunctionality 测试反序列化检测器基础功能
func TestDeserializationDetectorBasicFunctionality(t *testing.T) {
	detector := NewDeserializationDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "deserialization-comprehensive", detector.GetID())
	assert.Equal(t, "反序列化漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "critical", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-502")
	assert.Contains(t, detector.GetCVE(), "CVE-2015-8103")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)
	assert.Contains(t, ports, 9000)
	assert.Contains(t, ports, 9090)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestDeserializationDetectorApplicability 测试反序列化检测器适用性
func TestDeserializationDetectorApplicability(t *testing.T) {
	detector := NewDeserializationDetector()

	// 测试有序列化功能的目标
	serializationTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Content-Type": "application/x-java-serialized-object",
			"Server":       "Apache Tomcat/9.0.30",
		},
	}
	assert.True(t, detector.IsApplicable(serializationTarget))

	// 测试有序列化技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/service",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "Java", Version: "11", Confidence: 0.9},
			{Name: "Spring", Version: "5.2.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/upload",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Action: "/upload", Method: "POST", Fields: map[string]string{"data": "text", "object": "hidden"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api?data=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试API相关URL
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/serialize",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试普通Web目标（反序列化是通用漏洞）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestDeserializationDetectorConfiguration 测试反序列化检测器配置
func TestDeserializationDetectorConfiguration(t *testing.T) {
	detector := NewDeserializationDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 20*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.True(t, config.FollowRedirects)
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 30*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 4, updatedConfig.Concurrency)
}

// TestDeserializationDetectorJavaPayloads 测试Java反序列化载荷
func TestDeserializationDetectorJavaPayloads(t *testing.T) {
	detector := NewDeserializationDetector()

	// 检查Java反序列化载荷列表
	assert.NotEmpty(t, detector.javaPayloads)
	assert.Greater(t, len(detector.javaPayloads), 40)

	// 检查Java序列化魔术字节
	assert.Contains(t, detector.javaPayloads, "rO0AB")
	assert.Contains(t, detector.javaPayloads, "aced0005")
	assert.Contains(t, detector.javaPayloads, "rO0ABQ==")
	assert.Contains(t, detector.javaPayloads, "rO0ABXNy")

	// 检查Apache Commons Collections载荷
	assert.Contains(t, detector.javaPayloads, "org.apache.commons.collections")
	assert.Contains(t, detector.javaPayloads, "org.apache.commons.collections.functors.InvokerTransformer")
	assert.Contains(t, detector.javaPayloads, "org.apache.commons.collections.functors.ChainedTransformer")

	// 检查Spring Framework载荷
	assert.Contains(t, detector.javaPayloads, "org.springframework.beans.factory.ObjectFactory")
	assert.Contains(t, detector.javaPayloads, "org.springframework.context.support.ClassPathXmlApplicationContext")

	// 检查JNDI注入载荷
	assert.Contains(t, detector.javaPayloads, "ldap://evil.com/")
	assert.Contains(t, detector.javaPayloads, "rmi://evil.com/")
	assert.Contains(t, detector.javaPayloads, "javax.naming.InitialContext")

	// 检查Fastjson载荷
	assert.Contains(t, detector.javaPayloads, "com.sun.rowset.JdbcRowSetImpl")
	assert.Contains(t, detector.javaPayloads, "com.alibaba.fastjson.JSONObject")

	// 检查Jackson载荷
	assert.Contains(t, detector.javaPayloads, "com.fasterxml.jackson.databind.node.POJONode")

	// 检查Hibernate载荷
	assert.Contains(t, detector.javaPayloads, "org.hibernate.engine.spi.TypedValue")

	// 检查Groovy载荷
	assert.Contains(t, detector.javaPayloads, "org.codehaus.groovy.runtime.ConvertedClosure")

	// 检查ROME载荷
	assert.Contains(t, detector.javaPayloads, "com.sun.syndication.feed.impl.EqualsBean")

	// 检查中文Java载荷
	assert.Contains(t, detector.javaPayloads, "java序列化")
	assert.Contains(t, detector.javaPayloads, "反序列化")
}

// TestDeserializationDetectorDotnetPayloads 测试.NET反序列化载荷
func TestDeserializationDetectorDotnetPayloads(t *testing.T) {
	detector := NewDeserializationDetector()

	// 检查.NET反序列化载荷列表
	assert.NotEmpty(t, detector.dotnetPayloads)
	assert.Greater(t, len(detector.dotnetPayloads), 30)

	// 检查.NET序列化魔术字节
	assert.Contains(t, detector.dotnetPayloads, "AAEAAAD/////AQAAAAAAAAAMAgAAAF")
	assert.Contains(t, detector.dotnetPayloads, "AAEAAAD/////")
	assert.Contains(t, detector.dotnetPayloads, "AAEAAAD")

	// 检查.NET序列化类型
	assert.Contains(t, detector.dotnetPayloads, "System.Runtime.Serialization")
	assert.Contains(t, detector.dotnetPayloads, "System.Runtime.Serialization.Formatters.Binary.BinaryFormatter")

	// 检查.NET反序列化载荷
	assert.Contains(t, detector.dotnetPayloads, "System.Configuration.Install.AssemblyInstaller")
	assert.Contains(t, detector.dotnetPayloads, "System.Windows.Data.ObjectDataProvider")

	// 检查ViewState载荷
	assert.Contains(t, detector.dotnetPayloads, "/wEPDwUKLTI2NjY5")
	assert.Contains(t, detector.dotnetPayloads, "__VIEWSTATE")
	assert.Contains(t, detector.dotnetPayloads, "__EVENTVALIDATION")

	// 检查JSON.NET载荷
	assert.Contains(t, detector.dotnetPayloads, "Newtonsoft.Json.JsonConvert")
	assert.Contains(t, detector.dotnetPayloads, "$type")

	// 检查WCF载荷
	assert.Contains(t, detector.dotnetPayloads, "System.ServiceModel")
	assert.Contains(t, detector.dotnetPayloads, "NetDataContractSerializer")

	// 检查PowerShell载荷
	assert.Contains(t, detector.dotnetPayloads, "System.Management.Automation.PSObject")

	// 检查中文.NET载荷
	assert.Contains(t, detector.dotnetPayloads, "dotnet序列化")
	assert.Contains(t, detector.dotnetPayloads, "二进制格式化")
}

// TestDeserializationDetectorPythonPayloads 测试Python反序列化载荷
func TestDeserializationDetectorPythonPayloads(t *testing.T) {
	detector := NewDeserializationDetector()

	// 检查Python反序列化载荷列表
	assert.NotEmpty(t, detector.pythonPayloads)
	assert.Greater(t, len(detector.pythonPayloads), 30)

	// 检查Python pickle魔术字节
	assert.Contains(t, detector.pythonPayloads, "cposix\nsystem\n")
	assert.Contains(t, detector.pythonPayloads, "c__builtin__\neval\n")
	assert.Contains(t, detector.pythonPayloads, "\\x80\\x03")
	assert.Contains(t, detector.pythonPayloads, "gANjb3M=")

	// 检查Python对象载荷
	assert.Contains(t, detector.pythonPayloads, "__reduce__")
	assert.Contains(t, detector.pythonPayloads, "__reduce_ex__")
	assert.Contains(t, detector.pythonPayloads, "__setstate__")

	// 检查危险模块载荷
	assert.Contains(t, detector.pythonPayloads, "subprocess.call")
	assert.Contains(t, detector.pythonPayloads, "os.system")
	assert.Contains(t, detector.pythonPayloads, "eval")
	assert.Contains(t, detector.pythonPayloads, "__import__")

	// 检查Django载荷
	assert.Contains(t, detector.pythonPayloads, "django.core.management.commands.shell.Command")

	// 检查Flask载荷
	assert.Contains(t, detector.pythonPayloads, "flask.sessions.SecureCookieSessionInterface")

	// 检查Celery载荷
	assert.Contains(t, detector.pythonPayloads, "celery.canvas.Signature")

	// 检查PyYAML载荷
	assert.Contains(t, detector.pythonPayloads, "!!python/object/apply:os.system")

	// 检查中文Python载荷
	assert.Contains(t, detector.pythonPayloads, "python序列化")
	assert.Contains(t, detector.pythonPayloads, "pickle载荷")
}

// TestDeserializationDetectorPHPPayloads 测试PHP反序列化载荷
func TestDeserializationDetectorPHPPayloads(t *testing.T) {
	detector := NewDeserializationDetector()

	// 检查PHP反序列化载荷列表
	assert.NotEmpty(t, detector.phpPayloads)
	assert.Greater(t, len(detector.phpPayloads), 40)

	// 检查PHP序列化基础载荷
	assert.Contains(t, detector.phpPayloads, `O:8:"stdClass":1:{s:4:"test";s:4:"test";}`)
	assert.Contains(t, detector.phpPayloads, `a:1:{s:4:"test";s:4:"test";}`)
	assert.Contains(t, detector.phpPayloads, `s:4:"test";`)

	// 检查PHP对象注入载荷
	assert.Contains(t, detector.phpPayloads, `O:4:"Test":1:{s:4:"code";s:10:"phpinfo();";}`)
	assert.Contains(t, detector.phpPayloads, `O:9:"Exception":1:{s:7:"message";s:10:"phpinfo();";}`)

	// 检查PHP魔术方法载荷
	assert.Contains(t, detector.phpPayloads, `O:4:"Test":1:{s:11:"__destruct";s:10:"phpinfo();";}`)
	assert.Contains(t, detector.phpPayloads, `O:4:"Test":1:{s:8:"__wakeup";s:10:"phpinfo();";}`)

	// 检查Symfony载荷
	assert.Contains(t, detector.phpPayloads, "Symfony\\Component\\Process\\Process")

	// 检查Laravel载荷
	assert.Contains(t, detector.phpPayloads, "Illuminate\\Foundation\\Testing\\PendingCommand")

	// 检查Zend载荷
	assert.Contains(t, detector.phpPayloads, "Zend\\Mail\\Transport\\File")

	// 检查Magento载荷
	assert.Contains(t, detector.phpPayloads, "Mage_Core_Model_Layout")

	// 检查WordPress载荷
	assert.Contains(t, detector.phpPayloads, "WP_Object_Cache")

	// 检查Drupal载荷
	assert.Contains(t, detector.phpPayloads, "Drupal\\Core\\Database\\Query\\Select")

	// 检查CodeIgniter载荷
	assert.Contains(t, detector.phpPayloads, "CI_DB_mysql_driver")

	// 检查CakePHP载荷
	assert.Contains(t, detector.phpPayloads, "Cake\\ORM\\Query")

	// 检查中文PHP载荷
	assert.Contains(t, detector.phpPayloads, "php序列化")
	assert.Contains(t, detector.phpPayloads, "对象注入")
}

// TestDeserializationDetectorNodeJSPayloads 测试Node.js反序列化载荷
func TestDeserializationDetectorNodeJSPayloads(t *testing.T) {
	detector := NewDeserializationDetector()

	// 检查Node.js反序列化载荷列表
	assert.NotEmpty(t, detector.nodeJSPayloads)
	assert.Greater(t, len(detector.nodeJSPayloads), 15)

	// 检查Node.js序列化载荷
	assert.Contains(t, detector.nodeJSPayloads, `{"rce":"_$$ND_FUNC$$_function(){require('child_process').exec('whoami');}()"}`)
	assert.Contains(t, detector.nodeJSPayloads, `{"__proto__":{"isAdmin":true}}`)

	// 检查Node-serialize载荷
	assert.Contains(t, detector.nodeJSPayloads, `_$$ND_FUNC$$_function(){require('child_process').exec('whoami');}()`)

	// 检查Funcster载荷
	assert.Contains(t, detector.nodeJSPayloads, `{"__js_function":"function(){require('child_process').exec('whoami');}"}`)

	// 检查V8序列化载荷
	assert.Contains(t, detector.nodeJSPayloads, `ff0d6f2203666f6f`)

	// 检查Express载荷
	assert.Contains(t, detector.nodeJSPayloads, "express.static")

	// 检查Socket.io载荷
	assert.Contains(t, detector.nodeJSPayloads, "socket.io.parser")

	// 检查中文Node.js载荷
	assert.Contains(t, detector.nodeJSPayloads, "nodejs序列化")
	assert.Contains(t, detector.nodeJSPayloads, "原型污染")
}

// TestDeserializationDetectorRubyPayloads 测试Ruby反序列化载荷
func TestDeserializationDetectorRubyPayloads(t *testing.T) {
	detector := NewDeserializationDetector()

	// 检查Ruby反序列化载荷列表
	assert.NotEmpty(t, detector.rubyPayloads)
	assert.Greater(t, len(detector.rubyPayloads), 20)

	// 检查Ruby Marshal载荷
	assert.Contains(t, detector.rubyPayloads, "\\x04\\x08")
	assert.Contains(t, detector.rubyPayloads, "BAh7")

	// 检查Ruby对象载荷
	assert.Contains(t, detector.rubyPayloads, "Marshal.load")
	assert.Contains(t, detector.rubyPayloads, "YAML.load")

	// 检查Ruby危险类载荷
	assert.Contains(t, detector.rubyPayloads, "Kernel.system")
	assert.Contains(t, detector.rubyPayloads, "IO.popen")

	// 检查Rails载荷
	assert.Contains(t, detector.rubyPayloads, "ActiveRecord::Base")

	// 检查Sinatra载荷
	assert.Contains(t, detector.rubyPayloads, "Sinatra::Application")

	// 检查中文Ruby载荷
	assert.Contains(t, detector.rubyPayloads, "ruby序列化")
	assert.Contains(t, detector.rubyPayloads, "marshal载荷")
}

// TestDeserializationDetectorTestParameters 测试参数
func TestDeserializationDetectorTestParameters(t *testing.T) {
	detector := NewDeserializationDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.Greater(t, len(detector.testParameters), 80)

	// 检查序列化相关参数
	assert.Contains(t, detector.testParameters, "data")
	assert.Contains(t, detector.testParameters, "object")
	assert.Contains(t, detector.testParameters, "payload")
	assert.Contains(t, detector.testParameters, "serialize")
	assert.Contains(t, detector.testParameters, "deserialize")
	assert.Contains(t, detector.testParameters, "marshal")
	assert.Contains(t, detector.testParameters, "pickle")

	// 检查格式相关参数
	assert.Contains(t, detector.testParameters, "json")
	assert.Contains(t, detector.testParameters, "xml")
	assert.Contains(t, detector.testParameters, "binary")
	assert.Contains(t, detector.testParameters, "base64")

	// 检查对象相关参数
	assert.Contains(t, detector.testParameters, "obj")
	assert.Contains(t, detector.testParameters, "entity")
	assert.Contains(t, detector.testParameters, "model")
	assert.Contains(t, detector.testParameters, "bean")

	// 检查API相关参数
	assert.Contains(t, detector.testParameters, "api")
	assert.Contains(t, detector.testParameters, "service")
	assert.Contains(t, detector.testParameters, "method")

	// 检查文件相关参数
	assert.Contains(t, detector.testParameters, "file")
	assert.Contains(t, detector.testParameters, "upload")
	assert.Contains(t, detector.testParameters, "stream")

	// 检查配置相关参数
	assert.Contains(t, detector.testParameters, "config")
	assert.Contains(t, detector.testParameters, "settings")
	assert.Contains(t, detector.testParameters, "options")

	// 检查会话相关参数
	assert.Contains(t, detector.testParameters, "session")
	assert.Contains(t, detector.testParameters, "token")
	assert.Contains(t, detector.testParameters, "cookie")

	// 检查缓存相关参数
	assert.Contains(t, detector.testParameters, "cache")
	assert.Contains(t, detector.testParameters, "store")
	assert.Contains(t, detector.testParameters, "storage")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "数据")
	assert.Contains(t, detector.testParameters, "对象")
	assert.Contains(t, detector.testParameters, "序列化")
	assert.Contains(t, detector.testParameters, "反序列化")
}

// TestDeserializationDetectorPatterns 测试模式
func TestDeserializationDetectorPatterns(t *testing.T) {
	detector := NewDeserializationDetector()

	// 检查Java模式
	assert.NotEmpty(t, detector.javaPatterns)
	assert.Greater(t, len(detector.javaPatterns), 10)

	// 检查.NET模式
	assert.NotEmpty(t, detector.dotnetPatterns)
	assert.Greater(t, len(detector.dotnetPatterns), 10)

	// 检查Python模式
	assert.NotEmpty(t, detector.pythonPatterns)
	assert.Greater(t, len(detector.pythonPatterns), 10)

	// 检查PHP模式
	assert.NotEmpty(t, detector.phpPatterns)
	assert.Greater(t, len(detector.phpPatterns), 10)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.Greater(t, len(detector.errorPatterns), 5)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.Greater(t, len(detector.responsePatterns), 5)
}

// TestDeserializationDetectorSerializationFeatures 测试序列化功能检查
func TestDeserializationDetectorSerializationFeatures(t *testing.T) {
	detector := NewDeserializationDetector()

	// 测试有序列化头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Content-Type": "application/x-java-serialized-object",
			"Server":       "Apache Tomcat/9.0.30",
		},
	}
	assert.True(t, detector.hasSerializationFeatures(headerTarget))

	// 测试有序列化技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "Java", Version: "11", Confidence: 0.9},
			{Name: "Spring", Version: "5.2.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasSerializationFeatures(techTarget))

	// 测试有序列化链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api", Text: "API Service"},
		},
	}
	assert.True(t, detector.hasSerializationFeatures(linkTarget))

	// 测试有序列化表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"data": "text", "object": "hidden"}},
		},
	}
	assert.True(t, detector.hasSerializationFeatures(formTarget))

	// 测试无序列化功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasSerializationFeatures(simpleTarget))
}

// TestDeserializationDetectorRiskScore 测试风险评分计算
func TestDeserializationDetectorRiskScore(t *testing.T) {
	detector := NewDeserializationDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 8.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 2.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestDeserializationDetectorLifecycle 测试检测器生命周期
func TestDeserializationDetectorLifecycle(t *testing.T) {
	detector := NewDeserializationDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
