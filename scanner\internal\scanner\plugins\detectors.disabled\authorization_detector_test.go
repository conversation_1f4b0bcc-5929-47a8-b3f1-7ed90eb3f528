package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestAuthorizationDetectorBasicFunctionality 测试授权检测器基础功能
func TestAuthorizationDetectorBasicFunctionality(t *testing.T) {
	detector := NewAuthorizationDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "authorization-comprehensive", detector.GetID())
	assert.Equal(t, "授权漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-285")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestAuthorizationDetectorApplicability 测试授权检测器适用性
func TestAuthorizationDetectorApplicability(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 测试有授权头的目标
	authHeaderTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Authorization": "Bearer token123",
		},
	}
	assert.True(t, detector.IsApplicable(authHeaderTarget))

	// 测试有认证Cookie的目标
	authCookieTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Cookies: []plugins.CookieInfo{
			{
				Name:  "auth_token",
				Value: "abc123",
			},
		},
	}
	assert.True(t, detector.IsApplicable(authCookieTarget))

	// 测试有认证表单的目标
	authFormTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/login",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/login",
				Method: "POST",
				Fields: map[string]string{
					"username": "text",
					"password": "password",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(authFormTarget))

	// 测试管理相关URL
	adminTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/admin",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(adminTarget))

	// 测试API相关URL
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/users",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（无授权功能）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about",
		Protocol: "http",
		Port:     80,
	}
	assert.False(t, detector.IsApplicable(simpleTarget))
}

// TestAuthorizationDetectorConfiguration 测试授权检测器配置
func TestAuthorizationDetectorConfiguration(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 15*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 2, config.Concurrency)
	assert.True(t, config.FollowRedirects)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     3,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 3, updatedConfig.Concurrency)
}

// TestAuthorizationDetectorAdminPaths 测试授权检测器管理员路径
func TestAuthorizationDetectorAdminPaths(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 检查管理员路径列表
	assert.NotEmpty(t, detector.adminPaths)
	assert.Greater(t, len(detector.adminPaths), 40)

	// 检查基础管理路径
	assert.Contains(t, detector.adminPaths, "/admin")
	assert.Contains(t, detector.adminPaths, "/administrator")
	assert.Contains(t, detector.adminPaths, "/management")
	assert.Contains(t, detector.adminPaths, "/dashboard")
	assert.Contains(t, detector.adminPaths, "/control")

	// 检查API管理路径
	assert.Contains(t, detector.adminPaths, "/api/admin")
	assert.Contains(t, detector.adminPaths, "/admin/api")

	// 检查特定应用管理路径
	assert.Contains(t, detector.adminPaths, "/wp-admin")
	assert.Contains(t, detector.adminPaths, "/phpmyadmin")
}

// TestAuthorizationDetectorUserPaths 测试授权检测器用户路径
func TestAuthorizationDetectorUserPaths(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 检查用户路径列表
	assert.NotEmpty(t, detector.userPaths)
	assert.Greater(t, len(detector.userPaths), 20)

	// 检查基础用户路径
	assert.Contains(t, detector.userPaths, "/user/{id}")
	assert.Contains(t, detector.userPaths, "/users/{id}")
	assert.Contains(t, detector.userPaths, "/profile/{id}")
	assert.Contains(t, detector.userPaths, "/account/{id}")

	// 检查API用户路径
	assert.Contains(t, detector.userPaths, "/api/user/{id}")
	assert.Contains(t, detector.userPaths, "/api/users/{id}")
}

// TestAuthorizationDetectorAPIEndpoints 测试授权检测器API端点
func TestAuthorizationDetectorAPIEndpoints(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 检查API端点列表
	assert.NotEmpty(t, detector.apiEndpoints)
	assert.Greater(t, len(detector.apiEndpoints), 25)

	// 检查基础API端点
	assert.Contains(t, detector.apiEndpoints, "/api")
	assert.Contains(t, detector.apiEndpoints, "/api/v1")
	assert.Contains(t, detector.apiEndpoints, "/api/v2")

	// 检查用户API
	assert.Contains(t, detector.apiEndpoints, "/api/users")
	assert.Contains(t, detector.apiEndpoints, "/api/user")

	// 检查管理API
	assert.Contains(t, detector.apiEndpoints, "/api/admin")
	assert.Contains(t, detector.apiEndpoints, "/api/management")

	// 检查认证API
	assert.Contains(t, detector.apiEndpoints, "/api/auth")
	assert.Contains(t, detector.apiEndpoints, "/api/login")
}

// TestAuthorizationDetectorRoleNames 测试授权检测器角色名称
func TestAuthorizationDetectorRoleNames(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 检查角色名称列表
	assert.NotEmpty(t, detector.roleNames)
	assert.Greater(t, len(detector.roleNames), 30)

	// 检查基础角色
	assert.Contains(t, detector.roleNames, "admin")
	assert.Contains(t, detector.roleNames, "administrator")
	assert.Contains(t, detector.roleNames, "root")
	assert.Contains(t, detector.roleNames, "user")
	assert.Contains(t, detector.roleNames, "guest")

	// 检查业务角色
	assert.Contains(t, detector.roleNames, "manager")
	assert.Contains(t, detector.roleNames, "editor")
	assert.Contains(t, detector.roleNames, "member")
	assert.Contains(t, detector.roleNames, "customer")
}

// TestAuthorizationDetectorPermissionNames 测试授权检测器权限名称
func TestAuthorizationDetectorPermissionNames(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 检查权限名称列表
	assert.NotEmpty(t, detector.permissionNames)
	assert.Greater(t, len(detector.permissionNames), 40)

	// 检查基础权限
	assert.Contains(t, detector.permissionNames, "read")
	assert.Contains(t, detector.permissionNames, "write")
	assert.Contains(t, detector.permissionNames, "execute")
	assert.Contains(t, detector.permissionNames, "delete")
	assert.Contains(t, detector.permissionNames, "create")

	// 检查用户权限
	assert.Contains(t, detector.permissionNames, "user.read")
	assert.Contains(t, detector.permissionNames, "user.write")
	assert.Contains(t, detector.permissionNames, "user.manage")

	// 检查系统权限
	assert.Contains(t, detector.permissionNames, "system.admin")
	assert.Contains(t, detector.permissionNames, "config.read")
}

// TestAuthorizationDetectorBypassPayloads 测试授权检测器绕过载荷
func TestAuthorizationDetectorBypassPayloads(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 检查绕过载荷列表
	assert.NotEmpty(t, detector.bypassPayloads)
	assert.Greater(t, len(detector.bypassPayloads), 50)

	// 检查HTTP方法绕过
	assert.Contains(t, detector.bypassPayloads, "OPTIONS")
	assert.Contains(t, detector.bypassPayloads, "HEAD")
	assert.Contains(t, detector.bypassPayloads, "TRACE")

	// 检查路径绕过
	assert.Contains(t, detector.bypassPayloads, "../admin")
	assert.Contains(t, detector.bypassPayloads, "/./admin")
	assert.Contains(t, detector.bypassPayloads, "//admin")

	// 检查编码绕过
	assert.Contains(t, detector.bypassPayloads, "%2e%2e%2fadmin")
	assert.Contains(t, detector.bypassPayloads, "%252e%252e%252fadmin")

	// 检查参数绕过
	assert.Contains(t, detector.bypassPayloads, "/admin?bypass=true")
	assert.Contains(t, detector.bypassPayloads, "/admin?role=admin")

	// 检查角色绕过
	assert.Contains(t, detector.bypassPayloads, "role=admin")
	assert.Contains(t, detector.bypassPayloads, "user_role=admin")
}

// TestAuthorizationDetectorPatterns 测试授权检测器模式
func TestAuthorizationDetectorPatterns(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 检查认证模式
	assert.NotEmpty(t, detector.authPatterns)
	assert.GreaterOrEqual(t, len(detector.authPatterns), 15)

	// 检查访问模式
	assert.NotEmpty(t, detector.accessPatterns)
	assert.Greater(t, len(detector.accessPatterns), 10)

	// 检查权限模式
	assert.NotEmpty(t, detector.privilegePatterns)
	assert.Greater(t, len(detector.privilegePatterns), 12)
}

// TestAuthorizationDetectorAuthorizationFeatures 测试授权功能检查
func TestAuthorizationDetectorAuthorizationFeatures(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 测试有授权头的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Authorization": "Bearer token123",
		},
	}
	assert.True(t, detector.hasAuthorizationFeatures(headerTarget))

	// 测试有认证Cookie的目标
	cookieTarget := &plugins.ScanTarget{
		Cookies: []plugins.CookieInfo{
			{
				Name:  "auth_token",
				Value: "abc123",
			},
		},
	}
	assert.True(t, detector.hasAuthorizationFeatures(cookieTarget))

	// 测试有认证表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{
				Fields: map[string]string{
					"username": "text",
					"password": "password",
				},
			},
		},
	}
	assert.True(t, detector.hasAuthorizationFeatures(formTarget))

	// 测试无授权功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers: map[string]string{},
		Cookies: []plugins.CookieInfo{},
		Forms:   []plugins.FormInfo{},
	}
	assert.False(t, detector.hasAuthorizationFeatures(simpleTarget))
}

// TestAuthorizationDetectorRiskScore 测试风险评分计算
func TestAuthorizationDetectorRiskScore(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 4.5)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestAuthorizationDetectorLifecycle 测试检测器生命周期
func TestAuthorizationDetectorLifecycle(t *testing.T) {
	detector := NewAuthorizationDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
