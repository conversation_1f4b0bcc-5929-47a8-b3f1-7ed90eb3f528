package detectors

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// IsApplicable 检查是否适用于目标
func (d *GraphQLInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	if !d.IsEnabled() {
		return false
	}

	// 检查目标URL是否为HTTP/HTTPS
	if !strings.HasPrefix(target.URL, "http://") && !strings.HasPrefix(target.URL, "https://") {
		return false
	}

	// 检查是否可能是GraphQL端点
	return d.isLikelyGraphQLEndpoint(target.URL)
}

// isLikelyGraphQLEndpoint 检查是否可能是GraphQL端点
func (d *GraphQLInjectionDetector) isLikelyGraphQLEndpoint(targetURL string) bool {
	// 常见的GraphQL端点路径
	graphqlPaths := []string{
		"/graphql",
		"/graphiql",
		"/api/graphql",
		"/v1/graphql",
		"/v2/graphql",
		"/query",
		"/api/query",
		"/gql",
		"/api/gql",
	}

	urlLower := strings.ToLower(targetURL)
	for _, path := range graphqlPaths {
		if strings.Contains(urlLower, path) {
			return true
		}
	}

	// 检查URL参数中是否包含GraphQL相关关键词
	if strings.Contains(urlLower, "query=") || strings.Contains(urlLower, "mutation=") {
		return true
	}

	return false
}

// Detect 执行检测
func (d *GraphQLInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableResponse string

	// 1. GraphQL端点发现
	endpointEvidence, endpointConfidence, endpoint := d.discoverGraphQLEndpoint(ctx, target)
	evidence = append(evidence, endpointEvidence...)
	if endpointConfidence > maxConfidence {
		maxConfidence = endpointConfidence
	}

	// 如果没有发现GraphQL端点，返回
	if endpoint == "" {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
			Evidence:        evidence,
		}, nil
	}

	// 2. 内省查询测试
	introspectionEvidence, introspectionConfidence, introspectionPayload, introspectionResponse := d.testIntrospection(ctx, endpoint)
	evidence = append(evidence, introspectionEvidence...)
	if introspectionConfidence > maxConfidence {
		maxConfidence = introspectionConfidence
		vulnerablePayload = introspectionPayload
		vulnerableResponse = introspectionResponse
	}

	// 3. 注入漏洞测试
	injectionEvidence, injectionConfidence, injectionPayload, injectionResponse := d.testInjectionVulnerabilities(ctx, endpoint)
	evidence = append(evidence, injectionEvidence...)
	if injectionConfidence > maxConfidence {
		maxConfidence = injectionConfidence
		vulnerablePayload = injectionPayload
		vulnerableResponse = injectionResponse
	}

	// 4. 深度查询攻击测试
	depthEvidence, depthConfidence, depthPayload, depthResponse := d.testDepthAttacks(ctx, endpoint)
	evidence = append(evidence, depthEvidence...)
	if depthConfidence > maxConfidence {
		maxConfidence = depthConfidence
		vulnerablePayload = depthPayload
		vulnerableResponse = depthResponse
	}

	// 构建检测结果
	isVulnerable := maxConfidence >= 0.7
	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "GraphQL注入漏洞",
		Description:       d.generateDescription(maxConfidence, vulnerablePayload),
		Remediation:       d.generateSolution(),
		References:        d.generateReferences(),
		Evidence:          evidence,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Payload:           vulnerablePayload,
		Response:          vulnerableResponse,
		Tags:              []string{"graphql", "injection", "api", "web"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
		Metadata: map[string]interface{}{
			"endpoint":          endpoint,
			"detection_methods": []string{"endpoint_discovery", "introspection", "injection", "depth_attack"},
		},
	}

	return result, nil
}

// discoverGraphQLEndpoint 发现GraphQL端点
func (d *GraphQLInjectionDetector) discoverGraphQLEndpoint(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string) {
	var evidence []plugins.Evidence
	confidence := 0.0

	// 尝试常见的GraphQL端点
	endpoints := d.generatePossibleEndpoints(target.URL)

	for _, endpoint := range endpoints {
		// 发送基础GraphQL查询
		payload := GraphQLPayload{
			Query:   `query { __typename }`,
			Type:    "discovery",
			Purpose: "GraphQL端点发现",
		}

		resp, err := d.sendGraphQLRequest(ctx, endpoint, payload)
		if err != nil {
			continue
		}

		// 检查响应是否表明这是GraphQL端点
		if d.isGraphQLResponse(resp) {
			confidence = 0.9
			evidence = append(evidence, plugins.Evidence{
				Type:        "endpoint",
				Description: fmt.Sprintf("发现GraphQL端点: %s", endpoint),
				Content:     d.extractResponseEvidence(resp),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
			return evidence, confidence, endpoint
		}
	}

	return evidence, confidence, ""
}

// generatePossibleEndpoints 生成可能的GraphQL端点
func (d *GraphQLInjectionDetector) generatePossibleEndpoints(baseURL string) []string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return []string{baseURL}
	}

	baseSchemeHost := fmt.Sprintf("%s://%s", parsedURL.Scheme, parsedURL.Host)

	endpoints := []string{
		baseURL, // 原始URL
		baseSchemeHost + "/graphql",
		baseSchemeHost + "/graphiql",
		baseSchemeHost + "/api/graphql",
		baseSchemeHost + "/v1/graphql",
		baseSchemeHost + "/v2/graphql",
		baseSchemeHost + "/query",
		baseSchemeHost + "/api/query",
		baseSchemeHost + "/gql",
		baseSchemeHost + "/api/gql",
	}

	// 如果原始URL有路径，也尝试在该路径下添加GraphQL端点
	if parsedURL.Path != "" && parsedURL.Path != "/" {
		basePath := strings.TrimSuffix(parsedURL.Path, "/")
		endpoints = append(endpoints,
			baseSchemeHost+basePath+"/graphql",
			baseSchemeHost+basePath+"/query",
			baseSchemeHost+basePath+"/gql",
		)
	}

	return endpoints
}

// sendGraphQLRequest 发送GraphQL请求
func (d *GraphQLInjectionDetector) sendGraphQLRequest(ctx context.Context, endpoint string, payload GraphQLPayload) (string, error) {
	// 构建请求体
	requestBody, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", endpoint, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "Security Scanner GraphQL Detector/1.0")

	// 发送请求
	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// isGraphQLResponse 检查响应是否表明这是GraphQL端点
func (d *GraphQLInjectionDetector) isGraphQLResponse(response string) bool {
	// 检查JSON格式的GraphQL响应
	var jsonResp map[string]interface{}
	if err := json.Unmarshal([]byte(response), &jsonResp); err == nil {
		// 检查是否包含GraphQL特有的字段
		if _, hasData := jsonResp["data"]; hasData {
			return true
		}
		if _, hasErrors := jsonResp["errors"]; hasErrors {
			return true
		}
		if _, hasExtensions := jsonResp["extensions"]; hasExtensions {
			return true
		}
	}

	// 检查响应中是否包含GraphQL关键词
	responseLower := strings.ToLower(response)
	graphqlKeywords := []string{
		"graphql",
		"__typename",
		"__schema",
		"__type",
		"query",
		"mutation",
		"subscription",
		"introspection",
	}

	for _, keyword := range graphqlKeywords {
		if strings.Contains(responseLower, keyword) {
			return true
		}
	}

	return false
}

// testIntrospection 测试内省查询
func (d *GraphQLInjectionDetector) testIntrospection(ctx context.Context, endpoint string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	for _, query := range d.introspectionTests {
		payload := GraphQLPayload{
			Query:   query,
			Type:    "introspection",
			Purpose: "GraphQL内省查询测试",
		}

		resp, err := d.sendGraphQLRequest(ctx, endpoint, payload)
		if err != nil {
			continue
		}

		// 检查是否成功获取了schema信息
		if d.isSuccessfulIntrospection(resp) {
			confidence = 0.8
			vulnerablePayload = query
			vulnerableResponse = resp
			evidence = append(evidence, plugins.Evidence{
				Type:        "introspection",
				Description: "GraphQL内省查询成功，可能泄露API结构信息",
				Content:     d.extractIntrospectionEvidence(resp),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
			break
		}
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// isSuccessfulIntrospection 检查是否成功进行了内省查询
func (d *GraphQLInjectionDetector) isSuccessfulIntrospection(response string) bool {
	var jsonResp map[string]interface{}
	if err := json.Unmarshal([]byte(response), &jsonResp); err != nil {
		return false
	}

	// 检查是否包含schema信息
	if data, ok := jsonResp["data"].(map[string]interface{}); ok {
		if schema, hasSchema := data["__schema"]; hasSchema && schema != nil {
			return true
		}
		if typeInfo, hasType := data["__type"]; hasType && typeInfo != nil {
			return true
		}
	}

	return false
}

// extractIntrospectionEvidence 提取内省查询证据
func (d *GraphQLInjectionDetector) extractIntrospectionEvidence(response string) string {
	lines := strings.Split(response, "\n")
	var evidenceLines []string

	for i, line := range lines {
		if i >= 10 { // 限制证据长度
			break
		}
		if strings.TrimSpace(line) != "" {
			evidenceLines = append(evidenceLines, strings.TrimSpace(line))
		}
	}

	return strings.Join(evidenceLines, "\n")
}

// extractResponseEvidence 提取响应证据
func (d *GraphQLInjectionDetector) extractResponseEvidence(response string) string {
	if len(response) > 500 {
		return response[:500] + "... [截断]"
	}
	return response
}

// generateVulnID 生成漏洞ID
func (d *GraphQLInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("graphql-injection-%s-%d", d.id, time.Now().Unix())
}

// generateDescription 生成漏洞描述
func (d *GraphQLInjectionDetector) generateDescription(confidence float64, payload string) string {
	if confidence >= 0.9 {
		return fmt.Sprintf("发现高风险GraphQL注入漏洞。检测载荷: %s", payload)
	} else if confidence >= 0.7 {
		return fmt.Sprintf("发现可能的GraphQL注入漏洞。检测载荷: %s", payload)
	}
	return "GraphQL端点存在潜在安全风险"
}

// generateSolution 生成解决方案
func (d *GraphQLInjectionDetector) generateSolution() string {
	return `1. 实施严格的输入验证和参数化查询
2. 禁用生产环境中的GraphQL内省功能
3. 实施查询深度限制和复杂度分析
4. 使用白名单机制限制允许的查询
5. 实施适当的身份验证和授权机制
6. 监控和记录所有GraphQL查询`
}

// generateReferences 生成参考资料
func (d *GraphQLInjectionDetector) generateReferences() []string {
	return []string{
		"https://owasp.org/www-project-web-security-testing-guide/latest/4-Web_Application_Security_Testing/07-Input_Validation_Testing/05.7-Testing_for_GraphQL",
		"https://cheatsheetseries.owasp.org/cheatsheets/GraphQL_Cheat_Sheet.html",
		"https://github.com/swisskyrepo/PayloadsAllTheThings/tree/master/GraphQL%20Injection",
		"https://portswigger.net/web-security/graphql",
	}
}

// calculateRiskScore 计算风险评分
func (d *GraphQLInjectionDetector) calculateRiskScore(confidence float64) float64 {
	baseScore := 7.0 // GraphQL注入基础风险评分
	return baseScore * confidence
}

// testInjectionVulnerabilities 测试注入漏洞
func (d *GraphQLInjectionDetector) testInjectionVulnerabilities(ctx context.Context, endpoint string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	// 测试查询注入
	for _, payload := range d.queryPayloads {
		if payload.Type != "injection" {
			continue
		}

		resp, err := d.sendGraphQLRequest(ctx, endpoint, payload)
		if err != nil {
			continue
		}

		// 检查是否触发了错误或异常行为
		injectionConfidence := d.analyzeInjectionResponse(resp, payload.Query)
		if injectionConfidence > confidence {
			confidence = injectionConfidence
			vulnerablePayload = payload.Query
			vulnerableResponse = resp
		}

		if injectionConfidence > 0 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "injection",
				Description: fmt.Sprintf("GraphQL注入测试: %s", payload.Purpose),
				Content:     d.extractResponseEvidence(resp),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}
	}

	// 测试变异注入
	for _, payload := range d.mutationPayloads {
		if payload.Type != "mutation_injection" {
			continue
		}

		resp, err := d.sendGraphQLRequest(ctx, endpoint, payload)
		if err != nil {
			continue
		}

		injectionConfidence := d.analyzeInjectionResponse(resp, payload.Query)
		if injectionConfidence > confidence {
			confidence = injectionConfidence
			vulnerablePayload = payload.Query
			vulnerableResponse = resp
		}

		if injectionConfidence > 0 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "mutation_injection",
				Description: fmt.Sprintf("GraphQL变异注入测试: %s", payload.Purpose),
				Content:     d.extractResponseEvidence(resp),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// analyzeInjectionResponse 分析注入响应
func (d *GraphQLInjectionDetector) analyzeInjectionResponse(response, payload string) float64 {
	confidence := 0.0

	// 检查是否匹配错误模式
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
		}
	}

	// 检查是否包含SQL错误信息
	sqlErrors := []string{
		"sql syntax",
		"mysql error",
		"postgresql error",
		"sqlite error",
		"ora-",
		"sqlstate",
	}

	responseLower := strings.ToLower(response)
	for _, sqlError := range sqlErrors {
		if strings.Contains(responseLower, sqlError) {
			confidence += 0.4
		}
	}

	// 检查是否包含NoSQL错误信息
	nosqlErrors := []string{
		"mongodb error",
		"redis error",
		"cassandra error",
		"dynamodb error",
	}

	for _, nosqlError := range nosqlErrors {
		if strings.Contains(responseLower, nosqlError) {
			confidence += 0.4
		}
	}

	// 检查是否包含应用程序错误
	appErrors := []string{
		"internal server error",
		"stack trace",
		"exception",
		"null pointer",
		"array index",
	}

	for _, appError := range appErrors {
		if strings.Contains(responseLower, appError) {
			confidence += 0.2
		}
	}

	// 限制最大置信度
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// testDepthAttacks 测试深度查询攻击
func (d *GraphQLInjectionDetector) testDepthAttacks(ctx context.Context, endpoint string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	for _, payload := range d.queryPayloads {
		if payload.Type != "depth_attack" && payload.Type != "batch_attack" {
			continue
		}

		startTime := time.Now()
		resp, err := d.sendGraphQLRequest(ctx, endpoint, payload)
		duration := time.Since(startTime)

		if err != nil {
			// 如果请求超时或失败，可能表明服务器无法处理复杂查询
			if strings.Contains(err.Error(), "timeout") || strings.Contains(err.Error(), "deadline") {
				confidence = 0.6
				vulnerablePayload = payload.Query
				vulnerableResponse = fmt.Sprintf("请求超时: %v", err)
				evidence = append(evidence, plugins.Evidence{
					Type:        "depth_attack",
					Description: fmt.Sprintf("深度查询攻击导致超时: %s", payload.Purpose),
					Content:     fmt.Sprintf("查询超时，耗时: %v", duration),
					Location:    endpoint,
					Timestamp:   time.Now(),
				})
			}
			continue
		}

		// 检查响应时间是否异常长
		if duration > 10*time.Second {
			confidence = 0.5
			vulnerablePayload = payload.Query
			vulnerableResponse = resp
			evidence = append(evidence, plugins.Evidence{
				Type:        "depth_attack",
				Description: fmt.Sprintf("深度查询攻击导致响应延迟: %s", payload.Purpose),
				Content:     fmt.Sprintf("响应时间: %v", duration),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}

		// 检查是否返回了错误信息
		if d.containsDepthLimitError(resp) {
			confidence = 0.4
			evidence = append(evidence, plugins.Evidence{
				Type:        "depth_limit",
				Description: "GraphQL查询深度限制检测",
				Content:     d.extractResponseEvidence(resp),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// containsDepthLimitError 检查是否包含深度限制错误
func (d *GraphQLInjectionDetector) containsDepthLimitError(response string) bool {
	depthErrors := []string{
		"query depth",
		"too deep",
		"depth limit",
		"complexity limit",
		"query complexity",
		"max depth",
		"nested too deeply",
	}

	responseLower := strings.ToLower(response)
	for _, depthError := range depthErrors {
		if strings.Contains(responseLower, depthError) {
			return true
		}
	}

	return false
}

// Verify 验证检测结果
func (d *GraphQLInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的载荷进行验证
	verificationPayloads := []GraphQLPayload{
		{
			Query:   `query { __typename }`,
			Type:    "verification",
			Purpose: "验证GraphQL端点可访问性",
		},
		{
			Query:   `query { __schema { queryType { name } } }`,
			Type:    "verification_introspection",
			Purpose: "验证内省查询可用性",
		},
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	// 从元数据中获取端点信息
	endpoint := target.URL
	if result.Metadata != nil {
		if ep, ok := result.Metadata["endpoint"].(string); ok && ep != "" {
			endpoint = ep
		}
	}

	for _, payload := range verificationPayloads {
		resp, err := d.sendGraphQLRequest(ctx, endpoint, payload)
		if err != nil {
			continue
		}

		// 分析验证响应
		if d.isGraphQLResponse(resp) {
			verificationConfidence += 0.3
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证载荷 %s 确认了GraphQL端点", payload.Purpose),
				Content:     d.extractResponseEvidence(resp),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}

		if payload.Type == "verification_introspection" && d.isSuccessfulIntrospection(resp) {
			verificationConfidence += 0.4
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification_introspection",
				Description: "验证内省查询成功",
				Content:     d.extractIntrospectionEvidence(resp),
				Location:    endpoint,
				Timestamp:   time.Now(),
			})
		}
	}

	// 如果原始检测使用了注入载荷，尝试重新验证
	if result.Payload != "" {
		originalPayload := GraphQLPayload{
			Query:   result.Payload,
			Type:    "verification_original",
			Purpose: "重新验证原始漏洞载荷",
		}

		resp, err := d.sendGraphQLRequest(ctx, endpoint, originalPayload)
		if err == nil {
			injectionConfidence := d.analyzeInjectionResponse(resp, result.Payload)
			if injectionConfidence > 0.5 {
				verificationConfidence += 0.3
				evidence = append(evidence, plugins.Evidence{
					Type:        "verification_injection",
					Description: "重新验证注入载荷成功",
					Content:     d.extractResponseEvidence(resp),
					Location:    endpoint,
					Timestamp:   time.Now(),
				})
			}
		}
	}

	isVerified := verificationConfidence >= 0.6
	method := "graphql-endpoint-verification"
	if verificationConfidence >= 0.7 {
		method = "graphql-injection-verification"
	}

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     method,
		Evidence:   evidence,
		Notes:      d.generateVerificationNotes(verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// generateVerificationNotes 生成验证说明
func (d *GraphQLInjectionDetector) generateVerificationNotes(confidence float64) string {
	if confidence >= 0.8 {
		return "高置信度验证：GraphQL注入漏洞已确认"
	} else if confidence >= 0.6 {
		return "中等置信度验证：GraphQL端点存在安全风险"
	} else if confidence >= 0.3 {
		return "低置信度验证：发现GraphQL端点但无明显注入风险"
	}
	return "验证失败：无法确认GraphQL相关漏洞"
}
