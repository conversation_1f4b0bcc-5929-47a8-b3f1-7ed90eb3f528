package detectors

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// EncryptionDetector 加密检测器
// 支持弱加密算法、SSL/TLS配置错误、证书问题、密钥管理缺陷、JWT安全等多种加密检测
type EncryptionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	weakTLSVersions     []string         // 弱TLS版本
	weakCipherSuites    []string         // 弱加密套件
	weakHashAlgorithms  []string         // 弱哈希算法
	weakKeyExchanges    []string         // 弱密钥交换
	jwtWeakSecrets      []string         // JWT弱密钥
	jwtAlgorithms       []string         // JWT算法
	encryptionPatterns  []*regexp.Regexp // 加密模式
	certificatePatterns []*regexp.Regexp // 证书模式
	keyPatterns         []*regexp.Regexp // 密钥模式
	httpClient          *http.Client
	tlsClient           *tls.Config
}

// NewEncryptionDetector 创建加密检测器
func NewEncryptionDetector() *EncryptionDetector {
	detector := &EncryptionDetector{
		id:          "encryption-comprehensive",
		name:        "加密漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // 加密是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-326", "CWE-327", "CWE-328", "CWE-329", "CWE-330"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测加密漏洞，包括弱加密算法、SSL/TLS配置错误、证书问题、密钥管理缺陷、JWT安全等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // 加密检测需要较长时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,       // 需要检测SSL问题，不验证SSL
		MaxResponseSize: 1024 * 1024, // 1MB，加密响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化TLS客户端（用于SSL/TLS检测）
	detector.tlsClient = &tls.Config{
		InsecureSkipVerify: true,             // 需要检测SSL问题
		MinVersion:         tls.VersionTLS10, // 允许检测旧版本
		MaxVersion:         tls.VersionTLS13,
	}

	// 初始化检测数据
	detector.initializeWeakTLSVersions()
	detector.initializeWeakCipherSuites()
	detector.initializeWeakHashAlgorithms()
	detector.initializeWeakKeyExchanges()
	detector.initializeJWTWeakSecrets()
	detector.initializeJWTAlgorithms()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *EncryptionDetector) GetID() string                     { return d.id }
func (d *EncryptionDetector) GetName() string                   { return d.name }
func (d *EncryptionDetector) GetCategory() string               { return d.category }
func (d *EncryptionDetector) GetSeverity() string               { return d.severity }
func (d *EncryptionDetector) GetCVE() []string                  { return d.cve }
func (d *EncryptionDetector) GetCWE() []string                  { return d.cwe }
func (d *EncryptionDetector) GetVersion() string                { return d.version }
func (d *EncryptionDetector) GetAuthor() string                 { return d.author }
func (d *EncryptionDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *EncryptionDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *EncryptionDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *EncryptionDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *EncryptionDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *EncryptionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *EncryptionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *EncryptionDetector) GetDependencies() []string         { return []string{} }
func (d *EncryptionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *EncryptionDetector) SetEnabled(enabled bool)           { d.enabled = enabled; d.updatedAt = time.Now() }

func (d *EncryptionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *EncryptionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *EncryptionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *EncryptionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *EncryptionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.weakTLSVersions) == 0 {
		return fmt.Errorf("弱TLS版本列表不能为空")
	}
	if len(d.encryptionPatterns) == 0 {
		return fmt.Errorf("加密模式不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *EncryptionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 加密检测适用于所有Web应用，特别是HTTPS应用
	if target.Protocol == "https" || target.Port == 443 || target.Port == 8443 {
		return true
	}

	// 检查是否有加密相关的特征
	if d.hasEncryptionFeatures(target) {
		return true
	}

	// 对于HTTP应用，也可能有加密相关的内容
	if target.Protocol == "http" || target.Port == 80 || target.Port == 8080 {
		return true
	}

	return false
}

// hasEncryptionFeatures 检查是否有加密功能
func (d *EncryptionDetector) hasEncryptionFeatures(target *plugins.ScanTarget) bool {
	// 检查URL中是否有加密相关关键词
	targetLower := strings.ToLower(target.URL)
	encryptionKeywords := []string{
		"ssl", "tls", "https", "cert", "certificate", "crypto", "encrypt",
		"jwt", "token", "auth", "login", "secure", "key", "hash",
		"加密", "证书", "安全", "令牌", "认证", "登录", "密钥",
	}

	for _, keyword := range encryptionKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	// 检查头部中是否有加密相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "authorization" ||
			keyLower == "x-api-key" ||
			keyLower == "x-auth-token" ||
			strings.Contains(valueLower, "bearer") ||
			strings.Contains(valueLower, "jwt") ||
			strings.Contains(valueLower, "token") ||
			strings.Contains(valueLower, "ssl") ||
			strings.Contains(valueLower, "tls") {
			return true
		}
	}

	// 检查Cookie中是否有加密相关信息
	for _, cookie := range target.Cookies {
		cookieNameLower := strings.ToLower(cookie.Name)

		encryptionCookies := []string{
			"jwt", "token", "auth", "session", "secure", "ssl", "tls",
			"cert", "key", "hash", "crypto", "encrypt",
			"令牌", "认证", "会话", "安全", "证书", "密钥", "加密",
		}

		for _, encCookie := range encryptionCookies {
			if strings.Contains(cookieNameLower, encCookie) {
				return true
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *EncryptionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种加密检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. SSL/TLS配置检测
	sslEvidence, sslConfidence, sslPayload, sslRequest, sslResponse := d.detectSSLTLSConfiguration(ctx, target)
	if sslConfidence > maxConfidence {
		maxConfidence = sslConfidence
		vulnerablePayload = sslPayload
		vulnerableRequest = sslRequest
		vulnerableResponse = sslResponse
	}
	evidence = append(evidence, sslEvidence...)

	// 2. 证书安全检测
	certEvidence, certConfidence, certPayload, certRequest, certResponse := d.detectCertificateSecurity(ctx, target)
	if certConfidence > maxConfidence {
		maxConfidence = certConfidence
		vulnerablePayload = certPayload
		vulnerableRequest = certRequest
		vulnerableResponse = certResponse
	}
	evidence = append(evidence, certEvidence...)

	// 3. JWT安全检测
	jwtEvidence, jwtConfidence, jwtPayload, jwtRequest, jwtResponse := d.detectJWTSecurity(ctx, target)
	if jwtConfidence > maxConfidence {
		maxConfidence = jwtConfidence
		vulnerablePayload = jwtPayload
		vulnerableRequest = jwtRequest
		vulnerableResponse = jwtResponse
	}
	evidence = append(evidence, jwtEvidence...)

	// 4. 弱加密算法检测
	weakCryptoEvidence, weakCryptoConfidence, weakCryptoPayload, weakCryptoRequest, weakCryptoResponse := d.detectWeakCryptography(ctx, target)
	if weakCryptoConfidence > maxConfidence {
		maxConfidence = weakCryptoConfidence
		vulnerablePayload = weakCryptoPayload
		vulnerableRequest = weakCryptoRequest
		vulnerableResponse = weakCryptoResponse
	}
	evidence = append(evidence, weakCryptoEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "加密漏洞",
		Description:       "检测到加密漏洞，可能导致数据泄露、中间人攻击或其他加密安全问题",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "使用强加密算法，配置安全的SSL/TLS，管理好证书和密钥，实施安全的JWT配置",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Cryptography", "https://cwe.mitre.org/data/definitions/326.html"},
		Tags:              []string{"encryption", "ssl", "tls", "certificate", "jwt", "crypto", "web", "high"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *EncryptionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"ssl-configuration",
		"certificate-validation",
		"encryption-strength",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyEncryptionMethod(ctx, target, method)
		if methodConfidence > 0.4 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了加密问题: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "encryption-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用加密验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *EncryptionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("encryption_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *EncryptionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (加密通常是高风险)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyEncryptionMethod 验证加密方法
func (d *EncryptionDetector) verifyEncryptionMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "ssl-configuration":
		return d.verifySSLConfiguration(ctx, target)
	case "certificate-validation":
		return d.verifyCertificateValidation(ctx, target)
	case "encryption-strength":
		return d.verifyEncryptionStrength(ctx, target)
	default:
		return 0.0
	}
}

// verifySSLConfiguration 验证SSL配置
func (d *EncryptionDetector) verifySSLConfiguration(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的SSL配置验证
	if target.Protocol == "https" || target.Port == 443 || target.Port == 8443 {
		return 0.6 // HTTPS目标有SSL配置问题的可能性
	}
	return 0.2
}

// verifyCertificateValidation 验证证书验证
func (d *EncryptionDetector) verifyCertificateValidation(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的证书验证
	if target.Protocol == "https" {
		return 0.5 // HTTPS目标可能有证书问题
	}
	return 0.1
}

// verifyEncryptionStrength 验证加密强度
func (d *EncryptionDetector) verifyEncryptionStrength(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的加密强度验证
	if d.hasEncryptionFeatures(target) {
		return 0.4 // 有加密特征的目标可能有弱加密
	}
	return 0.1
}

// initializeWeakTLSVersions 初始化弱TLS版本列表
func (d *EncryptionDetector) initializeWeakTLSVersions() {
	d.weakTLSVersions = []string{
		// 过时的SSL版本
		"SSL 2.0",
		"SSL 3.0",
		"SSLv2",
		"SSLv3",

		// 过时的TLS版本
		"TLS 1.0",
		"TLS 1.1",
		"TLSv1.0",
		"TLSv1.1",
		"TLSv1",

		// 协议版本号
		"0x0200", // SSL 2.0
		"0x0300", // SSL 3.0
		"0x0301", // TLS 1.0
		"0x0302", // TLS 1.1

		// 中文版本
		"SSL 2.0版本",
		"SSL 3.0版本",
		"TLS 1.0版本",
		"TLS 1.1版本",
	}
}

// initializeWeakCipherSuites 初始化弱加密套件列表
func (d *EncryptionDetector) initializeWeakCipherSuites() {
	d.weakCipherSuites = []string{
		// NULL加密
		"TLS_NULL_WITH_NULL_NULL",
		"TLS_RSA_WITH_NULL_MD5",
		"TLS_RSA_WITH_NULL_SHA",
		"TLS_RSA_WITH_NULL_SHA256",

		// 匿名密钥交换
		"TLS_DH_anon_WITH_RC4_128_MD5",
		"TLS_DH_anon_WITH_3DES_EDE_CBC_SHA",
		"TLS_DH_anon_WITH_AES_128_CBC_SHA",
		"TLS_DH_anon_WITH_AES_256_CBC_SHA",
		"TLS_ECDH_anon_WITH_RC4_128_SHA",
		"TLS_ECDH_anon_WITH_3DES_EDE_CBC_SHA",
		"TLS_ECDH_anon_WITH_AES_128_CBC_SHA",
		"TLS_ECDH_anon_WITH_AES_256_CBC_SHA",

		// RC4加密
		"TLS_RSA_WITH_RC4_128_MD5",
		"TLS_RSA_WITH_RC4_128_SHA",
		"TLS_ECDHE_RSA_WITH_RC4_128_SHA",
		"TLS_ECDHE_ECDSA_WITH_RC4_128_SHA",

		// DES和3DES加密
		"TLS_RSA_WITH_DES_CBC_SHA",
		"TLS_DHE_RSA_WITH_DES_CBC_SHA",
		"TLS_DHE_DSS_WITH_DES_CBC_SHA",
		"TLS_RSA_WITH_3DES_EDE_CBC_SHA",
		"TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA",
		"TLS_DHE_DSS_WITH_3DES_EDE_CBC_SHA",

		// 弱哈希算法
		"TLS_RSA_WITH_AES_128_CBC_MD5",
		"TLS_RSA_WITH_AES_256_CBC_MD5",

		// 导出级加密
		"TLS_RSA_EXPORT_WITH_RC4_40_MD5",
		"TLS_RSA_EXPORT_WITH_RC2_CBC_40_MD5",
		"TLS_RSA_EXPORT_WITH_DES40_CBC_SHA",
		"TLS_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA",
		"TLS_DHE_DSS_EXPORT_WITH_DES40_CBC_SHA",

		// 中文加密套件
		"空加密",
		"匿名密钥交换",
		"RC4加密",
		"DES加密",
		"3DES加密",
		"弱哈希",
		"导出级加密",
	}
}

// initializeWeakHashAlgorithms 初始化弱哈希算法列表
func (d *EncryptionDetector) initializeWeakHashAlgorithms() {
	d.weakHashAlgorithms = []string{
		// 弱哈希算法
		"MD2",
		"MD4",
		"MD5",
		"SHA-0",
		"SHA0",
		"SHA1",
		"SHA-1",

		// 哈希算法标识
		"md2",
		"md4",
		"md5",
		"sha",
		"sha1",
		"sha-1",

		// 算法OID
		"1.2.840.113549.2.2", // MD2
		"1.2.840.113549.2.4", // MD4
		"1.2.840.113549.2.5", // MD5
		"1.3.14.3.2.26",      // SHA-1

		// 中文哈希算法
		"MD5算法",
		"SHA1算法",
		"弱哈希",
		"过时哈希",
	}
}

// initializeWeakKeyExchanges 初始化弱密钥交换列表
func (d *EncryptionDetector) initializeWeakKeyExchanges() {
	d.weakKeyExchanges = []string{
		// 弱密钥交换
		"RSA_512",
		"RSA_1024",
		"DH_512",
		"DH_1024",
		"DSA_512",
		"DSA_1024",
		"ECDH_160",
		"ECDH_192",
		"ECDSA_160",
		"ECDSA_192",

		// 密钥长度
		"512-bit",
		"1024-bit",
		"160-bit",
		"192-bit",

		// 弱曲线
		"secp160r1",
		"secp160r2",
		"secp192r1",
		"prime192v1",
		"prime192v2",
		"prime192v3",

		// 中文密钥交换
		"弱密钥",
		"短密钥",
		"512位密钥",
		"1024位密钥",
		"弱椭圆曲线",
	}
}

// initializeJWTWeakSecrets 初始化JWT弱密钥列表
func (d *EncryptionDetector) initializeJWTWeakSecrets() {
	d.jwtWeakSecrets = []string{
		// 常见弱密钥
		"secret",
		"password",
		"123456",
		"admin",
		"test",
		"key",
		"jwt",
		"token",
		"auth",
		"default",

		// 简单密钥
		"a",
		"abc",
		"123",
		"qwerty",
		"password123",
		"admin123",
		"secret123",
		"jwt_secret",
		"my_secret",
		"super_secret",

		// 空密钥
		"",
		" ",
		"null",
		"undefined",
		"none",

		// 常见应用密钥
		"laravel_session",
		"django_secret",
		"rails_secret",
		"spring_secret",
		"node_secret",

		// 中文弱密钥
		"密码",
		"密钥",
		"秘密",
		"管理员",
		"测试",
		"默认",
	}
}

// initializeJWTAlgorithms 初始化JWT算法列表
func (d *EncryptionDetector) initializeJWTAlgorithms() {
	d.jwtAlgorithms = []string{
		// 对称算法
		"HS256",
		"HS384",
		"HS512",

		// 非对称算法
		"RS256",
		"RS384",
		"RS512",
		"ES256",
		"ES384",
		"ES512",
		"PS256",
		"PS384",
		"PS512",

		// 危险算法
		"none",
		"NONE",
		"None",
		"null",
		"NULL",
		"Null",

		// 弱算法
		"HS1",
		"RS1",
		"ES1",
		"MD5",
		"SHA1",

		// 中文算法
		"无算法",
		"空算法",
		"弱算法",
	}
}

// initializePatterns 初始化检测模式
func (d *EncryptionDetector) initializePatterns() {
	// 加密模式 - 检测加密相关的响应内容
	encryptionPatternStrings := []string{
		// SSL/TLS错误
		`(?i)(ssl\s*error|tls\s*error|certificate\s*error|cert\s*error)`,
		`(?i)(handshake\s*failed|connection\s*failed|protocol\s*error)`,
		`(?i)(invalid\s*certificate|expired\s*certificate|self\s*signed)`,
		`(?i)(weak\s*cipher|insecure\s*cipher|deprecated\s*cipher)`,

		// 加密算法
		`(?i)(md5|sha1|des|3des|rc4|ssl\s*2|ssl\s*3|tls\s*1\.0|tls\s*1\.1)`,
		`(?i)(weak\s*encryption|insecure\s*encryption|deprecated\s*encryption)`,
		`(?i)(null\s*cipher|export\s*cipher|anonymous\s*cipher)`,

		// JWT相关
		`(?i)(jwt|json\s*web\s*token|bearer\s*token)`,
		`(?i)(algorithm\s*none|alg\s*none|no\s*signature)`,
		`(?i)(weak\s*secret|default\s*secret|empty\s*secret)`,

		// 证书问题
		`(?i)(certificate\s*expired|cert\s*expired|certificate\s*invalid)`,
		`(?i)(self\s*signed|untrusted\s*certificate|certificate\s*chain)`,
		`(?i)(certificate\s*mismatch|hostname\s*mismatch)`,

		// 密钥问题
		`(?i)(weak\s*key|short\s*key|insecure\s*key|default\s*key)`,
		`(?i)(key\s*length|key\s*size|rsa\s*512|rsa\s*1024)`,
		`(?i)(private\s*key|public\s*key|key\s*exposure)`,

		// 中文加密模式
		`(?i)(加密错误|证书错误|握手失败|协议错误)`,
		`(?i)(弱加密|不安全加密|过时加密|空加密)`,
		`(?i)(证书过期|证书无效|自签名证书|证书链)`,
		`(?i)(弱密钥|短密钥|默认密钥|密钥泄露)`,
	}

	d.encryptionPatterns = make([]*regexp.Regexp, 0, len(encryptionPatternStrings))
	for _, pattern := range encryptionPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.encryptionPatterns = append(d.encryptionPatterns, compiled)
		}
	}

	// 证书模式 - 检测证书相关的响应内容
	certificatePatternStrings := []string{
		// 证书状态
		`(?i)(certificate\s*valid|certificate\s*invalid|certificate\s*expired)`,
		`(?i)(cert\s*valid|cert\s*invalid|cert\s*expired|cert\s*revoked)`,
		`(?i)(certificate\s*authority|ca\s*certificate|root\s*certificate)`,

		// 证书类型
		`(?i)(ssl\s*certificate|tls\s*certificate|x\.509\s*certificate)`,
		`(?i)(wildcard\s*certificate|domain\s*certificate|ev\s*certificate)`,
		`(?i)(self\s*signed|ca\s*signed|publicly\s*trusted)`,

		// 证书信息
		`(?i)(subject\s*name|issuer\s*name|common\s*name|alternative\s*name)`,
		`(?i)(certificate\s*fingerprint|certificate\s*serial|certificate\s*version)`,
		`(?i)(not\s*before|not\s*after|valid\s*from|valid\s*to)`,

		// 中文证书模式
		`(?i)(证书有效|证书无效|证书过期|证书撤销)`,
		`(?i)(证书颁发机构|根证书|通配符证书|域名证书)`,
		`(?i)(主题名称|颁发者|公用名|备用名称)`,
	}

	d.certificatePatterns = make([]*regexp.Regexp, 0, len(certificatePatternStrings))
	for _, pattern := range certificatePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.certificatePatterns = append(d.certificatePatterns, compiled)
		}
	}

	// 密钥模式 - 检测密钥相关的响应内容
	keyPatternStrings := []string{
		// 密钥类型
		`(?i)(private\s*key|public\s*key|secret\s*key|api\s*key)`,
		`(?i)(rsa\s*key|dsa\s*key|ecdsa\s*key|ed25519\s*key)`,
		`(?i)(encryption\s*key|signing\s*key|session\s*key)`,

		// 密钥格式
		`(?i)(pem\s*format|der\s*format|pkcs\s*format|openssh\s*format)`,
		`(?i)(-----begin|-----end|ssh-rsa|ssh-dss|ssh-ed25519)`,

		// 密钥强度
		`(?i)(key\s*length|key\s*size|bit\s*length|key\s*strength)`,
		`(?i)(weak\s*key|strong\s*key|secure\s*key|insecure\s*key)`,
		`(?i)(512\s*bit|1024\s*bit|2048\s*bit|4096\s*bit)`,

		// 密钥管理
		`(?i)(key\s*generation|key\s*exchange|key\s*derivation|key\s*rotation)`,
		`(?i)(key\s*storage|key\s*backup|key\s*recovery|key\s*escrow)`,

		// 中文密钥模式
		`(?i)(私钥|公钥|密钥|秘钥|加密密钥|签名密钥)`,
		`(?i)(密钥长度|密钥强度|弱密钥|强密钥|安全密钥)`,
		`(?i)(密钥生成|密钥交换|密钥派生|密钥轮换)`,
	}

	d.keyPatterns = make([]*regexp.Regexp, 0, len(keyPatternStrings))
	for _, pattern := range keyPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.keyPatterns = append(d.keyPatterns, compiled)
		}
	}
}
