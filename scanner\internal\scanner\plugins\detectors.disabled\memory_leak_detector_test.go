package detectors

import (
	"testing"
	"time"

	"scanner/internal/scanner/plugins"
)

// TestMemoryLeakDetectorBasicFunctionality 测试内存泄露检测器基本功能
func TestMemoryLeakDetectorBasicFunctionality(t *testing.T) {
	detector := NewMemoryLeakDetector()

	// 测试基本信息
	if detector.GetID() != "memory-leak-comprehensive" {
		t.<PERSON><PERSON>("Expected ID 'memory-leak-comprehensive', got '%s'", detector.GetID())
	}

	if detector.GetName() != "内存泄露漏洞综合检测器" {
		t.<PERSON><PERSON><PERSON>("Expected name '内存泄露漏洞综合检测器', got '%s'", detector.GetName())
	}

	if detector.GetCategory() != "web" {
		t.<PERSON>rf("Expected category 'web', got '%s'", detector.GetCategory())
	}

	if detector.GetSeverity() != "medium" {
		t.<PERSON><PERSON>rf("Expected severity 'medium', got '%s'", detector.GetSeverity())
	}

	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled by default")
	}

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	expectedTypes := []string{"http", "https"}
	if len(targetTypes) != len(expectedTypes) {
		t.Errorf("Expected %d target types, got %d", len(expectedTypes), len(targetTypes))
	}

	// 测试端口
	ports := detector.GetRequiredPorts()
	if len(ports) == 0 {
		t.Error("Expected some required ports")
	}

	// 测试服务
	services := detector.GetRequiredServices()
	expectedServices := []string{"http", "https", "web", "api"}
	if len(services) != len(expectedServices) {
		t.Errorf("Expected %d services, got %d", len(expectedServices), len(services))
	}
}

// TestMemoryLeakDetectorApplicability 测试内存泄露检测器适用性
func TestMemoryLeakDetectorApplicability(t *testing.T) {
	detector := NewMemoryLeakDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "HTTP URL目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有内存关键词的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com/memory/status",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有调试关键词的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com/debug/info",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有表单的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Forms: []plugins.FormInfo{
					{
						Action: "/submit",
						Method: "POST",
						Fields: map[string]string{"memory": "text"},
					},
				},
			},
			expected: true,
		},
		{
			name: "有内存技术栈的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Technologies: []plugins.TechnologyInfo{
					{Name: "Java", Version: "11", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "非Web目标",
			target: &plugins.ScanTarget{
				Type:     "ip",
				Protocol: "tcp",
				Port:     22,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.IsApplicable(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestMemoryLeakDetectorConfiguration 测试内存泄露检测器配置
func TestMemoryLeakDetectorConfiguration(t *testing.T) {
	detector := NewMemoryLeakDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	if config.Timeout != 15*time.Second {
		t.Errorf("Expected timeout 15s, got %v", config.Timeout)
	}

	if config.MaxRetries != 2 {
		t.Errorf("Expected max retries 2, got %d", config.MaxRetries)
	}

	if config.Concurrency != 2 {
		t.Errorf("Expected concurrency 2, got %d", config.Concurrency)
	}

	if config.RateLimit != 2 {
		t.Errorf("Expected rate limit 2, got %d", config.RateLimit)
	}

	if !config.FollowRedirects {
		t.Error("Expected FollowRedirects to be true")
	}

	if config.VerifySSL {
		t.Error("Expected VerifySSL to be false")
	}

	if config.MaxResponseSize != 3*1024*1024 {
		t.Errorf("Expected max response size 3MB, got %d", config.MaxResponseSize)
	}

	// 测试配置更新
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       4,
		FollowRedirects: false,
		VerifySSL:       true,
		MaxResponseSize: 5 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	if err != nil {
		t.Errorf("Failed to set configuration: %v", err)
	}

	updatedConfig := detector.GetConfiguration()
	if updatedConfig.Timeout != 30*time.Second {
		t.Errorf("Expected updated timeout 30s, got %v", updatedConfig.Timeout)
	}
}

// TestMemoryLeakDetectorSensitiveInfoPayloads 测试敏感信息载荷
func TestMemoryLeakDetectorSensitiveInfoPayloads(t *testing.T) {
	detector := NewMemoryLeakDetector()

	if len(detector.sensitiveInfoPayloads) == 0 {
		t.Error("Expected some sensitive info payloads")
	}

	// 检查是否包含关键的敏感信息载荷
	expectedPayloads := []string{
		"database", "password", "api_key", "config", "version",
		"session", "error", "file", "ip", "数据库", "密码", "配置",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.sensitiveInfoPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find sensitive info payload '%s'", expected)
		}
	}
}

// TestMemoryLeakDetectorMemoryOverflowPayloads 测试内存溢出载荷
func TestMemoryLeakDetectorMemoryOverflowPayloads(t *testing.T) {
	detector := NewMemoryLeakDetector()

	if len(detector.memoryOverflowPayloads) == 0 {
		t.Error("Expected some memory overflow payloads")
	}

	// 检查是否包含不同大小的载荷
	foundSizes := make(map[int]bool)
	for _, payload := range detector.memoryOverflowPayloads {
		foundSizes[len(payload)] = true
	}

	// 应该有不同大小的载荷
	if len(foundSizes) < 5 {
		t.Errorf("Expected at least 5 different payload sizes, got %d", len(foundSizes))
	}
}

// TestMemoryLeakDetectorStackTracePayloads 测试堆栈跟踪载荷
func TestMemoryLeakDetectorStackTracePayloads(t *testing.T) {
	detector := NewMemoryLeakDetector()

	if len(detector.stackTracePayloads) == 0 {
		t.Error("Expected some stack trace payloads")
	}

	// 检查是否包含关键的堆栈跟踪载荷
	expectedPayloads := []string{
		"java.lang.NullPointerException",
		"Traceback (most recent call last):",
		"System.NullReferenceException",
		"Fatal error:",
		"ReferenceError:",
		"NoMethodError:",
		"空指针异常",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.stackTracePayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find stack trace payload '%s'", expected)
		}
	}
}

// TestMemoryLeakDetectorDebugInfoPayloads 测试调试信息载荷
func TestMemoryLeakDetectorDebugInfoPayloads(t *testing.T) {
	detector := NewMemoryLeakDetector()

	if len(detector.debugInfoPayloads) == 0 {
		t.Error("Expected some debug info payloads")
	}

	// 检查是否包含关键的调试信息载荷
	expectedPayloads := []string{
		"debug", "trace", "verbose", "info", "development",
		"debug=true", "X-Debug", "debug_mode", "/debug",
		"debug.log", "调试", "调试模式",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.debugInfoPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find debug info payload '%s'", expected)
		}
	}
}

// TestMemoryLeakDetectorErrorInfoPayloads 测试错误信息载荷
func TestMemoryLeakDetectorErrorInfoPayloads(t *testing.T) {
	detector := NewMemoryLeakDetector()

	if len(detector.errorInfoPayloads) == 0 {
		t.Error("Expected some error info payloads")
	}

	// 检查是否包含关键的错误信息载荷
	expectedPayloads := []string{
		"error", "exception", "fault", "failure", "crash",
		"error=true", "X-Error", "error_mode", "/error",
		"error.log", "错误", "异常",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.errorInfoPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find error info payload '%s'", expected)
		}
	}
}

// TestMemoryLeakDetectorTestParameters 测试测试参数
func TestMemoryLeakDetectorTestParameters(t *testing.T) {
	detector := NewMemoryLeakDetector()

	if len(detector.testParameters) == 0 {
		t.Error("Expected some test parameters")
	}

	// 检查是否包含关键的测试参数
	expectedParams := []string{
		"memory", "debug", "error", "status", "system", "config",
		"session", "database", "network", "file", "user", "app",
		"内存", "调试", "错误", "状态", "系统", "配置",
	}

	for _, expected := range expectedParams {
		found := false
		for _, param := range detector.testParameters {
			if param == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find test parameter '%s'", expected)
		}
	}
}

// TestMemoryLeakDetectorPatterns 测试检测模式
func TestMemoryLeakDetectorPatterns(t *testing.T) {
	detector := NewMemoryLeakDetector()

	// 测试敏感信息模式
	if len(detector.sensitivePatterns) == 0 {
		t.Error("Expected some sensitive patterns")
	}

	// 测试内存泄露模式
	if len(detector.memoryPatterns) == 0 {
		t.Error("Expected some memory patterns")
	}

	// 测试堆栈跟踪模式
	if len(detector.stackPatterns) == 0 {
		t.Error("Expected some stack patterns")
	}

	// 测试调试信息模式
	if len(detector.debugPatterns) == 0 {
		t.Error("Expected some debug patterns")
	}

	// 测试错误信息模式
	if len(detector.errorPatterns) == 0 {
		t.Error("Expected some error patterns")
	}
}

// TestMemoryLeakDetectorMemoryFeatures 测试内存功能检查
func TestMemoryLeakDetectorMemoryFeatures(t *testing.T) {
	detector := NewMemoryLeakDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "有调试头部的目标",
			target: &plugins.ScanTarget{
				Headers: map[string]string{
					"X-Debug": "true",
				},
			},
			expected: true,
		},
		{
			name: "有Java技术栈的目标",
			target: &plugins.ScanTarget{
				Technologies: []plugins.TechnologyInfo{
					{Name: "Java", Version: "11", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有调试链接的目标",
			target: &plugins.ScanTarget{
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/debug", Text: "Debug Info"},
				},
			},
			expected: true,
		},
		{
			name: "有内存字段的表单目标",
			target: &plugins.ScanTarget{
				Forms: []plugins.FormInfo{
					{
						Fields: map[string]string{"memory_limit": "text"},
					},
				},
			},
			expected: true,
		},
		{
			name: "普通目标",
			target: &plugins.ScanTarget{
				URL: "https://example.com",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.hasMemoryFeatures(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestMemoryLeakDetectorRiskScore 测试风险评分
func TestMemoryLeakDetectorRiskScore(t *testing.T) {
	detector := NewMemoryLeakDetector()

	testCases := []struct {
		confidence float64
		expected   float64
	}{
		{0.0, 0.0},
		{0.5, 3.0},
		{0.8, 4.8},
		{1.0, 6.0},
	}

	for _, tc := range testCases {
		score := detector.calculateRiskScore(tc.confidence)
		// 使用浮点数比较的容差
		if score < tc.expected-0.01 || score > tc.expected+0.01 {
			t.Errorf("Expected risk score %.1f for confidence %.1f, got %.1f", tc.expected, tc.confidence, score)
		}
	}
}

// TestMemoryLeakDetectorLifecycle 测试检测器生命周期
func TestMemoryLeakDetectorLifecycle(t *testing.T) {
	detector := NewMemoryLeakDetector()

	// 测试初始化
	err := detector.Initialize()
	if err != nil {
		t.Errorf("Failed to initialize detector: %v", err)
	}

	// 测试验证
	err = detector.Validate()
	if err != nil {
		t.Errorf("Detector validation failed: %v", err)
	}

	// 测试启用/禁用
	detector.SetEnabled(false)
	if detector.IsEnabled() {
		t.Error("Expected detector to be disabled")
	}

	detector.SetEnabled(true)
	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled")
	}

	// 测试清理
	err = detector.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup detector: %v", err)
	}
}
