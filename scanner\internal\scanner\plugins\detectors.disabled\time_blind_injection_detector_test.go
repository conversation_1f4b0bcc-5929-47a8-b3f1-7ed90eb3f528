package detectors

import (
	"testing"
	"time"

	"scanner/internal/scanner/plugins"
)

// TestTimeBlindInjectionDetectorBasicFunctionality 测试时间盲注检测器基本功能
func TestTimeBlindInjectionDetectorBasicFunctionality(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	// 测试基本信息
	if detector.GetID() != "time-blind-injection-comprehensive" {
		t.<PERSON>("Expected ID 'time-blind-injection-comprehensive', got '%s'", detector.GetID())
	}

	if detector.GetName() != "时间盲注漏洞综合检测器" {
		t.<PERSON><PERSON><PERSON>("Expected name '时间盲注漏洞综合检测器', got '%s'", detector.GetName())
	}

	if detector.GetCategory() != "web" {
		t.<PERSON>rrorf("Expected category 'web', got '%s'", detector.GetCategory())
	}

	if detector.GetSeverity() != "high" {
		t.<PERSON><PERSON><PERSON>("Expected severity 'high', got '%s'", detector.GetSeverity())
	}

	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled by default")
	}

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	expectedTypes := []string{"http", "https"}
	if len(targetTypes) != len(expectedTypes) {
		t.Errorf("Expected %d target types, got %d", len(expectedTypes), len(targetTypes))
	}

	// 测试端口
	ports := detector.GetRequiredPorts()
	if len(ports) == 0 {
		t.Error("Expected some required ports")
	}

	// 测试服务
	services := detector.GetRequiredServices()
	expectedServices := []string{"http", "https", "web", "api"}
	if len(services) != len(expectedServices) {
		t.Errorf("Expected %d services, got %d", len(expectedServices), len(services))
	}
}

// TestTimeBlindInjectionDetectorApplicability 测试时间盲注检测器适用性
func TestTimeBlindInjectionDetectorApplicability(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "HTTP URL目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有时间盲注关键词的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com/time/delay",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有SQL关键词的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com/sql/query",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有表单的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Forms: []plugins.FormInfo{
					{
						Action: "/login",
						Method: "POST",
						Fields: map[string]string{"username": "text", "password": "password"},
					},
				},
			},
			expected: true,
		},
		{
			name: "有数据库技术栈的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Technologies: []plugins.TechnologyInfo{
					{Name: "MySQL", Version: "8.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "非Web目标",
			target: &plugins.ScanTarget{
				Type:     "ip",
				Protocol: "tcp",
				Port:     22,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.IsApplicable(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestTimeBlindInjectionDetectorConfiguration 测试时间盲注检测器配置
func TestTimeBlindInjectionDetectorConfiguration(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	if config.Timeout != 30*time.Second {
		t.Errorf("Expected timeout 30s, got %v", config.Timeout)
	}

	if config.MaxRetries != 1 {
		t.Errorf("Expected max retries 1, got %d", config.MaxRetries)
	}

	if config.Concurrency != 1 {
		t.Errorf("Expected concurrency 1, got %d", config.Concurrency)
	}

	if config.RateLimit != 1 {
		t.Errorf("Expected rate limit 1, got %d", config.RateLimit)
	}

	if !config.FollowRedirects {
		t.Error("Expected FollowRedirects to be true")
	}

	if config.VerifySSL {
		t.Error("Expected VerifySSL to be false")
	}

	if config.MaxResponseSize != 1*1024*1024 {
		t.Errorf("Expected max response size 1MB, got %d", config.MaxResponseSize)
	}

	// 测试配置更新
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         45 * time.Second,
		MaxRetries:      2,
		Concurrency:     2,
		RateLimit:       2,
		FollowRedirects: false,
		VerifySSL:       true,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	if err != nil {
		t.Errorf("Failed to set configuration: %v", err)
	}

	updatedConfig := detector.GetConfiguration()
	if updatedConfig.Timeout != 45*time.Second {
		t.Errorf("Expected updated timeout 45s, got %v", updatedConfig.Timeout)
	}
}

// TestTimeBlindInjectionDetectorSQLTimePayloads 测试SQL时间盲注载荷
func TestTimeBlindInjectionDetectorSQLTimePayloads(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	if len(detector.sqlTimePayloads) == 0 {
		t.Error("Expected some SQL time payloads")
	}

	// 检查是否包含关键的SQL时间盲注载荷
	expectedPayloads := []string{
		"' AND SLEEP(5)--",
		"' OR SLEEP(5)--",
		"'; WAITFOR DELAY '00:00:05'--",
		"' AND pg_sleep(5)--",
		"' AND BENCHMARK(5000000,MD5(1))--",
		"' AND DBMS_LOCK.SLEEP(5)--",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.sqlTimePayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find SQL time payload '%s'", expected)
		}
	}
}

// TestTimeBlindInjectionDetectorNoSQLTimePayloads 测试NoSQL时间盲注载荷
func TestTimeBlindInjectionDetectorNoSQLTimePayloads(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	if len(detector.nosqlTimePayloads) == 0 {
		t.Error("Expected some NoSQL time payloads")
	}

	// 检查是否包含关键的NoSQL时间盲注载荷
	expectedPayloads := []string{
		`{"$where": "sleep(5000)"}`,
		`{"$where": "this.sleep(5000)"}`,
		`{"$where": "function(){sleep(5000)}"}`,
		`EVAL "redis.call('DEBUG', 'SLEEP', 5)" 0`,
		`DEBUG SLEEP 5`,
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.nosqlTimePayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find NoSQL time payload '%s'", expected)
		}
	}
}

// TestTimeBlindInjectionDetectorCommandTimePayloads 测试命令时间盲注载荷
func TestTimeBlindInjectionDetectorCommandTimePayloads(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	if len(detector.commandTimePayloads) == 0 {
		t.Error("Expected some command time payloads")
	}

	// 检查是否包含关键的命令时间盲注载荷
	expectedPayloads := []string{
		"; sleep 5",
		"| sleep 5",
		"& sleep 5",
		"&& sleep 5",
		"|| sleep 5",
		"`sleep 5`",
		"$(sleep 5)",
		"; timeout 5",
		"; ping -n 6 127.0.0.1",
		"; Start-Sleep 5",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.commandTimePayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find command time payload '%s'", expected)
		}
	}
}

// TestTimeBlindInjectionDetectorCodeTimePayloads 测试代码时间盲注载荷
func TestTimeBlindInjectionDetectorCodeTimePayloads(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	if len(detector.codeTimePayloads) == 0 {
		t.Error("Expected some code time payloads")
	}

	// 检查是否包含关键的代码时间盲注载荷
	expectedPayloads := []string{
		"sleep(5)",
		"usleep(5000000)",
		"time.sleep(5)",
		"Thread.sleep(5000)",
		"setTimeout(function(){}, 5000)",
		"System.Threading.Thread.Sleep(5000)",
		"time.Sleep(5 * time.Second)",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.codeTimePayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find code time payload '%s'", expected)
		}
	}
}

// TestTimeBlindInjectionDetectorTestParameters 测试测试参数
func TestTimeBlindInjectionDetectorTestParameters(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	if len(detector.testParameters) == 0 {
		t.Error("Expected some test parameters")
	}

	// 检查是否包含关键的测试参数
	expectedParams := []string{
		"time", "delay", "sleep", "wait", "timeout", "benchmark",
		"sql", "query", "search", "filter", "sort", "order",
		"user", "username", "login", "auth", "password",
		"data", "info", "content", "text", "value",
		"cmd", "command", "exec", "execute", "run",
		"file", "filename", "filepath", "path",
		"api", "endpoint", "service", "method",
		"时间", "延迟", "睡眠", "等待", "超时", "查询",
	}

	for _, expected := range expectedParams {
		found := false
		for _, param := range detector.testParameters {
			if param == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find test parameter '%s'", expected)
		}
	}
}

// TestTimeBlindInjectionDetectorPatterns 测试检测模式
func TestTimeBlindInjectionDetectorPatterns(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	// 测试时间相关模式
	if len(detector.timePatterns) == 0 {
		t.Error("Expected some time patterns")
	}

	// 测试延迟模式
	if len(detector.delayPatterns) == 0 {
		t.Error("Expected some delay patterns")
	}

	// 测试注入模式
	if len(detector.injectionPatterns) == 0 {
		t.Error("Expected some injection patterns")
	}
}

// TestTimeBlindInjectionDetectorTimeBlindInjectionFeatures 测试时间盲注功能检查
func TestTimeBlindInjectionDetectorTimeBlindInjectionFeatures(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "有时间头部的目标",
			target: &plugins.ScanTarget{
				Headers: map[string]string{
					"X-Response-Time": "100ms",
				},
			},
			expected: true,
		},
		{
			name: "有数据库技术栈的目标",
			target: &plugins.ScanTarget{
				Technologies: []plugins.TechnologyInfo{
					{Name: "MySQL", Version: "8.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有时间盲注链接的目标",
			target: &plugins.ScanTarget{
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/time", Text: "Time Query"},
				},
			},
			expected: true,
		},
		{
			name: "有时间盲注字段的表单目标",
			target: &plugins.ScanTarget{
				Forms: []plugins.FormInfo{
					{
						Fields: map[string]string{"time": "text", "delay": "text"},
					},
				},
			},
			expected: true,
		},
		{
			name: "普通目标",
			target: &plugins.ScanTarget{
				URL: "https://example.com",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.hasTimeBlindInjectionFeatures(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestTimeBlindInjectionDetectorRiskScore 测试风险评分
func TestTimeBlindInjectionDetectorRiskScore(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	testCases := []struct {
		confidence float64
		expected   float64
	}{
		{0.0, 0.0},
		{0.5, 4.25},
		{0.8, 6.8},
		{1.0, 8.5},
	}

	for _, tc := range testCases {
		score := detector.calculateRiskScore(tc.confidence)
		// 使用浮点数比较的容差
		if score < tc.expected-0.01 || score > tc.expected+0.01 {
			t.Errorf("Expected risk score %.2f for confidence %.1f, got %.2f", tc.expected, tc.confidence, score)
		}
	}
}

// TestTimeBlindInjectionDetectorLifecycle 测试检测器生命周期
func TestTimeBlindInjectionDetectorLifecycle(t *testing.T) {
	detector := NewTimeBlindInjectionDetector()

	// 测试初始化
	err := detector.Initialize()
	if err != nil {
		t.Errorf("Failed to initialize detector: %v", err)
	}

	// 测试验证
	err = detector.Validate()
	if err != nil {
		t.Errorf("Detector validation failed: %v", err)
	}

	// 测试启用/禁用
	detector.SetEnabled(false)
	if detector.IsEnabled() {
		t.Error("Expected detector to be disabled")
	}

	detector.SetEnabled(true)
	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled")
	}

	// 测试清理
	err = detector.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup detector: %v", err)
	}
}
