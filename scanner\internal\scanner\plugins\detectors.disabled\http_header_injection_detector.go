package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// HTTPHeaderInjectionDetector HTTP头部注入检测器
// 支持HTTP头部注入检测，包括HTTP响应分割、CRLF注入、头部注入、缓存投毒等多种HTTP协议注入检测技术
type HTTPHeaderInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	crlfPayloads             []string         // CRLF注入载荷
	responseSpittingPayloads []string         // HTTP响应分割载荷
	headerInjectionPayloads  []string         // 头部注入载荷
	cachePoisoningPayloads   []string         // 缓存投毒载荷
	hostHeaderPayloads       []string         // Host头注入载荷
	xffPayloads              []string         // X-Forwarded-For注入载荷
	testParameters           []string         // 测试参数
	crlfPatterns             []*regexp.Regexp // CRLF特征模式
	headerPatterns           []*regexp.Regexp // 头部特征模式
	responsePatterns         []*regexp.Regexp // 响应特征模式
	errorPatterns            []*regexp.Regexp // 错误模式
	httpClient               *http.Client
}

// NewHTTPHeaderInjectionDetector 创建HTTP头部注入检测器
func NewHTTPHeaderInjectionDetector() *HTTPHeaderInjectionDetector {
	detector := &HTTPHeaderInjectionDetector{
		id:          "http-header-injection-comprehensive",
		name:        "HTTP头部注入漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-0708"},
		cwe:         []string{"CWE-113", "CWE-117", "CWE-20", "CWE-79"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测HTTP头部注入漏洞，包括HTTP响应分割、CRLF注入、头部注入、缓存投毒等多种HTTP协议注入检测技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // HTTP头部注入检测需要适中时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: false, // 不跟随重定向，检查原始响应
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，头部注入响应通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeCRLFPayloads()
	detector.initializeResponseSplittingPayloads()
	detector.initializeHeaderInjectionPayloads()
	detector.initializeCachePoisoningPayloads()
	detector.initializeHostHeaderPayloads()
	detector.initializeXFFPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *HTTPHeaderInjectionDetector) GetID() string            { return d.id }
func (d *HTTPHeaderInjectionDetector) GetName() string          { return d.name }
func (d *HTTPHeaderInjectionDetector) GetCategory() string      { return d.category }
func (d *HTTPHeaderInjectionDetector) GetSeverity() string      { return d.severity }
func (d *HTTPHeaderInjectionDetector) GetCVE() []string         { return d.cve }
func (d *HTTPHeaderInjectionDetector) GetCWE() []string         { return d.cwe }
func (d *HTTPHeaderInjectionDetector) GetVersion() string       { return d.version }
func (d *HTTPHeaderInjectionDetector) GetAuthor() string        { return d.author }
func (d *HTTPHeaderInjectionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *HTTPHeaderInjectionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *HTTPHeaderInjectionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *HTTPHeaderInjectionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *HTTPHeaderInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *HTTPHeaderInjectionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *HTTPHeaderInjectionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *HTTPHeaderInjectionDetector) GetDependencies() []string         { return []string{} }
func (d *HTTPHeaderInjectionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *HTTPHeaderInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *HTTPHeaderInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *HTTPHeaderInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *HTTPHeaderInjectionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *HTTPHeaderInjectionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *HTTPHeaderInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.crlfPayloads) == 0 {
		return fmt.Errorf("CRLF载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *HTTPHeaderInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// HTTP头部注入检测适用于有HTTP功能的Web应用
	// 检查是否有HTTP相关的特征
	if d.hasHTTPFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于API相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	apiKeywords := []string{
		"api", "rest", "redirect", "forward", "proxy", "cache", "header",
		"endpoint", "data", "query", "search", "config", "settings",
		"接口", "服务", "数据", "查询", "配置", "设置", "重定向", "转发",
	}

	for _, keyword := range apiKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // HTTP头部注入是通用Web漏洞，默认适用于所有Web目标
}

// hasHTTPFeatures 检查是否有HTTP功能
func (d *HTTPHeaderInjectionDetector) hasHTTPFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有HTTP相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "redirect") ||
			strings.Contains(keyLower, "forward") ||
			strings.Contains(keyLower, "proxy") ||
			strings.Contains(keyLower, "cache") ||
			strings.Contains(keyLower, "location") ||
			strings.Contains(valueLower, "redirect") ||
			strings.Contains(valueLower, "forward") ||
			strings.Contains(valueLower, "proxy") ||
			strings.Contains(valueLower, "cache") {
			return true
		}
	}

	// 检查技术栈中是否有HTTP相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		httpTechnologies := []string{
			"apache", "nginx", "iis", "tomcat", "jetty", "express",
			"spring", "django", "flask", "laravel", "symfony",
			"代理", "缓存", "重定向", "转发", "负载均衡",
		}

		for _, httpTech := range httpTechnologies {
			if strings.Contains(techNameLower, httpTech) {
				return true
			}
		}
	}

	// 检查链接中是否有HTTP相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "redirect") ||
			strings.Contains(linkURLLower, "forward") ||
			strings.Contains(linkURLLower, "proxy") ||
			strings.Contains(linkURLLower, "cache") ||
			strings.Contains(linkTextLower, "redirect") ||
			strings.Contains(linkTextLower, "forward") ||
			strings.Contains(linkTextLower, "proxy") ||
			strings.Contains(linkTextLower, "cache") ||
			strings.Contains(linkTextLower, "重定向") ||
			strings.Contains(linkTextLower, "转发") {
			return true
		}
	}

	// 检查表单中是否有HTTP相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			httpFields := []string{
				"redirect", "forward", "proxy", "cache", "location", "url",
				"header", "referer", "origin", "host", "user-agent",
				"重定向", "转发", "代理", "缓存", "位置", "头部", "来源",
			}

			for _, httpField := range httpFields {
				if strings.Contains(fieldNameLower, httpField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *HTTPHeaderInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种HTTP头部注入检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. CRLF注入检测
	crlfEvidence, crlfConfidence, crlfPayload, crlfRequest, crlfResponse := d.detectCRLFInjection(ctx, target)
	if crlfConfidence > maxConfidence {
		maxConfidence = crlfConfidence
		vulnerablePayload = crlfPayload
		vulnerableRequest = crlfRequest
		vulnerableResponse = crlfResponse
	}
	evidence = append(evidence, crlfEvidence...)

	// 2. HTTP响应分割检测
	splittingEvidence, splittingConfidence, splittingPayload, splittingRequest, splittingResponse := d.detectResponseSplitting(ctx, target)
	if splittingConfidence > maxConfidence {
		maxConfidence = splittingConfidence
		vulnerablePayload = splittingPayload
		vulnerableRequest = splittingRequest
		vulnerableResponse = splittingResponse
	}
	evidence = append(evidence, splittingEvidence...)

	// 3. 头部注入检测
	headerEvidence, headerConfidence, headerPayload, headerRequest, headerResponse := d.detectHeaderInjection(ctx, target)
	if headerConfidence > maxConfidence {
		maxConfidence = headerConfidence
		vulnerablePayload = headerPayload
		vulnerableRequest = headerRequest
		vulnerableResponse = headerResponse
	}
	evidence = append(evidence, headerEvidence...)

	// 4. 缓存投毒检测
	cacheEvidence, cacheConfidence, cachePayload, cacheRequest, cacheResponse := d.detectCachePoisoning(ctx, target)
	if cacheConfidence > maxConfidence {
		maxConfidence = cacheConfidence
		vulnerablePayload = cachePayload
		vulnerableRequest = cacheRequest
		vulnerableResponse = cacheResponse
	}
	evidence = append(evidence, cacheEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "HTTP头部注入漏洞",
		Description:       "检测到HTTP头部注入漏洞，攻击者可能通过恶意HTTP头部执行响应分割、缓存投毒或其他攻击",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "验证和过滤用户输入，禁止在HTTP头部中使用CRLF字符，实施严格的输入验证",
		References:        []string{"https://owasp.org/www-community/attacks/HTTP_Response_Splitting", "https://cwe.mitre.org/data/definitions/113.html", "https://cwe.mitre.org/data/definitions/117.html"},
		Tags:              []string{"http", "header", "injection", "crlf", "response-splitting", "cache-poisoning", "web"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *HTTPHeaderInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"crlf-injection",
		"response-splitting",
		"header-injection",
		"cache-poisoning",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyHTTPMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了HTTP头部注入漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "http-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用HTTP验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *HTTPHeaderInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("http_header_injection_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *HTTPHeaderInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (HTTP头部注入通常是高风险漏洞)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyHTTPMethod 验证HTTP方法
func (d *HTTPHeaderInjectionDetector) verifyHTTPMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "crlf-injection":
		return d.verifyCRLFInjection(ctx, target)
	case "response-splitting":
		return d.verifyResponseSplitting(ctx, target)
	case "header-injection":
		return d.verifyHeaderInjection(ctx, target)
	case "cache-poisoning":
		return d.verifyCachePoisoning(ctx, target)
	default:
		return 0.0
	}
}

// verifyCRLFInjection 验证CRLF注入
func (d *HTTPHeaderInjectionDetector) verifyCRLFInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的CRLF注入验证
	if d.hasHTTPFeatures(target) {
		return 0.8 // 有HTTP特征的目标可能有CRLF注入
	}
	return 0.4
}

// verifyResponseSplitting 验证响应分割
func (d *HTTPHeaderInjectionDetector) verifyResponseSplitting(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的响应分割验证
	if d.hasHTTPFeatures(target) {
		return 0.7 // 有HTTP特征的目标可能有响应分割
	}
	return 0.3
}

// verifyHeaderInjection 验证头部注入
func (d *HTTPHeaderInjectionDetector) verifyHeaderInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的头部注入验证
	if d.hasHTTPFeatures(target) {
		return 0.6 // 有HTTP特征的目标可能有头部注入
	}
	return 0.2
}

// verifyCachePoisoning 验证缓存投毒
func (d *HTTPHeaderInjectionDetector) verifyCachePoisoning(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的缓存投毒验证
	if d.hasHTTPFeatures(target) {
		return 0.6 // 有HTTP特征的目标可能有缓存投毒
	}
	return 0.2
}

// initializeCRLFPayloads 初始化CRLF载荷列表
func (d *HTTPHeaderInjectionDetector) initializeCRLFPayloads() {
	d.crlfPayloads = []string{
		// 基础CRLF注入
		"test\r\nSet-Cookie: injected=true",
		"test\r\nLocation: http://evil.com",
		"test\r\nContent-Type: text/html",
		"test\r\nX-Injected: true",

		// URL编码CRLF注入
		"test%0d%0aSet-Cookie: injected=true",
		"test%0d%0aLocation: http://evil.com",
		"test%0d%0aContent-Type: text/html",
		"test%0d%0aX-Injected: true",

		// 双重编码CRLF注入
		"test%250d%250aSet-Cookie: injected=true",
		"test%250d%250aLocation: http://evil.com",
		"test%250d%250aContent-Type: text/html",
		"test%250d%250aX-Injected: true",

		// Unicode编码CRLF注入
		"test\u000d\u000aSet-Cookie: injected=true",
		"test\u000d\u000aLocation: http://evil.com",
		"test\u000d\u000aContent-Type: text/html",
		"test\u000d\u000aX-Injected: true",

		// 混合编码CRLF注入
		"test\r%0aSet-Cookie: injected=true",
		"test%0d\nLocation: http://evil.com",
		"test\r\n%0d%0aContent-Type: text/html",
		"test%0d%0a\r\nX-Injected: true",

		// 空格绕过CRLF注入
		"test \r\nSet-Cookie: injected=true",
		"test\t\r\nLocation: http://evil.com",
		"test\r\n Set-Cookie: injected=true",
		"test\r\n\tLocation: http://evil.com",

		// 中文CRLF载荷
		"测试\r\nSet-Cookie: 注入=真",
		"测试\r\nLocation: http://恶意.com",
		"测试%0d%0aSet-Cookie: 注入=真",
		"测试%0d%0aLocation: http://恶意.com",
	}
}

// initializeResponseSplittingPayloads 初始化响应分割载荷列表
func (d *HTTPHeaderInjectionDetector) initializeResponseSplittingPayloads() {
	d.responseSpittingPayloads = []string{
		// 基础响应分割
		"test\r\n\r\n<script>alert('XSS')</script>",
		"test\r\n\r\n<html><body>Injected Content</body></html>",
		"test\r\n\r\nHTTP/1.1 200 OK\r\nContent-Type: text/html\r\n\r\n<script>alert('XSS')</script>",
		"test\r\n\r\nHTTP/1.1 302 Found\r\nLocation: http://evil.com\r\n\r\n",

		// URL编码响应分割
		"test%0d%0a%0d%0a<script>alert('XSS')</script>",
		"test%0d%0a%0d%0a<html><body>Injected Content</body></html>",
		"test%0d%0a%0d%0aHTTP/1.1 200 OK%0d%0aContent-Type: text/html%0d%0a%0d%0a<script>alert('XSS')</script>",
		"test%0d%0a%0d%0aHTTP/1.1 302 Found%0d%0aLocation: http://evil.com%0d%0a%0d%0a",

		// 双重编码响应分割
		"test%250d%250a%250d%250a<script>alert('XSS')</script>",
		"test%250d%250a%250d%250a<html><body>Injected Content</body></html>",
		"test%250d%250a%250d%250aHTTP/1.1 200 OK%250d%250aContent-Type: text/html%250d%250a%250d%250a<script>alert('XSS')</script>",
		"test%250d%250a%250d%250aHTTP/1.1 302 Found%250d%250aLocation: http://evil.com%250d%250a%250d%250a",

		// 混合编码响应分割
		"test\r\n%0d%0a<script>alert('XSS')</script>",
		"test%0d%0a\r\n<html><body>Injected Content</body></html>",
		"test\r\n\r%0a<script>alert('XSS')</script>",
		"test%0d\n%0d%0a<html><body>Injected Content</body></html>",

		// 缓存投毒响应分割
		"test\r\n\r\nHTTP/1.1 200 OK\r\nCache-Control: max-age=3600\r\nContent-Type: text/html\r\n\r\n<script>alert('Cache Poisoning')</script>",
		"test\r\n\r\nHTTP/1.1 200 OK\r\nExpires: Thu, 01 Jan 2025 00:00:00 GMT\r\nContent-Type: text/html\r\n\r\n<script>alert('Cache Poisoning')</script>",

		// 中文响应分割载荷
		"测试\r\n\r\n<script>alert('注入')</script>",
		"测试\r\n\r\n<html><body>注入内容</body></html>",
		"测试%0d%0a%0d%0a<script>alert('注入')</script>",
		"测试%0d%0a%0d%0a<html><body>注入内容</body></html>",
	}
}

// initializeHeaderInjectionPayloads 初始化头部注入载荷列表
func (d *HTTPHeaderInjectionDetector) initializeHeaderInjectionPayloads() {
	d.headerInjectionPayloads = []string{
		// 基础头部注入
		"test\r\nX-Forwarded-For: 127.0.0.1",
		"test\r\nX-Real-IP: 127.0.0.1",
		"test\r\nX-Originating-IP: 127.0.0.1",
		"test\r\nX-Remote-IP: 127.0.0.1",
		"test\r\nX-Remote-Addr: 127.0.0.1",
		"test\r\nX-Client-IP: 127.0.0.1",

		// 认证绕过头部注入
		"test\r\nX-Forwarded-User: admin",
		"test\r\nX-Remote-User: admin",
		"test\r\nX-User: admin",
		"test\r\nX-Username: admin",
		"test\r\nAuthorization: Bearer admin_token",
		"test\r\nX-API-Key: admin_key",

		// 缓存控制头部注入
		"test\r\nCache-Control: no-cache",
		"test\r\nCache-Control: max-age=0",
		"test\r\nPragma: no-cache",
		"test\r\nExpires: Thu, 01 Jan 1970 00:00:00 GMT",

		// 安全头部注入
		"test\r\nX-Frame-Options: DENY",
		"test\r\nX-XSS-Protection: 0",
		"test\r\nX-Content-Type-Options: nosniff",
		"test\r\nStrict-Transport-Security: max-age=31536000",

		// 内容类型头部注入
		"test\r\nContent-Type: text/html",
		"test\r\nContent-Type: application/json",
		"test\r\nContent-Type: text/plain",
		"test\r\nContent-Encoding: gzip",

		// 重定向头部注入
		"test\r\nLocation: http://evil.com",
		"test\r\nRefresh: 0; url=http://evil.com",
		"test\r\nX-Redirect-To: http://evil.com",
		"test\r\nX-Forward-To: http://evil.com",

		// 中文头部注入载荷
		"测试\r\nX-Forwarded-For: 127.0.0.1",
		"测试\r\nX-用户: 管理员",
		"测试\r\nLocation: http://恶意.com",
		"测试\r\nX-注入: 真",
	}
}

// initializeCachePoisoningPayloads 初始化缓存投毒载荷列表
func (d *HTTPHeaderInjectionDetector) initializeCachePoisoningPayloads() {
	d.cachePoisoningPayloads = []string{
		// 基础缓存投毒
		"test\r\nX-Forwarded-Host: evil.com",
		"test\r\nHost: evil.com",
		"test\r\nX-Original-Host: evil.com",
		"test\r\nX-Host: evil.com",

		// 缓存键投毒
		"test\r\nX-Forwarded-Proto: https",
		"test\r\nX-Forwarded-Scheme: https",
		"test\r\nX-Scheme: https",
		"test\r\nX-Proto: https",

		// 缓存控制投毒
		"test\r\nVary: X-Forwarded-Host",
		"test\r\nVary: Host",
		"test\r\nVary: X-Original-Host",
		"test\r\nVary: User-Agent",

		// Web缓存欺骗
		"test\r\nX-Cache-Key: evil_key",
		"test\r\nX-Cache-Tag: evil_tag",
		"test\r\nX-Edge-Cache: evil_cache",
		"test\r\nX-Varnish-Cache: evil_varnish",

		// CDN缓存投毒
		"test\r\nX-Forwarded-Server: evil.com",
		"test\r\nX-Server-Name: evil.com",
		"test\r\nX-Backend-Server: evil.com",
		"test\r\nX-Upstream-Server: evil.com",

		// 中文缓存投毒载荷
		"测试\r\nX-Forwarded-Host: 恶意.com",
		"测试\r\nHost: 恶意.com",
		"测试\r\nX-缓存键: 恶意键",
		"测试\r\nX-服务器: 恶意.com",
	}
}

// initializeHostHeaderPayloads 初始化Host头注入载荷列表
func (d *HTTPHeaderInjectionDetector) initializeHostHeaderPayloads() {
	d.hostHeaderPayloads = []string{
		// 基础Host头注入
		"evil.com",
		"attacker.com",
		"malicious.com",
		"127.0.0.1",
		"localhost",
		"0.0.0.0",

		// 端口Host头注入
		"evil.com:80",
		"evil.com:443",
		"evil.com:8080",
		"127.0.0.1:80",
		"localhost:8080",

		// 子域名Host头注入
		"admin.evil.com",
		"api.evil.com",
		"www.evil.com",
		"test.evil.com",
		"dev.evil.com",

		// 特殊字符Host头注入
		"evil.com/",
		"evil.com\\",
		"evil.com#",
		"evil.com?",
		"evil.com@",

		// 编码Host头注入
		"evil%2ecom",
		"evil%252ecom",
		"evil\\.com",
		"evil..com",

		// 中文Host头载荷
		"恶意.com",
		"攻击者.com",
		"测试.恶意.com",
		"管理员.恶意.com",
	}
}

// initializeXFFPayloads 初始化X-Forwarded-For载荷列表
func (d *HTTPHeaderInjectionDetector) initializeXFFPayloads() {
	d.xffPayloads = []string{
		// 基础XFF注入
		"127.0.0.1",
		"localhost",
		"0.0.0.0",
		"***********",
		"********",
		"**********",

		// 多IP XFF注入
		"127.0.0.1, ***********",
		"evil.com, 127.0.0.1",
		"***********, localhost",
		"********, **********",

		// 特殊XFF注入
		"admin",
		"root",
		"administrator",
		"system",
		"internal",
		"trusted",

		// 绕过XFF注入
		"127.0.0.1\r\nX-Real-IP: 127.0.0.1",
		"localhost\r\nX-Originating-IP: localhost",
		"***********\r\nX-Remote-IP: ***********",
		"********\r\nX-Client-IP: ********",

		// 中文XFF载荷
		"本地主机",
		"内部",
		"信任的",
		"管理员",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *HTTPHeaderInjectionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// HTTP相关参数
		"redirect", "forward", "proxy", "cache", "location", "url", "uri", "link",
		"header", "referer", "origin", "host", "user-agent", "accept", "content-type",
		"authorization", "cookie", "session", "token", "api-key", "x-api-key",

		// 重定向相关参数
		"redirect_url", "redirect_uri", "return_url", "return_uri", "callback_url",
		"callback_uri", "next_url", "next_uri", "continue_url", "continue_uri",
		"target_url", "target_uri", "destination_url", "destination_uri",

		// 代理相关参数
		"proxy_url", "proxy_uri", "proxy_host", "proxy_server", "upstream_url",
		"upstream_uri", "upstream_host", "upstream_server", "backend_url",
		"backend_uri", "backend_host", "backend_server",

		// 缓存相关参数
		"cache_key", "cache_tag", "cache_control", "cache_policy", "cache_timeout",
		"cache_expire", "cache_ttl", "cache_max_age", "cache_no_cache", "cache_no_store",

		// 头部相关参数
		"x_forwarded_for", "x_real_ip", "x_originating_ip", "x_remote_ip", "x_remote_addr",
		"x_client_ip", "x_forwarded_host", "x_forwarded_proto", "x_forwarded_scheme",
		"x_forwarded_server", "x_original_host", "x_host", "x_server_name",

		// 通用参数
		"param", "parameter", "arg", "argument", "value", "val", "var", "variable",
		"field", "property", "attribute", "option", "setting", "config", "configuration",
		"query", "search", "filter", "where", "select", "insert", "update", "delete",

		// 用户相关参数
		"user", "username", "userid", "login", "auth", "authentication", "session",
		"password", "pass", "pwd", "token", "credential", "profile", "account",
		"member", "customer", "person", "identity", "role", "permission", "access",

		// 数据相关参数
		"data", "content", "body", "payload", "input", "output", "source", "target",
		"destination", "file", "path", "filename", "filepath", "document", "text",
		"message", "info", "information", "detail", "record", "item", "object",

		// 中文参数
		"重定向", "转发", "代理", "缓存", "位置", "链接", "头部", "来源", "主机",
		"用户代理", "授权", "会话", "令牌", "密钥", "参数", "变量", "字段",
		"属性", "选项", "设置", "配置", "查询", "搜索", "过滤", "用户", "用户名",
		"密码", "登录", "认证", "凭据", "数据", "内容", "输入", "输出", "文件",
		"路径", "文档", "消息", "信息", "详情", "记录", "对象", "服务器", "客户端",
	}
}

// initializePatterns 初始化检测模式
func (d *HTTPHeaderInjectionDetector) initializePatterns() {
	// CRLF模式 - 检测CRLF注入相关的响应内容
	crlfPatternStrings := []string{
		// CRLF注入成功模式
		`(?i)set-cookie:.*injected`,
		`(?i)location:.*evil\.com`,
		`(?i)x-injected:.*true`,
		`(?i)content-type:.*text/html`,

		// CRLF字符检测
		`\r\n`,
		`%0d%0a`,
		`%250d%250a`,
		`\u000d\u000a`,

		// 头部注入成功模式
		`(?i)x-forwarded-for:.*127\.0\.0\.1`,
		`(?i)x-real-ip:.*127\.0\.0\.1`,
		`(?i)x-originating-ip:.*127\.0\.0\.1`,
		`(?i)authorization:.*bearer`,

		// 响应分割成功模式
		`(?i)http/1\.[01]\s+200\s+ok`,
		`(?i)http/1\.[01]\s+302\s+found`,
		`(?i)<script>alert\(`,
		`(?i)<html><body>`,

		// 中文CRLF模式
		`(?i)注入.*真`,
		`(?i)恶意\.com`,
		`(?i)set-cookie:.*注入`,
		`(?i)location:.*恶意`,
	}

	d.crlfPatterns = make([]*regexp.Regexp, 0, len(crlfPatternStrings))
	for _, pattern := range crlfPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.crlfPatterns = append(d.crlfPatterns, compiled)
		}
	}

	// 头部模式 - 检测头部注入相关的响应内容
	headerPatternStrings := []string{
		// 头部注入成功模式
		`(?i)x-forwarded-.*:`,
		`(?i)x-real-.*:`,
		`(?i)x-originating-.*:`,
		`(?i)x-remote-.*:`,
		`(?i)x-client-.*:`,
		`(?i)x-user.*:`,

		// 认证绕过模式
		`(?i)x-forwarded-user:.*admin`,
		`(?i)x-remote-user:.*admin`,
		`(?i)authorization:.*admin`,
		`(?i)x-api-key:.*admin`,

		// 缓存控制模式
		`(?i)cache-control:.*no-cache`,
		`(?i)cache-control:.*max-age`,
		`(?i)pragma:.*no-cache`,
		`(?i)expires:.*1970`,

		// 安全头部模式
		`(?i)x-frame-options:`,
		`(?i)x-xss-protection:`,
		`(?i)x-content-type-options:`,
		`(?i)strict-transport-security:`,

		// 重定向模式
		`(?i)location:.*evil`,
		`(?i)refresh:.*url=`,
		`(?i)x-redirect-to:`,
		`(?i)x-forward-to:`,

		// 中文头部模式
		`(?i)x-用户:.*管理员`,
		`(?i)location:.*恶意`,
		`(?i)x-注入:.*真`,
		`(?i)x-转发:`,
	}

	d.headerPatterns = make([]*regexp.Regexp, 0, len(headerPatternStrings))
	for _, pattern := range headerPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.headerPatterns = append(d.headerPatterns, compiled)
		}
	}

	// 响应模式 - 检测HTTP头部注入成功的响应特征
	responsePatternStrings := []string{
		// 成功注入指示器
		`(?i)(injection|exploit).*success`,
		`(?i)(bypass|circumvent).*success`,
		`(?i)(header|crlf).*injected`,
		`(?i)(response|http).*split`,
		`(?i)(cache|proxy).*poison`,

		// HTTP响应分割指示器
		`(?i)http/1\.[01].*200.*ok`,
		`(?i)http/1\.[01].*302.*found`,
		`(?i)http/1\.[01].*301.*moved`,
		`(?i)content-type:.*text/html`,
		`(?i)content-length:.*\d+`,

		// 缓存投毒指示器
		`(?i)cache.*hit`,
		`(?i)cache.*miss`,
		`(?i)x-cache:.*hit`,
		`(?i)x-cache:.*miss`,
		`(?i)vary:.*host`,

		// XSS注入指示器
		`(?i)<script>.*alert`,
		`(?i)<script>.*eval`,
		`(?i)<img.*onerror`,
		`(?i)<svg.*onload`,
		`(?i)javascript:`,

		// 重定向注入指示器
		`(?i)location:.*evil`,
		`(?i)location:.*attacker`,
		`(?i)location:.*malicious`,
		`(?i)refresh:.*url=.*evil`,

		// 中文响应模式
		`(?i)(注入|利用).*成功`,
		`(?i)(绕过|规避).*成功`,
		`(?i)(头部|响应).*注入`,
		`(?i)(缓存|代理).*投毒`,
		`(?i)location:.*恶意`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}

	// 错误模式 - 检测HTTP头部注入相关的错误信息
	errorPatternStrings := []string{
		// 通用HTTP错误
		`(?i)(http|header).*error`,
		`(?i)(invalid|illegal).*header`,
		`(?i)(malformed|corrupt).*request`,
		`(?i)(bad|invalid).*request`,
		`(?i)(syntax|format).*error`,

		// CRLF特定错误
		`(?i)crlf.*error`,
		`(?i)line.*break.*error`,
		`(?i)carriage.*return.*error`,
		`(?i)newline.*error`,
		`(?i)header.*injection.*error`,

		// 头部特定错误
		`(?i)header.*not.*allowed`,
		`(?i)header.*forbidden`,
		`(?i)header.*blocked`,
		`(?i)header.*filtered`,
		`(?i)header.*sanitized`,

		// 缓存特定错误
		`(?i)cache.*error`,
		`(?i)proxy.*error`,
		`(?i)upstream.*error`,
		`(?i)backend.*error`,
		`(?i)gateway.*error`,

		// 注入相关错误
		`(?i)(injection|inject).*detected`,
		`(?i)(malicious|suspicious).*header`,
		`(?i)(blocked|filtered).*request`,
		`(?i)(security|safety).*violation`,

		// 中文错误模式
		`(?i)(http|头部).*错误`,
		`(?i)(无效|非法).*头部`,
		`(?i)(恶意|可疑).*请求`,
		`(?i)(安全|防护).*违规`,
		`(?i)crlf.*错误`,
		`(?i)注入.*检测`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}
}
