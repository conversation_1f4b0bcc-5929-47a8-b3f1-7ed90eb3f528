package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestCommandInjectionDetectorBasicFunctionality 测试命令注入检测器基础功能
func TestCommandInjectionDetectorBasicFunctionality(t *testing.T) {
	detector := NewCommandInjectionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "command-injection-comprehensive", detector.GetID())
	assert.Equal(t, "命令注入漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "critical", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-78")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestCommandInjectionDetectorApplicability 测试命令注入检测器适用性
func TestCommandInjectionDetectorApplicability(t *testing.T) {
	detector := NewCommandInjectionDetector()

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/search",
				Method: "POST",
				Fields: map[string]string{
					"query": "text",
					"type":  "select",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的URL
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search?q=test&type=all",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试命令相关的URL
	cmdTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/admin/exec",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(cmdTarget))

	// 测试API端点
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/system/info",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（命令注入检测适用于所有Web目标）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(simpleTarget))
}

// TestCommandInjectionDetectorConfiguration 测试命令注入检测器配置
func TestCommandInjectionDetectorConfiguration(t *testing.T) {
	detector := NewCommandInjectionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 20*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.True(t, config.FollowRedirects) // 命令注入检测跟随重定向

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 30*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestCommandInjectionDetectorPayloads 测试命令注入检测器载荷
func TestCommandInjectionDetectorPayloads(t *testing.T) {
	detector := NewCommandInjectionDetector()

	// 检查Unix载荷
	assert.NotEmpty(t, detector.unixPayloads)
	assert.Greater(t, len(detector.unixPayloads), 20)

	// 检查Windows载荷
	assert.NotEmpty(t, detector.windowsPayloads)
	assert.Greater(t, len(detector.windowsPayloads), 15)

	// 检查时间延迟载荷
	assert.NotEmpty(t, detector.timeDelayPayloads)
	assert.Greater(t, len(detector.timeDelayPayloads), 10)

	// 检查盲注载荷
	assert.NotEmpty(t, detector.blindPayloads)
	assert.Greater(t, len(detector.blindPayloads), 5)

	// 检查命令指示器
	assert.NotEmpty(t, detector.commandIndicators)
	assert.Greater(t, len(detector.commandIndicators), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.Greater(t, len(detector.errorPatterns), 10)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.Greater(t, len(detector.responsePatterns), 15)

	// 检查特定的载荷
	assert.Contains(t, detector.unixPayloads, "; id")
	assert.Contains(t, detector.unixPayloads, "| whoami")
	assert.Contains(t, detector.windowsPayloads, "& dir")
	assert.Contains(t, detector.timeDelayPayloads, "; sleep 5")
}

// TestCommandInjectionDetectorCommandResponse 测试命令响应检查
func TestCommandInjectionDetectorCommandResponse(t *testing.T) {
	detector := NewCommandInjectionDetector()

	// 测试Unix id命令响应
	idResponse := "uid=0(root) gid=0(root) groups=0(root)"
	idPayload := "; id"
	confidence := detector.checkCommandResponse(idResponse, idPayload)
	assert.Greater(t, confidence, 0.7)

	// 测试Unix /etc/passwd响应
	passwdResponse := `root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin`
	passwdPayload := "; cat /etc/passwd"
	confidence2 := detector.checkCommandResponse(passwdResponse, passwdPayload)
	assert.Greater(t, confidence2, 0.6)

	// 测试Windows dir命令响应
	dirResponse := `Volume in drive C has no label.
 Volume Serial Number is 1234-5678

 Directory of C:\

01/01/2023  12:00 AM    <DIR>          Program Files
01/01/2023  12:00 AM    <DIR>          Windows`
	dirPayload := "& dir"
	confidence3 := detector.checkCommandResponse(dirResponse, dirPayload)
	assert.Greater(t, confidence3, 0.6)

	// 测试Windows whoami响应
	whoamiResponse := "nt authority\\system"
	whoamiPayload := "& whoami"
	confidence4 := detector.checkCommandResponse(whoamiResponse, whoamiPayload)
	assert.Greater(t, confidence4, 0.6)

	// 测试无命令注入特征的响应
	normalResponse := "Hello World"
	normalPayload := "; id"
	confidence5 := detector.checkCommandResponse(normalResponse, normalPayload)
	assert.Equal(t, 0.0, confidence5)
}

// TestCommandInjectionDetectorTimeDelayResponse 测试时间延迟响应检查
func TestCommandInjectionDetectorTimeDelayResponse(t *testing.T) {
	detector := NewCommandInjectionDetector()

	// 测试5秒延迟（期望的延迟）
	fiveSecondDelay := 5 * time.Second
	sleepPayload := "; sleep 5"
	confidence := detector.checkTimeDelayResponse(fiveSecondDelay, sleepPayload)
	assert.Greater(t, confidence, 0.7)

	// 测试4.5秒延迟（接近期望的延迟）
	nearDelayResponse := 4500 * time.Millisecond
	pingPayload := "& ping -n 5 127.0.0.1"
	confidence2 := detector.checkTimeDelayResponse(nearDelayResponse, pingPayload)
	assert.Greater(t, confidence2, 0.6)

	// 测试1秒延迟（太短）
	shortDelay := 1 * time.Second
	confidence3 := detector.checkTimeDelayResponse(shortDelay, sleepPayload)
	assert.Equal(t, 0.0, confidence3)

	// 测试15秒延迟（太长）
	longDelay := 15 * time.Second
	confidence4 := detector.checkTimeDelayResponse(longDelay, sleepPayload)
	assert.Equal(t, 0.0, confidence4)

	// 测试非延迟载荷
	nonDelayPayload := "; id"
	confidence5 := detector.checkTimeDelayResponse(fiveSecondDelay, nonDelayPayload)
	assert.Equal(t, 0.0, confidence5)
}

// TestCommandInjectionDetectorRiskScore 测试风险评分计算
func TestCommandInjectionDetectorRiskScore(t *testing.T) {
	detector := NewCommandInjectionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 8.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestCommandInjectionDetectorLifecycle 测试检测器生命周期
func TestCommandInjectionDetectorLifecycle(t *testing.T) {
	detector := NewCommandInjectionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkCommandInjectionDetectorCommandCheck 基准测试命令检查性能
func BenchmarkCommandInjectionDetectorCommandCheck(b *testing.B) {
	detector := NewCommandInjectionDetector()
	response := "uid=0(root) gid=0(root) groups=0(root)"
	payload := "; id"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkCommandResponse(response, payload)
	}
}

// BenchmarkCommandInjectionDetectorTimeDelayCheck 基准测试时间延迟检查性能
func BenchmarkCommandInjectionDetectorTimeDelayCheck(b *testing.B) {
	detector := NewCommandInjectionDetector()
	responseTime := 5 * time.Second
	payload := "; sleep 5"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkTimeDelayResponse(responseTime, payload)
	}
}
