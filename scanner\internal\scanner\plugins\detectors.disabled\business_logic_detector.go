package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// BusinessLogicDetector 业务逻辑检测器
// 支持支付逻辑漏洞、业务流程绕过、数据验证问题、逻辑缺陷等多种业务逻辑检测
type BusinessLogicDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	priceParameters    []string         // 价格参数
	quantityParameters []string         // 数量参数
	redirectParameters []string         // 重定向参数
	businessEndpoints  []string         // 业务端点
	logicPatterns      []*regexp.Regexp // 逻辑模式
	errorPatterns      []*regexp.Regexp // 错误模式
	successPatterns    []*regexp.Regexp // 成功模式
	httpClient         *http.Client
}

// NewBusinessLogicDetector 创建业务逻辑检测器
func NewBusinessLogicDetector() *BusinessLogicDetector {
	detector := &BusinessLogicDetector{
		id:          "business-logic-comprehensive",
		name:        "业务逻辑漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // 业务逻辑是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-840", "CWE-841", "CWE-842", "CWE-843", "CWE-20"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测业务逻辑漏洞，包括支付逻辑漏洞、业务流程绕过、数据验证问题、逻辑缺陷等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // 业务逻辑检测需要较长时间
		MaxRetries:      2,
		Concurrency:     2,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，响应内容适中
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *BusinessLogicDetector) GetID() string                     { return d.id }
func (d *BusinessLogicDetector) GetName() string                   { return d.name }
func (d *BusinessLogicDetector) GetCategory() string               { return d.category }
func (d *BusinessLogicDetector) GetSeverity() string               { return d.severity }
func (d *BusinessLogicDetector) GetCVE() []string                  { return d.cve }
func (d *BusinessLogicDetector) GetCWE() []string                  { return d.cwe }
func (d *BusinessLogicDetector) GetVersion() string                { return d.version }
func (d *BusinessLogicDetector) GetAuthor() string                 { return d.author }
func (d *BusinessLogicDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *BusinessLogicDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *BusinessLogicDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *BusinessLogicDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *BusinessLogicDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *BusinessLogicDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *BusinessLogicDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *BusinessLogicDetector) GetDependencies() []string         { return []string{} }
func (d *BusinessLogicDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *BusinessLogicDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *BusinessLogicDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *BusinessLogicDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *BusinessLogicDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 2
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *BusinessLogicDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *BusinessLogicDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.priceParameters) == 0 {
		return fmt.Errorf("价格参数列表不能为空")
	}
	if len(d.logicPatterns) == 0 {
		return fmt.Errorf("逻辑模式不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *BusinessLogicDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 业务逻辑检测适用于有业务功能的Web应用
	// 检查是否有表单、参数或业务相关的URL
	if d.hasBusinessFeatures(target) {
		return true
	}

	// 对于电商、支付、用户管理等业务相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	businessKeywords := []string{
		"shop", "cart", "order", "pay", "payment", "checkout", "buy", "purchase",
		"user", "profile", "account", "member", "customer", "admin", "manage",
		"api", "service", "business", "商城", "购物", "支付", "订单", "用户", "管理",
	}

	for _, keyword := range businessKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return false
}

// hasBusinessFeatures 检查是否有业务功能
func (d *BusinessLogicDetector) hasBusinessFeatures(target *plugins.ScanTarget) bool {
	// 检查表单中是否有业务相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			// 检查价格相关字段
			for _, priceParam := range d.priceParameters {
				if strings.Contains(fieldNameLower, strings.ToLower(priceParam)) {
					return true
				}
			}

			// 检查数量相关字段
			for _, quantityParam := range d.quantityParameters {
				if strings.Contains(fieldNameLower, strings.ToLower(quantityParam)) {
					return true
				}
			}

			// 检查重定向相关字段
			for _, redirectParam := range d.redirectParameters {
				if strings.Contains(fieldNameLower, strings.ToLower(redirectParam)) {
					return true
				}
			}
		}
	}

	// 检查链接中是否有业务相关参数
	for _, link := range target.Links {
		linkLower := strings.ToLower(link.URL)
		if strings.Contains(linkLower, "price=") ||
			strings.Contains(linkLower, "amount=") ||
			strings.Contains(linkLower, "quantity=") ||
			strings.Contains(linkLower, "redirect=") ||
			strings.Contains(linkLower, "return=") {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *BusinessLogicDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种业务逻辑检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 支付逻辑漏洞检测
	paymentEvidence, paymentConfidence, paymentPayload, paymentRequest, paymentResponse := d.detectPaymentLogicFlaws(ctx, target)
	if paymentConfidence > maxConfidence {
		maxConfidence = paymentConfidence
		vulnerablePayload = paymentPayload
		vulnerableRequest = paymentRequest
		vulnerableResponse = paymentResponse
	}
	evidence = append(evidence, paymentEvidence...)

	// 2. 业务流程绕过检测
	processEvidence, processConfidence, processPayload, processRequest, processResponse := d.detectBusinessProcessBypass(ctx, target)
	if processConfidence > maxConfidence {
		maxConfidence = processConfidence
		vulnerablePayload = processPayload
		vulnerableRequest = processRequest
		vulnerableResponse = processResponse
	}
	evidence = append(evidence, processEvidence...)

	// 3. 数据验证问题检测
	validationEvidence, validationConfidence, validationPayload, validationRequest, validationResponse := d.detectDataValidationFlaws(ctx, target)
	if validationConfidence > maxConfidence {
		maxConfidence = validationConfidence
		vulnerablePayload = validationPayload
		vulnerableRequest = validationRequest
		vulnerableResponse = validationResponse
	}
	evidence = append(evidence, validationEvidence...)

	// 4. 逻辑缺陷检测
	logicEvidence, logicConfidence, logicPayload, logicRequest, logicResponse := d.detectLogicFlaws(ctx, target)
	if logicConfidence > maxConfidence {
		maxConfidence = logicConfidence
		vulnerablePayload = logicPayload
		vulnerableRequest = logicRequest
		vulnerableResponse = logicResponse
	}
	evidence = append(evidence, logicEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "业务逻辑漏洞",
		Description:       "检测到业务逻辑漏洞，可能允许攻击者绕过业务规则或操纵业务流程",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "实施适当的业务逻辑验证，加强数据验证和业务规则检查，使用服务端验证",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Business_logic_vulnerability", "https://cwe.mitre.org/data/definitions/840.html"},
		Tags:              []string{"business-logic", "logic-flaw", "web", "high"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *BusinessLogicDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的参数进行验证
	verificationParams := map[string]string{
		"price":    "0.01",
		"amount":   "1",
		"quantity": "1",
		"total":    "0.01",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	// 发送验证请求
	resp, err := d.sendBusinessRequest(ctx, target.URL, verificationParams)
	if err != nil {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "business-logic-verification",
			Notes:      fmt.Sprintf("验证请求失败: %v", err),
			VerifiedAt: time.Now(),
		}, nil
	}

	// 检查业务逻辑响应特征
	responseConfidence := d.checkBusinessLogicResponse(resp, verificationParams)
	if responseConfidence > 0.5 {
		verificationConfidence += 0.4
		evidence = append(evidence, plugins.Evidence{
			Type:        "response",
			Description: fmt.Sprintf("验证参数触发了业务逻辑响应"),
			Content:     resp,
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	isVerified := verificationConfidence >= 0.4

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "business-logic-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用业务逻辑验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *BusinessLogicDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("business_logic_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *BusinessLogicDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (业务逻辑通常是高风险)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializeParameters 初始化参数列表
func (d *BusinessLogicDetector) initializeParameters() {
	// 价格参数
	d.priceParameters = []string{
		"price", "amount", "cost", "total", "sum", "value", "money",
		"fee", "charge", "rate", "tariff", "fare", "toll", "levy",
		"价格", "金额", "总价", "费用", "收费", "价值", "货币",
		"单价", "总额", "付费", "计费", "税费", "运费", "手续费",
	}

	// 数量参数
	d.quantityParameters = []string{
		"quantity", "qty", "count", "number", "num", "amount", "size",
		"volume", "weight", "length", "width", "height", "depth",
		"数量", "个数", "计数", "总数", "份数", "件数", "重量",
		"体积", "长度", "宽度", "高度", "深度", "尺寸", "规格",
	}

	// 重定向参数
	d.redirectParameters = []string{
		"redirect", "return", "callback", "next", "continue", "forward",
		"goto", "url", "link", "target", "destination", "location",
		"重定向", "返回", "回调", "下一步", "继续", "转发",
		"跳转", "链接", "目标", "目的地", "位置", "地址",
	}

	// 业务端点
	d.businessEndpoints = []string{
		// 支付相关
		"/pay", "/payment", "/checkout", "/order", "/cart", "/buy",
		"/purchase", "/billing", "/invoice", "/receipt", "/transaction",

		// 用户相关
		"/user", "/profile", "/account", "/member", "/customer",
		"/register", "/signup", "/login", "/signin", "/logout",

		// 管理相关
		"/admin", "/manage", "/control", "/dashboard", "/panel",
		"/settings", "/config", "/preferences", "/options",

		// API相关
		"/api/pay", "/api/order", "/api/user", "/api/admin",
		"/api/cart", "/api/checkout", "/api/payment", "/api/billing",

		// 业务流程
		"/process", "/workflow", "/step", "/stage", "/phase",
		"/submit", "/confirm", "/verify", "/validate", "/approve",

		// 中文端点
		"/支付", "/订单", "/购物车", "/结账", "/用户", "/会员",
		"/管理", "/设置", "/配置", "/流程", "/提交", "/确认",
	}
}

// initializePatterns 初始化检测模式
func (d *BusinessLogicDetector) initializePatterns() {
	// 逻辑模式 - 检测业务逻辑相关的响应内容
	logicPatternStrings := []string{
		// 支付成功指示器
		`(?i)(payment\s+successful|payment\s+completed|支付成功|付款成功)`,
		`(?i)(order\s+placed|order\s+confirmed|订单已下单|订单确认)`,
		`(?i)(transaction\s+successful|交易成功|交易完成)`,
		`(?i)(purchase\s+successful|购买成功|购买完成)`,

		// 价格相关指示器
		`(?i)(total\s*[:=]\s*\$?\d+\.?\d*|总价\s*[:=]\s*\$?\d+\.?\d*)`,
		`(?i)(amount\s*[:=]\s*\$?\d+\.?\d*|金额\s*[:=]\s*\$?\d+\.?\d*)`,
		`(?i)(price\s*[:=]\s*\$?\d+\.?\d*|价格\s*[:=]\s*\$?\d+\.?\d*)`,
		`(?i)(cost\s*[:=]\s*\$?\d+\.?\d*|费用\s*[:=]\s*\$?\d+\.?\d*)`,

		// 数量相关指示器
		`(?i)(quantity\s*[:=]\s*\d+|数量\s*[:=]\s*\d+)`,
		`(?i)(qty\s*[:=]\s*\d+|个数\s*[:=]\s*\d+)`,
		`(?i)(count\s*[:=]\s*\d+|计数\s*[:=]\s*\d+)`,

		// 业务流程指示器
		`(?i)(step\s+\d+|阶段\s+\d+|步骤\s+\d+)`,
		`(?i)(process\s+completed|流程完成|处理完成)`,
		`(?i)(workflow\s+status|工作流状态|流程状态)`,

		// 权限相关指示器
		`(?i)(access\s+granted|访问授权|权限通过)`,
		`(?i)(permission\s+granted|权限授予|授权成功)`,
		`(?i)(role\s+assigned|角色分配|角色指定)`,

		// 验证相关指示器
		`(?i)(validation\s+passed|验证通过|校验成功)`,
		`(?i)(verification\s+successful|验证成功|核实成功)`,
		`(?i)(check\s+passed|检查通过|检验成功)`,

		// JSON响应指示器
		`(?i)("status":\s*"success"|"状态":\s*"成功")`,
		`(?i)("result":\s*"ok"|"结果":\s*"正确")`,
		`(?i)("error":\s*false|"错误":\s*false)`,
		`(?i)("valid":\s*true|"有效":\s*true)`,
	}

	d.logicPatterns = make([]*regexp.Regexp, 0, len(logicPatternStrings))
	for _, pattern := range logicPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.logicPatterns = append(d.logicPatterns, compiled)
		}
	}

	// 错误模式 - 检测业务逻辑错误
	errorPatternStrings := []string{
		// 支付错误
		`(?i)(payment\s+failed|payment\s+error|支付失败|付款失败)`,
		`(?i)(insufficient\s+funds|余额不足|资金不足)`,
		`(?i)(invalid\s+amount|金额无效|数额无效)`,
		`(?i)(transaction\s+failed|交易失败|事务失败)`,

		// 验证错误
		`(?i)(validation\s+failed|验证失败|校验失败)`,
		`(?i)(invalid\s+input|输入无效|参数无效)`,
		`(?i)(data\s+error|数据错误|数据异常)`,
		`(?i)(format\s+error|格式错误|格式异常)`,

		// 权限错误
		`(?i)(access\s+denied|访问被拒绝|权限不足)`,
		`(?i)(unauthorized|未授权|无权限)`,
		`(?i)(forbidden|禁止访问|访问禁止)`,

		// 业务规则错误
		`(?i)(business\s+rule\s+violation|业务规则违反|规则冲突)`,
		`(?i)(constraint\s+violation|约束违反|限制冲突)`,
		`(?i)(limit\s+exceeded|超出限制|限额超出)`,

		// 通用错误
		`(?i)(error|错误|异常|失败)`,
		`(?i)(exception|例外|异常情况)`,
		`(?i)(failure|失败|故障)`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 成功模式 - 检测业务逻辑成功
	successPatternStrings := []string{
		// 成功指示器
		`(?i)(success|successful|成功|完成)`,
		`(?i)(completed|完成|结束)`,
		`(?i)(confirmed|确认|证实)`,
		`(?i)(approved|批准|通过)`,
		`(?i)(accepted|接受|同意)`,
		`(?i)(valid|有效|正确)`,
		`(?i)(ok|okay|正常)`,

		// 状态指示器
		`(?i)(status.*success|状态.*成功)`,
		`(?i)(result.*ok|结果.*正确)`,
		`(?i)(response.*success|响应.*成功)`,

		// 重定向指示器
		`(?i)(redirect|重定向|跳转)`,
		`(?i)(location|位置|地址)`,
		`(?i)(forward|转发|前进)`,
	}

	d.successPatterns = make([]*regexp.Regexp, 0, len(successPatternStrings))
	for _, pattern := range successPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.successPatterns = append(d.successPatterns, compiled)
		}
	}
}
