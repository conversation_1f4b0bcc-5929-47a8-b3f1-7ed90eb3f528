package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectJavaDeserialization 检测Java反序列化
func (d *DeserializationDetector) detectJavaDeserialization(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Java反序列化载荷
	for _, payload := range d.javaPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送Java反序列化请求
		resp, err := d.sendDeserializationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查Java反序列化响应
		confidence := d.checkJavaDeserializationResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("Java反序列化: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "java-deserialization",
				Description: fmt.Sprintf("发现Java反序列化: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractDeserializationEvidence(resp, "java-deserialization"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDotnetDeserialization 检测.NET反序列化
func (d *DeserializationDetector) detectDotnetDeserialization(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试.NET反序列化载荷
	for _, payload := range d.dotnetPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送.NET反序列化请求
		resp, err := d.sendDeserializationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查.NET反序列化响应
		confidence := d.checkDotnetDeserializationResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf(".NET反序列化: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "dotnet-deserialization",
				Description: fmt.Sprintf("发现.NET反序列化: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractDeserializationEvidence(resp, "dotnet-deserialization"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 250)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPythonDeserialization 检测Python反序列化
func (d *DeserializationDetector) detectPythonDeserialization(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Python反序列化载荷
	for _, payload := range d.pythonPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送Python反序列化请求
		resp, err := d.sendDeserializationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查Python反序列化响应
		confidence := d.checkPythonDeserializationResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("Python反序列化: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "python-deserialization",
				Description: fmt.Sprintf("发现Python反序列化: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractDeserializationEvidence(resp, "python-deserialization"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 250)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPHPDeserialization 检测PHP反序列化
func (d *DeserializationDetector) detectPHPDeserialization(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试PHP反序列化载荷
	for _, payload := range d.phpPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送PHP反序列化请求
		resp, err := d.sendDeserializationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查PHP反序列化响应
		confidence := d.checkPHPDeserializationResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("PHP反序列化: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "php-deserialization",
				Description: fmt.Sprintf("发现PHP反序列化: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractDeserializationEvidence(resp, "php-deserialization"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 250)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendDeserializationRequest 发送反序列化请求
func (d *DeserializationDetector) sendDeserializationRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendDeserializationGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST参数注入
	postResp, err := d.sendDeserializationPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 尝试JSON载荷注入
	jsonResp, err := d.sendDeserializationJSONRequest(ctx, targetURL, payload)
	if err == nil && jsonResp != "" {
		return jsonResp, nil
	}

	// 返回GET响应（即使有错误）
	if getResp != "" {
		return getResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendDeserializationGETRequest 发送反序列化GET请求
func (d *DeserializationDetector) sendDeserializationGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendDeserializationPOSTRequest 发送反序列化POST请求
func (d *DeserializationDetector) sendDeserializationPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造POST数据
	postData := url.Values{}

	for _, param := range d.testParameters {
		postData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendDeserializationJSONRequest 发送反序列化JSON请求
func (d *DeserializationDetector) sendDeserializationJSONRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造JSON数据
	jsonData := fmt.Sprintf(`{"data":"%s","object":"%s","payload":"%s"}`, payload, payload, payload)

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(jsonData))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkJavaDeserializationResponse 检查Java反序列化响应
func (d *DeserializationDetector) checkJavaDeserializationResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示反序列化错误
	} else if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示反序列化成功
	}

	// 检查Java模式匹配
	for _, pattern := range d.javaPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查Java特定错误
	javaErrors := []string{
		"java.io.streamcorruptedexception", "java.io.invalidclassexception",
		"java.lang.classnotfoundexception", "java.io.optionaldataexception",
		"java.lang.classcastexception", "java.lang.nosuchmethodexception",
		"java.security.accesscontrolexception", "java.lang.illegalaccessexception",
		"javax.naming.namingexception", "javax.naming.communicationexception",
		"java序列化异常", "类加载异常", "方法调用异常", "访问控制异常",
	}

	for _, error := range javaErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // Java特定错误的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkDotnetDeserializationResponse 检查.NET反序列化响应
func (d *DeserializationDetector) checkDotnetDeserializationResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示反序列化错误
	} else if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示反序列化成功
	}

	// 检查.NET模式匹配
	for _, pattern := range d.dotnetPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查.NET特定错误
	dotnetErrors := []string{
		"system.runtime.serialization.serializationexception",
		"system.reflection.targetinvocationexception",
		"system.typeloadexception", "system.methodaccessexception",
		"system.fieldaccessexception", "system.memberaccessexception",
		"system.argumentexception", "system.invalidoperationexception",
		"viewstate validation", "mac validation failed",
		"dotnet序列化异常", "类型加载异常", "方法访问异常", "视图状态验证",
	}

	for _, error := range dotnetErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // .NET特定错误的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkPythonDeserializationResponse 检查Python反序列化响应
func (d *DeserializationDetector) checkPythonDeserializationResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示反序列化错误
	} else if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示反序列化成功
	}

	// 检查Python模式匹配
	for _, pattern := range d.pythonPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查Python特定错误
	pythonErrors := []string{
		"pickle.picklingerror", "pickle.unpicklingerror",
		"attributeerror", "typeerror", "valueerror", "importerror",
		"nameerror", "syntaxerror", "runtimeerror", "systemexit",
		"modulenotfounderror", "filenotfounderror",
		"python序列化异常", "pickle异常", "模块导入异常", "属性错误",
	}

	for _, error := range pythonErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // Python特定错误的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkPHPDeserializationResponse 检查PHP反序列化响应
func (d *DeserializationDetector) checkPHPDeserializationResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示反序列化错误
	} else if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示反序列化成功
	}

	// 检查PHP模式匹配
	for _, pattern := range d.phpPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查PHP特定错误
	phpErrors := []string{
		"fatal error", "parse error", "notice", "warning", "deprecated",
		"call to undefined", "class not found", "function not found",
		"unserialize(): error", "unserialize(): offset",
		"object of class", "could not be converted",
		"php致命错误", "解析错误", "类未找到", "函数未找到", "反序列化错误",
	}

	for _, error := range phpErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // PHP特定错误的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// extractDeserializationEvidence 提取反序列化证据
func (d *DeserializationDetector) extractDeserializationEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var deserializationLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "java-deserialization":
			if strings.Contains(lineLower, "java.") ||
				strings.Contains(lineLower, "javax.") ||
				strings.Contains(lineLower, "org.apache") ||
				strings.Contains(lineLower, "org.springframework") ||
				strings.Contains(lineLower, "serializ") ||
				strings.Contains(lineLower, "deserializ") ||
				strings.Contains(lineLower, "classnotfound") ||
				strings.Contains(lineLower, "classcast") ||
				strings.Contains(lineLower, "序列化") ||
				strings.Contains(lineLower, "反序列化") ||
				strings.Contains(lineLower, "类加载") ||
				strings.Contains(lineLower, "方法调用") {
				deserializationLines = append(deserializationLines, line)
			}
		case "dotnet-deserialization":
			if strings.Contains(lineLower, "system.") ||
				strings.Contains(lineLower, "microsoft.") ||
				strings.Contains(lineLower, "binaryformatter") ||
				strings.Contains(lineLower, "viewstate") ||
				strings.Contains(lineLower, "serializ") ||
				strings.Contains(lineLower, "deserializ") ||
				strings.Contains(lineLower, "typeload") ||
				strings.Contains(lineLower, "methodaccess") ||
				strings.Contains(lineLower, "序列化") ||
				strings.Contains(lineLower, "反序列化") ||
				strings.Contains(lineLower, "类型加载") ||
				strings.Contains(lineLower, "方法访问") {
				deserializationLines = append(deserializationLines, line)
			}
		case "python-deserialization":
			if strings.Contains(lineLower, "pickle") ||
				strings.Contains(lineLower, "cpickle") ||
				strings.Contains(lineLower, "_pickle") ||
				strings.Contains(lineLower, "yaml") ||
				strings.Contains(lineLower, "serializ") ||
				strings.Contains(lineLower, "deserializ") ||
				strings.Contains(lineLower, "attributeerror") ||
				strings.Contains(lineLower, "typeerror") ||
				strings.Contains(lineLower, "序列化") ||
				strings.Contains(lineLower, "反序列化") ||
				strings.Contains(lineLower, "模块导入") ||
				strings.Contains(lineLower, "属性错误") {
				deserializationLines = append(deserializationLines, line)
			}
		case "php-deserialization":
			if strings.Contains(lineLower, "unserialize") ||
				strings.Contains(lineLower, "serialize") ||
				strings.Contains(lineLower, "o:") ||
				strings.Contains(lineLower, "a:") ||
				strings.Contains(lineLower, "s:") ||
				strings.Contains(lineLower, "fatal error") ||
				strings.Contains(lineLower, "parse error") ||
				strings.Contains(lineLower, "序列化") ||
				strings.Contains(lineLower, "反序列化") ||
				strings.Contains(lineLower, "致命错误") ||
				strings.Contains(lineLower, "解析错误") {
				deserializationLines = append(deserializationLines, line)
			}
		default:
			if strings.Contains(lineLower, "serializ") ||
				strings.Contains(lineLower, "deserializ") ||
				strings.Contains(lineLower, "marshal") ||
				strings.Contains(lineLower, "unmarshal") ||
				strings.Contains(lineLower, "pickle") ||
				strings.Contains(lineLower, "object") ||
				strings.Contains(lineLower, "序列化") ||
				strings.Contains(lineLower, "反序列化") ||
				strings.Contains(lineLower, "对象") ||
				strings.Contains(lineLower, "载荷") {
				deserializationLines = append(deserializationLines, line)
			}
		}

		if len(deserializationLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(deserializationLines) > 0 {
		return strings.Join(deserializationLines, "\n")
	}

	// 如果没有找到特定的反序列化信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
