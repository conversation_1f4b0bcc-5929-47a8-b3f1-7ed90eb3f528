package detectors

import (
	"testing"
	"time"

	"scanner/internal/scanner/plugins"
)

// TestRaceConditionDetectorBasicFunctionality 测试竞态条件检测器基本功能
func TestRaceConditionDetectorBasicFunctionality(t *testing.T) {
	detector := NewRaceConditionDetector()

	// 测试基本信息
	if detector.GetID() != "race-condition-comprehensive" {
		t.<PERSON><PERSON>("Expected ID 'race-condition-comprehensive', got '%s'", detector.GetID())
	}

	if detector.GetName() != "竞态条件漏洞综合检测器" {
		t.<PERSON><PERSON><PERSON>("Expected name '竞态条件漏洞综合检测器', got '%s'", detector.GetName())
	}

	if detector.GetCategory() != "web" {
		t.<PERSON>rf("Expected category 'web', got '%s'", detector.GetCategory())
	}

	if detector.GetSeverity() != "high" {
		t.<PERSON><PERSON>rf("Expected severity 'high', got '%s'", detector.GetSeverity())
	}

	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled by default")
	}

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	expectedTypes := []string{"http", "https"}
	if len(targetTypes) != len(expectedTypes) {
		t.Errorf("Expected %d target types, got %d", len(expectedTypes), len(targetTypes))
	}

	// 测试端口
	ports := detector.GetRequiredPorts()
	if len(ports) == 0 {
		t.Error("Expected some required ports")
	}

	// 测试服务
	services := detector.GetRequiredServices()
	expectedServices := []string{"http", "https", "web", "api"}
	if len(services) != len(expectedServices) {
		t.Errorf("Expected %d services, got %d", len(expectedServices), len(services))
	}
}

// TestRaceConditionDetectorApplicability 测试竞态条件检测器适用性
func TestRaceConditionDetectorApplicability(t *testing.T) {
	detector := NewRaceConditionDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "HTTP URL目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有竞态条件关键词的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com/race/condition",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有并发关键词的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com/concurrent/access",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有表单的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Forms: []plugins.FormInfo{
					{
						Action: "/submit",
						Method: "POST",
						Fields: map[string]string{"race": "text"},
					},
				},
			},
			expected: true,
		},
		{
			name: "有并发技术栈的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Technologies: []plugins.TechnologyInfo{
					{Name: "Java", Version: "11", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "非Web目标",
			target: &plugins.ScanTarget{
				Type:     "ip",
				Protocol: "tcp",
				Port:     22,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.IsApplicable(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestRaceConditionDetectorConfiguration 测试竞态条件检测器配置
func TestRaceConditionDetectorConfiguration(t *testing.T) {
	detector := NewRaceConditionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	if config.Timeout != 20*time.Second {
		t.Errorf("Expected timeout 20s, got %v", config.Timeout)
	}

	if config.MaxRetries != 1 {
		t.Errorf("Expected max retries 1, got %d", config.MaxRetries)
	}

	if config.Concurrency != 10 {
		t.Errorf("Expected concurrency 10, got %d", config.Concurrency)
	}

	if config.RateLimit != 10 {
		t.Errorf("Expected rate limit 10, got %d", config.RateLimit)
	}

	if !config.FollowRedirects {
		t.Error("Expected FollowRedirects to be true")
	}

	if config.VerifySSL {
		t.Error("Expected VerifySSL to be false")
	}

	if config.MaxResponseSize != 2*1024*1024 {
		t.Errorf("Expected max response size 2MB, got %d", config.MaxResponseSize)
	}

	// 测试配置更新
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      2,
		Concurrency:     15,
		RateLimit:       15,
		FollowRedirects: false,
		VerifySSL:       true,
		MaxResponseSize: 5 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	if err != nil {
		t.Errorf("Failed to set configuration: %v", err)
	}

	updatedConfig := detector.GetConfiguration()
	if updatedConfig.Timeout != 30*time.Second {
		t.Errorf("Expected updated timeout 30s, got %v", updatedConfig.Timeout)
	}
}

// TestRaceConditionDetectorTimeWindowPayloads 测试时间窗口载荷
func TestRaceConditionDetectorTimeWindowPayloads(t *testing.T) {
	detector := NewRaceConditionDetector()

	if len(detector.timeWindowPayloads) == 0 {
		t.Error("Expected some time window payloads")
	}

	// 检查是否包含关键的时间窗口载荷
	expectedPayloads := []string{
		"sleep(1)", "Thread.sleep(1000)", "time.sleep(1)", "setTimeout(1000)",
		"WAITFOR DELAY '00:00:01'", "pg_sleep(1)", "race_window", "time_window",
		"timestamp", "current_time", "时间窗口", "时间竞争",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.timeWindowPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find time window payload '%s'", expected)
		}
	}
}

// TestRaceConditionDetectorResourceRacePayloads 测试资源竞争载荷
func TestRaceConditionDetectorResourceRacePayloads(t *testing.T) {
	detector := NewRaceConditionDetector()

	if len(detector.resourceRacePayloads) == 0 {
		t.Error("Expected some resource race payloads")
	}

	// 检查是否包含关键的资源竞争载荷
	expectedPayloads := []string{
		"file_lock", "mutex_lock", "semaphore_lock", "database_lock",
		"connection_pool", "thread_pool", "deadlock", "lock_timeout",
		"资源竞争", "文件锁", "数据库锁", "连接池",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.resourceRacePayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find resource race payload '%s'", expected)
		}
	}
}

// TestRaceConditionDetectorStateRacePayloads 测试状态竞争载荷
func TestRaceConditionDetectorStateRacePayloads(t *testing.T) {
	detector := NewRaceConditionDetector()

	if len(detector.stateRacePayloads) == 0 {
		t.Error("Expected some state race payloads")
	}

	// 检查是否包含关键的状态竞争载荷
	expectedPayloads := []string{
		"state_variable", "counter", "sequence_number", "session_state",
		"transaction_state", "flag", "状态竞争", "计数器", "会话状态",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.stateRacePayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find state race payload '%s'", expected)
		}
	}
}

// TestRaceConditionDetectorConcurrencyPayloads 测试并发控制载荷
func TestRaceConditionDetectorConcurrencyPayloads(t *testing.T) {
	detector := NewRaceConditionDetector()

	if len(detector.concurrencyPayloads) == 0 {
		t.Error("Expected some concurrency payloads")
	}

	// 检查是否包含关键的并发控制载荷
	expectedPayloads := []string{
		"mutex", "semaphore", "atomic", "thread_sync", "process_sync",
		"concurrent_queue", "producer_consumer", "actor_model",
		"并发控制", "互斥锁", "信号量", "原子操作", "线程同步",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.concurrencyPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find concurrency payload '%s'", expected)
		}
	}
}

// TestRaceConditionDetectorTestParameters 测试测试参数
func TestRaceConditionDetectorTestParameters(t *testing.T) {
	detector := NewRaceConditionDetector()

	if len(detector.testParameters) == 0 {
		t.Error("Expected some test parameters")
	}

	// 检查是否包含关键的测试参数
	expectedParams := []string{
		"race", "concurrent", "parallel", "thread", "process", "lock",
		"mutex", "semaphore", "atomic", "sync", "time", "state",
		"竞态", "并发", "并行", "线程", "进程", "锁", "时间", "状态",
	}

	for _, expected := range expectedParams {
		found := false
		for _, param := range detector.testParameters {
			if param == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find test parameter '%s'", expected)
		}
	}
}

// TestRaceConditionDetectorPatterns 测试检测模式
func TestRaceConditionDetectorPatterns(t *testing.T) {
	detector := NewRaceConditionDetector()

	// 测试竞态条件模式
	if len(detector.racePatterns) == 0 {
		t.Error("Expected some race patterns")
	}

	// 测试时间相关模式
	if len(detector.timePatterns) == 0 {
		t.Error("Expected some time patterns")
	}

	// 测试资源竞争模式
	if len(detector.resourcePatterns) == 0 {
		t.Error("Expected some resource patterns")
	}

	// 测试状态不一致模式
	if len(detector.statePatterns) == 0 {
		t.Error("Expected some state patterns")
	}

	// 测试并发控制模式
	if len(detector.concurrencyPatterns) == 0 {
		t.Error("Expected some concurrency patterns")
	}
}

// TestRaceConditionDetectorRaceFeatures 测试竞态条件功能检查
func TestRaceConditionDetectorRaceFeatures(t *testing.T) {
	detector := NewRaceConditionDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "有并发头部的目标",
			target: &plugins.ScanTarget{
				Headers: map[string]string{
					"X-Concurrent": "true",
				},
			},
			expected: true,
		},
		{
			name: "有Java技术栈的目标",
			target: &plugins.ScanTarget{
				Technologies: []plugins.TechnologyInfo{
					{Name: "Java", Version: "11", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有并发链接的目标",
			target: &plugins.ScanTarget{
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/concurrent", Text: "Concurrent Access"},
				},
			},
			expected: true,
		},
		{
			name: "有竞态条件字段的表单目标",
			target: &plugins.ScanTarget{
				Forms: []plugins.FormInfo{
					{
						Fields: map[string]string{"race_condition": "text"},
					},
				},
			},
			expected: true,
		},
		{
			name: "普通目标",
			target: &plugins.ScanTarget{
				URL: "https://example.com",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.hasRaceConditionFeatures(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestRaceConditionDetectorRiskScore 测试风险评分
func TestRaceConditionDetectorRiskScore(t *testing.T) {
	detector := NewRaceConditionDetector()

	testCases := []struct {
		confidence float64
		expected   float64
	}{
		{0.0, 0.0},
		{0.5, 4.0},
		{0.8, 6.4},
		{1.0, 8.0},
	}

	for _, tc := range testCases {
		score := detector.calculateRiskScore(tc.confidence)
		// 使用浮点数比较的容差
		if score < tc.expected-0.01 || score > tc.expected+0.01 {
			t.Errorf("Expected risk score %.1f for confidence %.1f, got %.1f", tc.expected, tc.confidence, score)
		}
	}
}

// TestRaceConditionDetectorLifecycle 测试检测器生命周期
func TestRaceConditionDetectorLifecycle(t *testing.T) {
	detector := NewRaceConditionDetector()

	// 测试初始化
	err := detector.Initialize()
	if err != nil {
		t.Errorf("Failed to initialize detector: %v", err)
	}

	// 测试验证
	err = detector.Validate()
	if err != nil {
		t.Errorf("Detector validation failed: %v", err)
	}

	// 测试启用/禁用
	detector.SetEnabled(false)
	if detector.IsEnabled() {
		t.Error("Expected detector to be disabled")
	}

	detector.SetEnabled(true)
	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled")
	}

	// 测试清理
	err = detector.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup detector: %v", err)
	}
}
