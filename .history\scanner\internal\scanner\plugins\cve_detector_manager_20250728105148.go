package plugins

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"scanner/internal/scanner/plugins/detectors"
	"scanner/pkg/logger"
)

// CVEDetectorManager CVE检测器管理器
// 专门管理CVE相关的检测器插件，提供CVE特定的功能
type CVEDetectorManager struct {
	// 基础检测器管理器
	*DetectorManager
	
	// CVE特定索引
	cveDetectors    map[string]detectors.CVEDetectorInterface // CVE ID -> 检测器
	productIndex    map[string][]string                       // 产品 -> CVE检测器ID列表
	severityIndex   map[string][]string                       // 严重程度 -> CVE检测器ID列表
	yearIndex       map[int][]string                          // 年份 -> CVE检测器ID列表
	
	// 统计信息
	cveStats *CVEDetectorStats
	
	// 配置
	config *CVEDetectorManagerConfig
	
	// 并发控制
	mutex sync.RWMutex
}

// CVEDetectorManagerConfig CVE检测器管理器配置
type CVEDetectorManagerConfig struct {
	*DetectorManagerConfig
	
	// CVE特定配置
	EnableYearFiltering    bool     `json:"enable_year_filtering"`    // 启用年份过滤
	MinCVSSScore          float64  `json:"min_cvss_score"`           // 最小CVSS分数
	MaxCVEAge             int      `json:"max_cve_age"`              // 最大CVE年龄（年）
	EnabledSeverities     []string `json:"enabled_severities"`       // 启用的严重程度
	EnabledCategories     []string `json:"enabled_categories"`       // 启用的分类
	AutoLoadCVEDetectors  bool     `json:"auto_load_cve_detectors"`  // 自动加载CVE检测器
	CVEDetectorPaths      []string `json:"cve_detector_paths"`       // CVE检测器路径
	EnablePoCVerification bool     `json:"enable_poc_verification"`  // 启用PoC验证
	PoCTimeout            time.Duration `json:"poc_timeout"`          // PoC超时时间
}

// CVEDetectorStats CVE检测器统计信息
type CVEDetectorStats struct {
	TotalCVEDetectors     int                    `json:"total_cve_detectors"`
	DetectorsByYear       map[int]int            `json:"detectors_by_year"`
	DetectorsBySeverity   map[string]int         `json:"detectors_by_severity"`
	DetectorsByCategory   map[string]int         `json:"detectors_by_category"`
	DetectorsWithPoC      int                    `json:"detectors_with_poc"`
	DetectorsWithExploit  int                    `json:"detectors_with_exploit"`
	LastUpdated           time.Time              `json:"last_updated"`
}

// CVEDetectionRequest CVE检测请求
type CVEDetectionRequest struct {
	Target            *ScanTarget `json:"target"`
	CVEFilters        []string    `json:"cve_filters"`        // CVE ID过滤
	SeverityFilters   []string    `json:"severity_filters"`   // 严重程度过滤
	CategoryFilters   []string    `json:"category_filters"`   // 分类过滤
	YearFilters       []int       `json:"year_filters"`       // 年份过滤
	ProductFilters    []string    `json:"product_filters"`    // 产品过滤
	EnablePoC         bool        `json:"enable_poc"`         // 启用PoC验证
	MaxDetectors      int         `json:"max_detectors"`      // 最大检测器数量
	Timeout           time.Duration `json:"timeout"`          // 超时时间
}

// CVEDetectionResponse CVE检测响应
type CVEDetectionResponse struct {
	TotalDetectors    int                                    `json:"total_detectors"`
	ExecutedDetectors int                                    `json:"executed_detectors"`
	Results           []*detectors.CVEDetectionResult       `json:"results"`
	Vulnerabilities   []*detectors.CVEDetectionResult       `json:"vulnerabilities"`
	ExecutionTime     time.Duration                         `json:"execution_time"`
	Errors            []string                              `json:"errors"`
	Statistics        *CVEDetectionStatistics               `json:"statistics"`
}

// CVEDetectionStatistics CVE检测统计
type CVEDetectionStatistics struct {
	VulnerabilitiesBySeverity map[string]int `json:"vulnerabilities_by_severity"`
	VulnerabilitiesByCategory map[string]int `json:"vulnerabilities_by_category"`
	VulnerabilitiesByYear     map[int]int    `json:"vulnerabilities_by_year"`
	AverageConfidence         float64        `json:"average_confidence"`
	HighConfidenceCount       int            `json:"high_confidence_count"`
	PoCExecutedCount          int            `json:"poc_executed_count"`
}

// NewCVEDetectorManager 创建CVE检测器管理器
func NewCVEDetectorManager(config *CVEDetectorManagerConfig) *CVEDetectorManager {
	// 创建基础检测器管理器
	baseManager := NewDetectorManager(config.DetectorManagerConfig, logger.GetLogger())
	
	manager := &CVEDetectorManager{
		DetectorManager: baseManager,
		cveDetectors:    make(map[string]detectors.CVEDetectorInterface),
		productIndex:    make(map[string][]string),
		severityIndex:   make(map[string][]string),
		yearIndex:       make(map[int][]string),
		config:          config,
		cveStats: &CVEDetectorStats{
			DetectorsByYear:     make(map[int]int),
			DetectorsBySeverity: make(map[string]int),
			DetectorsByCategory: make(map[string]int),
			LastUpdated:         time.Now(),
		},
	}
	
	// 自动加载内置CVE检测器
	if config.AutoLoadCVEDetectors {
		manager.loadBuiltinCVEDetectors()
	}
	
	return manager
}

// RegisterCVEDetector 注册CVE检测器
func (cm *CVEDetectorManager) RegisterCVEDetector(detector detectors.CVEDetectorInterface) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	// 先注册到基础管理器
	if err := cm.DetectorManager.RegisterDetector(detector); err != nil {
		return fmt.Errorf("注册到基础管理器失败: %w", err)
	}
	
	// 注册到CVE特定索引
	cveID := detector.GetCVEID()
	cm.cveDetectors[cveID] = detector
	
	// 更新产品索引
	for _, product := range detector.GetAffectedProducts() {
		cm.productIndex[product] = append(cm.productIndex[product], detector.GetID())
	}
	
	// 更新严重程度索引
	severity := detector.GetSeverity()
	cm.severityIndex[severity] = append(cm.severityIndex[severity], detector.GetID())
	
	// 更新年份索引
	year := extractYearFromCVE(cveID)
	if year > 0 {
		cm.yearIndex[year] = append(cm.yearIndex[year], detector.GetID())
	}
	
	// 更新统计信息
	cm.updateCVEStats()
	
	logger.GetLogger().Infof("CVE检测器注册成功: %s (%s)", detector.GetName(), cveID)
	return nil
}

// GetCVEDetector 获取CVE检测器
func (cm *CVEDetectorManager) GetCVEDetector(cveID string) (detectors.CVEDetectorInterface, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	detector, exists := cm.cveDetectors[cveID]
	if !exists {
		return nil, fmt.Errorf("CVE检测器不存在: %s", cveID)
	}
	
	return detector, nil
}

// DetectCVEs 执行CVE检测
func (cm *CVEDetectorManager) DetectCVEs(ctx context.Context, request *CVEDetectionRequest) (*CVEDetectionResponse, error) {
	startTime := time.Now()
	
	// 选择适用的CVE检测器
	selectedDetectors := cm.selectCVEDetectors(request)
	
	response := &CVEDetectionResponse{
		TotalDetectors:    len(selectedDetectors),
		ExecutedDetectors: 0,
		Results:           make([]*detectors.CVEDetectionResult, 0),
		Vulnerabilities:   make([]*detectors.CVEDetectionResult, 0),
		Errors:            make([]string, 0),
		Statistics: &CVEDetectionStatistics{
			VulnerabilitiesBySeverity: make(map[string]int),
			VulnerabilitiesByCategory: make(map[string]int),
			VulnerabilitiesByYear:     make(map[int]int),
		},
	}
	
	// 并发执行检测
	resultChan := make(chan *detectors.CVEDetectionResult, len(selectedDetectors))
	errorChan := make(chan error, len(selectedDetectors))
	
	// 控制并发数
	semaphore := make(chan struct{}, cm.config.MaxConcurrency)
	
	var wg sync.WaitGroup
	for _, detector := range selectedDetectors {
		wg.Add(1)
		go func(det detectors.CVEDetectorInterface) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			
			// 执行检测
			result, err := det.DetectCVE(ctx, request.Target)
			if err != nil {
				errorChan <- fmt.Errorf("检测器 %s 执行失败: %w", det.GetID(), err)
				return
			}
			
			resultChan <- result
		}(detector)
	}
	
	// 等待所有检测完成
	go func() {
		wg.Wait()
		close(resultChan)
		close(errorChan)
	}()
	
	// 收集结果
	for result := range resultChan {
		response.ExecutedDetectors++
		response.Results = append(response.Results, result)
		
		if result.IsVulnerable {
			response.Vulnerabilities = append(response.Vulnerabilities, result)
			
			// 更新统计
			response.Statistics.VulnerabilitiesBySeverity[result.Severity]++
			response.Statistics.VulnerabilitiesByCategory[result.Category]++
			
			year := extractYearFromCVE(result.CVEID)
			if year > 0 {
				response.Statistics.VulnerabilitiesByYear[year]++
			}
			
			if result.Confidence >= 0.8 {
				response.Statistics.HighConfidenceCount++
			}
			
			if result.PoCExecuted {
				response.Statistics.PoCExecutedCount++
			}
		}
	}
	
	// 收集错误
	for err := range errorChan {
		response.Errors = append(response.Errors, err.Error())
	}
	
	// 计算平均置信度
	if len(response.Vulnerabilities) > 0 {
		var totalConfidence float64
		for _, vuln := range response.Vulnerabilities {
			totalConfidence += vuln.Confidence
		}
		response.Statistics.AverageConfidence = totalConfidence / float64(len(response.Vulnerabilities))
	}
	
	response.ExecutionTime = time.Since(startTime)
	
	return response, nil
}

// selectCVEDetectors 选择适用的CVE检测器
func (cm *CVEDetectorManager) selectCVEDetectors(request *CVEDetectionRequest) []detectors.CVEDetectorInterface {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	var candidates []detectors.CVEDetectorInterface
	
	// 如果指定了CVE过滤器，直接使用
	if len(request.CVEFilters) > 0 {
		for _, cveID := range request.CVEFilters {
			if detector, exists := cm.cveDetectors[cveID]; exists {
				if detector.IsApplicable(request.Target) {
					candidates = append(candidates, detector)
				}
			}
		}
		return candidates
	}
	
	// 否则根据其他过滤条件选择
	for _, detector := range cm.cveDetectors {
		if !detector.IsApplicable(request.Target) {
			continue
		}
		
		// 严重程度过滤
		if len(request.SeverityFilters) > 0 {
			if !contains(request.SeverityFilters, detector.GetSeverity()) {
				continue
			}
		}
		
		// 分类过滤
		if len(request.CategoryFilters) > 0 {
			if !contains(request.CategoryFilters, detector.GetCategory()) {
				continue
			}
		}
		
		// 年份过滤
		if len(request.YearFilters) > 0 {
			year := extractYearFromCVE(detector.GetCVEID())
			if !containsInt(request.YearFilters, year) {
				continue
			}
		}
		
		candidates = append(candidates, detector)
	}
	
	// 按严重程度排序
	sort.Slice(candidates, func(i, j int) bool {
		return getSeverityWeight(candidates[i].GetSeverity()) > getSeverityWeight(candidates[j].GetSeverity())
	})
	
	// 限制数量
	if request.MaxDetectors > 0 && len(candidates) > request.MaxDetectors {
		candidates = candidates[:request.MaxDetectors]
	}
	
	return candidates
}

// loadBuiltinCVEDetectors 加载内置CVE检测器
func (cm *CVEDetectorManager) loadBuiltinCVEDetectors() {
	// 注册Log4Shell检测器
	log4shellDetector := detectors.NewCVE202144228Detector()
	if err := cm.RegisterCVEDetector(log4shellDetector); err != nil {
		logger.GetLogger().Errorf("注册Log4Shell检测器失败: %v", err)
	}
	
	// 可以继续添加其他内置CVE检测器
	// TODO: 添加更多CVE检测器
}

// updateCVEStats 更新CVE统计信息
func (cm *CVEDetectorManager) updateCVEStats() {
	cm.cveStats.TotalCVEDetectors = len(cm.cveDetectors)
	cm.cveStats.LastUpdated = time.Now()
	
	// 重置计数器
	cm.cveStats.DetectorsByYear = make(map[int]int)
	cm.cveStats.DetectorsBySeverity = make(map[string]int)
	cm.cveStats.DetectorsByCategory = make(map[string]int)
	cm.cveStats.DetectorsWithPoC = 0
	cm.cveStats.DetectorsWithExploit = 0
	
	// 重新计算统计
	for _, detector := range cm.cveDetectors {
		// 年份统计
		year := extractYearFromCVE(detector.GetCVEID())
		if year > 0 {
			cm.cveStats.DetectorsByYear[year]++
		}
		
		// 严重程度统计
		cm.cveStats.DetectorsBySeverity[detector.GetSeverity()]++
		
		// 分类统计
		cm.cveStats.DetectorsByCategory[detector.GetCategory()]++
		
		// PoC和Exploit统计
		if detector.GetPoCAvailable() {
			cm.cveStats.DetectorsWithPoC++
		}
		if detector.GetExploitAvailable() {
			cm.cveStats.DetectorsWithExploit++
		}
	}
}

// 工具函数
func extractYearFromCVE(cveID string) int {
	// 从CVE-YYYY-NNNN格式中提取年份
	if len(cveID) >= 8 && cveID[:4] == "CVE-" {
		yearStr := cveID[4:8]
		var year int
		if _, err := fmt.Sscanf(yearStr, "%d", &year); err == nil {
			return year
		}
	}
	return 0
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func containsInt(slice []int, item int) bool {
	for _, i := range slice {
		if i == item {
			return true
		}
	}
	return false
}

func getSeverityWeight(severity string) int {
	switch severity {
	case "critical":
		return 4
	case "high":
		return 3
	case "medium":
		return 2
	case "low":
		return 1
	default:
		return 0
	}
}
