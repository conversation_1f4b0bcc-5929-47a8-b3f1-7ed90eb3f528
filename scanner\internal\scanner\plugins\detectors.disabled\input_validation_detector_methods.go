package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectLengthBypass 检测长度限制绕过
func (d *InputValidationDetector) detectLengthBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试不同长度的输入
	for _, size := range d.lengthTestSizes {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 生成指定长度的测试载荷
		payload := strings.Repeat("A", size)

		// 发送长度测试请求
		resp, err := d.sendInputValidationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查长度绕过响应
		confidence := d.checkLengthBypassResponse(resp, payload, size)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("长度绕过: %d字符", size)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "length-bypass",
				Description: fmt.Sprintf("发现长度限制绕过: %d字符 (置信度: %.2f)", size, confidence),
				Content:     d.extractInputEvidence(resp, "length-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectSpecialCharacterBypass 检测特殊字符绕过
func (d *InputValidationDetector) detectSpecialCharacterBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	for _, char := range d.specialCharacters {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造特殊字符测试载荷
		payload := fmt.Sprintf("test%svalue", char)

		// 发送特殊字符测试请求
		resp, err := d.sendInputValidationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查特殊字符绕过响应
		confidence := d.checkSpecialCharacterBypassResponse(resp, char)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("特殊字符绕过: %s", char)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "special-character-bypass",
				Description: fmt.Sprintf("发现特殊字符绕过: %s (置信度: %.2f)", char, confidence),
				Content:     d.extractInputEvidence(resp, "special-character-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDataTypeBypass 检测数据类型验证绕过
func (d *InputValidationDetector) detectDataTypeBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试数据类型混淆
	typeTestCases := map[string]string{
		"string_as_number":  "\"123\"",
		"number_as_string":  "123",
		"boolean_as_string": "\"true\"",
		"string_as_boolean": "true",
		"array_as_string":   "[1,2,3]",
		"object_as_string":  "{\"key\":\"value\"}",
		"null_as_string":    "null",
		"undefined":         "undefined",
	}

	for testType, payload := range typeTestCases {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送数据类型测试请求
		resp, err := d.sendInputValidationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查数据类型绕过响应
		confidence := d.checkDataTypeBypassResponse(resp, testType, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("数据类型绕过: %s", testType)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "data-type-bypass",
				Description: fmt.Sprintf("发现数据类型验证绕过: %s (置信度: %.2f)", testType, confidence),
				Content:     d.extractInputEvidence(resp, "data-type-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 400)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectFormatBypass 检测格式验证绕过
func (d *InputValidationDetector) detectFormatBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试格式验证绕过
	formatTestCases := map[string]string{
		"invalid_email":    "invalid-email-format",
		"invalid_url":      "not-a-valid-url",
		"invalid_phone":    "123-not-phone",
		"invalid_date":     "2023-13-45",
		"invalid_json":     "{invalid:json}",
		"invalid_xml":      "<invalid>xml<unclosed>",
		"malformed_base64": "invalid=base64=",
		"unicode_bypass":   "\\u003cscript\\u003e",
		"encoding_bypass":  "%3Cscript%3E",
		"case_bypass":      "SCRIPT",
		"space_bypass":     "< script >",
		"comment_bypass":   "<!--script-->",
	}

	for testType, payload := range formatTestCases {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送格式验证测试请求
		resp, err := d.sendInputValidationRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查格式验证绕过响应
		confidence := d.checkFormatBypassResponse(resp, testType, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("格式验证绕过: %s", testType)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "format-bypass",
				Description: fmt.Sprintf("发现格式验证绕过: %s (置信度: %.2f)", testType, confidence),
				Content:     d.extractInputEvidence(resp, "format-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 500)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendInputValidationRequest 发送输入验证请求
func (d *InputValidationDetector) sendInputValidationRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造请求数据
	data := url.Values{}
	data.Set("input", payload)
	data.Set("test", payload)
	data.Set("value", payload)
	data.Set("data", payload)

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkInputValidationResponse 检查输入验证响应（通用方法）
func (d *InputValidationDetector) checkInputValidationResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2
	}

	// 检查格式模式匹配
	for _, pattern := range d.formatPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.1
			break
		}
	}

	// 检查绕过模式匹配
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	return confidence
}

// checkLengthBypassResponse 检查长度绕过响应
func (d *InputValidationDetector) checkLengthBypassResponse(response, payload string, size int) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查是否包含完整的载荷（表示长度限制被绕过）
	if strings.Contains(response, strings.ToLower(payload)) {
		confidence += 0.4
	}

	// 检查长度相关的错误信息
	lengthIndicators := []string{
		"length", "size", "limit", "maxlength", "minlength",
		"too long", "too short", "exceeded", "overflow",
		"长度", "大小", "限制", "超长", "溢出",
	}

	for _, indicator := range lengthIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.2
			break
		}
	}

	// 检查绕过模式匹配
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 根据长度大小调整置信度
	if size > 10000 && confidence > 0.3 {
		confidence += 0.2 // 超大长度更可能是绕过
	}

	return confidence
}

// checkSpecialCharacterBypassResponse 检查特殊字符绕过响应
func (d *InputValidationDetector) checkSpecialCharacterBypassResponse(response, char string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查是否包含特殊字符（表示字符过滤被绕过）
	if strings.Contains(response, strings.ToLower(char)) {
		confidence += 0.4
	}

	// 检查字符相关的错误信息
	charIndicators := []string{
		"character", "char", "invalid", "illegal", "forbidden",
		"not allowed", "blocked", "filtered", "escaped",
		"字符", "无效", "非法", "禁止", "不允许", "过滤",
	}

	for _, indicator := range charIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.2
			break
		}
	}

	// 检查绕过模式匹配
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 根据特殊字符类型调整置信度
	dangerousChars := []string{"<", ">", "'", "\"", "&", ";", "|"}
	for _, dangerous := range dangerousChars {
		if char == dangerous && confidence > 0.3 {
			confidence += 0.2 // 危险字符更可能是绕过
			break
		}
	}

	return confidence
}

// checkDataTypeBypassResponse 检查数据类型绕过响应
func (d *InputValidationDetector) checkDataTypeBypassResponse(response, testType, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查是否包含载荷（表示类型验证被绕过）
	if strings.Contains(response, strings.ToLower(payload)) {
		confidence += 0.4
	}

	// 检查类型相关的错误信息
	typeIndicators := []string{
		"type", "typeof", "datatype", "format", "parse",
		"invalid", "mismatch", "expected", "conversion",
		"类型", "格式", "解析", "无效", "转换", "期望",
	}

	for _, indicator := range typeIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.2
			break
		}
	}

	// 检查绕过模式匹配
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 根据测试类型调整置信度
	if strings.Contains(testType, "string_as_number") || strings.Contains(testType, "number_as_string") {
		if confidence > 0.3 {
			confidence += 0.1 // 字符串/数字混淆更常见
		}
	}

	return confidence
}

// checkFormatBypassResponse 检查格式验证绕过响应
func (d *InputValidationDetector) checkFormatBypassResponse(response, testType, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查是否包含载荷（表示格式验证被绕过）
	if strings.Contains(response, strings.ToLower(payload)) {
		confidence += 0.4
	}

	// 检查格式相关的错误信息
	formatIndicators := []string{
		"format", "pattern", "regex", "validation", "syntax",
		"malformed", "invalid", "parse", "decode", "encode",
		"格式", "模式", "验证", "语法", "解析", "编码",
	}

	for _, indicator := range formatIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.2
			break
		}
	}

	// 检查格式模式匹配
	for _, pattern := range d.formatPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查绕过模式匹配
	for _, pattern := range d.bypassPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 根据测试类型调整置信度
	if strings.Contains(testType, "bypass") && confidence > 0.3 {
		confidence += 0.2 // 明确的绕过测试
	}

	return confidence
}

// extractInputEvidence 提取输入验证证据
func (d *InputValidationDetector) extractInputEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var inputLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "length-bypass":
			if strings.Contains(lineLower, "length") ||
				strings.Contains(lineLower, "size") ||
				strings.Contains(lineLower, "limit") ||
				strings.Contains(lineLower, "maxlength") ||
				strings.Contains(lineLower, "minlength") ||
				strings.Contains(lineLower, "too long") ||
				strings.Contains(lineLower, "too short") ||
				strings.Contains(lineLower, "长度") ||
				strings.Contains(lineLower, "大小") ||
				strings.Contains(lineLower, "限制") {
				inputLines = append(inputLines, line)
			}
		case "special-character-bypass":
			if strings.Contains(lineLower, "character") ||
				strings.Contains(lineLower, "char") ||
				strings.Contains(lineLower, "invalid") ||
				strings.Contains(lineLower, "illegal") ||
				strings.Contains(lineLower, "forbidden") ||
				strings.Contains(lineLower, "not allowed") ||
				strings.Contains(lineLower, "blocked") ||
				strings.Contains(lineLower, "filtered") ||
				strings.Contains(lineLower, "字符") ||
				strings.Contains(lineLower, "无效") ||
				strings.Contains(lineLower, "非法") ||
				strings.Contains(lineLower, "禁止") {
				inputLines = append(inputLines, line)
			}
		case "data-type-bypass":
			if strings.Contains(lineLower, "type") ||
				strings.Contains(lineLower, "typeof") ||
				strings.Contains(lineLower, "datatype") ||
				strings.Contains(lineLower, "format") ||
				strings.Contains(lineLower, "parse") ||
				strings.Contains(lineLower, "conversion") ||
				strings.Contains(lineLower, "类型") ||
				strings.Contains(lineLower, "格式") ||
				strings.Contains(lineLower, "解析") ||
				strings.Contains(lineLower, "转换") {
				inputLines = append(inputLines, line)
			}
		case "format-bypass":
			if strings.Contains(lineLower, "format") ||
				strings.Contains(lineLower, "pattern") ||
				strings.Contains(lineLower, "regex") ||
				strings.Contains(lineLower, "validation") ||
				strings.Contains(lineLower, "syntax") ||
				strings.Contains(lineLower, "malformed") ||
				strings.Contains(lineLower, "decode") ||
				strings.Contains(lineLower, "encode") ||
				strings.Contains(lineLower, "格式") ||
				strings.Contains(lineLower, "模式") ||
				strings.Contains(lineLower, "验证") ||
				strings.Contains(lineLower, "语法") {
				inputLines = append(inputLines, line)
			}
		default:
			if strings.Contains(lineLower, "input") ||
				strings.Contains(lineLower, "validation") ||
				strings.Contains(lineLower, "error") ||
				strings.Contains(lineLower, "invalid") ||
				strings.Contains(lineLower, "bypass") ||
				strings.Contains(lineLower, "filter") ||
				strings.Contains(lineLower, "sanitize") ||
				strings.Contains(lineLower, "escape") ||
				strings.Contains(lineLower, "输入") ||
				strings.Contains(lineLower, "验证") ||
				strings.Contains(lineLower, "错误") ||
				strings.Contains(lineLower, "无效") ||
				strings.Contains(lineLower, "绕过") ||
				strings.Contains(lineLower, "过滤") {
				inputLines = append(inputLines, line)
			}
		}

		if len(inputLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(inputLines) > 0 {
		return strings.Join(inputLines, "\n")
	}

	// 如果没有找到特定的输入验证信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
