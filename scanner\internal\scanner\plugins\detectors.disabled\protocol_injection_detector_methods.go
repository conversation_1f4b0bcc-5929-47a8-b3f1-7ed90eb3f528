package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectHTTPProtocolInjection 检测HTTP协议注入
func (d *ProtocolInjectionDetector) detectHTTPProtocolInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试HTTP协议注入载荷
	for _, payload := range d.httpProtocolPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送HTTP协议注入请求
		resp, err := d.sendProtocolRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查HTTP协议注入响应
		confidence := d.checkHTTPResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("HTTP协议注入: %s", payload[:min(50, len(payload))])
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "http-protocol-injection",
				Description: fmt.Sprintf("发现HTTP协议注入: %s (置信度: %.2f)", payload[:min(20, len(payload))], confidence),
				Content:     d.extractProtocolEvidence(resp, "http-protocol-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectSMTPProtocolInjection 检测SMTP协议注入
func (d *ProtocolInjectionDetector) detectSMTPProtocolInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试SMTP协议注入载荷
	for _, payload := range d.smtpProtocolPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送SMTP协议注入请求
		resp, err := d.sendProtocolRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查SMTP协议注入响应
		confidence := d.checkSMTPResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("SMTP协议注入: %s", payload[:min(50, len(payload))])
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "smtp-protocol-injection",
				Description: fmt.Sprintf("发现SMTP协议注入: %s (置信度: %.2f)", payload[:min(20, len(payload))], confidence),
				Content:     d.extractProtocolEvidence(resp, "smtp-protocol-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectFTPProtocolInjection 检测FTP协议注入
func (d *ProtocolInjectionDetector) detectFTPProtocolInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试FTP协议注入载荷
	for _, payload := range d.ftpProtocolPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送FTP协议注入请求
		resp, err := d.sendProtocolRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查FTP协议注入响应
		confidence := d.checkFTPResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("FTP协议注入: %s", payload[:min(50, len(payload))])
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ftp-protocol-injection",
				Description: fmt.Sprintf("发现FTP协议注入: %s (置信度: %.2f)", payload[:min(20, len(payload))], confidence),
				Content:     d.extractProtocolEvidence(resp, "ftp-protocol-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectLDAPProtocolInjection 检测LDAP协议注入
func (d *ProtocolInjectionDetector) detectLDAPProtocolInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试LDAP协议注入载荷
	for _, payload := range d.ldapProtocolPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送LDAP协议注入请求
		resp, err := d.sendProtocolRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查LDAP协议注入响应
		confidence := d.checkLDAPResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("LDAP协议注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ldap-protocol-injection",
				Description: fmt.Sprintf("发现LDAP协议注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractProtocolEvidence(resp, "ldap-protocol-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendProtocolRequest 发送协议注入请求
func (d *ProtocolInjectionDetector) sendProtocolRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendProtocolGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST表单注入
	postResp, err := d.sendProtocolPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 尝试头部注入
	headerResp, err := d.sendProtocolHeaderRequest(ctx, targetURL, payload)
	if err == nil && headerResp != "" {
		return headerResp, nil
	}

	// 返回POST响应（即使有错误）
	if postResp != "" {
		return postResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendProtocolGETRequest 发送协议注入GET请求
func (d *ProtocolInjectionDetector) sendProtocolGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendProtocolPOSTRequest 发送协议注入POST请求
func (d *ProtocolInjectionDetector) sendProtocolPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendProtocolHeaderRequest 发送协议注入头部请求
func (d *ProtocolInjectionDetector) sendProtocolHeaderRequest(ctx context.Context, targetURL, payload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置基础请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Connection", "keep-alive")

	// 尝试在各种头部中注入载荷
	protocolHeaders := []string{
		"X-Forwarded-For", "X-Real-IP", "X-Originating-IP", "X-Remote-IP",
		"X-Forwarded-Host", "X-Forwarded-Proto", "X-Forwarded-Server",
		"Referer", "Origin", "Host", "User-Agent", "Accept", "Accept-Language",
		"X-Requested-With", "X-Custom-Header", "X-Protocol-Test",
	}

	for _, header := range protocolHeaders {
		req.Header.Set(header, payload)
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkHTTPResponse 检查HTTP协议注入响应
func (d *ProtocolInjectionDetector) checkHTTPResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能包含注入内容
	} else if strings.Contains(response, "status: 302") {
		confidence += 0.4 // 重定向可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.2 // 服务器错误可能表示注入影响
	}

	// 检查HTTP协议模式匹配
	for _, pattern := range d.httpPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查HTTP协议注入特定指示器
	httpIndicators := []string{
		"set-cookie:", "location:", "x-injected-header:", "cache-control:",
		"content-type:", "%0d%0a", "%0a%0d", "\\r\\n", "\\n\\r",
		"http/1.1 200 ok", "http/1.0 200 ok", "content-length:",
		"transfer-encoding:", "注入头部", "恶意请求", "协议注入", "响应分割",
	}

	for _, indicator := range httpIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // HTTP协议注入特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映（可能表示注入成功）
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkSMTPResponse 检查SMTP协议注入响应
func (d *ProtocolInjectionDetector) checkSMTPResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能包含SMTP注入内容
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示SMTP注入影响
	}

	// 检查SMTP协议模式匹配
	for _, pattern := range d.smtpPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查SMTP协议注入特定指示器
	smtpIndicators := []string{
		"mail from:", "rcpt to:", "data", "quit", "helo",
		"220", "250 ok", "354 start mail", "221 bye", "550",
		"subject:", "from:", "to:", "bcc:", "cc:",
		"邮件注入", "发件人攻击", "主题注入", "邮件伪造",
	}

	for _, indicator := range smtpIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // SMTP协议注入特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkFTPResponse 检查FTP协议注入响应
func (d *ProtocolInjectionDetector) checkFTPResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能包含FTP注入内容
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示FTP注入影响
	}

	// 检查FTP协议模式匹配
	for _, pattern := range d.ftpPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查FTP协议注入特定指示器
	ftpIndicators := []string{
		"user", "pass", "list", "pwd", "cwd",
		"220", "230 user logged in", "331 password required", "226 transfer complete", "550",
		"../", "..\\", "/etc/passwd", "\\windows\\system32", "/tmp/",
		"文件注入", "路径遍历", "上传攻击", "传输注入",
	}

	for _, indicator := range ftpIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // FTP协议注入特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkLDAPResponse 检查LDAP协议注入响应
func (d *ProtocolInjectionDetector) checkLDAPResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能包含LDAP注入内容
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示LDAP注入影响
	}

	// 检查LDAP协议模式匹配
	for _, pattern := range d.ldapPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查LDAP协议注入特定指示器
	ldapIndicators := []string{
		"*)(", "(|(", "(&(", "(!(", "objectclass=",
		"userpassword", "sambantpassword", "unicodepwd", "useraccountcontrol",
		"ldap error", "invalid filter", "bad search", "syntax error", "authentication failed",
		"ldap注入", "过滤器注入", "认证绕过", "用户泄露",
	}

	for _, indicator := range ldapIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // LDAP协议注入特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// extractProtocolEvidence 提取协议注入证据
func (d *ProtocolInjectionDetector) extractProtocolEvidence(response, protocolType string) string {
	// 限制证据长度
	maxLength := 500
	if len(response) > maxLength {
		response = response[:maxLength] + "..."
	}

	// 根据协议类型提取相关证据
	switch protocolType {
	case "http-protocol-injection":
		return d.extractHTTPEvidence(response)
	case "smtp-protocol-injection":
		return d.extractSMTPEvidence(response)
	case "ftp-protocol-injection":
		return d.extractFTPEvidence(response)
	case "ldap-protocol-injection":
		return d.extractLDAPEvidence(response)
	default:
		return response
	}
}

// extractHTTPEvidence 提取HTTP协议注入证据
func (d *ProtocolInjectionDetector) extractHTTPEvidence(response string) string {
	evidence := "HTTP协议注入证据:\n"

	// 查找HTTP协议注入特定内容
	httpIndicators := []string{
		"set-cookie:", "location:", "x-injected-header:", "cache-control:",
		"content-type:", "%0d%0a", "%0a%0d", "\\r\\n",
		"http/1.1 200 ok", "content-length:", "transfer-encoding:",
	}

	for _, indicator := range httpIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现HTTP协议注入指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractSMTPEvidence 提取SMTP协议注入证据
func (d *ProtocolInjectionDetector) extractSMTPEvidence(response string) string {
	evidence := "SMTP协议注入证据:\n"

	// 查找SMTP协议注入特定内容
	smtpIndicators := []string{
		"mail from:", "rcpt to:", "data", "quit", "helo",
		"220", "250 ok", "354 start mail", "221 bye", "550",
		"subject:", "from:", "to:", "bcc:", "cc:",
	}

	for _, indicator := range smtpIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现SMTP协议注入指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractFTPEvidence 提取FTP协议注入证据
func (d *ProtocolInjectionDetector) extractFTPEvidence(response string) string {
	evidence := "FTP协议注入证据:\n"

	// 查找FTP协议注入特定内容
	ftpIndicators := []string{
		"user", "pass", "list", "pwd", "cwd",
		"220", "230 user logged in", "331 password required", "226 transfer complete", "550",
		"../", "..\\", "/etc/passwd", "\\windows\\system32", "/tmp/",
	}

	for _, indicator := range ftpIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现FTP协议注入指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractLDAPEvidence 提取LDAP协议注入证据
func (d *ProtocolInjectionDetector) extractLDAPEvidence(response string) string {
	evidence := "LDAP协议注入证据:\n"

	// 查找LDAP协议注入特定内容
	ldapIndicators := []string{
		"*)(", "(|(", "(&(", "(!(", "objectclass=",
		"userpassword", "sambantpassword", "unicodepwd", "useraccountcontrol",
		"ldap error", "invalid filter", "bad search", "syntax error", "authentication failed",
	}

	for _, indicator := range ldapIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现LDAP协议注入指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}
